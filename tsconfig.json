{"compilerOptions": {"outDir": "build/dist", "module": "esnext", "target": "esnext", "lib": ["esnext", "dom"], "sourceMap": true, "baseUrl": ".", "jsx": "react-jsx", "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "moduleResolution": "node", "forceConsistentCasingInFileNames": true, "noImplicitReturns": true, "suppressImplicitAnyIndexErrors": true, "noUnusedLocals": true, "allowJs": true, "skipLibCheck": true, "experimentalDecorators": true, "strict": true, "paths": {"@/*": ["./src/*"], "@@/*": ["./src/.oula/*"], "@config/*": ["./config/*"]}}, "include": ["mock/**/*", "src/**/*", "tests/**/*", "test/**/*", "__test__/**/*", "typings/**/*", "config/**/*", ".eslintrc.js", ".stylelintrc.js", ".prettierrc.js", "jest.config.js", "mock/*"], "exclude": ["node_modules", "build", "dist", "scripts", "src/.umi/*", "webpack", "jest"]}