# -*- coding: utf-8 -*-
import time
import requests
if __name__ == '__main__':
    begin = 1
    header = {
        "operationID": str(time.time()) + "_" + str(begin),
        "token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySUQiOiJpbUFkbWluIiwiUGxhdGZvcm1JRCI6MTAsImV4cCI6MTc1NTYxNTM0NCwiaWF0IjoxNzQ3ODM5MzM5fQ.hZlSXP-X0o_YGTHLiq96V5YAbeMQQtJNCw6HTcmYECY"
    }
    proxies = {
        "http": "",
        "https": "",
    }
    while begin < 200:
        data = {
            "content":{
                # "content": "🚨🚨🚨\n\n---\n\n**级别:** ERROR \n**环境:** dev_nj \n**时间:** 2025-05-21 17:18:00.935 \n**系统:** 000228@open-im-server \n**主机:** ************* [ 🖥️](http://eip.htsc.com.cn/portalweb/portal2/itrmss/v2/index.html#/itrmss/workspace) \n**包:** htbridge/employee.go:313 \n**方法:** github.com/openimsdk/open-im-server/v3/internal/rpc/htbridge.(*htbridgeServer).RefreshEmployees.func1 \n**行号:** 313 \n**TraceId:** edb155a1-1bc5-476d-8ea3-038919529821 \n**路径:** /openim-server/internal/rpc/htbridge/employee.go \n\n---\n**内容:**\n{\"level\":\"error\",\"ts\":\"2025-05-21T17:18:00.935311576+08:00\",\"caller\":\"htbridge/employee.go:313\",\"msg\":\"UserInfo not exists in user table.\",\"operationID\":\"cron_htbridge_1_1747818900000\",\"opUserID\":\"imAdmin\",\"userID\":\"1000aa5a14573ce55888bcb6f0c3de42e0e7\"}\n\n[🔎 查看日志详情](http://eip.htsc.com.cn/paas/dashboard.html#/apm/main/logs)\n\n---\n**标签:** `sdk_version: 2.0.2`, `app_version: (devel)`, `go_version: go1.23.8`, `app_no: 000228`, `app_name: im-server-rpc-htbridge`" +  str(begin)
                "content":str(begin) + '-----' + "im-server-rpc-htbridge:"
            },
            "contentType": 101,
            "groupID": "2836141827",
            "sendID": "6c73216bdd10f61ad55d47f0e4d675a731",
            "sessionType": 3
        }
        begin += 1
        ans = requests.post("http://openim-api.saassit.htsc.com.cn/msg/send_msg", headers=header, json=data, proxies=proxies)
        # time.sleep(0.05)
        print(ans.text + str(begin))