import {
  getCleanText,
  replaceEmoji2Str,
  convertChar,
} from '../../src/components/CKEditor/utils';

// 模拟 DOMParser
class MockDOMParser {
  parseFromString(text: string, mimeType: string) {
    return {
      querySelectorAll: () => [],
    };
  }
}

// 替换全局 DOMParser
(global as any).DOMParser = MockDOMParser;

// 模拟 document.createElement
if (!(global as any).document) {
  (global as any).document = {
    createElement: (tagName: string) => {
      if (tagName === 'textarea') {
        const element = {
          __value: '',
          get value() {
            return this.__value;
          },
          get innerHTML() {
            return this.__value;
          },
          set innerHTML(value: string) {
            // 简单的HTML实体解码模拟
            this.__value = value
              .replace(/&lt;/g, '<')
              .replace(/&gt;/g, '>')
              .replace(/&amp;/g, '&')
              .replace(/&quot;/g, '"')
              .replace(/&#39;/g, "'")
              .replace(/&nbsp;/g, ' ');
          },
        };
        return element;
      }
      return {};
    },
  };
}

describe('CKEditor utils', () => {
  describe('getCleanText', () => {
    it('should replace </p><p> with newline but keep HTML tags', () => {
      const html = '<p>Hello</p><p>World</p>';
      const result = getCleanText(html);
      // 注意：HTML标签不会被移除，因为代码中注释了移除标签的逻辑
      expect(result).toBe('<p>Hello\nWorld</p>');
    });

    it('should replace <br> tags with newline', () => {
      const html = 'Hello<br>World';
      const result = getCleanText(html);
      expect(result).toBe('Hello\nWorld');
    });

    it('should replace <br/> tags with newline', () => {
      const html = 'Hello<br/>World';
      const result = getCleanText(html);
      expect(result).toBe('Hello\nWorld');
    });

    it('should replace <br /> tags with newline', () => {
      const html = 'Hello<br />World';
      const result = getCleanText(html);
      expect(result).toBe('Hello\nWorld');
    });

    it('should convert &nbsp; to space', () => {
      const html = 'Hello&nbsp;World';
      const result = getCleanText(html);
      expect(result).toBe('Hello World');
    });

    it('should filter empty lines', () => {
      const html = '<p>Hello</p>\n\n<p>World</p>';
      const result = getCleanText(html);
      expect(result).toBe('<p>Hello</p>\n<p>World</p>');
    });

    it('should keep URLs', () => {
      const html = 'https://example.com';
      const result = getCleanText(html);
      expect(result).toBe('https://example.com');
    });

    it('should filter lines with only quote markers', () => {
      const html = 'Hello\n>\nWorld';
      const result = getCleanText(html);
      expect(result).toBe('Hello\nWorld');
    });

    it('should filter lines with only ordered list markers', () => {
      const html = 'Hello\n1. \nWorld';
      const result = getCleanText(html);
      expect(result).toBe('Hello\nWorld');
    });

    it('should filter lines with mixed quote and ordered list markers', () => {
      const html = 'Hello\n> 1. \nWorld';
      const result = getCleanText(html);
      expect(result).toBe('Hello\nWorld');
    });

    it('should filter lines with only asterisk', () => {
      const html = 'Hello\n*\nWorld';
      const result = getCleanText(html);
      expect(result).toBe('Hello\nWorld');
    });

    it('should filter lines with markdown syntax characters', () => {
      const html = 'Hello\n#\n-\n+\n[]\n|\n`\nWorld';
      const result = getCleanText(html);
      expect(result).toBe('Hello\nWorld');
    });

    it('should trim the result', () => {
      const html = '  Hello World  ';
      const result = getCleanText(html);
      expect(result).toBe('Hello World');
    });

    it('should handle multiple consecutive newlines', () => {
      const html = 'Hello\n\n\n\nWorld';
      const result = getCleanText(html);
      expect(result).toBe('Hello\nWorld');
    });

    it('should preserve content with URLs mixed with other text', () => {
      const html = 'Check this: https://example.com for more info';
      const result = getCleanText(html);
      expect(result).toBe('Check this: https://example.com for more info');
    });

    it('should handle empty input', () => {
      const html = '';
      const result = getCleanText(html);
      expect(result).toBe('');
    });

    it('should handle whitespace-only input', () => {
      const html = '   \n  \n   ';
      const result = getCleanText(html);
      expect(result).toBe('');
    });

    // Markdown 语法测试用例
    describe('Markdown syntax filtering', () => {
      it('should filter lines with only heading markers', () => {
        const html = 'Hello\n#\n##\n###\n####\n#####\n######\nWorld';
        const result = getCleanText(html);
        expect(result).toBe('Hello\nWorld');
      });

      it('should filter lines with only unordered list markers', () => {
        const html = 'Hello\n-\n+\n*\n- \n+ \n* \nWorld';
        const result = getCleanText(html);
        expect(result).toBe('Hello\nWorld');
      });

      it('should filter lines with various ordered list markers', () => {
        const html = 'Hello\n1.\n2. \n10.\n100. \nWorld';
        const result = getCleanText(html);
        expect(result).toBe('Hello\nWorld');
      });

      it('should filter lines with blockquote markers', () => {
        const html = 'Hello\n>\n> \n>>\n>>> \nWorld';
        const result = getCleanText(html);
        expect(result).toBe('Hello\nWorld');
      });

      it('should not filter code block markers when followed by actual code', () => {
        const html = 'hello\n\n```\nconst x = 1;\nconst y = 2;\n```\n\nworld\n';
        const result = getCleanText(html);
        expect(result).toBe(
          'hello\n```\nconst x = 1;\nconst y = 2;\n```\nworld'
        );
      });

      it('should filter lines with table markers', () => {
        const html = 'Hello\n|\n| |\n|---|\n|---|---|\nWorld';
        const result = getCleanText(html);
        expect(result).toBe('Hello\nWorld');
      });

      it('should filter complex mixed markdown syntax', () => {
        const html = 'Hello\n> # \n> 1. *\n>> - `\n>>> + []\nWorld';
        const result = getCleanText(html);
        expect(result).toBe('Hello\nWorld');
      });

      it('should keep lines with actual content mixed with markdown', () => {
        const html = 'Hello\n# Heading\n- Item 1\n> Quote text\n`code`\nWorld';
        const result = getCleanText(html);
        expect(result).toBe(
          'Hello\n# Heading\n- Item 1\n> Quote text\n`code`\nWorld'
        );
      });

      it('should filter nested quote and list combinations', () => {
        const html = 'Hello\n> 1. \n>> 2. \n>>> - \n>>>> + \nWorld';
        const result = getCleanText(html);
        expect(result).toBe('Hello\nWorld');
      });

      it('should handle mixed whitespace with markdown syntax', () => {
        const html = 'Hello\n  >  \n   1.   \n    *    \n     #     \nWorld';
        const result = getCleanText(html);
        expect(result).toBe('Hello\nWorld');
      });

      it('should preserve URLs even when mixed with markdown-like syntax', () => {
        const html =
          'hello\n\n<https://example.com>\n\n> <https://example.com>\n>\n> 1. <https://example.com>\n\n';
        const result = getCleanText(html);
        expect(result).toBe(
          'hello\n<https://example.com>\n> <https://example.com>\n> 1. <https://example.com>'
        );
      });

      it('should handle complex real-world markdown structure', () => {
        const html = `Content
>
> 1.
>>
>>> *
#
##
---
***
|
More content
https://example.com
Final content`;
        const result = getCleanText(html);
        expect(result).toBe(
          'Content\nMore content\nhttps://example.com\nFinal content'
        );
      });
    });
  });

  describe('replaceEmoji2Str', () => {
    it('should return the original text when no emoji is present', () => {
      const text = 'Hello World';
      const result = replaceEmoji2Str(text);
      expect(result).toBe(text);
    });

    it('should handle empty string', () => {
      const text = '';
      const result = replaceEmoji2Str(text);
      expect(result).toBe('');
    });

    it('should handle text with HTML but no emojis', () => {
      const text = '<p>Hello <strong>World</strong></p>';
      const result = replaceEmoji2Str(text);
      expect(result).toBe(text);
    });

    it('should not modify text without emojione class', () => {
      const text = '<img src="emoji.png" alt="😀" class="other-class">';
      const result = replaceEmoji2Str(text);
      expect(result).toBe(text);
    });

    // 注意：由于我们的 mock DOMParser 返回空数组，实际的 emoji 替换不会发生
    // 在真实环境中，这个函数会查找 .emojione 元素并用 alt 属性替换
    it('should handle text with potential emoji markup', () => {
      const text = '<img class="emojione" alt="😀" src="emoji.png">';
      const result = replaceEmoji2Str(text);
      // 由于 mock 的限制，这里不会发生实际替换
      expect(result).toBe(text);
    });
  });

  describe('convertChar', () => {
    it('should replace &nbsp; with space', () => {
      const text = 'Hello&nbsp;World';
      const result = convertChar(text);
      expect(result).toBe('Hello World');
    });

    it('should replace multiple &nbsp; occurrences', () => {
      const text = 'Hello&nbsp;World&nbsp;!';
      const result = convertChar(text);
      expect(result).toBe('Hello World !');
    });

    it('should handle case insensitive &nbsp;', () => {
      const text = 'Hello&NBSP;World&Nbsp;Test&nBsP;End';
      const result = convertChar(text);
      expect(result).toBe('Hello World Test End');
    });

    it('should handle empty string', () => {
      const text = '';
      const result = convertChar(text);
      expect(result).toBe('');
    });

    it('should handle text without &nbsp;', () => {
      const text = 'Hello World';
      const result = convertChar(text);
      expect(result).toBe('Hello World');
    });

    it('should handle consecutive &nbsp; entities', () => {
      const text = 'Hello&nbsp;&nbsp;&nbsp;World';
      const result = convertChar(text);
      expect(result).toBe('Hello   World');
    });

    it('should handle &nbsp; at the beginning and end', () => {
      const text = '&nbsp;Hello World&nbsp;';
      const result = convertChar(text);
      expect(result).toBe(' Hello World ');
    });

    it('should handle mixed case and partial matches', () => {
      const text = 'Hello&nbsp;World&nbspTest&NBSP;End&nbs;Keep';
      const result = convertChar(text);
      expect(result).toBe('Hello World&nbspTest End&nbs;Keep');
    });
  });
});
