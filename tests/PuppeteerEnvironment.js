const NodeEnvironment = require('jest-environment-node').default;

class PuppeteerEnvironment extends NodeEnvironment {
  async setup() {
    await super.setup();
    // 设置全局变量，模拟DOM环境
    this.global.DOMParser = class DOMParser {
      parseFromString(text, mimeType) {
        return {
          querySelectorAll: () => [],
        };
      }
    };

    this.global.document = {
      createElement: (tagName) => {
        if (tagName === 'textarea') {
          return {
            innerHTML: '',
            get value() {
              return this.innerHTML;
            },
          };
        }
        return {};
      },
    };
  }

  async teardown() {
    await super.teardown();
  }
}

module.exports = PuppeteerEnvironment;
