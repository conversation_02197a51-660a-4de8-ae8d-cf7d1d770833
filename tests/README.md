# 单元测试说明

## 测试框架

本项目使用 Jest 作为测试框架，配合 babel-jest 进行 TypeScript 文件的转换。

## 目录结构

- `tests/`: 测试相关的配置和环境文件
  - `PuppeteerEnvironment.js`: 自定义测试环境，提供DOM模拟
  - `README.md`: 测试说明文档
  - `__tests__/`: 测试文件目录
    - `*.test.ts`: 测试文件

## 运行测试

```bash
# 运行所有测试
npm test

# 监视模式运行测试
npm run test:watch

# 生成测试覆盖率报告
npm run test:coverage
```

## 编写测试

测试文件应放在组件目录下的 `__tests__` 文件夹中，文件名格式为 `*.test.ts` 或 `*.test.tsx`。

### 示例

```typescript
// src/components/Example/__tests__/Example.test.tsx
import { render } from '@testing-library/react';
import Example from '../Example';

describe('Example', () => {
  it('renders correctly', () => {
    const { getByText } = render(<Example />);
    expect(getByText('Example')).toBeInTheDocument();
  });
});
```

## 注意事项

1. 对于依赖DOM的测试，可以使用 `@testing-library/react` 和 `@testing-library/jest-dom` 进行辅助。
2. 对于需要模拟的模块，可以使用 Jest 的 mock 功能。
3. 对于异步测试，可以使用 async/await 或 done 回调。