function lamejs(){function U(f){return new Int32Array(f)}function G(f){return new Float32Array(f)}function ha(f){if(1==f.length)return G(f[0]);var k=f[0];f=f.slice(1);for(var r=[],x=0;x<k;x++)r.push(ha(f));return r}function Ua(f){if(1==f.length)return U(f[0]);var k=f[0];f=f.slice(1);for(var r=[],x=0;x<k;x++)r.push(Ua(f));return r}function fc(f){if(1==f.length)return new Int16Array(f[0]);var k=f[0];f=f.slice(1);for(var r=[],x=0;x<k;x++)r.push(fc(f));return r}function Tb(f){if(1==f.length)return Array(f[0]);
var k=f[0];f=f.slice(1);for(var r=[],x=0;x<k;x++)r.push(Tb(f));return r}function pa(f){this.ordinal=f}function J(f){this.ordinal=f}function ka(f){this.ordinal=function(){return f}}function ic(){this.getLameVersion=function(){return"3.98.4"};this.getLameShortVersion=function(){return"3.98.4"};this.getLameVeryShortVersion=function(){return"LAME3.98r"};this.getPsyVersion=function(){return"0.93"};this.getLameUrl=function(){return"http://www.mp3dev.org/"};this.getLameOsBitness=function(){return"32bits"}}
function fb(){function x(a){this.bits=0|a}function k(a,e,c,b,g,l){e=.5946/e;for(a>>=1;0!=a--;)g[l++]=e>c[b++]?0:1,g[l++]=e>c[b++]?0:1}function r(a,e,c,b,g,l){a>>=1;var h=a%2;for(a>>=1;0!=a--;){var p=c[b++]*e;var m=c[b++]*e;var n=0|p;var d=c[b++]*e;var z=0|m;var E=c[b++]*e;var f=0|d;p+=u.adj43[n];n=0|E;m+=u.adj43[z];g[l++]=0|p;d+=u.adj43[f];g[l++]=0|m;E+=u.adj43[n];g[l++]=0|d;g[l++]=0|E}0!=h&&(p=c[b++]*e,m=c[b++]*e,p+=u.adj43[0|p],m+=u.adj43[0|m],g[l++]=0|p,g[l++]=0|m)}function K(a,e,c,b){var m,g=
e,h=m=0;do{var p=a[g++],n=a[g++];m<p&&(m=p);h<n&&(h=n)}while(g<c);m<h&&(m=h);switch(m){case 0:return m;case 1:g=e;e=0;m=v.ht[1].hlen;do h=2*a[g+0]+a[g+1],g+=2,e+=m[h];while(g<c);b.bits+=e;return 1;case 2:case 3:g=e;e=d[m-1];m=0;h=v.ht[e].xlen;p=2==e?v.table23:v.table56;do n=a[g+0]*h+a[g+1],g+=2,m+=p[n];while(g<c);a=m&65535;m>>=16;m>a&&(m=a,e++);b.bits+=m;return e;case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 12:case 13:case 14:case 15:g=e;e=d[m-1];var p=h=m=0,n=v.ht[e].xlen,l=v.ht[e].hlen,
f=v.ht[e+1].hlen,q=v.ht[e+2].hlen;do{var D=a[g+0]*n+a[g+1],g=g+2;m+=l[D];h+=f[D];p+=q[D]}while(g<c);a=e;m>h&&(m=h,a++);m>p&&(m=p,a=e+2);b.bits+=m;return a;default:if(m>Y.IXMAX_VAL)return b.bits=Y.LARGE_BITS,-1;m-=15;for(g=24;32>g&&!(v.ht[g].linmax>=m);g++);for(h=g-8;24>h&&!(v.ht[h].linmax>=m);h++);m=h;p=65536*v.ht[m].xlen+v.ht[g].xlen;h=0;do n=a[e++],l=a[e++],0!=n&&(14<n&&(n=15,h+=p),n*=16),0!=l&&(14<l&&(l=15,h+=p),n+=l),h+=v.largetbl[n];while(e<c);a=h&65535;h>>=16;h>a&&(h=a,m=g);b.bits+=h;return m}}
function C(a,e,b,c,g,l,h,p){for(var m=e.big_values,n=2;n<f.SBMAX_l+1;n++){var d=a.scalefac_band.l[n];if(d>=m)break;var E=g[n-2]+e.count1bits;if(b.part2_3_length<=E)break;E=new x(E);d=K(c,d,m,E);E=E.bits;b.part2_3_length<=E||(b.assign(e),b.part2_3_length=E,b.region0_count=l[n-2],b.region1_count=n-2-l[n-2],b.table_select[0]=h[n-2],b.table_select[1]=p[n-2],b.table_select[2]=d)}}var u=null;this.qupvt=null;this.setModules=function(a){u=this.qupvt=a};var fa=[[0,0],[0,0],[0,0],[0,0],[0,0],[0,1],[1,1],[1,
1],[1,2],[2,2],[2,3],[2,3],[3,4],[3,4],[3,4],[4,5],[4,5],[4,6],[5,6],[5,6],[5,7],[6,7],[6,7]],d=[1,2,5,7,7,10,10,13,13,13,13,13,13,13,13];this.noquant_count_bits=function(a,e,b){var c=e.l3_enc,m=Math.min(576,e.max_nonzero_coeff+2>>1<<1);null!=b&&(b.sfb_count1=0);for(;1<m&&0==(c[m-1]|c[m-2]);m-=2);e.count1=m;for(var g=0,h=0;3<m&&!(1<((c[m-1]|c[m-2]|c[m-3]|c[m-4])&2147483647));m-=4){var p=2*(2*(2*c[m-4]+c[m-3])+c[m-2])+c[m-1];g+=v.t32l[p];h+=v.t33l[p]}p=g;e.count1table_select=0;g>h&&(p=h,e.count1table_select=
1);e.count1bits=p;e.big_values=m;if(0==m)return p;e.block_type==f.SHORT_TYPE?(g=3*a.scalefac_band.s[3],g>e.big_values&&(g=e.big_values),h=e.big_values):e.block_type==f.NORM_TYPE?(g=e.region0_count=a.bv_scf[m-2],h=e.region1_count=a.bv_scf[m-1],h=a.scalefac_band.l[g+h+2],g=a.scalefac_band.l[g+1],h<m&&(p=new x(p),e.table_select[2]=K(c,h,m,p),p=p.bits)):(e.region0_count=7,e.region1_count=f.SBMAX_l-1-7-1,g=a.scalefac_band.l[8],h=m,g>h&&(g=h));g=Math.min(g,m);h=Math.min(h,m);0<g&&(p=new x(p),e.table_select[0]=
K(c,0,g,p),p=p.bits);g<h&&(p=new x(p),e.table_select[1]=K(c,g,h,p),p=p.bits);2==a.use_best_huffman&&(e.part2_3_length=p,best_huffman_divide(a,e),p=e.part2_3_length);if(null!=b&&e.block_type==f.NORM_TYPE){for(c=0;a.scalefac_band.l[c]<e.big_values;)c++;b.sfb_count1=c}return p};this.count_bits=function(a,e,c,b){var m=c.l3_enc,g=Y.IXMAX_VAL/u.IPOW20(c.global_gain);if(c.xrpow_max>g)return Y.LARGE_BITS;var g=u.IPOW20(c.global_gain),h,p=0,n=0,l=0,d=0,q=0,w=m,t=0,D=e,H=0;var B=null!=b&&c.global_gain==b.global_gain;
var Q=c.block_type==f.SHORT_TYPE?38:21;for(h=0;h<=Q;h++){var V=-1;if(B||c.block_type==f.NORM_TYPE)V=c.global_gain-(c.scalefac[h]+(0!=c.preflag?u.pretab[h]:0)<<c.scalefac_scale+1)-8*c.subblock_gain[c.window[h]];if(B&&b.step[h]==V)0!=n&&(r(n,g,D,H,w,t),n=0),0!=l&&(k(l,g,D,H,w,t),l=0);else{var N=c.width[h];p+c.width[h]>c.max_nonzero_coeff&&(h=c.max_nonzero_coeff-p+1,qa.fill(m,c.max_nonzero_coeff,576,0),N=h,0>N&&(N=0),h=Q+1);0==n&&0==l&&(w=m,t=q,D=e,H=d);null!=b&&0<b.sfb_count1&&h>=b.sfb_count1&&0<b.step[h]&&
V>=b.step[h]?(0!=n&&(r(n,g,D,H,w,t),n=0,w=m,t=q,D=e,H=d),l+=N):(0!=l&&(k(l,g,D,H,w,t),l=0,w=m,t=q,D=e,H=d),n+=N);if(0>=N){0!=l&&(k(l,g,D,H,w,t),l=0);0!=n&&(r(n,g,D,H,w,t),n=0);break}}h<=Q&&(q+=c.width[h],d+=c.width[h],p+=c.width[h])}0!=n&&r(n,g,D,H,w,t);0!=l&&k(l,g,D,H,w,t);if(0!=(a.substep_shaping&2))for(g=0,Q=.634521682242439/u.IPOW20(c.global_gain+c.scalefac_scale),p=0;p<c.sfbmax;p++)if(B=c.width[p],0==a.pseudohalf[p])g+=B;else for(n=g,g+=B;n<g;++n)m[n]=e[n]>=Q?m[n]:0;return this.noquant_count_bits(a,
c,b)};this.best_huffman_divide=function(a,e){var c=new rb,b=e.l3_enc,g=U(23),l=U(23),h=U(23),p=U(23);if(e.block_type!=f.SHORT_TYPE||1!=a.mode_gr){c.assign(e);if(e.block_type==f.NORM_TYPE){for(var d=e.big_values,q=0;22>=q;q++)g[q]=Y.LARGE_BITS;for(q=0;16>q;q++){var w=a.scalefac_band.l[q+1];if(w>=d)break;for(var t=0,D=new x(t),k=K(b,0,w,D),t=D.bits,I=0;8>I;I++){var H=a.scalefac_band.l[q+I+2];if(H>=d)break;D=t;D=new x(D);H=K(b,w,H,D);D=D.bits;g[q+I]>D&&(g[q+I]=D,l[q+I]=q,h[q+I]=k,p[q+I]=H)}}C(a,c,e,
b,g,l,h,p)}d=c.big_values;if(!(0==d||1<(b[d-2]|b[d-1])||(d=e.count1+2,576<d))){c.assign(e);c.count1=d;for(w=q=0;d>c.big_values;d-=4)t=2*(2*(2*b[d-4]+b[d-3])+b[d-2])+b[d-1],q+=v.t32l[t],w+=v.t33l[t];c.big_values=d;c.count1table_select=0;q>w&&(q=w,c.count1table_select=1);c.count1bits=q;c.block_type==f.NORM_TYPE?C(a,c,e,b,g,l,h,p):(c.part2_3_length=q,q=a.scalefac_band.l[8],q>d&&(q=d),0<q&&(a=new x(c.part2_3_length),c.table_select[0]=K(b,0,q,a),c.part2_3_length=a.bits),d>q&&(a=new x(c.part2_3_length),
c.table_select[1]=K(b,q,d,a),c.part2_3_length=a.bits),e.part2_3_length>c.part2_3_length&&e.assign(c))}}};var c=[1,1,1,1,8,2,2,2,4,4,4,8,8,8,16,16],w=[1,2,4,8,1,2,4,8,2,4,8,2,4,8,4,8],a=[0,0,0,0,3,1,1,1,2,2,2,3,3,3,4,4],b=[0,1,2,3,0,1,2,3,1,2,3,1,2,3,2,3];fb.slen1_tab=a;fb.slen2_tab=b;this.best_scalefac_store=function(e,g,m,n){var l=n.tt[g][m],d,h,p=0;for(d=h=0;d<l.sfbmax;d++){var q=l.width[d];h+=q;for(q=-q;0>q&&0==l.l3_enc[q+h];q++);0==q&&(l.scalefac[d]=p=-2)}if(0==l.scalefac_scale&&0==l.preflag){for(d=
h=0;d<l.sfbmax;d++)0<l.scalefac[d]&&(h|=l.scalefac[d]);if(0==(h&1)&&0!=h){for(d=0;d<l.sfbmax;d++)0<l.scalefac[d]&&(l.scalefac[d]>>=1);l.scalefac_scale=p=1}}if(0==l.preflag&&l.block_type!=f.SHORT_TYPE&&2==e.mode_gr){for(d=11;d<f.SBPSY_l&&!(l.scalefac[d]<u.pretab[d]&&-2!=l.scalefac[d]);d++);if(d==f.SBPSY_l){for(d=11;d<f.SBPSY_l;d++)0<l.scalefac[d]&&(l.scalefac[d]-=u.pretab[d]);l.preflag=p=1}}for(d=0;4>d;d++)n.scfsi[m][d]=0;if(2==e.mode_gr&&1==g&&n.tt[0][m].block_type!=f.SHORT_TYPE&&n.tt[1][m].block_type!=
f.SHORT_TYPE){g=n.tt[1][m];h=n.tt[0][m];for(p=0;p<v.scfsi_band.length-1;p++){for(d=v.scfsi_band[p];d<v.scfsi_band[p+1]&&!(h.scalefac[d]!=g.scalefac[d]&&0<=g.scalefac[d]);d++);if(d==v.scfsi_band[p+1]){for(d=v.scfsi_band[p];d<v.scfsi_band[p+1];d++)g.scalefac[d]=-1;n.scfsi[m][p]=1}}for(d=n=m=0;11>d;d++)-1!=g.scalefac[d]&&(n++,m<g.scalefac[d]&&(m=g.scalefac[d]));for(q=h=0;d<f.SBPSY_l;d++)-1!=g.scalefac[d]&&(q++,h<g.scalefac[d]&&(h=g.scalefac[d]));for(p=0;16>p;p++)m<c[p]&&h<w[p]&&(d=a[p]*n+b[p]*q,g.part2_length>
d&&(g.part2_length=d,g.scalefac_compress=p));p=0}for(d=0;d<l.sfbmax;d++)-2==l.scalefac[d]&&(l.scalefac[d]=0);0!=p&&(2==e.mode_gr?this.scale_bitcount(l):this.scale_bitcount_lsf(e,l))};var B=[0,18,36,54,54,36,54,72,54,72,90,72,90,108,108,126],e=[0,18,36,54,51,35,53,71,52,70,88,69,87,105,104,122],l=[0,10,20,30,33,21,31,41,32,42,52,43,53,63,64,74];this.scale_bitcount=function(a){var b,g=0,d=0,q=a.scalefac;if(a.block_type==f.SHORT_TYPE){var t=B;0!=a.mixed_block_flag&&(t=e)}else if(t=l,0==a.preflag){for(b=
11;b<f.SBPSY_l&&!(q[b]<u.pretab[b]);b++);if(b==f.SBPSY_l)for(a.preflag=1,b=11;b<f.SBPSY_l;b++)q[b]-=u.pretab[b]}for(b=0;b<a.sfbdivide;b++)g<q[b]&&(g=q[b]);for(;b<a.sfbmax;b++)d<q[b]&&(d=q[b]);a.part2_length=Y.LARGE_BITS;for(b=0;16>b;b++)g<c[b]&&d<w[b]&&a.part2_length>t[b]&&(a.part2_length=t[b],a.scalefac_compress=b);return a.part2_length==Y.LARGE_BITS};var q=[[15,15,7,7],[15,15,7,0],[7,3,0,0],[15,31,31,0],[7,7,7,0],[3,3,0,0]];this.scale_bitcount_lsf=function(a,e){var b,c,d,l,h=U(4),p=e.scalefac;a=
0!=e.preflag?2:0;for(d=0;4>d;d++)h[d]=0;if(e.block_type==f.SHORT_TYPE){var A=1;var w=u.nr_of_sfb_block[a][A];for(b=l=0;4>b;b++){var t=w[b]/3;for(d=0;d<t;d++,l++)for(c=0;3>c;c++)p[3*l+c]>h[b]&&(h[b]=p[3*l+c])}}else for(A=0,w=u.nr_of_sfb_block[a][A],b=l=0;4>b;b++)for(t=w[b],d=0;d<t;d++,l++)p[l]>h[b]&&(h[b]=p[l]);w=!1;for(b=0;4>b;b++)h[b]>q[a][b]&&(w=!0);if(!w){e.sfb_partition_table=u.nr_of_sfb_block[a][A];for(b=0;4>b;b++)e.slen[b]=g[h[b]];A=e.slen[0];b=e.slen[1];h=e.slen[2];t=e.slen[3];switch(a){case 0:e.scalefac_compress=
(5*A+b<<4)+(h<<2)+t;break;case 1:e.scalefac_compress=400+(5*A+b<<2)+h;break;case 2:e.scalefac_compress=500+3*A+b;break;default:S.err.printf("intensity stereo not implemented yet\n")}}if(!w)for(b=e.part2_length=0;4>b;b++)e.part2_length+=e.slen[b]*e.sfb_partition_table[b];return w};var g=[0,1,2,2,3,3,3,3,4,4,4,4,4,4,4,4];this.huffman_init=function(a){for(var e=2;576>=e;e+=2){for(var b=0,c;a.scalefac_band.l[++b]<e;);for(c=fa[b][0];a.scalefac_band.l[c+1]>e;)c--;0>c&&(c=fa[b][0]);a.bv_scf[e-2]=c;for(c=
fa[b][1];a.scalefac_band.l[c+a.bv_scf[e-2]+2]>e;)c--;0>c&&(c=fa[b][1]);a.bv_scf[e-1]=c}}}function ca(){function f(d,c,f,a,b,k){for(;0!=b--;)f[a]=1E-10+d[c+0]*k[0]-f[a-1]*k[1]+d[c-1]*k[2]-f[a-2]*k[3]+d[c-2]*k[4]-f[a-3]*k[5]+d[c-3]*k[6]-f[a-4]*k[7]+d[c-4]*k[8]-f[a-5]*k[9]+d[c-5]*k[10]-f[a-6]*k[11]+d[c-6]*k[12]-f[a-7]*k[13]+d[c-7]*k[14]-f[a-8]*k[15]+d[c-8]*k[16]-f[a-9]*k[17]+d[c-9]*k[18]-f[a-10]*k[19]+d[c-10]*k[20],++a,++c}function k(d,c,f,a,b,k){for(;0!=b--;)f[a]=d[c+0]*k[0]-f[a-1]*k[1]+d[c-1]*k[2]-
f[a-2]*k[3]+d[c-2]*k[4],++a,++c}function r(d){return d*d}var K=ca.RMS_WINDOW_TIME_NUMERATOR,C=ca.RMS_WINDOW_TIME_DENOMINATOR,u=[[.038575994352,-3.84664617118067,-.02160367184185,7.81501653005538,-.00123395316851,-11.34170355132042,-9.291677959E-5,13.05504219327545,-.01655260341619,-12.28759895145294,.02161526843274,9.4829380631979,-.02074045215285,-5.87257861775999,.00594298065125,2.75465861874613,.00306428023191,-.86984376593551,1.2025322027E-4,.13919314567432,.00288463683916],[.0541865640643,-3.47845948550071,
-.02911007808948,6.36317777566148,-.00848709379851,-8.54751527471874,-.00851165645469,9.4769360780128,-.00834990904936,-8.81498681370155,.02245293253339,6.85401540936998,-.02596338512915,-4.39470996079559,.01624864962975,2.19611684890774,-.00240879051584,-.75104302451432,.00674613682247,.13149317958808,-.00187763777362],[.15457299681924,-2.37898834973084,-.09331049056315,2.84868151156327,-.06247880153653,-2.64577170229825,.02163541888798,2.23697657451713,-.05588393329856,-1.67148153367602,.04781476674921,
1.00595954808547,.00222312597743,-.45953458054983,.03174092540049,.16378164858596,-.01390589421898,-.05032077717131,.00651420667831,.0234789740702,-.00881362733839],[.30296907319327,-1.61273165137247,-.22613988682123,1.0797749225997,-.08587323730772,-.2565625775407,.03282930172664,-.1627671912044,-.00915702933434,-.22638893773906,-.02364141202522,.39120800788284,-.00584456039913,-.22138138954925,.06276101321749,.04500235387352,-8.28086748E-6,.02005851806501,.00205861885564,.00302439095741,-.02950134983287],
[.33642304856132,-1.49858979367799,-.2557224142557,.87350271418188,-.11828570177555,.12205022308084,.11921148675203,-.80774944671438,-.07834489609479,.47854794562326,-.0046997791438,-.12453458140019,-.0058950022444,-.04067510197014,.05724228140351,.08333755284107,.00832043980773,-.04237348025746,-.0163538138454,.02977207319925,-.0176017656815],[.4491525660845,-.62820619233671,-.14351757464547,.29661783706366,-.22784394429749,-.372563729424,-.01419140100551,.00213767857124,.04078262797139,-.42029820170918,
-.12398163381748,.22199650564824,.04097565135648,.00613424350682,.10478503600251,.06747620744683,-.01863887810927,.05784820375801,-.03193428438915,.03222754072173,.00541907748707],[.56619470757641,-1.04800335126349,-.75464456939302,.29156311971249,.1624213774223,-.26806001042947,.16744243493672,.00819999645858,-.18901604199609,.45054734505008,.3093178284183,-.33032403314006,-.27562961986224,.0673936833311,.00647310677246,-.04784254229033,.08647503780351,.01639907836189,-.0378898455484,.01807364323573,
-.00588215443421],[.58100494960553,-.51035327095184,-.53174909058578,-.31863563325245,-.14289799034253,-.20256413484477,.17520704835522,.1472815413433,.02377945217615,.38952639978999,.15558449135573,-.23313271880868,-.25344790059353,-.05246019024463,.01628462406333,-.02505961724053,.06920467763959,.02442357316099,-.03721611395801,.01818801111503,-.00749618797172],[.53648789255105,-.2504987195602,-.42163034350696,-.43193942311114,-.00275953611929,-.03424681017675,.04267842219415,-.04678328784242,-.10214864179676,
.26408300200955,.14590772289388,.15113130533216,-.02459864859345,-.17556493366449,-.11202315195388,-.18823009262115,-.04060034127,.05477720428674,.0478866554818,.0470440968812,-.02217936801134]],v=[[.98621192462708,-1.97223372919527,-1.97242384925416,.97261396931306,.98621192462708],[.98500175787242,-1.96977855582618,-1.97000351574484,.9702284756635,.98500175787242],[.97938932735214,-1.95835380975398,-1.95877865470428,.95920349965459,.97938932735214],[.97531843204928,-1.95002759149878,-1.95063686409857,
.95124613669835,.97531843204928],[.97316523498161,-1.94561023566527,-1.94633046996323,.94705070426118,.97316523498161],[.96454515552826,-1.92783286977036,-1.92909031105652,.93034775234268,.96454515552826],[.96009142950541,-1.91858953033784,-1.92018285901082,.92177618768381,.96009142950541],[.95856916599601,-1.9154210807478,-1.91713833199203,.91885558323625,.95856916599601],[.94597685600279,-1.88903307939452,-1.89195371200558,.89487434461664,.94597685600279]];this.InitGainAnalysis=function(d,c){a:{for(var f=
0;f<MAX_ORDER;f++)d.linprebuf[f]=d.lstepbuf[f]=d.loutbuf[f]=d.rinprebuf[f]=d.rstepbuf[f]=d.routbuf[f]=0;switch(0|c){case 48E3:d.reqindex=0;break;case 44100:d.reqindex=1;break;case 32E3:d.reqindex=2;break;case 24E3:d.reqindex=3;break;case 22050:d.reqindex=4;break;case 16E3:d.reqindex=5;break;case 12E3:d.reqindex=6;break;case 11025:d.reqindex=7;break;case 8E3:d.reqindex=8;break;default:c=INIT_GAIN_ANALYSIS_ERROR;break a}d.sampleWindow=0|(c*K+C-1)/C;d.lsum=0;d.rsum=0;d.totsamp=0;qa.ill(d.A,0);c=INIT_GAIN_ANALYSIS_OK}if(c!=
INIT_GAIN_ANALYSIS_OK)return INIT_GAIN_ANALYSIS_ERROR;d.linpre=MAX_ORDER;d.rinpre=MAX_ORDER;d.lstep=MAX_ORDER;d.rstep=MAX_ORDER;d.lout=MAX_ORDER;d.rout=MAX_ORDER;qa.fill(d.B,0);return INIT_GAIN_ANALYSIS_OK};this.AnalyzeSamples=function(d,c,w,a,b,B,e){if(0==B)return GAIN_ANALYSIS_OK;var l=0;var q=B;switch(e){case 1:a=c;b=w;break;case 2:break;default:return GAIN_ANALYSIS_ERROR}B<MAX_ORDER?(S.arraycopy(c,w,d.linprebuf,MAX_ORDER,B),S.arraycopy(a,b,d.rinprebuf,MAX_ORDER,B)):(S.arraycopy(c,w,d.linprebuf,
MAX_ORDER,MAX_ORDER),S.arraycopy(a,b,d.rinprebuf,MAX_ORDER,MAX_ORDER));for(;0<q;){var g=q>d.sampleWindow-d.totsamp?d.sampleWindow-d.totsamp:q;if(l<MAX_ORDER){e=d.linpre+l;var t=d.linprebuf;var D=d.rinpre+l;var m=d.rinprebuf;g>MAX_ORDER-l&&(g=MAX_ORDER-l)}else e=w+l,t=c,D=b+l,m=a;f(t,e,d.lstepbuf,d.lstep+d.totsamp,g,u[d.reqindex]);f(m,D,d.rstepbuf,d.rstep+d.totsamp,g,u[d.reqindex]);k(d.lstepbuf,d.lstep+d.totsamp,d.loutbuf,d.lout+d.totsamp,g,v[d.reqindex]);k(d.rstepbuf,d.rstep+d.totsamp,d.routbuf,d.rout+
d.totsamp,g,v[d.reqindex]);e=d.lout+d.totsamp;t=d.loutbuf;D=d.rout+d.totsamp;m=d.routbuf;for(var n=g%8;0!=n--;)d.lsum+=r(t[e++]),d.rsum+=r(m[D++]);for(n=g/8;0!=n--;)d.lsum+=r(t[e+0])+r(t[e+1])+r(t[e+2])+r(t[e+3])+r(t[e+4])+r(t[e+5])+r(t[e+6])+r(t[e+7]),e+=8,d.rsum+=r(m[D+0])+r(m[D+1])+r(m[D+2])+r(m[D+3])+r(m[D+4])+r(m[D+5])+r(m[D+6])+r(m[D+7]),D+=8;q-=g;l+=g;d.totsamp+=g;d.totsamp==d.sampleWindow&&(e=10*ca.STEPS_per_dB*Math.log10((d.lsum+d.rsum)/d.totsamp*.5+1E-37),e=0>=e?0:0|e,e>=d.A.length&&(e=
d.A.length-1),d.A[e]++,d.lsum=d.rsum=0,S.arraycopy(d.loutbuf,d.totsamp,d.loutbuf,0,MAX_ORDER),S.arraycopy(d.routbuf,d.totsamp,d.routbuf,0,MAX_ORDER),S.arraycopy(d.lstepbuf,d.totsamp,d.lstepbuf,0,MAX_ORDER),S.arraycopy(d.rstepbuf,d.totsamp,d.rstepbuf,0,MAX_ORDER),d.totsamp=0);if(d.totsamp>d.sampleWindow)return GAIN_ANALYSIS_ERROR}B<MAX_ORDER?(S.arraycopy(d.linprebuf,B,d.linprebuf,0,MAX_ORDER-B),S.arraycopy(d.rinprebuf,B,d.rinprebuf,0,MAX_ORDER-B),S.arraycopy(c,w,d.linprebuf,MAX_ORDER-B,B),S.arraycopy(a,
b,d.rinprebuf,MAX_ORDER-B,B)):(S.arraycopy(c,w+B-MAX_ORDER,d.linprebuf,0,MAX_ORDER),S.arraycopy(a,b+B-MAX_ORDER,d.rinprebuf,0,MAX_ORDER));return GAIN_ANALYSIS_OK};this.GetTitleGain=function(d){var c=d.A;var f=d.A.length,a,b=0;for(a=0;a<f;a++)b+=c[a];if(0==b)c=GAIN_NOT_ENOUGH_SAMPLES;else{b=0|Math.ceil(b*(1-.95));for(a=f;0<a--&&!(0>=(b-=c[a])););c=64.82-a/ca.STEPS_per_dB}for(f=0;f<d.A.length;f++)d.B[f]+=d.A[f],d.A[f]=0;for(f=0;f<MAX_ORDER;f++)d.linprebuf[f]=d.lstepbuf[f]=d.loutbuf[f]=d.rinprebuf[f]=
d.rstepbuf[f]=d.routbuf[f]=0;d.totsamp=0;d.lsum=d.rsum=0;return c}}function wc(){function f(c,d,a,b,f,e,l,q,g,t,k,m,n,z,E){this.vbr_q=c;this.quant_comp=d;this.quant_comp_s=a;this.expY=b;this.st_lrm=f;this.st_s=e;this.masking_adj=l;this.masking_adj_short=q;this.ath_lower=g;this.ath_curve=t;this.ath_sensitivity=k;this.interch=m;this.safejoint=n;this.sfb21mod=z;this.msfix=E}function k(c,d,a,b,f,e,l,q,g,t,k,m,n,z){this.quant_comp=d;this.quant_comp_s=a;this.safejoint=b;this.nsmsfix=f;this.st_lrm=e;this.st_s=
l;this.nsbass=q;this.scale=g;this.masking_adj=t;this.ath_lower=k;this.ath_curve=m;this.interch=n;this.sfscale=z}function r(c,d,a){var b=c.VBR==J.vbr_rh?u:v,f=c.VBR_q_frac,e=b[d],b=b[d+1];e.st_lrm+=f*(b.st_lrm-e.st_lrm);e.st_s+=f*(b.st_s-e.st_s);e.masking_adj+=f*(b.masking_adj-e.masking_adj);e.masking_adj_short+=f*(b.masking_adj_short-e.masking_adj_short);e.ath_lower+=f*(b.ath_lower-e.ath_lower);e.ath_curve+=f*(b.ath_curve-e.ath_curve);e.ath_sensitivity+=f*(b.ath_sensitivity-e.ath_sensitivity);e.interch+=
f*(b.interch-e.interch);e.msfix+=f*(b.msfix-e.msfix);b=e.vbr_q;0>b&&(b=0);9<b&&(b=9);c.VBR_q=b;c.VBR_q_frac=0;0!=a?c.quant_comp=e.quant_comp:0<Math.abs(c.quant_comp- -1)||(c.quant_comp=e.quant_comp);0!=a?c.quant_comp_short=e.quant_comp_s:0<Math.abs(c.quant_comp_short- -1)||(c.quant_comp_short=e.quant_comp_s);0!=e.expY&&(c.experimentalY=0!=e.expY);0!=a?c.internal_flags.nsPsy.attackthre=e.st_lrm:0<Math.abs(c.internal_flags.nsPsy.attackthre- -1)||(c.internal_flags.nsPsy.attackthre=e.st_lrm);0!=a?c.internal_flags.nsPsy.attackthre_s=
e.st_s:0<Math.abs(c.internal_flags.nsPsy.attackthre_s- -1)||(c.internal_flags.nsPsy.attackthre_s=e.st_s);0!=a?c.maskingadjust=e.masking_adj:0<Math.abs(c.maskingadjust-0)||(c.maskingadjust=e.masking_adj);0!=a?c.maskingadjust_short=e.masking_adj_short:0<Math.abs(c.maskingadjust_short-0)||(c.maskingadjust_short=e.masking_adj_short);0!=a?c.ATHlower=-e.ath_lower/10:0<Math.abs(10*-c.ATHlower-0)||(c.ATHlower=-e.ath_lower/10);0!=a?c.ATHcurve=e.ath_curve:0<Math.abs(c.ATHcurve- -1)||(c.ATHcurve=e.ath_curve);
0!=a?c.athaa_sensitivity=e.ath_sensitivity:0<Math.abs(c.athaa_sensitivity- -1)||(c.athaa_sensitivity=e.ath_sensitivity);0<e.interch&&(0!=a?c.interChRatio=e.interch:0<Math.abs(c.interChRatio- -1)||(c.interChRatio=e.interch));0<e.safejoint&&(c.exp_nspsytune|=e.safejoint);0<e.sfb21mod&&(c.exp_nspsytune|=e.sfb21mod<<20);0!=a?c.msfix=e.msfix:0<Math.abs(c.msfix- -1)||(c.msfix=e.msfix);0==a&&(c.VBR_q=d,c.VBR_q_frac=f)}function K(c,f,a){var b=C.nearestBitrateFullIndex(f);c.VBR=J.vbr_abr;c.VBR_mean_bitrate_kbps=
f;c.VBR_mean_bitrate_kbps=Math.min(c.VBR_mean_bitrate_kbps,320);c.VBR_mean_bitrate_kbps=Math.max(c.VBR_mean_bitrate_kbps,8);c.brate=c.VBR_mean_bitrate_kbps;320<c.VBR_mean_bitrate_kbps&&(c.disable_reservoir=!0);0<d[b].safejoint&&(c.exp_nspsytune|=2);0<d[b].sfscale&&(c.internal_flags.noise_shaping=2);if(0<Math.abs(d[b].nsbass)){var w=int(4*d[b].nsbass);0>w&&(w+=64);c.exp_nspsytune|=w<<2}0!=a?c.quant_comp=d[b].quant_comp:0<Math.abs(c.quant_comp- -1)||(c.quant_comp=d[b].quant_comp);0!=a?c.quant_comp_short=
d[b].quant_comp_s:0<Math.abs(c.quant_comp_short- -1)||(c.quant_comp_short=d[b].quant_comp_s);0!=a?c.msfix=d[b].nsmsfix:0<Math.abs(c.msfix- -1)||(c.msfix=d[b].nsmsfix);0!=a?c.internal_flags.nsPsy.attackthre=d[b].st_lrm:0<Math.abs(c.internal_flags.nsPsy.attackthre- -1)||(c.internal_flags.nsPsy.attackthre=d[b].st_lrm);0!=a?c.internal_flags.nsPsy.attackthre_s=d[b].st_s:0<Math.abs(c.internal_flags.nsPsy.attackthre_s- -1)||(c.internal_flags.nsPsy.attackthre_s=d[b].st_s);0!=a?c.scale=d[b].scale:0<Math.abs(c.scale-
-1)||(c.scale=d[b].scale);0!=a?c.maskingadjust=d[b].masking_adj:0<Math.abs(c.maskingadjust-0)||(c.maskingadjust=d[b].masking_adj);0<d[b].masking_adj?0!=a?c.maskingadjust_short=.9*d[b].masking_adj:0<Math.abs(c.maskingadjust_short-0)||(c.maskingadjust_short=.9*d[b].masking_adj):0!=a?c.maskingadjust_short=1.1*d[b].masking_adj:0<Math.abs(c.maskingadjust_short-0)||(c.maskingadjust_short=1.1*d[b].masking_adj);0!=a?c.ATHlower=-d[b].ath_lower/10:0<Math.abs(10*-c.ATHlower-0)||(c.ATHlower=-d[b].ath_lower/10);
0!=a?c.ATHcurve=d[b].ath_curve:0<Math.abs(c.ATHcurve- -1)||(c.ATHcurve=d[b].ath_curve);0!=a?c.interChRatio=d[b].interch:0<Math.abs(c.interChRatio- -1)||(c.interChRatio=d[b].interch);return f}var C;this.setModules=function(c){C=c};var u=[new f(0,9,9,0,5.2,125,-4.2,-6.3,4.8,1,0,0,2,21,.97),new f(1,9,9,0,5.3,125,-3.6,-5.6,4.5,1.5,0,0,2,21,1.35),new f(2,9,9,0,5.6,125,-2.2,-3.5,2.8,2,0,0,2,21,1.49),new f(3,9,9,1,5.8,130,-1.8,-2.8,2.6,3,-4,0,2,20,1.64),new f(4,9,9,1,6,135,-.7,-1.1,1.1,3.5,-8,0,2,0,1.79),
new f(5,9,9,1,6.4,140,.5,.4,-7.5,4,-12,2E-4,0,0,1.95),new f(6,9,9,1,6.6,145,.67,.65,-14.7,6.5,-19,4E-4,0,0,2.3),new f(7,9,9,1,6.6,145,.8,.75,-19.7,8,-22,6E-4,0,0,2.7),new f(8,9,9,1,6.6,145,1.2,1.15,-27.5,10,-23,7E-4,0,0,0),new f(9,9,9,1,6.6,145,1.6,1.6,-36,11,-25,8E-4,0,0,0),new f(10,9,9,1,6.6,145,2,2,-36,12,-25,8E-4,0,0,0)],v=[new f(0,9,9,0,4.2,25,-7,-4,7.5,1,0,0,2,26,.97),new f(1,9,9,0,4.2,25,-5.6,-3.6,4.5,1.5,0,0,2,21,1.35),new f(2,9,9,0,4.2,25,-4.4,-1.8,2,2,0,0,2,18,1.49),new f(3,9,9,1,4.2,25,
-3.4,-1.25,1.1,3,-4,0,2,15,1.64),new f(4,9,9,1,4.2,25,-2.2,.1,0,3.5,-8,0,2,0,1.79),new f(5,9,9,1,4.2,25,-1,1.65,-7.7,4,-12,2E-4,0,0,1.95),new f(6,9,9,1,4.2,25,-0,2.47,-7.7,6.5,-19,4E-4,0,0,2),new f(7,9,9,1,4.2,25,.5,2,-14.5,8,-22,6E-4,0,0,2),new f(8,9,9,1,4.2,25,1,2.4,-22,10,-23,7E-4,0,0,2),new f(9,9,9,1,4.2,25,1.5,2.95,-30,11,-25,8E-4,0,0,2),new f(10,9,9,1,4.2,25,2,2.95,-36,12,-30,8E-4,0,0,2)],d=[new k(8,9,9,0,0,6.6,145,0,.95,0,-30,11,.0012,1),new k(16,9,9,0,0,6.6,145,0,.95,0,-25,11,.001,1),new k(24,
9,9,0,0,6.6,145,0,.95,0,-20,11,.001,1),new k(32,9,9,0,0,6.6,145,0,.95,0,-15,11,.001,1),new k(40,9,9,0,0,6.6,145,0,.95,0,-10,11,9E-4,1),new k(48,9,9,0,0,6.6,145,0,.95,0,-10,11,9E-4,1),new k(56,9,9,0,0,6.6,145,0,.95,0,-6,11,8E-4,1),new k(64,9,9,0,0,6.6,145,0,.95,0,-2,11,8E-4,1),new k(80,9,9,0,0,6.6,145,0,.95,0,0,8,7E-4,1),new k(96,9,9,0,2.5,6.6,145,0,.95,0,1,5.5,6E-4,1),new k(112,9,9,0,2.25,6.6,145,0,.95,0,2,4.5,5E-4,1),new k(128,9,9,0,1.95,6.4,140,0,.95,0,3,4,2E-4,1),new k(160,9,9,1,1.79,6,135,0,.95,
-2,5,3.5,0,1),new k(192,9,9,1,1.49,5.6,125,0,.97,-4,7,3,0,0),new k(224,9,9,1,1.25,5.2,125,0,.98,-6,9,2,0,0),new k(256,9,9,1,.97,5.2,125,0,1,-8,10,1,0,0),new k(320,9,9,1,.9,5.2,125,0,1,-10,12,0,0,0)];this.apply_preset=function(c,d,a){switch(d){case W.R3MIX:d=W.V3;c.VBR=J.vbr_mtrh;break;case W.MEDIUM:d=W.V4;c.VBR=J.vbr_rh;break;case W.MEDIUM_FAST:d=W.V4;c.VBR=J.vbr_mtrh;break;case W.STANDARD:d=W.V2;c.VBR=J.vbr_rh;break;case W.STANDARD_FAST:d=W.V2;c.VBR=J.vbr_mtrh;break;case W.EXTREME:d=W.V0;c.VBR=J.vbr_rh;
break;case W.EXTREME_FAST:d=W.V0;c.VBR=J.vbr_mtrh;break;case W.INSANE:return d=320,c.preset=d,K(c,d,a),c.VBR=J.vbr_off,d}c.preset=d;switch(d){case W.V9:return r(c,9,a),d;case W.V8:return r(c,8,a),d;case W.V7:return r(c,7,a),d;case W.V6:return r(c,6,a),d;case W.V5:return r(c,5,a),d;case W.V4:return r(c,4,a),d;case W.V3:return r(c,3,a),d;case W.V2:return r(c,2,a),d;case W.V1:return r(c,1,a),d;case W.V0:return r(c,0,a),d}if(8<=d&&320>=d)return K(c,d,a);c.preset=0;return d}}function xc(){var f;this.setModules=
function(k){f=k};this.ResvFrameBegin=function(k,r){var x=k.internal_flags,C=x.l3_side,u=f.getframebits(k);r.bits=(u-8*x.sideinfo_len)/x.mode_gr;var v=2048*x.mode_gr-8;if(320<k.brate)var d=8*int(1E3*k.brate/(k.out_samplerate/1152)/8+.5);else d=11520,k.strict_ISO&&(d=8*int(32E4/(k.out_samplerate/1152)/8+.5));x.ResvMax=d-u;x.ResvMax>v&&(x.ResvMax=v);if(0>x.ResvMax||k.disable_reservoir)x.ResvMax=0;k=r.bits*x.mode_gr+Math.min(x.ResvSize,x.ResvMax);k>d&&(k=d);C.resvDrain_pre=0;null!=x.pinfo&&(x.pinfo.mean_bits=
r.bits/2,x.pinfo.resvsize=x.ResvSize);return k};this.ResvMaxBits=function(f,r,x,C){var k=f.internal_flags,v=k.ResvSize,d=k.ResvMax;0!=C&&(v+=r);0!=(k.substep_shaping&1)&&(d*=.9);x.bits=r;10*v>9*d?(C=v-9*d/10,x.bits+=C,k.substep_shaping|=128):(C=0,k.substep_shaping&=127,f.disable_reservoir||0!=(k.substep_shaping&1)||(x.bits-=.1*r));f=v<6*k.ResvMax/10?v:6*k.ResvMax/10;f-=C;0>f&&(f=0);return f};this.ResvAdjust=function(f,r){f.ResvSize-=r.part2_3_length+r.part2_length};this.ResvFrameEnd=function(f,r){var k,
x=f.l3_side;f.ResvSize+=r*f.mode_gr;r=0;x.resvDrain_post=0;x.resvDrain_pre=0;0!=(k=f.ResvSize%8)&&(r+=k);k=f.ResvSize-r-f.ResvMax;0<k&&(r+=k);k=Math.min(8*x.main_data_begin,r)/8;x.resvDrain_pre+=8*k;r-=8*k;f.ResvSize-=8*k;x.main_data_begin-=k;x.resvDrain_post+=r;f.ResvSize-=r}}function gb(){function f(a,b){var e=a[b+0]&255,e=e<<8|a[b+1]&255,e=e<<8|a[b+2]&255;return e=e<<8|a[b+3]&255}function k(a,b,c){a[b+0]=c>>24&255;a[b+1]=c>>16&255;a[b+2]=c>>8&255;a[b+3]=c&255}function r(a,b,c){a[b+0]=c>>8&255;
a[b+1]=c&255}function K(a,b,c){return 255&(a<<b|c&~(-1<<b))}function C(a,b){var e=a.internal_flags;b[0]=K(b[0],8,255);b[1]=K(b[1],3,7);b[1]=K(b[1],1,16E3>a.out_samplerate?0:1);b[1]=K(b[1],1,a.version);b[1]=K(b[1],2,1);b[1]=K(b[1],1,a.error_protection?0:1);b[2]=K(b[2],4,e.bitrate_index);b[2]=K(b[2],2,e.samplerate_index);b[2]=K(b[2],1,0);b[2]=K(b[2],1,a.extension);b[3]=K(b[3],2,a.mode.ordinal());b[3]=K(b[3],2,e.mode_ext);b[3]=K(b[3],1,a.copyright);b[3]=K(b[3],1,a.original);b[3]=K(b[3],2,a.emphasis);
b[0]=255;var e=b[1]&241;var c=1==a.version?128:16E3>a.out_samplerate?32:64;a.VBR==J.vbr_off&&(c=a.brate);c=a.free_format?0:255&16*G.BitrateIndex(c,a.version,a.out_samplerate);b[1]=1==a.version?255&(e|10):255&(e|2);e=b[2]&13;b[2]=255&(c|e)}function u(a,b){return b=b>>8^B[(b^a)&255]}var G,d,c;this.setModules=function(a,b,f){G=a;d=b;c=f};var w=gb.NUMTOCENTRIES,a=gb.MAXFRAMESIZE,b=w+4+4+4+4+4+9+1+1+8+1+1+3+1+1+2+4+2+2,B=[0,49345,49537,320,49921,960,640,49729,50689,1728,1920,51009,1280,50625,50305,1088,
52225,3264,3456,52545,3840,53185,52865,3648,2560,51905,52097,2880,51457,2496,2176,51265,55297,6336,6528,55617,6912,56257,55937,6720,7680,57025,57217,8E3,56577,7616,7296,56385,5120,54465,54657,5440,55041,6080,5760,54849,53761,4800,4992,54081,4352,53697,53377,4160,61441,12480,12672,61761,13056,62401,62081,12864,13824,63169,63361,14144,62721,13760,13440,62529,15360,64705,64897,15680,65281,16320,16E3,65089,64001,15040,15232,64321,14592,63937,63617,14400,10240,59585,59777,10560,60161,11200,10880,59969,
60929,11968,12160,61249,11520,60865,60545,11328,58369,9408,9600,58689,9984,59329,59009,9792,8704,58049,58241,9024,57601,8640,8320,57409,40961,24768,24960,41281,25344,41921,41601,25152,26112,42689,42881,26432,42241,26048,25728,42049,27648,44225,44417,27968,44801,28608,28288,44609,43521,27328,27520,43841,26880,43457,43137,26688,30720,47297,47489,31040,47873,31680,31360,47681,48641,32448,32640,48961,32E3,48577,48257,31808,46081,29888,30080,46401,30464,47041,46721,30272,29184,45761,45953,29504,45313,
29120,28800,45121,20480,37057,37249,20800,37633,21440,21120,37441,38401,22208,22400,38721,21760,38337,38017,21568,39937,23744,23936,40257,24320,40897,40577,24128,23040,39617,39809,23360,39169,22976,22656,38977,34817,18624,18816,35137,19200,35777,35457,19008,19968,36545,36737,20288,36097,19904,19584,35905,17408,33985,34177,17728,34561,18368,18048,34369,33281,17088,17280,33601,16640,33217,32897,16448];this.addVbrFrame=function(a){var b=a.internal_flags;var e=b.VBR_seek_table;a=v.bitrate_table[a.version][b.bitrate_index];
e.nVbrNumFrames++;e.sum+=a;e.seen++;if(!(e.seen<e.want)&&(e.pos<e.size&&(e.bag[e.pos]=e.sum,e.pos++,e.seen=0),e.pos==e.size)){for(a=1;a<e.size;a+=2)e.bag[a/2]=e.bag[a];e.want*=2;e.pos/=2}};this.getVbrTag=function(a){var b=new VBRTagData,e=0;b.flags=0;var c=a[e+1]>>3&1,d=a[e+2]>>2&3,k=a[e+3]>>6&3,m=a[e+2]>>4&15,m=v.bitrate_table[c][m];b.samprate=14==a[e+1]>>4?v.samplerate_table[2][d]:v.samplerate_table[c][d];d=e=0!=c?3!=k?e+36:e+21:3!=k?e+21:e+13;if(!(new String(a,d,4(),null)).equals("Xing")&&!(new String(a,
d,4(),null)).equals("Info"))return null;e+=4;b.hId=c;d=b.flags=f(a,e);e+=4;0!=(d&1)&&(b.frames=f(a,e),e+=4);0!=(d&2)&&(b.bytes=f(a,e),e+=4);if(0!=(d&4)){if(null!=b.toc)for(k=0;k<w;k++)b.toc[k]=a[e+k];e+=w}b.vbrScale=-1;0!=(d&8)&&(b.vbrScale=f(a,e),e+=4);b.headersize=72E3*(c+1)*m/b.samprate;e+=21;c=a[e+0]<<4;c+=a[e+1]>>4;m=(a[e+1]&15)<<8;m+=a[e+2]&255;if(0>c||3E3<c)c=-1;if(0>m||3E3<m)m=-1;b.encDelay=c;b.encPadding=m;return b};this.InitVbrTag=function(e){var c=e.internal_flags;var f=1==e.version?128:
16E3>e.out_samplerate?32:64;e.VBR==J.vbr_off&&(f=e.brate);f=72E3*(e.version+1)*f/e.out_samplerate;var g=c.sideinfo_len+b;c.VBR_seek_table.TotalFrameSize=f;if(f<g||f>a)e.bWriteVbrTag=!1;else for(c.VBR_seek_table.nVbrNumFrames=0,c.VBR_seek_table.nBytesWritten=0,c.VBR_seek_table.sum=0,c.VBR_seek_table.seen=0,c.VBR_seek_table.want=1,c.VBR_seek_table.pos=0,null==c.VBR_seek_table.bag&&(c.VBR_seek_table.bag=new int[400],c.VBR_seek_table.size=400),f=new Int8Array(a),C(e,f),c=c.VBR_seek_table.TotalFrameSize,
g=0;g<c;++g)d.add_dummy_byte(e,f[g]&255,1)};this.updateMusicCRC=function(a,b,c,d){for(var e=0;e<d;++e)a[0]=u(b[c+e],a[0])};this.getLameTagFrame=function(a,b){var e=a.internal_flags;if(!a.bWriteVbrTag||e.Class_ID!=W.LAME_ID||0>=e.VBR_seek_table.pos)return 0;if(b.length<e.VBR_seek_table.TotalFrameSize)return e.VBR_seek_table.TotalFrameSize;qa.fill(b,0,e.VBR_seek_table.TotalFrameSize,0);C(a,b);var g=new Int8Array(w);if(a.free_format)for(var f=1;f<w;++f)g[f]=255&255*f/100;else{var l=e.VBR_seek_table;
if(!(0>=l.pos))for(f=1;f<w;++f){var m=0|Math.floor(f/w*l.pos);m>l.pos-1&&(m=l.pos-1);m=0|256*l.bag[m]/l.sum;255<m&&(m=255);g[f]=255&m}}m=e.sideinfo_len;a.error_protection&&(m-=2);b[m++]=0;b[m++]=0;b[m++]=0;b[m++]=0;k(b,m,15);m+=4;k(b,m,e.VBR_seek_table.nVbrNumFrames);m+=4;l=e.VBR_seek_table.nBytesWritten+e.VBR_seek_table.TotalFrameSize;k(b,m,0|l);m+=4;S.arraycopy(g,0,b,m,g.length);m+=g.length;a.error_protection&&d.CRC_writeheader(e,b);for(var n=0,f=0;f<m;f++)n=u(b[f],n);var g=m,f=n,z=a.internal_flags,
m=0,n=a.encoder_delay,E=a.encoder_padding,h=100-10*a.VBR_q-a.quality,p=c.getLameVeryShortVersion();var A=[1,5,3,2,4,0,3];var y=0|(255<a.lowpassfreq/100+.5?255:a.lowpassfreq/100+.5),B=0,x=0,R=a.internal_flags.noise_shaping,X=0,I,H=0!=(a.exp_nspsytune&1);var M=0!=(a.exp_nspsytune&2);var Q=I=!1,V=a.internal_flags.nogap_total,N=a.internal_flags.nogap_current,v=a.ATHtype;switch(a.VBR){case vbr_abr:var Qa=a.VBR_mean_bitrate_kbps;break;case vbr_off:Qa=a.brate;break;default:Qa=a.VBR_min_bitrate_kbps}A=0+
(a.VBR.ordinal()<A.length?A[a.VBR.ordinal()]:0);z.findReplayGain&&(510<z.RadioGain&&(z.RadioGain=510),-510>z.RadioGain&&(z.RadioGain=-510),x=11264,x=0<=z.RadioGain?x|z.RadioGain:x|512|-z.RadioGain);z.findPeakSample&&(B=Math.abs(0|z.PeakSample/32767*Math.pow(2,23)+.5));-1!=V&&(0<N&&(Q=!0),N<V-1&&(I=!0));H=v+((H?1:0)<<4)+((M?1:0)<<5)+((I?1:0)<<6)+((Q?1:0)<<7);0>h&&(h=0);switch(a.mode){case MONO:M=0;break;case STEREO:M=1;break;case DUAL_CHANNEL:M=2;break;case JOINT_STEREO:M=a.force_ms?4:3;break;default:M=
7}I=32E3>=a.in_samplerate?0:48E3==a.in_samplerate?2:48E3<a.in_samplerate?3:1;if(a.short_blocks==pa.short_block_forced||a.short_blocks==pa.short_block_dispensed||-1==a.lowpassfreq&&-1==a.highpassfreq||a.scale_left<a.scale_right||a.scale_left>a.scale_right||a.disable_reservoir&&320>a.brate||a.noATH||a.ATHonly||0==v||32E3>=a.in_samplerate)X=1;R=R+(M<<2)+(X<<5)+(I<<6);z=z.nMusicCRC;k(b,g+m,h);m+=4;for(h=0;9>h;h++)b[g+m+h]=255&p.charAt(h);m+=9;b[g+m]=255&A;m++;b[g+m]=255&y;m++;k(b,g+m,B);m+=4;r(b,g+m,
x);m+=2;r(b,g+m,0);m+=2;b[g+m]=255&H;m++;b[g+m]=255<=Qa?255:255&Qa;m++;b[g+m]=255&n>>4;b[g+m+1]=255&(n<<4)+(E>>8);b[g+m+2]=255&E;m+=3;b[g+m]=255&R;m++;b[g+m++]=0;r(b,g+m,a.preset);m+=2;k(b,g+m,l);m+=4;r(b,g+m,z);m+=2;for(a=0;a<m;a++)f=u(b[g+a],f);r(b,g+m,f);return e.VBR_seek_table.TotalFrameSize};this.putVbrTag=function(b,c){if(0>=b.internal_flags.VBR_seek_table.pos)return-1;c.seek(c.length());if(0==c.length())return-1;c.seek(0);var e=new Int8Array(10);c.readFully(e);e=(new String(e,"ISO-8859-1")).startsWith("ID3")?
0:((e[6]&127)<<21|(e[7]&127)<<14|(e[8]&127)<<7|e[9]&127)+e.length;c.seek(e);e=new Int8Array(a);b=getLameTagFrame(b,e);if(b>e.length)return-1;if(1>b)return 0;c.write(e,0,b);return 0}}function da(){function x(a,b,c){for(;0<c;){if(0==D){D=8;t++;if(a.header[a.w_ptr].write_timing==g){var e=a;S.arraycopy(e.header[e.w_ptr].buf,0,q,t,e.sideinfo_len);t+=e.sideinfo_len;g+=8*e.sideinfo_len;e.w_ptr=e.w_ptr+1&ia.MAX_HEADER_BUF-1}q[t]=0}e=Math.min(c,D);c-=e;D-=e;q[t]|=b>>c<<D;g+=e}}function k(a,b){var c=a.internal_flags,
d;8<=b&&(x(c,76,8),b-=8);8<=b&&(x(c,65,8),b-=8);8<=b&&(x(c,77,8),b-=8);8<=b&&(x(c,69,8),b-=8);if(32<=b){var h=e.getLameShortVersion();if(32<=b)for(d=0;d<h.length&&8<=b;++d)b-=8,x(c,h.charAt(d),8)}for(;1<=b;--b)x(c,c.ancillary_flag,1),c.ancillary_flag^=a.disable_reservoir?0:1}function r(a,b,c){for(var e=a.header[a.h_ptr].ptr;0<c;){var h=Math.min(c,8-(e&7));c-=h;a.header[a.h_ptr].buf[e>>3]|=b>>c<<8-(e&7)-h;e+=h}a.header[a.h_ptr].ptr=e}function K(a,b){a<<=8;for(var c=0;8>c;c++)a<<=1,b<<=1,0!=((b^a)&
65536)&&(b^=32773);return b}function C(a,b){var c=v.ht[b.count1table_select+32],e,h=0,d=b.big_values,g=b.big_values;for(e=(b.count1-b.big_values)/4;0<e;--e){var f=0,m=0;var n=b.l3_enc[d+0];0!=n&&(m+=8,0>b.xr[g+0]&&f++);n=b.l3_enc[d+1];0!=n&&(m+=4,f*=2,0>b.xr[g+1]&&f++);n=b.l3_enc[d+2];0!=n&&(m+=2,f*=2,0>b.xr[g+2]&&f++);n=b.l3_enc[d+3];0!=n&&(m++,f*=2,0>b.xr[g+3]&&f++);d+=4;g+=4;x(a,f+c.table[m],c.hlen[m]);h+=c.hlen[m]}return h}function u(a,b,c,e,h){var d=v.ht[b],g=0;if(0==b)return g;for(;c<e;c+=2){var f=
0,m=0,n=d.xlen,l=d.xlen,q=0,I=h.l3_enc[c],k=h.l3_enc[c+1];0!=I&&(0>h.xr[c]&&q++,f--);15<b&&(14<I&&(q|=I-15<<1,m=n,I=15),14<k&&(l=k-15,q<<=n,q|=l,m+=n,k=15),l=16);0!=k&&(q<<=1,0>h.xr[c+1]&&q++,f--);I=I*l+k;m-=f;f+=d.hlen[I];x(a,d.table[I],f);x(a,q,m);g+=f+m}return g}function G(a,b){var c=3*a.scalefac_band.s[3];c>b.big_values&&(c=b.big_values);var e=u(a,b.table_select[0],0,c,b);return e+=u(a,b.table_select[1],c,b.big_values,b)}function d(a,b){var c=b.big_values;var e=b.region0_count+1;var h=a.scalefac_band.l[e];
e+=b.region1_count+1;var d=a.scalefac_band.l[e];h>c&&(h=c);d>c&&(d=c);e=u(a,b.table_select[0],0,h,b);e+=u(a,b.table_select[1],h,d,b);return e+=u(a,b.table_select[2],d,c,b)}function c(){this.total=0}function w(b,c){var e=b.internal_flags;var d=e.w_ptr;var h=e.h_ptr-1;-1==h&&(h=ia.MAX_HEADER_BUF-1);var f=e.header[h].write_timing-g;c.total=f;if(0<=f){var m=1+h-d;h<d&&(m=1+h-d+ia.MAX_HEADER_BUF);f-=8*m*e.sideinfo_len}b=a.getframebits(b);f+=b;c.total+=b;c.total=0!=c.total%8?1+c.total/8:c.total/8;c.total+=
t+1;0>f&&S.err.println("strange error flushing buffer ... \n");return f}var a=this,b=null,B=null,e=null,l=null;this.setModules=function(a,c,d,f){b=a;B=c;e=d;l=f};var q=null,g=0,t=0,D=0;this.getframebits=function(a){var b=a.internal_flags;return 8*(0|72E3*(a.version+1)*(0!=b.bitrate_index?v.bitrate_table[a.version][b.bitrate_index]:a.brate)/a.out_samplerate+b.padding)};this.CRC_writeheader=function(a,b){var c=K(b[2]&255,65535);c=K(b[3]&255,c);for(var e=6;e<a.sideinfo_len;e++)c=K(b[e]&255,c);b[4]=byte(c>>
8);b[5]=byte(c&255)};this.flush_bitstream=function(a){var e=a.internal_flags,d;var f=e.l3_side;0>(d=w(a,new c))||(k(a,d),e.ResvSize=0,f.main_data_begin=0,e.findReplayGain&&(f=b.GetTitleGain(e.rgdata),e.RadioGain=Math.floor(10*f+.5)|0),e.findPeakSample&&(e.noclipGainChange=Math.ceil(200*Math.log10(e.PeakSample/32767))|0,0<e.noclipGainChange?EQ(a.scale,1)||EQ(a.scale,0)?e.noclipScale=Math.floor(32767/e.PeakSample*100)/100:e.noclipScale=-1:e.noclipScale=-1))};this.add_dummy_byte=function(a,b,c){a=a.internal_flags;
for(var e;0<c--;){e=b;for(var h=8;0<h;){0==D&&(D=8,t++,q[t]=0);var d=Math.min(h,D);h-=d;D-=d;q[t]|=e>>h<<D;g+=d}for(e=0;e<ia.MAX_HEADER_BUF;++e)a.header[e].write_timing+=8}};this.format_bitstream=function(a){var b=a.internal_flags;var e=b.l3_side;var m=this.getframebits(a);k(a,e.resvDrain_pre);var h,p=a.internal_flags,l,q;var t=p.l3_side;p.header[p.h_ptr].ptr=0;qa.fill(p.header[p.h_ptr].buf,0,p.sideinfo_len,0);16E3>a.out_samplerate?r(p,4094,12):r(p,4095,12);r(p,a.version,1);r(p,1,2);r(p,a.error_protection?
0:1,1);r(p,p.bitrate_index,4);r(p,p.samplerate_index,2);r(p,p.padding,1);r(p,a.extension,1);r(p,a.mode.ordinal(),2);r(p,p.mode_ext,2);r(p,a.copyright,1);r(p,a.original,1);r(p,a.emphasis,2);a.error_protection&&r(p,0,16);if(1==a.version){r(p,t.main_data_begin,9);2==p.channels_out?r(p,t.private_bits,3):r(p,t.private_bits,5);for(q=0;q<p.channels_out;q++)for(h=0;4>h;h++)r(p,t.scfsi[q][h],1);for(l=0;2>l;l++)for(q=0;q<p.channels_out;q++)h=t.tt[l][q],r(p,h.part2_3_length+h.part2_length,12),r(p,h.big_values/
2,9),r(p,h.global_gain,8),r(p,h.scalefac_compress,4),h.block_type!=f.NORM_TYPE?(r(p,1,1),r(p,h.block_type,2),r(p,h.mixed_block_flag,1),14==h.table_select[0]&&(h.table_select[0]=16),r(p,h.table_select[0],5),14==h.table_select[1]&&(h.table_select[1]=16),r(p,h.table_select[1],5),r(p,h.subblock_gain[0],3),r(p,h.subblock_gain[1],3),r(p,h.subblock_gain[2],3)):(r(p,0,1),14==h.table_select[0]&&(h.table_select[0]=16),r(p,h.table_select[0],5),14==h.table_select[1]&&(h.table_select[1]=16),r(p,h.table_select[1],
5),14==h.table_select[2]&&(h.table_select[2]=16),r(p,h.table_select[2],5),r(p,h.region0_count,4),r(p,h.region1_count,3)),r(p,h.preflag,1),r(p,h.scalefac_scale,1),r(p,h.count1table_select,1)}else for(r(p,t.main_data_begin,8),r(p,t.private_bits,p.channels_out),q=l=0;q<p.channels_out;q++)h=t.tt[l][q],r(p,h.part2_3_length+h.part2_length,12),r(p,h.big_values/2,9),r(p,h.global_gain,8),r(p,h.scalefac_compress,9),h.block_type!=f.NORM_TYPE?(r(p,1,1),r(p,h.block_type,2),r(p,h.mixed_block_flag,1),14==h.table_select[0]&&
(h.table_select[0]=16),r(p,h.table_select[0],5),14==h.table_select[1]&&(h.table_select[1]=16),r(p,h.table_select[1],5),r(p,h.subblock_gain[0],3),r(p,h.subblock_gain[1],3),r(p,h.subblock_gain[2],3)):(r(p,0,1),14==h.table_select[0]&&(h.table_select[0]=16),r(p,h.table_select[0],5),14==h.table_select[1]&&(h.table_select[1]=16),r(p,h.table_select[1],5),14==h.table_select[2]&&(h.table_select[2]=16),r(p,h.table_select[2],5),r(p,h.region0_count,4),r(p,h.region1_count,3)),r(p,h.scalefac_scale,1),r(p,h.count1table_select,
1);a.error_protection&&CRC_writeheader(p,p.header[p.h_ptr].buf);t=p.h_ptr;p.h_ptr=t+1&ia.MAX_HEADER_BUF-1;p.header[p.h_ptr].write_timing=p.header[t].write_timing+m;p.h_ptr==p.w_ptr&&S.err.println("Error: MAX_HEADER_BUF too small in bitstream.c \n");var p=8*b.sideinfo_len,D,B=0,u=a.internal_flags,I=u.l3_side;if(1==a.version)for(q=0;2>q;q++)for(h=0;h<u.channels_out;h++){t=I.tt[q][h];var H=fb.slen1_tab[t.scalefac_compress];var M=fb.slen2_tab[t.scalefac_compress];for(l=D=0;l<t.sfbdivide;l++)-1!=t.scalefac[l]&&
(x(u,t.scalefac[l],H),D+=H);for(;l<t.sfbmax;l++)-1!=t.scalefac[l]&&(x(u,t.scalefac[l],M),D+=M);D=t.block_type==f.SHORT_TYPE?D+G(u,t):D+d(u,t);D+=C(u,t);B+=D}else for(h=q=0;h<u.channels_out;h++){t=I.tt[q][h];var Q,V=0;var N=l=D=0;if(t.block_type==f.SHORT_TYPE){for(;4>N;N++)for(M=t.sfb_partition_table[N]/3,H=t.slen[N],Q=0;Q<M;Q++,l++)x(u,Math.max(t.scalefac[3*l+0],0),H),x(u,Math.max(t.scalefac[3*l+1],0),H),x(u,Math.max(t.scalefac[3*l+2],0),H),V+=3*H;D+=G(u,t)}else{for(;4>N;N++)for(M=t.sfb_partition_table[N],
H=t.slen[N],Q=0;Q<M;Q++,l++)x(u,Math.max(t.scalefac[l],0),H),V+=H;D+=d(u,t)}D+=C(u,t);B+=V+D}p+=B;k(a,e.resvDrain_post);p+=e.resvDrain_post;e.main_data_begin+=(m-p)/8;w(a,new c)!=b.ResvSize&&S.err.println("Internal buffer inconsistency. flushbits <> ResvSize");8*e.main_data_begin!=b.ResvSize&&(S.err.printf("bit reservoir error: \nl3_side.main_data_begin: %d \nResvoir size:             %d \nresv drain (post)         %d \nresv drain (pre)          %d \nheader and sideinfo:      %d \ndata bits:                %d \ntotal bits:               %d (remainder: %d) \nbitsperframe:             %d \n",
8*e.main_data_begin,b.ResvSize,e.resvDrain_post,e.resvDrain_pre,8*b.sideinfo_len,p-e.resvDrain_post-8*b.sideinfo_len,p,p%8,m),S.err.println("This is a fatal error.  It has several possible causes:"),S.err.println("90%%  LAME compiled with buggy version of gcc using advanced optimizations"),S.err.println(" 9%%  Your system is overclocked"),S.err.println(" 1%%  bug in LAME encoding library"),b.ResvSize=8*e.main_data_begin);if(1E9<g){for(a=0;a<ia.MAX_HEADER_BUF;++a)b.header[a].write_timing-=g;g=0}return 0};
this.copy_buffer=function(a,c,e,d,h){var f=t+1;if(0>=f)return 0;if(0!=d&&f>d)return-1;S.arraycopy(q,0,c,e,f);t=-1;D=0;if(0!=h&&(d=U(1),d[0]=a.nMusicCRC,l.updateMusicCRC(d,c,e,f),a.nMusicCRC=d[0],0<f&&(a.VBR_seek_table.nBytesWritten+=f),a.decode_on_the_fly)){d=ha([2,1152]);h=f;for(var g=-1,m;0!=g;)if(g=B.hip_decode1_unclipped(a.hip,c,e,h,d[0],d[1]),h=0,-1==g&&(g=0),0<g){if(a.findPeakSample){for(m=0;m<g;m++)d[0][m]>a.PeakSample?a.PeakSample=d[0][m]:-d[0][m]>a.PeakSample&&(a.PeakSample=-d[0][m]);if(1<
a.channels_out)for(m=0;m<g;m++)d[1][m]>a.PeakSample?a.PeakSample=d[1][m]:-d[1][m]>a.PeakSample&&(a.PeakSample=-d[1][m])}if(a.findReplayGain&&b.AnalyzeSamples(a.rgdata,d[0],0,d[1],0,g,a.channels_out)==ca.GAIN_ANALYSIS_ERROR)return-6}}return f};this.init_bit_stream_w=function(a){q=new Int8Array(W.LAME_MAXMP3BUFFER);a.h_ptr=a.w_ptr=0;a.header[a.h_ptr].write_timing=0;t=-1;g=D=0}}function ba(f,k,r,v){this.xlen=f;this.linmax=k;this.table=r;this.hlen=v}function Fa(f){this.bits=f}function Fb(){this.bits=
this.over_SSD=this.over_count=this.max_noise=this.tot_noise=this.over_noise=0}function yc(){this.setModules=function(f,k){}}function zc(){this.floor=this.decay=this.adjustLimit=this.adjust=this.aaSensitivityP=this.useAdjust=0;this.l=G(f.SBMAX_l);this.s=G(f.SBMAX_s);this.psfb21=G(f.PSFB21);this.psfb12=G(f.PSFB12);this.cb_l=G(f.CBANDS);this.cb_s=G(f.CBANDS);this.eql_w=G(f.BLKSIZE/2)}function Ac(){this.scale_right=this.scale_left=this.scale=this.out_samplerate=this.in_samplerate=this.num_channels=this.num_samples=
this.class_id=0;this.decode_only=this.bWriteVbrTag=this.analysis=!1;this.quality=0;this.mode=ka.STEREO;this.write_id3tag_automatic=this.decode_on_the_fly=this.findReplayGain=this.free_format=this.force_ms=!1;this.error_protection=this.emphasis=this.extension=this.original=this.copyright=this.compression_ratio=this.brate=0;this.disable_reservoir=this.strict_ISO=!1;this.quant_comp_short=this.quant_comp=0;this.experimentalY=!1;this.preset=this.exp_nspsytune=this.experimentalZ=0;this.VBR=null;this.maskingadjust_short=
this.maskingadjust=this.highpasswidth=this.lowpasswidth=this.highpassfreq=this.lowpassfreq=this.VBR_hard_min=this.VBR_max_bitrate_kbps=this.VBR_min_bitrate_kbps=this.VBR_mean_bitrate_kbps=this.VBR_q=this.VBR_q_frac=0;this.noATH=this.ATHshort=this.ATHonly=!1;this.athaa_sensitivity=this.athaa_loudapprox=this.athaa_type=this.ATHlower=this.ATHcurve=this.ATHtype=0;this.short_blocks=null;this.useTemporal=!1;this.msfix=this.interChRatio=0;this.tune=!1;this.lame_allocated_gfp=this.frameNum=this.framesize=
this.encoder_padding=this.encoder_delay=this.version=this.tune_value_a=0;this.internal_flags=null}function Bc(x){this.quantize=x;this.iteration_loop=function(k,r,x,v){var u=k.internal_flags,C=G(ra.SFBMAX),d=G(576),c=U(2),w=u.l3_side;var a=new Fa(0);this.quantize.rv.ResvFrameBegin(k,a);a=a.bits;for(var b=0;b<u.mode_gr;b++){var B=this.quantize.qupvt.on_pe(k,r,c,a,b,b);u.mode_ext==f.MPG_MD_MS_LR&&(this.quantize.ms_convert(u.l3_side,b),this.quantize.qupvt.reduce_side(c,x[b],a,B));for(B=0;B<u.channels_out;B++){var e=
w.tt[b][B];if(e.block_type!=f.SHORT_TYPE){var l=0;l=u.PSY.mask_adjust-l}else l=0,l=u.PSY.mask_adjust_short-l;u.masking_lower=Math.pow(10,.1*l);this.quantize.init_outer_loop(u,e);this.quantize.init_xrpow(u,e,d)&&(this.quantize.qupvt.calc_xmin(k,v[b][B],e,C),this.quantize.outer_loop(k,e,C,d,B,c[B]));this.quantize.iteration_finish_one(u,b,B)}}this.quantize.rv.ResvFrameEnd(u,a)}}function Cc(){this.linprebuf=G(2*ca.MAX_ORDER);this.linpre=0;this.lstepbuf=G(ca.MAX_SAMPLES_PER_WINDOW+ca.MAX_ORDER);this.lstep=
0;this.loutbuf=G(ca.MAX_SAMPLES_PER_WINDOW+ca.MAX_ORDER);this.lout=0;this.rinprebuf=G(2*ca.MAX_ORDER);this.rinpre=0;this.rstepbuf=G(ca.MAX_SAMPLES_PER_WINDOW+ca.MAX_ORDER);this.rstep=0;this.routbuf=G(ca.MAX_SAMPLES_PER_WINDOW+ca.MAX_ORDER);this.first=this.freqindex=this.rsum=this.lsum=this.totsamp=this.sampleWindow=this.rout=0;this.A=U(0|ca.STEPS_per_dB*ca.MAX_dB);this.B=U(0|ca.STEPS_per_dB*ca.MAX_dB)}function ma(x,k,r,v){this.l=U(1+f.SBMAX_l);this.s=U(1+f.SBMAX_s);this.psfb21=U(1+f.PSFB21);this.psfb12=
U(1+f.PSFB12);var C=this.l,u=this.s;4==arguments.length&&(this.arrL=arguments[0],this.arrS=arguments[1],this.arr21=arguments[2],this.arr12=arguments[3],S.arraycopy(this.arrL,0,C,0,Math.min(this.arrL.length,this.l.length)),S.arraycopy(this.arrS,0,u,0,Math.min(this.arrS.length,this.s.length)),S.arraycopy(this.arr21,0,this.psfb21,0,Math.min(this.arr21.length,this.psfb21.length)),S.arraycopy(this.arr12,0,this.psfb12,0,Math.min(this.arr12.length,this.psfb12.length)))}function Y(){function x(a,b){b=C.ATHformula(b,
a);return b=Math.pow(10,(b-100)/10+a.ATHlower)}function k(a){this.s=a}var r=null,v=null,C=null;this.setModules=function(a,b,c){r=a;v=b;C=c};this.IPOW20=function(b){return a[b]};var u=Y.IXMAX_VAL+2,fa=Y.Q_MAX,d=Y.Q_MAX2;this.nr_of_sfb_block=[[[6,5,5,5],[9,9,9,9],[6,9,9,9]],[[6,5,7,3],[9,9,12,6],[6,9,12,6]],[[11,10,0,0],[18,18,0,0],[15,18,0,0]],[[7,7,7,0],[12,12,12,0],[6,15,12,0]],[[6,6,6,3],[12,9,9,6],[6,12,9,6]],[[8,8,5,0],[15,12,9,0],[6,18,9,0]]];var c=[0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,2,2,3,3,3,2,
0];this.pretab=c;this.sfBandIndex=[new ma([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,24,32,42,56,74,100,132,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new ma([0,6,12,18,24,30,36,44,54,66,80,96,114,136,162,194,232,278,332,394,464,540,576],[0,4,8,12,18,26,36,48,62,80,104,136,180,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new ma([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,26,36,48,62,80,104,134,174,192],[0,0,
0,0,0,0,0],[0,0,0,0,0,0,0]),new ma([0,4,8,12,16,20,24,30,36,44,52,62,74,90,110,134,162,196,238,288,342,418,576],[0,4,8,12,16,22,30,40,52,66,84,106,136,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new ma([0,4,8,12,16,20,24,30,36,42,50,60,72,88,106,128,156,190,230,276,330,384,576],[0,4,8,12,16,22,28,38,50,64,80,100,126,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new ma([0,4,8,12,16,20,24,30,36,44,54,66,82,102,126,156,194,240,296,364,448,550,576],[0,4,8,12,16,22,30,42,58,78,104,138,180,192],[0,0,0,0,0,0,0],[0,0,
0,0,0,0,0]),new ma([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,26,36,48,62,80,104,134,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new ma([0,6,12,18,24,30,36,44,54,66,80,96,116,140,168,200,238,284,336,396,464,522,576],[0,4,8,12,18,26,36,48,62,80,104,134,174,192],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]),new ma([0,12,24,36,48,60,72,88,108,132,160,192,232,280,336,400,476,566,568,570,572,574,576],[0,8,16,24,36,52,72,96,124,160,162,164,166,192],[0,0,0,0,0,0,0],[0,
0,0,0,0,0,0])];var w=G(fa+d+1),a=G(fa),b=G(u),B=G(u);this.adj43=B;this.iteration_init=function(c){var e=c.internal_flags,q=e.l3_side;if(0==e.iteration_init_init){e.iteration_init_init=1;q.main_data_begin=0;for(var g,t,k=c.internal_flags.ATH.l,m=c.internal_flags.ATH.psfb21,n=c.internal_flags.ATH.s,z=c.internal_flags.ATH.psfb12,E=c.internal_flags,h=c.out_samplerate,p=0;p<f.SBMAX_l;p++)for(t=E.scalefac_band.l[p],q=E.scalefac_band.l[p+1],k[p]=sb.MAX_VALUE;t<q;t++)g=t*h/1152,g=x(c,g),k[p]=Math.min(k[p],
g);for(p=0;p<f.PSFB21;p++)for(t=E.scalefac_band.psfb21[p],q=E.scalefac_band.psfb21[p+1],m[p]=sb.MAX_VALUE;t<q;t++)g=t*h/1152,g=x(c,g),m[p]=Math.min(m[p],g);for(p=0;p<f.SBMAX_s;p++){t=E.scalefac_band.s[p];q=E.scalefac_band.s[p+1];for(n[p]=sb.MAX_VALUE;t<q;t++)g=t*h/384,g=x(c,g),n[p]=Math.min(n[p],g);n[p]*=E.scalefac_band.s[p+1]-E.scalefac_band.s[p]}for(p=0;p<f.PSFB12;p++){t=E.scalefac_band.psfb12[p];q=E.scalefac_band.psfb12[p+1];for(z[p]=sb.MAX_VALUE;t<q;t++)g=t*h/384,g=x(c,g),z[p]=Math.min(z[p],g);
z[p]*=E.scalefac_band.s[13]-E.scalefac_band.s[12]}if(c.noATH){for(p=0;p<f.SBMAX_l;p++)k[p]=1E-20;for(p=0;p<f.PSFB21;p++)m[p]=1E-20;for(p=0;p<f.SBMAX_s;p++)n[p]=1E-20;for(p=0;p<f.PSFB12;p++)z[p]=1E-20}E.ATH.floor=10*Math.log10(x(c,-1));b[0]=0;for(q=1;q<u;q++)b[q]=Math.pow(q,4/3);for(q=0;q<u-1;q++)B[q]=q+1-Math.pow(.5*(b[q]+b[q+1]),.75);B[q]=.5;for(q=0;q<fa;q++)a[q]=Math.pow(2,-.1875*(q-210));for(q=0;q<=fa+d;q++)w[q]=Math.pow(2,.25*(q-210-d));r.huffman_init(e);q=c.exp_nspsytune>>2&63;32<=q&&(q-=64);
k=Math.pow(10,q/4/10);q=c.exp_nspsytune>>8&63;32<=q&&(q-=64);m=Math.pow(10,q/4/10);q=c.exp_nspsytune>>14&63;32<=q&&(q-=64);n=Math.pow(10,q/4/10);q=c.exp_nspsytune>>20&63;32<=q&&(q-=64);z=n*Math.pow(10,q/4/10);for(q=0;q<f.SBMAX_l;q++)c=6>=q?k:13>=q?m:20>=q?n:z,e.nsPsy.longfact[q]=c;for(q=0;q<f.SBMAX_s;q++)c=5>=q?k:10>=q?m:11>=q?n:z,e.nsPsy.shortfact[q]=c}};this.on_pe=function(a,b,c,d,f,k){var e=a.internal_flags,g=0,l=U(2),q,g=new Fa(g);a=v.ResvMaxBits(a,d,g,k);var g=g.bits,h=g+a;h>ia.MAX_BITS_PER_GRANULE&&
(h=ia.MAX_BITS_PER_GRANULE);for(q=k=0;q<e.channels_out;++q)c[q]=Math.min(ia.MAX_BITS_PER_CHANNEL,g/e.channels_out),l[q]=0|c[q]*b[f][q]/700-c[q],l[q]>3*d/4&&(l[q]=3*d/4),0>l[q]&&(l[q]=0),l[q]+c[q]>ia.MAX_BITS_PER_CHANNEL&&(l[q]=Math.max(0,ia.MAX_BITS_PER_CHANNEL-c[q])),k+=l[q];if(k>a)for(q=0;q<e.channels_out;++q)l[q]=a*l[q]/k;for(q=0;q<e.channels_out;++q)c[q]+=l[q],a-=l[q];for(q=k=0;q<e.channels_out;++q)k+=c[q];if(k>ia.MAX_BITS_PER_GRANULE)for(q=0;q<e.channels_out;++q)c[q]*=ia.MAX_BITS_PER_GRANULE,
c[q]/=k;return h};this.reduce_side=function(a,b,c,d){b=.33*(.5-b)/.5;0>b&&(b=0);.5<b&&(b=.5);b=0|.5*b*(a[0]+a[1]);b>ia.MAX_BITS_PER_CHANNEL-a[0]&&(b=ia.MAX_BITS_PER_CHANNEL-a[0]);0>b&&(b=0);125<=a[1]&&(125<a[1]-b?(a[0]<c&&(a[0]+=b),a[1]-=b):(a[0]+=a[1]-125,a[1]=125));b=a[0]+a[1];b>d&&(a[0]=d*a[0]/b,a[1]=d*a[1]/b)};this.athAdjust=function(a,b,c){b=Z.FAST_LOG10_X(b,10);a*=a;var e=0;b-=c;1E-20<a&&(e=1+Z.FAST_LOG10_X(a,10/90.30873362));0>e&&(e=0);return Math.pow(10,.1*(b*e+(c+90.30873362-94.82444863)))};
this.calc_xmin=function(a,b,c,d){var e=0,g=a.internal_flags,m,l=0,q=0,k=g.ATH,h=c.xr,p=a.VBR==J.vbr_mtrh?1:0,A=g.masking_lower;if(a.VBR==J.vbr_mtrh||a.VBR==J.vbr_mt)A=1;for(m=0;m<c.psy_lmax;m++){var w=a.VBR==J.vbr_rh||a.VBR==J.vbr_mtrh?athAdjust(k.adjust,k.l[m],k.floor):k.adjust*k.l[m];var r=c.width[m];var B=w/r;var x=2.220446049250313E-16;var u=r>>1;var I=0;do{var H=h[l]*h[l];I+=H;x+=H<B?H:B;l++;H=h[l]*h[l];I+=H;x+=H<B?H:B;l++}while(0<--u);I>w&&q++;m==f.SBPSY_l&&(u=w*g.nsPsy.longfact[m],x<u&&(x=
u));0!=p&&(w=x);a.ATHonly||(x=b.en.l[m],0<x&&(u=I*b.thm.l[m]*A/x,0!=p&&(u*=g.nsPsy.longfact[m]),w<u&&(w=u)));0!=p?d[e++]=w:d[e++]=w*g.nsPsy.longfact[m]}w=575;if(c.block_type!=f.SHORT_TYPE)for(I=576;0!=I--&&da.EQ(h[I],0);)w=I;c.max_nonzero_coeff=w;for(var M=c.sfb_smin;m<c.psymax;M++,m+=3){var Q;var V=a.VBR==J.vbr_rh||a.VBR==J.vbr_mtrh?athAdjust(k.adjust,k.s[M],k.floor):k.adjust*k.s[M];r=c.width[m];for(Q=0;3>Q;Q++){I=0;u=r>>1;B=V/r;x=2.220446049250313E-16;do H=h[l]*h[l],I+=H,x+=H<B?H:B,l++,H=h[l]*h[l],
I+=H,x+=H<B?H:B,l++;while(0<--u);I>V&&q++;M==f.SBPSY_s&&(u=V*g.nsPsy.shortfact[M],x<u&&(x=u));w=0!=p?x:V;a.ATHonly||a.ATHshort||(x=b.en.s[M][Q],0<x&&(u=I*b.thm.s[M][Q]*A/x,0!=p&&(u*=g.nsPsy.shortfact[M]),w<u&&(w=u)));0!=p?d[e++]=w:d[e++]=w*g.nsPsy.shortfact[M]}a.useTemporal&&(d[e-3]>d[e-3+1]&&(d[e-3+1]+=(d[e-3]-d[e-3+1])*g.decay),d[e-3+1]>d[e-3+2]&&(d[e-3+2]+=(d[e-3+1]-d[e-3+2])*g.decay))}return q};this.calc_noise_core=function(a,c,d,f){var e=0,g=c.s,m=a.l3_enc;if(g>a.count1)for(;0!=d--;){var l=a.xr[g];
g++;e+=l*l;l=a.xr[g];g++;e+=l*l}else if(g>a.big_values){var q=G(2);q[0]=0;for(q[1]=f;0!=d--;)l=Math.abs(a.xr[g])-q[m[g]],g++,e+=l*l,l=Math.abs(a.xr[g])-q[m[g]],g++,e+=l*l}else for(;0!=d--;)l=Math.abs(a.xr[g])-b[m[g]]*f,g++,e+=l*l,l=Math.abs(a.xr[g])-b[m[g]]*f,g++,e+=l*l;c.s=g;return e};this.calc_noise=function(a,b,d,f,t){var e=0,g=0,l,q=0,r=0,h=0,p=-20,A=0,y=a.scalefac,x=0;for(l=f.over_SSD=0;l<a.psymax;l++){var B=a.global_gain-(y[x++]+(0!=a.preflag?c[l]:0)<<a.scalefac_scale+1)-8*a.subblock_gain[a.window[l]];
if(null!=t&&t.step[l]==B){var u=t.noise[l];A+=a.width[l];d[e++]=u/b[g++];u=t.noise_log[l]}else{u=w[B+Y.Q_MAX2];var v=a.width[l]>>1;A+a.width[l]>a.max_nonzero_coeff&&(v=a.max_nonzero_coeff-A+1,v=0<v?v>>1:0);A=new k(A);u=this.calc_noise_core(a,A,v,u);A=A.s;null!=t&&(t.step[l]=B,t.noise[l]=u);u=d[e++]=u/b[g++];u=Z.FAST_LOG10(Math.max(u,1E-20));null!=t&&(t.noise_log[l]=u)}null!=t&&(t.global_gain=a.global_gain);h+=u;0<u&&(B=Math.max(0|10*u+.5,1),f.over_SSD+=B*B,q++,r+=u);p=Math.max(p,u)}f.over_count=q;
f.tot_noise=h;f.over_noise=r;f.max_noise=p;return q};this.set_pinfo=function(a,b,d,g,k){var e=a.internal_flags,m,l,q=0==b.scalefac_scale?.5:1,w=b.scalefac,h=G(ra.SFBMAX),p=G(ra.SFBMAX),A=new Fb;calc_xmin(a,d,b,h);calc_noise(b,h,p,A,null);var t=0;var r=b.sfb_lmax;b.block_type!=f.SHORT_TYPE&&0==b.mixed_block_flag&&(r=22);for(m=0;m<r;m++){var B=e.scalefac_band.l[m];var x=e.scalefac_band.l[m+1];var u=x-B;for(l=0;t<x;t++)l+=b.xr[t]*b.xr[t];l/=u;var I=1E15;e.pinfo.en[g][k][m]=I*l;e.pinfo.xfsf[g][k][m]=
I*h[m]*p[m]/u;l=0<d.en.l[m]&&!a.ATHonly?l/d.en.l[m]:0;e.pinfo.thr[g][k][m]=I*Math.max(l*d.thm.l[m],e.ATH.l[m]);e.pinfo.LAMEsfb[g][k][m]=0;0!=b.preflag&&11<=m&&(e.pinfo.LAMEsfb[g][k][m]=-q*c[m]);m<f.SBPSY_l&&(e.pinfo.LAMEsfb[g][k][m]-=q*w[m])}if(b.block_type==f.SHORT_TYPE)for(r=m,m=b.sfb_smin;m<f.SBMAX_s;m++){B=e.scalefac_band.s[m];x=e.scalefac_band.s[m+1];u=x-B;for(var H=0;3>H;H++){l=0;for(I=B;I<x;I++)l+=b.xr[t]*b.xr[t],t++;l=Math.max(l/u,1E-20);I=1E15;e.pinfo.en_s[g][k][3*m+H]=I*l;e.pinfo.xfsf_s[g][k][3*
m+H]=I*h[r]*p[r]/u;l=0<d.en.s[m][H]?l/d.en.s[m][H]:0;if(a.ATHonly||a.ATHshort)l=0;e.pinfo.thr_s[g][k][3*m+H]=I*Math.max(l*d.thm.s[m][H],e.ATH.s[m]);e.pinfo.LAMEsfb_s[g][k][3*m+H]=-2*b.subblock_gain[H];m<f.SBPSY_s&&(e.pinfo.LAMEsfb_s[g][k][3*m+H]-=q*w[r]);r++}}e.pinfo.LAMEqss[g][k]=b.global_gain;e.pinfo.LAMEmainbits[g][k]=b.part2_3_length+b.part2_length;e.pinfo.LAMEsfbits[g][k]=b.part2_length;e.pinfo.over[g][k]=A.over_count;e.pinfo.max_noise[g][k]=10*A.max_noise;e.pinfo.over_noise[g][k]=10*A.over_noise;
e.pinfo.tot_noise[g][k]=10*A.tot_noise;e.pinfo.over_SSD[g][k]=A.over_SSD}}function Dc(){this.sfb_count1=this.global_gain=0;this.step=U(39);this.noise=G(39);this.noise_log=G(39)}function rb(){this.xr=G(576);this.l3_enc=U(576);this.scalefac=U(ra.SFBMAX);this.mixed_block_flag=this.block_type=this.scalefac_compress=this.global_gain=this.count1=this.big_values=this.part2_3_length=this.xrpow_max=0;this.table_select=U(3);this.subblock_gain=U(4);this.sfbdivide=this.psymax=this.sfbmax=this.psy_lmax=this.sfb_smin=
this.sfb_lmax=this.part2_length=this.count1table_select=this.scalefac_scale=this.preflag=this.region1_count=this.region0_count=0;this.width=U(ra.SFBMAX);this.window=U(ra.SFBMAX);this.count1bits=0;this.sfb_partition_table=null;this.slen=U(4);this.max_nonzero_coeff=0;var f=this;this.assign=function(k){f.xr=new Float32Array(k.xr);f.l3_enc=new Int32Array(k.l3_enc);f.scalefac=new Int32Array(k.scalefac);f.xrpow_max=k.xrpow_max;f.part2_3_length=k.part2_3_length;f.big_values=k.big_values;f.count1=k.count1;
f.global_gain=k.global_gain;f.scalefac_compress=k.scalefac_compress;f.block_type=k.block_type;f.mixed_block_flag=k.mixed_block_flag;f.table_select=new Int32Array(k.table_select);f.subblock_gain=new Int32Array(k.subblock_gain);f.region0_count=k.region0_count;f.region1_count=k.region1_count;f.preflag=k.preflag;f.scalefac_scale=k.scalefac_scale;f.count1table_select=k.count1table_select;f.part2_length=k.part2_length;f.sfb_lmax=k.sfb_lmax;f.sfb_smin=k.sfb_smin;f.psy_lmax=k.psy_lmax;f.sfbmax=k.sfbmax;f.psymax=
k.psymax;f.sfbdivide=k.sfbdivide;f.width=new Int32Array(k.width);f.window=new Int32Array(k.window);f.count1bits=k.count1bits;f.sfb_partition_table=k.sfb_partition_table.slice(0);f.slen=new Int32Array(k.slen);f.max_nonzero_coeff=k.max_nonzero_coeff}}function Ec(){function x(d){this.ordinal=d}function k(d){for(var c=0;c<d.sfbmax;c++)if(0==d.scalefac[c]+d.subblock_gain[d.window[c]])return!1;return!0}var r;this.rv=null;var v;this.qupvt=null;var C,u=new yc,fa;this.setModules=function(d,c,f,a){r=d;this.rv=
v=c;this.qupvt=C=f;fa=a;u.setModules(C,fa)};this.ms_convert=function(d,c){for(var f=0;576>f;++f){var a=d.tt[c][0].xr[f],b=d.tt[c][1].xr[f];d.tt[c][0].xr[f]=.5*(a+b)*Z.SQRT2;d.tt[c][1].xr[f]=.5*(a-b)*Z.SQRT2}};this.init_xrpow=function(d,c,f){var a=0|c.max_nonzero_coeff;c.xrpow_max=0;qa.fill(f,a,576,0);for(var b,k=b=0;k<=a;++k){var e=Math.abs(c.xr[k]);b+=e;f[k]=Math.sqrt(e*Math.sqrt(e));f[k]>c.xrpow_max&&(c.xrpow_max=f[k])}if(1E-20<b){f=0;0!=(d.substep_shaping&2)&&(f=1);for(a=0;a<c.psymax;a++)d.pseudohalf[a]=
f;return!0}qa.fill(c.l3_enc,0,576,0);return!1};this.init_outer_loop=function(d,c){c.part2_3_length=0;c.big_values=0;c.count1=0;c.global_gain=210;c.scalefac_compress=0;c.table_select[0]=0;c.table_select[1]=0;c.table_select[2]=0;c.subblock_gain[0]=0;c.subblock_gain[1]=0;c.subblock_gain[2]=0;c.subblock_gain[3]=0;c.region0_count=0;c.region1_count=0;c.preflag=0;c.scalefac_scale=0;c.count1table_select=0;c.part2_length=0;c.sfb_lmax=f.SBPSY_l;c.sfb_smin=f.SBPSY_s;c.psy_lmax=d.sfb21_extra?f.SBMAX_l:f.SBPSY_l;
c.psymax=c.psy_lmax;c.sfbmax=c.sfb_lmax;c.sfbdivide=11;for(var k=0;k<f.SBMAX_l;k++)c.width[k]=d.scalefac_band.l[k+1]-d.scalefac_band.l[k],c.window[k]=3;if(c.block_type==f.SHORT_TYPE){var a=G(576);c.sfb_smin=0;c.sfb_lmax=0;0!=c.mixed_block_flag&&(c.sfb_smin=3,c.sfb_lmax=2*d.mode_gr+4);c.psymax=c.sfb_lmax+3*((d.sfb21_extra?f.SBMAX_s:f.SBPSY_s)-c.sfb_smin);c.sfbmax=c.sfb_lmax+3*(f.SBPSY_s-c.sfb_smin);c.sfbdivide=c.sfbmax-18;c.psy_lmax=c.sfb_lmax;var b=d.scalefac_band.l[c.sfb_lmax];S.arraycopy(c.xr,0,
a,0,576);for(k=c.sfb_smin;k<f.SBMAX_s;k++)for(var r=d.scalefac_band.s[k],e=d.scalefac_band.s[k+1],l=0;3>l;l++)for(var q=r;q<e;q++)c.xr[b++]=a[3*q+l];a=c.sfb_lmax;for(k=c.sfb_smin;k<f.SBMAX_s;k++)c.width[a]=c.width[a+1]=c.width[a+2]=d.scalefac_band.s[k+1]-d.scalefac_band.s[k],c.window[a]=0,c.window[a+1]=1,c.window[a+2]=2,a+=3}c.count1bits=0;c.sfb_partition_table=C.nr_of_sfb_block[0][0];c.slen[0]=0;c.slen[1]=0;c.slen[2]=0;c.slen[3]=0;c.max_nonzero_coeff=575;qa.fill(c.scalefac,0);k=d.ATH;a=c.xr;if(c.block_type!=
f.SHORT_TYPE)for(b=!1,r=f.PSFB21-1;0<=r&&!b;r--)for(c=d.scalefac_band.psfb21[r],e=d.scalefac_band.psfb21[r+1],l=C.athAdjust(k.adjust,k.psfb21[r],k.floor),1E-12<d.nsPsy.longfact[21]&&(l*=d.nsPsy.longfact[21]),--e;e>=c;e--)if(Math.abs(a[e])<l)a[e]=0;else{b=!0;break}else for(l=0;3>l;l++)for(b=!1,r=f.PSFB12-1;0<=r&&!b;r--)for(c=3*d.scalefac_band.s[12]+(d.scalefac_band.s[13]-d.scalefac_band.s[12])*l+(d.scalefac_band.psfb12[r]-d.scalefac_band.psfb12[0]),e=c+(d.scalefac_band.psfb12[r+1]-d.scalefac_band.psfb12[r]),
q=C.athAdjust(k.adjust,k.psfb12[r],k.floor),1E-12<d.nsPsy.shortfact[12]&&(q*=d.nsPsy.shortfact[12]),--e;e>=c;e--)if(Math.abs(a[e])<q)a[e]=0;else{b=!0;break}};x.BINSEARCH_NONE=new x(0);x.BINSEARCH_UP=new x(1);x.BINSEARCH_DOWN=new x(2);this.trancate_smallspectrums=function(d,c,k,a){var b=G(ra.SFBMAX);if((0!=(d.substep_shaping&4)||c.block_type!=f.SHORT_TYPE)&&0==(d.substep_shaping&128)){C.calc_noise(c,k,b,new Fb,null);for(var r=0;576>r;r++){var e=0;0!=c.l3_enc[r]&&(e=Math.abs(c.xr[r]));a[r]=e}r=0;e=
8;c.block_type==f.SHORT_TYPE&&(e=6);do{var l,q,g=c.width[e],r=r+g;if(!(1<=b[e]||(qa.sort(a,r-g,g),da.EQ(a[r-1],0)))){var t=(1-b[e])*k[e];var w=l=0;do{for(q=1;w+q<g&&!da.NEQ(a[w+r-g],a[w+r+q-g]);q++);var m=a[w+r-g]*a[w+r-g]*q;if(t<m){0!=w&&(l=a[w+r-g-1]);break}t-=m;w+=q}while(w<g);if(!da.EQ(l,0)){do Math.abs(c.xr[r-g])<=l&&(c.l3_enc[r-g]=0);while(0<--g)}}}while(++e<c.psymax);c.part2_3_length=fa.noquant_count_bits(d,c,null)}};this.outer_loop=function(d,c,r,a,b,u){var e=d.internal_flags,l=new rb,q=G(576),
g=G(ra.SFBMAX),t=new Fb,w=new Dc,m=9999999,n=!1,B=!1,v=0,h,p=e.CurrentStep[b],A=!1,y=e.OldValue[b];var O=x.BINSEARCH_NONE;c.global_gain=y;for(h=u-c.part2_length;;){var F=fa.count_bits(e,a,c,null);if(1==p||F==h)break;F>h?(O==x.BINSEARCH_DOWN&&(A=!0),A&&(p/=2),O=x.BINSEARCH_UP,F=p):(O==x.BINSEARCH_UP&&(A=!0),A&&(p/=2),O=x.BINSEARCH_DOWN,F=-p);c.global_gain+=F;0>c.global_gain&&(c.global_gain=0,A=!0);255<c.global_gain&&(c.global_gain=255,A=!0)}for(;F>h&&255>c.global_gain;)c.global_gain++,F=fa.count_bits(e,
a,c,null);e.CurrentStep[b]=4<=y-c.global_gain?4:2;e.OldValue[b]=c.global_gain;c.part2_3_length=F;if(0==e.noise_shaping)return 100;C.calc_noise(c,r,g,t,w);t.bits=c.part2_3_length;l.assign(c);b=0;for(S.arraycopy(a,0,q,0,576);!n;){do{h=new Fb;A=255;p=0!=(e.substep_shaping&2)?20:3;if(e.sfb21_extra){if(1<g[l.sfbmax])break;if(l.block_type==f.SHORT_TYPE&&(1<g[l.sfbmax+1]||1<g[l.sfbmax+2]))break}y=l;F=a;O=d.internal_flags;var R=y,X=g,I=F,H=d.internal_flags;var M=0==R.scalefac_scale?1.2968395546510096:1.6817928305074292;
for(var Q=0,V=0;V<R.sfbmax;V++)Q<X[V]&&(Q=X[V]);V=H.noise_shaping_amp;3==V&&(V=B?2:1);switch(V){case 2:break;case 1:Q=1<Q?Math.pow(Q,.5):.95*Q;break;default:Q=1<Q?1:.95*Q}for(var N=0,V=0;V<R.sfbmax;V++){var la=R.width[V],N=N+la;if(!(X[V]<Q)){if(0!=(H.substep_shaping&2)&&(H.pseudohalf[V]=0==H.pseudohalf[V]?1:0,0==H.pseudohalf[V]&&2==H.noise_shaping_amp))break;R.scalefac[V]++;for(la=-la;0>la;la++)I[N+la]*=M,I[N+la]>R.xrpow_max&&(R.xrpow_max=I[N+la]);if(2==H.noise_shaping_amp)break}}if(M=k(y))y=!1;else if(M=
2==O.mode_gr?fa.scale_bitcount(y):fa.scale_bitcount_lsf(O,y)){if(1<O.noise_shaping)if(qa.fill(O.pseudohalf,0),0==y.scalefac_scale){M=y;for(X=R=0;X<M.sfbmax;X++){H=M.width[X];I=M.scalefac[X];0!=M.preflag&&(I+=C.pretab[X]);R+=H;if(0!=(I&1))for(I++,H=-H;0>H;H++)F[R+H]*=1.2968395546510096,F[R+H]>M.xrpow_max&&(M.xrpow_max=F[R+H]);M.scalefac[X]=I>>1}M.preflag=0;M.scalefac_scale=1;M=!1}else if(y.block_type==f.SHORT_TYPE&&0<O.subblock_gain){b:{M=O;R=y;X=F;I=R.scalefac;for(F=0;F<R.sfb_lmax;F++)if(16<=I[F]){F=
!0;break b}for(H=0;3>H;H++){V=Q=0;for(F=R.sfb_lmax+H;F<R.sfbdivide;F+=3)Q<I[F]&&(Q=I[F]);for(;F<R.sfbmax;F+=3)V<I[F]&&(V=I[F]);if(!(16>Q&&8>V)){if(7<=R.subblock_gain[H]){F=!0;break b}R.subblock_gain[H]++;Q=M.scalefac_band.l[R.sfb_lmax];for(F=R.sfb_lmax+H;F<R.sfbmax;F+=3)if(V=R.width[F],N=I[F],N-=4>>R.scalefac_scale,0<=N)I[F]=N,Q+=3*V;else{I[F]=0;N=C.IPOW20(210+(N<<R.scalefac_scale+1));Q+=V*(H+1);for(la=-V;0>la;la++)X[Q+la]*=N,X[Q+la]>R.xrpow_max&&(R.xrpow_max=X[Q+la]);Q+=V*(3-H-1)}N=C.IPOW20(202);
Q+=R.width[F]*(H+1);for(la=-R.width[F];0>la;la++)X[Q+la]*=N,X[Q+la]>R.xrpow_max&&(R.xrpow_max=X[Q+la])}}F=!1}M=F||k(y)}M||(M=2==O.mode_gr?fa.scale_bitcount(y):fa.scale_bitcount_lsf(O,y));y=!M}else y=!0;if(!y)break;0!=l.scalefac_scale&&(A=254);y=u-l.part2_length;if(0>=y)break;for(;(l.part2_3_length=fa.count_bits(e,a,l,w))>y&&l.global_gain<=A;)l.global_gain++;if(l.global_gain>A)break;if(0==t.over_count){for(;(l.part2_3_length=fa.count_bits(e,a,l,w))>m&&l.global_gain<=A;)l.global_gain++;if(l.global_gain>
A)break}C.calc_noise(l,r,g,h,w);h.bits=l.part2_3_length;O=c.block_type!=f.SHORT_TYPE?d.quant_comp:d.quant_comp_short;A=t;y=h;M=l;F=g;switch(O){default:case 9:0<A.over_count?(O=y.over_SSD<=A.over_SSD,y.over_SSD==A.over_SSD&&(O=y.bits<A.bits)):O=0>y.max_noise&&10*y.max_noise+y.bits<=10*A.max_noise+A.bits;break;case 0:O=y.over_count<A.over_count||y.over_count==A.over_count&&y.over_noise<A.over_noise||y.over_count==A.over_count&&da.EQ(y.over_noise,A.over_noise)&&y.tot_noise<A.tot_noise;break;case 8:O=
y;X=1E-37;for(R=0;R<M.psymax;R++)I=F[R],I=Z.FAST_LOG10(.368+.632*I*I*I),X+=I;O.max_noise=Math.max(1E-20,X);case 1:O=y.max_noise<A.max_noise;break;case 2:O=y.tot_noise<A.tot_noise;break;case 3:O=y.tot_noise<A.tot_noise&&y.max_noise<A.max_noise;break;case 4:O=0>=y.max_noise&&.2<A.max_noise||0>=y.max_noise&&0>A.max_noise&&A.max_noise>y.max_noise-.2&&y.tot_noise<A.tot_noise||0>=y.max_noise&&0<A.max_noise&&A.max_noise>y.max_noise-.2&&y.tot_noise<A.tot_noise+A.over_noise||0<y.max_noise&&-.05<A.max_noise&&
A.max_noise>y.max_noise-.1&&y.tot_noise+y.over_noise<A.tot_noise+A.over_noise||0<y.max_noise&&-.1<A.max_noise&&A.max_noise>y.max_noise-.15&&y.tot_noise+y.over_noise+y.over_noise<A.tot_noise+A.over_noise+A.over_noise;break;case 5:O=y.over_noise<A.over_noise||da.EQ(y.over_noise,A.over_noise)&&y.tot_noise<A.tot_noise;break;case 6:O=y.over_noise<A.over_noise||da.EQ(y.over_noise,A.over_noise)&&(y.max_noise<A.max_noise||da.EQ(y.max_noise,A.max_noise)&&y.tot_noise<=A.tot_noise);break;case 7:O=y.over_count<
A.over_count||y.over_noise<A.over_noise}0==A.over_count&&(O=O&&y.bits<A.bits);O=O?1:0;if(0!=O)m=c.part2_3_length,t=h,c.assign(l),b=0,S.arraycopy(a,0,q,0,576);else if(0==e.full_outer_loop){if(++b>p&&0==t.over_count)break;if(3==e.noise_shaping_amp&&B&&30<b)break;if(3==e.noise_shaping_amp&&B&&15<l.global_gain-v)break}}while(255>l.global_gain+l.scalefac_scale);3==e.noise_shaping_amp?B?n=!0:(l.assign(c),S.arraycopy(q,0,a,0,576),b=0,v=l.global_gain,B=!0):n=!0}d.VBR==J.vbr_rh||d.VBR==J.vbr_mtrh?S.arraycopy(q,
0,a,0,576):0!=(e.substep_shaping&1)&&trancate_smallspectrums(e,c,r,a);return t.over_count};this.iteration_finish_one=function(d,c,f){var a=d.l3_side,b=a.tt[c][f];fa.best_scalefac_store(d,c,f,a);1==d.use_best_huffman&&fa.best_huffman_divide(d,b);v.ResvAdjust(d,b)};this.VBR_encode_granule=function(d,c,f,a,b,k,e){var l=d.internal_flags,q=new rb,g=G(576),r=e,w=(e+k)/2,m=0,n=l.sfb21_extra;qa.fill(q.l3_enc,0);do{l.sfb21_extra=w>r-42?!1:n;var u=outer_loop(d,c,f,a,b,w);0>=u?(m=1,e=c.part2_3_length,q.assign(c),
S.arraycopy(a,0,g,0,576),e-=32,u=e-k,w=(e+k)/2):(k=w+32,u=e-k,w=(e+k)/2,0!=m&&(m=2,c.assign(q),S.arraycopy(g,0,a,0,576)))}while(12<u);l.sfb21_extra=n;2==m&&S.arraycopy(q.l3_enc,0,c.l3_enc,0,576)};this.get_framebits=function(d,c){var f=d.internal_flags;f.bitrate_index=f.VBR_min_bitrate;r.getframebits(d);f.bitrate_index=1;var a=r.getframebits(d);for(var b=1;b<=f.VBR_max_bitrate;b++)f.bitrate_index=b,a=new Fa(a),c[b]=v.ResvFrameBegin(d,a),a=a.bits};this.VBR_old_prepare=function(d,c,k,a,b,r,e,l,q){var g=
d.internal_flags,t=1,w=0;g.bitrate_index=g.VBR_max_bitrate;var m=v.ResvFrameBegin(d,new Fa(0))/g.mode_gr;get_framebits(d,r);for(var n=0;n<g.mode_gr;n++){var u=C.on_pe(d,c,l[n],m,n,0);g.mode_ext==f.MPG_MD_MS_LR&&(ms_convert(g.l3_side,n),C.reduce_side(l[n],k[n],m,u));for(u=0;u<g.channels_out;++u){var x=g.l3_side.tt[n][u];if(x.block_type!=f.SHORT_TYPE){var h=1.28/(1+Math.exp(3.5-c[n][u]/300))-.05;h=g.PSY.mask_adjust-h}else h=2.56/(1+Math.exp(3.5-c[n][u]/300))-.14,h=g.PSY.mask_adjust_short-h;g.masking_lower=
Math.pow(10,.1*h);init_outer_loop(g,x);q[n][u]=C.calc_xmin(d,a[n][u],x,b[n][u]);0!=q[n][u]&&(t=0);e[n][u]=126;w+=l[n][u]}}for(n=0;n<g.mode_gr;n++)for(u=0;u<g.channels_out;u++)w>r[g.VBR_max_bitrate]&&(l[n][u]*=r[g.VBR_max_bitrate],l[n][u]/=w),e[n][u]>l[n][u]&&(e[n][u]=l[n][u]);return t};this.bitpressure_strategy=function(d,c,k,a){for(var b=0;b<d.mode_gr;b++)for(var r=0;r<d.channels_out;r++){for(var e=d.l3_side.tt[b][r],l=c[b][r],q=0,g=0;g<e.psy_lmax;g++)l[q++]*=1+.029*g*g/f.SBMAX_l/f.SBMAX_l;if(e.block_type==
f.SHORT_TYPE)for(g=e.sfb_smin;g<f.SBMAX_s;g++)l[q++]*=1+.029*g*g/f.SBMAX_s/f.SBMAX_s,l[q++]*=1+.029*g*g/f.SBMAX_s/f.SBMAX_s,l[q++]*=1+.029*g*g/f.SBMAX_s/f.SBMAX_s;a[b][r]=0|Math.max(k[b][r],.9*a[b][r])}};this.VBR_new_prepare=function(d,c,k,a,b,r){var e,l=d.internal_flags,q=1,g=e=0;if(d.free_format){l.bitrate_index=0;e=new Fa(e);var t=v.ResvFrameBegin(d,e);e=e.bits;b[0]=t}else l.bitrate_index=l.VBR_max_bitrate,e=new Fa(e),v.ResvFrameBegin(d,e),e=e.bits,get_framebits(d,b),t=b[l.VBR_max_bitrate];for(b=
0;b<l.mode_gr;b++){C.on_pe(d,c,r[b],e,b,0);l.mode_ext==f.MPG_MD_MS_LR&&ms_convert(l.l3_side,b);for(var u=0;u<l.channels_out;++u){var m=l.l3_side.tt[b][u];l.masking_lower=Math.pow(10,.1*l.PSY.mask_adjust);init_outer_loop(l,m);0!=C.calc_xmin(d,k[b][u],m,a[b][u])&&(q=0);g+=r[b][u]}}for(b=0;b<l.mode_gr;b++)for(u=0;u<l.channels_out;u++)g>t&&(r[b][u]*=t,r[b][u]/=g);return q};this.calc_target_bits=function(d,c,k,a,b,u){var e=d.internal_flags,l=e.l3_side;e.bitrate_index=e.VBR_max_bitrate;var q=new Fa(0);
u[0]=v.ResvFrameBegin(d,q);e.bitrate_index=1;q=r.getframebits(d)-8*e.sideinfo_len;b[0]=q/(e.mode_gr*e.channels_out);q=d.VBR_mean_bitrate_kbps*d.framesize*1E3;0!=(e.substep_shaping&1)&&(q*=1.09);q/=d.out_samplerate;q-=8*e.sideinfo_len;q/=e.mode_gr*e.channels_out;var g=.93+.07*(11-d.compression_ratio)/5.5;.9>g&&(g=.9);1<g&&(g=1);for(d=0;d<e.mode_gr;d++){var t=0;for(b=0;b<e.channels_out;b++){a[d][b]=int(g*q);if(700<c[d][b]){var w=int((c[d][b]-700)/1.4),m=l.tt[d][b];a[d][b]=int(g*q);m.block_type==f.SHORT_TYPE&&
w<q/2&&(w=q/2);w>3*q/2?w=3*q/2:0>w&&(w=0);a[d][b]+=w}a[d][b]>ia.MAX_BITS_PER_CHANNEL&&(a[d][b]=ia.MAX_BITS_PER_CHANNEL);t+=a[d][b]}if(t>ia.MAX_BITS_PER_GRANULE)for(b=0;b<e.channels_out;++b)a[d][b]*=ia.MAX_BITS_PER_GRANULE,a[d][b]/=t}if(e.mode_ext==f.MPG_MD_MS_LR)for(d=0;d<e.mode_gr;d++)C.reduce_side(a[d],k[d],q*e.channels_out,ia.MAX_BITS_PER_GRANULE);for(d=c=0;d<e.mode_gr;d++)for(b=0;b<e.channels_out;b++)a[d][b]>ia.MAX_BITS_PER_CHANNEL&&(a[d][b]=ia.MAX_BITS_PER_CHANNEL),c+=a[d][b];if(c>u[0])for(d=
0;d<e.mode_gr;d++)for(b=0;b<e.channels_out;b++)a[d][b]*=u[0],a[d][b]/=c}}function Fc(){function x(c,d,a){for(var b,f,e=10,l=d+238-14-286,q=-15;0>q;q++){var g=k[e+-10];f=c[l+-224]*g;b=c[d+224]*g;g=k[e+-9];f+=c[l+-160]*g;b+=c[d+160]*g;g=k[e+-8];f+=c[l+-96]*g;b+=c[d+96]*g;g=k[e+-7];f+=c[l+-32]*g;b+=c[d+32]*g;g=k[e+-6];f+=c[l+32]*g;b+=c[d+-32]*g;g=k[e+-5];f+=c[l+96]*g;b+=c[d+-96]*g;g=k[e+-4];f+=c[l+160]*g;b+=c[d+-160]*g;g=k[e+-3];f+=c[l+224]*g;b+=c[d+-224]*g;g=k[e+-2];f+=c[d+-256]*g;b-=c[l+256]*g;g=k[e+
-1];f+=c[d+-192]*g;b-=c[l+192]*g;g=k[e+0];f+=c[d+-128]*g;b-=c[l+128]*g;g=k[e+1];f+=c[d+-64]*g;b-=c[l+64]*g;g=k[e+2];f+=c[d+0]*g;b-=c[l+0]*g;g=k[e+3];f+=c[d+64]*g;b-=c[l+-64]*g;g=k[e+4];f+=c[d+128]*g;b-=c[l+-128]*g;g=k[e+5];f+=c[d+192]*g;b-=c[l+-192]*g;f*=k[e+6];g=b-f;a[30+2*q]=b+f;a[31+2*q]=k[e+7]*g;e+=18;d--;l++}b=c[d+-16]*k[e+-10];f=c[d+-32]*k[e+-2];b+=(c[d+-48]-c[d+16])*k[e+-9];f+=c[d+-96]*k[e+-1];b+=(c[d+-80]+c[d+48])*k[e+-8];f+=c[d+-160]*k[e+0];b+=(c[d+-112]-c[d+80])*k[e+-7];f+=c[d+-224]*k[e+
1];b+=(c[d+-144]+c[d+112])*k[e+-6];f-=c[d+32]*k[e+2];b+=(c[d+-176]-c[d+144])*k[e+-5];f-=c[d+96]*k[e+3];b+=(c[d+-208]+c[d+176])*k[e+-4];f-=c[d+160]*k[e+4];b+=(c[d+-240]-c[d+208])*k[e+-3];f-=c[d+224];c=f-b;d=f+b;b=a[14];f=a[15]-b;a[31]=d+b;a[30]=c+f;a[15]=c-f;a[14]=d-b;b=a[28]-a[0];a[0]+=a[28];a[28]=b*k[e+-36+7];b=a[29]-a[1];a[1]+=a[29];a[29]=b*k[e+-36+7];b=a[26]-a[2];a[2]+=a[26];a[26]=b*k[e+-72+7];b=a[27]-a[3];a[3]+=a[27];a[27]=b*k[e+-72+7];b=a[24]-a[4];a[4]+=a[24];a[24]=b*k[e+-108+7];b=a[25]-a[5];
a[5]+=a[25];a[25]=b*k[e+-108+7];b=a[22]-a[6];a[6]+=a[22];a[22]=b*Z.SQRT2;b=a[23]-a[7];a[7]+=a[23];a[23]=b*Z.SQRT2-a[7];a[7]-=a[6];a[22]-=a[7];a[23]-=a[22];b=a[6];a[6]=a[31]-b;a[31]+=b;b=a[7];a[7]=a[30]-b;a[30]+=b;b=a[22];a[22]=a[15]-b;a[15]+=b;b=a[23];a[23]=a[14]-b;a[14]+=b;b=a[20]-a[8];a[8]+=a[20];a[20]=b*k[e+-180+7];b=a[21]-a[9];a[9]+=a[21];a[21]=b*k[e+-180+7];b=a[18]-a[10];a[10]+=a[18];a[18]=b*k[e+-216+7];b=a[19]-a[11];a[11]+=a[19];a[19]=b*k[e+-216+7];b=a[16]-a[12];a[12]+=a[16];a[16]=b*k[e+-252+
7];b=a[17]-a[13];a[13]+=a[17];a[17]=b*k[e+-252+7];b=-a[20]+a[24];a[20]+=a[24];a[24]=b*k[e+-216+7];b=-a[21]+a[25];a[21]+=a[25];a[25]=b*k[e+-216+7];b=a[4]-a[8];a[4]+=a[8];a[8]=b*k[e+-216+7];b=a[5]-a[9];a[5]+=a[9];a[9]=b*k[e+-216+7];b=a[0]-a[12];a[0]+=a[12];a[12]=b*k[e+-72+7];b=a[1]-a[13];a[1]+=a[13];a[13]=b*k[e+-72+7];b=a[16]-a[28];a[16]+=a[28];a[28]=b*k[e+-72+7];b=-a[17]+a[29];a[17]+=a[29];a[29]=b*k[e+-72+7];b=Z.SQRT2*(a[2]-a[10]);a[2]+=a[10];a[10]=b;b=Z.SQRT2*(a[3]-a[11]);a[3]+=a[11];a[11]=b;b=Z.SQRT2*
(-a[18]+a[26]);a[18]+=a[26];a[26]=b-a[18];b=Z.SQRT2*(-a[19]+a[27]);a[19]+=a[27];a[27]=b-a[19];b=a[2];a[19]-=a[3];a[3]-=b;a[2]=a[31]-b;a[31]+=b;b=a[3];a[11]-=a[19];a[18]-=b;a[3]=a[30]-b;a[30]+=b;b=a[18];a[27]-=a[11];a[19]-=b;a[18]=a[15]-b;a[15]+=b;b=a[19];a[10]-=b;a[19]=a[14]-b;a[14]+=b;b=a[10];a[11]-=b;a[10]=a[23]-b;a[23]+=b;b=a[11];a[26]-=b;a[11]=a[22]-b;a[22]+=b;b=a[26];a[27]-=b;a[26]=a[7]-b;a[7]+=b;b=a[27];a[27]=a[6]-b;a[6]+=b;b=Z.SQRT2*(a[0]-a[4]);a[0]+=a[4];a[4]=b;b=Z.SQRT2*(a[1]-a[5]);a[1]+=
a[5];a[5]=b;b=Z.SQRT2*(a[16]-a[20]);a[16]+=a[20];a[20]=b;b=Z.SQRT2*(a[17]-a[21]);a[17]+=a[21];a[21]=b;b=-Z.SQRT2*(a[8]-a[12]);a[8]+=a[12];a[12]=b-a[8];b=-Z.SQRT2*(a[9]-a[13]);a[9]+=a[13];a[13]=b-a[9];b=-Z.SQRT2*(a[25]-a[29]);a[25]+=a[29];a[29]=b-a[25];b=-Z.SQRT2*(a[24]+a[28]);a[24]-=a[28];a[28]=b-a[24];b=a[24]-a[16];a[24]=b;b=a[20]-b;a[20]=b;b=a[28]-b;a[28]=b;b=a[25]-a[17];a[25]=b;b=a[21]-b;a[21]=b;b=a[29]-b;a[29]=b;b=a[17]-a[1];a[17]=b;b=a[9]-b;a[9]=b;b=a[25]-b;a[25]=b;b=a[5]-b;a[5]=b;b=a[21]-b;
a[21]=b;b=a[13]-b;a[13]=b;b=a[29]-b;a[29]=b;b=a[1]-a[0];a[1]=b;b=a[16]-b;a[16]=b;b=a[17]-b;a[17]=b;b=a[8]-b;a[8]=b;b=a[9]-b;a[9]=b;b=a[24]-b;a[24]=b;b=a[25]-b;a[25]=b;b=a[4]-b;a[4]=b;b=a[5]-b;a[5]=b;b=a[20]-b;a[20]=b;b=a[21]-b;a[21]=b;b=a[12]-b;a[12]=b;b=a[13]-b;a[13]=b;b=a[28]-b;a[28]=b;b=a[29]-b;a[29]=b;b=a[0];a[0]+=a[31];a[31]-=b;b=a[1];a[1]+=a[30];a[30]-=b;b=a[16];a[16]+=a[15];a[15]-=b;b=a[17];a[17]+=a[14];a[14]-=b;b=a[8];a[8]+=a[23];a[23]-=b;b=a[9];a[9]+=a[22];a[22]-=b;b=a[24];a[24]+=a[7];a[7]-=
b;b=a[25];a[25]+=a[6];a[6]-=b;b=a[4];a[4]+=a[27];a[27]-=b;b=a[5];a[5]+=a[26];a[26]-=b;b=a[20];a[20]+=a[11];a[11]-=b;b=a[21];a[21]+=a[10];a[10]-=b;b=a[12];a[12]+=a[19];a[19]-=b;b=a[13];a[13]+=a[18];a[18]-=b;b=a[28];a[28]+=a[3];a[3]-=b;b=a[29];a[29]+=a[2];a[2]-=b}var k=[-.1482523854003001,32.308141959636465,296.40344946382766,883.1344870032432,11113.947376231741,1057.2713659324597,305.7402417275812,30.825928907280012,3.8533188138216365,59.42900443849514,709.5899960123345,5281.91112291017,-5829.66483675846,
-817.6293103748613,-76.91656988279972,-4.594269939176596,.9063471690191471,.1960342806591213,-.15466694054279598,34.324387823855965,301.8067566458425,817.599602898885,11573.795901679885,1181.2520595540152,321.59731579894424,31.232021761053772,3.7107095756221318,53.650946155329365,684.167428119626,5224.56624370173,-6366.391851890084,-908.9766368219582,-89.83068876699639,-5.411397422890401,.8206787908286602,.3901806440322567,-.16070888947830023,36.147034243915876,304.11815768187864,732.7429163887613,
11989.60988270091,1300.012278487897,335.28490093152146,31.48816102859945,3.373875931311736,47.232241542899175,652.7371796173471,5132.414255594984,-6909.087078780055,-1001.9990371107289,-103.62185754286375,-6.104916304710272,.7416505462720353,.5805693545089249,-.16636367662261495,37.751650073343995,303.01103387567713,627.9747488785183,12358.763425278165,1412.2779918482834,346.7496836825721,31.598286663170416,3.1598635433980946,40.57878626349686,616.1671130880391,5007.833007176154,-7454.040671756168,
-1095.7960341867115,-118.24411666465777,-6.818469345853504,.6681786379192989,.7653668647301797,-.1716176790982088,39.11551877123304,298.3413246578966,503.5259106886539,12679.589408408976,1516.5821921214542,355.9850766329023,31.395241710249053,2.9164211881972335,33.79716964664243,574.8943997801362,4853.234992253242,-7997.57021486075,-1189.7624067269965,-133.6444792601766,-7.7202770609839915,.5993769336819237,.9427934736519954,-.17645823955292173,40.21879108166477,289.9982036694474,359.3226160751053,
12950.259102786438,1612.1013903507662,362.85067106591504,31.045922092242872,2.822222032597987,26.988862316190684,529.8996541764288,4671.371946949588,-8535.899136645805,-1282.5898586244496,-149.58553632943463,-8.643494270763135,.5345111359507916,1.111140466039205,-.36174739330527045,41.04429910497807,277.5463268268618,195.6386023135583,13169.43812144731,1697.6433561479398,367.40983966190305,30.557037410382826,2.531473372857427,20.070154905927314,481.50208566532336,4464.970341588308,-9065.36882077239,
-1373.62841526722,-166.1660487028118,-9.58289321133207,.4729647758913199,1.268786568327291,-.36970682634889585,41.393213350082036,261.2935935556502,12.935476055240873,13336.131683328815,1772.508612059496,369.76534388639965,29.751323653701338,2.4023193045459172,13.304795348228817,430.5615775526625,4237.0568611071185,-9581.931701634761,-1461.6913552409758,-183.12733958476446,-10.718010163869403,.41421356237309503,1.414213562373095,-.37677560326535325,41.619486213528496,241.05423794991074,-187.94665032361226,
13450.063605744153,1836.153896465782,369.4908799925761,29.001847876923147,2.0714759319987186,6.779591200894186,377.7767837205709,3990.386575512536,-10081.709459700915,-1545.947424837898,-200.3762958015653,-11.864482073055006,.3578057213145241,1.546020906725474,-.3829366947518991,41.1516456456653,216.47684307105183,-406.1569483347166,13511.136535077321,1887.8076599260432,367.3025214564151,28.136213436723654,1.913880671464418,.3829366947518991,323.85365704338597,3728.1472257487526,-10561.233882199509,
-1625.2025997821418,-217.62525175416,-13.015432208941645,.3033466836073424,1.66293922460509,-.5822628872992417,40.35639251440489,188.20071124269245,-640.2706748618148,13519.21490106562,1927.6022433578062,362.8197642637487,26.968821921868447,1.7463817695935329,-5.62650678237171,269.3016715297017,3453.386536448852,-11016.145278780888,-1698.6569643425091,-234.7658734267683,-14.16351421663124,.2504869601913055,1.76384252869671,-.5887180101749253,39.23429103868072,155.76096234403798,-889.2492977967378,
13475.470561874661,1955.0535223723712,356.4450994756727,25.894952980042156,1.5695032905781554,-11.181939564328772,214.80884394039484,3169.1640829158237,-11443.321309975563,-1765.1588461316153,-251.68908574481912,-15.49755935939164,.198912367379658,1.847759065022573,-.7912582233652842,37.39369355329111,119.699486012458,-1151.0956593239027,13380.446257078214,1970.3952110853447,348.01959814116185,24.731487364283044,1.3850130831637748,-16.421408865300393,161.05030052864092,2878.3322807850063,-11838.991423510031,
-1823.985884688674,-268.2854986386903,-16.81724543849939,.1483359875383474,1.913880671464418,-.7960642926861912,35.2322109610459,80.01928065061526,-1424.0212633405113,13235.794061869668,1973.804052543835,337.9908651258184,23.289159354463873,1.3934255946442087,-21.099669467133474,108.48348407242611,2583.700758091299,-12199.726194855148,-1874.2780658979746,-284.2467154529415,-18.11369784385905,.09849140335716425,1.961570560806461,-.998795456205172,32.56307803611191,36.958364584370486,-1706.075448829146,
13043.287458812016,1965.3831106103316,326.43182772364605,22.175018750622293,1.198638339011324,-25.371248002043963,57.53505923036915,2288.41886619975,-12522.674544337233,-1914.8400385312243,-299.26241273417224,-19.37805630698734,.04912684976946725,1.990369453344394,.0178904535*Z.SQRT2/2.384E-6,.008938074*Z.SQRT2/2.384E-6,.0015673635*Z.SQRT2/2.384E-6,.001228571*Z.SQRT2/2.384E-6,4.856585E-4*Z.SQRT2/2.384E-6,1.09434E-4*Z.SQRT2/2.384E-6,5.0783E-5*Z.SQRT2/2.384E-6,6.914E-6*Z.SQRT2/2.384E-6,12804.797818791945,
1945.5515939597317,313.4244966442953,20.801593959731544,1995.1556208053692,9.000838926174497,-29.20218120805369],r=[[2.382191739347913E-13,6.423305872147834E-13,9.400849094049688E-13,1.122435026096556E-12,1.183840321267481E-12,1.122435026096556E-12,9.40084909404969E-13,6.423305872147839E-13,2.382191739347918E-13,5.456116108943412E-12,4.878985199565852E-12,4.240448995017367E-12,3.559909094758252E-12,2.858043359288075E-12,2.156177623817898E-12,1.475637723558783E-12,8.371015190102974E-13,2.599706096327376E-13,
-5.456116108943412E-12,-4.878985199565852E-12,-4.240448995017367E-12,-3.559909094758252E-12,-2.858043359288076E-12,-2.156177623817898E-12,-1.475637723558783E-12,-8.371015190102975E-13,-2.599706096327376E-13,-2.382191739347923E-13,-6.423305872147843E-13,-9.400849094049696E-13,-1.122435026096556E-12,-1.183840321267481E-12,-1.122435026096556E-12,-9.400849094049694E-13,-6.42330587214784E-13,-2.382191739347918E-13],[2.382191739347913E-13,6.423305872147834E-13,9.400849094049688E-13,1.122435026096556E-12,
1.183840321267481E-12,1.122435026096556E-12,9.400849094049688E-13,6.423305872147841E-13,2.382191739347918E-13,5.456116108943413E-12,4.878985199565852E-12,4.240448995017367E-12,3.559909094758253E-12,2.858043359288075E-12,2.156177623817898E-12,1.475637723558782E-12,8.371015190102975E-13,2.599706096327376E-13,-5.461314069809755E-12,-4.921085770524055E-12,-4.343405037091838E-12,-3.732668368707687E-12,-3.093523840190885E-12,-2.430835727329465E-12,-1.734679010007751E-12,-9.74825365660928E-13,-2.797435120168326E-13,
0,0,0,0,0,0,-2.283748241799531E-13,-4.037858874020686E-13,-2.146547464825323E-13],[.1316524975873958,.414213562373095,.7673269879789602,1.091308501069271,1.303225372841206,1.56968557711749,1.920982126971166,2.414213562373094,3.171594802363212,4.510708503662055,7.595754112725146,22.90376554843115,.984807753012208,.6427876096865394,.3420201433256688,.9396926207859084,-.1736481776669303,-.7660444431189779,.8660254037844387,.5,-.5144957554275265,-.4717319685649723,-.3133774542039019,-.1819131996109812,
-.09457419252642064,-.04096558288530405,-.01419856857247115,-.003699974673760037,.8574929257125442,.8817419973177052,.9496286491027329,.9833145924917901,.9955178160675857,.9991605581781475,.999899195244447,.9999931550702802],[0,0,0,0,0,0,2.283748241799531E-13,4.037858874020686E-13,2.146547464825323E-13,5.461314069809755E-12,4.921085770524055E-12,4.343405037091838E-12,3.732668368707687E-12,3.093523840190885E-12,2.430835727329466E-12,1.734679010007751E-12,9.74825365660928E-13,2.797435120168326E-13,
-5.456116108943413E-12,-4.878985199565852E-12,-4.240448995017367E-12,-3.559909094758253E-12,-2.858043359288075E-12,-2.156177623817898E-12,-1.475637723558782E-12,-8.371015190102975E-13,-2.599706096327376E-13,-2.382191739347913E-13,-6.423305872147834E-13,-9.400849094049688E-13,-1.122435026096556E-12,-1.183840321267481E-12,-1.122435026096556E-12,-9.400849094049688E-13,-6.423305872147841E-13,-2.382191739347918E-13]],v=r[f.SHORT_TYPE],C=r[f.SHORT_TYPE],u=r[f.SHORT_TYPE],J=r[f.SHORT_TYPE],d=[0,1,16,17,
8,9,24,25,4,5,20,21,12,13,28,29,2,3,18,19,10,11,26,27,6,7,22,23,14,15,30,31];this.mdct_sub48=function(c,k,a){for(var b=286,w=0;w<c.channels_out;w++){for(var e=0;e<c.mode_gr;e++){for(var l,q=c.l3_side.tt[e][w],g=q.xr,t=0,D=c.sb_sample[w][1-e],m=0,n=0;9>n;n++)for(x(k,b,D[m]),x(k,b+32,D[m+1]),m+=2,b+=64,l=1;32>l;l+=2)D[m-1][l]*=-1;for(l=0;32>l;l++,t+=18){var D=q.block_type,m=c.sb_sample[w][e],z=c.sb_sample[w][1-e];0!=q.mixed_block_flag&&2>l&&(D=0);if(1E-12>c.amp_filter[l])qa.fill(g,t+0,t+18,0);else{if(1>
c.amp_filter[l])for(n=0;18>n;n++)z[n][d[l]]*=c.amp_filter[l];if(D==f.SHORT_TYPE){for(n=-3;0>n;n++){var E=r[f.SHORT_TYPE][n+3];g[t+3*n+9]=m[9+n][d[l]]*E-m[8-n][d[l]];g[t+3*n+18]=m[14-n][d[l]]*E+m[15+n][d[l]];g[t+3*n+10]=m[15+n][d[l]]*E-m[14-n][d[l]];g[t+3*n+19]=z[2-n][d[l]]*E+z[3+n][d[l]];g[t+3*n+11]=z[3+n][d[l]]*E-z[2-n][d[l]];g[t+3*n+20]=z[8-n][d[l]]*E+z[9+n][d[l]]}n=g;m=t;for(E=0;3>E;E++){var h=n[m+6]*r[f.SHORT_TYPE][0]-n[m+15];z=n[m+0]*r[f.SHORT_TYPE][2]-n[m+9];var p=h+z;var A=h-z;h=n[m+15]*r[f.SHORT_TYPE][0]+
n[m+6];z=n[m+9]*r[f.SHORT_TYPE][2]+n[m+0];var y=h+z;var O=-h+z;z=2.069978111953089E-11*(n[m+3]*r[f.SHORT_TYPE][1]-n[m+12]);h=2.069978111953089E-11*(n[m+12]*r[f.SHORT_TYPE][1]+n[m+3]);n[m+0]=1.90752519173728E-11*p+z;n[m+15]=1.90752519173728E-11*-y+h;A*=1.6519652744032674E-11;y=9.537625958686404E-12*y+h;n[m+3]=A-y;n[m+6]=A+y;p=9.537625958686404E-12*p-z;O*=1.6519652744032674E-11;n[m+9]=p+O;n[m+12]=p-O;m++}}else{E=G(18);for(n=-9;0>n;n++)p=r[D][n+27]*z[n+9][d[l]]+r[D][n+36]*z[8-n][d[l]],A=r[D][n+9]*m[n+
9][d[l]]-r[D][n+18]*m[8-n][d[l]],E[n+9]=p-A*v[3+n+9],E[n+18]=p*v[3+n+9]+A;var n=g,m=t;p=E;var F=p[17]-p[9];var R=p[15]-p[11];var X=p[14]-p[12];O=p[0]+p[8];y=p[1]+p[7];h=p[2]+p[6];A=p[3]+p[5];n[m+17]=O+h-A-(y-p[4]);E=(O+h-A)*C[19]+(y-p[4]);z=(F-R-X)*C[18];n[m+5]=z+E;n[m+6]=z-E;var I=(p[16]-p[10])*C[18];y=y*C[19]+p[4];z=F*C[12]+I+R*C[13]+X*C[14];E=-O*C[16]+y-h*C[17]+A*C[15];n[m+1]=z+E;n[m+2]=z-E;z=F*C[13]-I-R*C[14]+X*C[12];E=-O*C[17]+y-h*C[15]+A*C[16];n[m+9]=z+E;n[m+10]=z-E;z=F*C[14]-I+R*C[12]-X*C[13];
E=O*C[15]-y+h*C[16]-A*C[17];n[m+13]=z+E;n[m+14]=z-E;F=p[8]-p[0];R=p[6]-p[2];X=p[5]-p[3];O=p[17]+p[9];y=p[16]+p[10];h=p[15]+p[11];A=p[14]+p[12];n[m+0]=O+h+A+(y+p[13]);z=(O+h+A)*C[19]-(y+p[13]);E=(F-R+X)*C[18];n[m+11]=z+E;n[m+12]=z-E;I=(p[7]-p[1])*C[18];y=p[13]-y*C[19];z=O*C[15]-y+h*C[16]+A*C[17];E=F*C[14]+I+R*C[12]+X*C[13];n[m+3]=z+E;n[m+4]=z-E;z=-O*C[17]+y-h*C[15]-A*C[16];E=F*C[13]+I-R*C[14]-X*C[12];n[m+7]=z+E;n[m+8]=z-E;z=-O*C[16]+y-h*C[17]-A*C[15];E=F*C[12]-I+R*C[13]-X*C[14];n[m+15]=z+E;n[m+16]=
z-E}}if(D!=f.SHORT_TYPE&&0!=l)for(n=7;0<=n;--n)D=g[t+n]*u[20+n]+g[t+-1-n]*J[28+n],m=g[t+n]*J[28+n]-g[t+-1-n]*u[20+n],g[t+-1-n]=D,g[t+n]=m}}k=a;b=286;if(1==c.mode_gr)for(e=0;18>e;e++)S.arraycopy(c.sb_sample[w][1][e],0,c.sb_sample[w][0][e],0,32)}}}function za(){this.thm=new tb;this.en=new tb}function f(){var x=f.FFTOFFSET,k=f.MPG_MD_MS_LR,r=null,v=this.psy=null,C=null,u=null;this.setModules=function(d,c,f,a){r=d;v=this.psy=c;C=a;u=f};var fa=new Fc;this.lame_encode_mp3_frame=function(d,c,w,a,b,B){var e=
Tb([2,2]);e[0][0]=new za;e[0][1]=new za;e[1][0]=new za;e[1][1]=new za;var l=Tb([2,2]);l[0][0]=new za;l[0][1]=new za;l[1][0]=new za;l[1][1]=new za;var q=[null,null],g=d.internal_flags,t=ha([2,4]),D=[.5,.5],m=[[0,0],[0,0]],n=[[0,0],[0,0]];q[0]=c;q[1]=w;if(0==g.lame_encode_frame_init){c=d.internal_flags;var z,E;if(0==c.lame_encode_frame_init){w=G(2014);var h=G(2014);c.lame_encode_frame_init=1;for(E=z=0;z<286+576*(1+c.mode_gr);++z)z<576*c.mode_gr?(w[z]=0,2==c.channels_out&&(h[z]=0)):(w[z]=q[0][E],2==
c.channels_out&&(h[z]=q[1][E]),++E);for(E=0;E<c.mode_gr;E++)for(z=0;z<c.channels_out;z++)c.l3_side.tt[E][z].block_type=f.SHORT_TYPE;fa.mdct_sub48(c,w,h)}}g.padding=0;0>(g.slot_lag-=g.frac_SpF)&&(g.slot_lag+=d.out_samplerate,g.padding=1);if(0!=g.psymodel)for(h=[null,null],z=0,E=U(2),w=0;w<g.mode_gr;w++){for(c=0;c<g.channels_out;c++)h[c]=q[c],z=576+576*w-f.FFTOFFSET;c=d.VBR==J.vbr_mtrh||d.VBR==J.vbr_mt?v.L3psycho_anal_vbr(d,h,z,w,e,l,m[w],n[w],t[w],E):v.L3psycho_anal_ns(d,h,z,w,e,l,m[w],n[w],t[w],E);
if(0!=c)return-4;d.mode==ka.JOINT_STEREO&&(D[w]=t[w][2]+t[w][3],0<D[w]&&(D[w]=t[w][3]/D[w]));for(c=0;c<g.channels_out;c++){var p=g.l3_side.tt[w][c];p.block_type=E[c];p.mixed_block_flag=0}}else for(w=0;w<g.mode_gr;w++)for(c=0;c<g.channels_out;c++)g.l3_side.tt[w][c].block_type=f.NORM_TYPE,g.l3_side.tt[w][c].mixed_block_flag=0,n[w][c]=m[w][c]=700;0==g.ATH.useAdjust?g.ATH.adjust=1:(c=g.loudness_sq[0][0],t=g.loudness_sq[1][0],2==g.channels_out?(c+=g.loudness_sq[0][1],t+=g.loudness_sq[1][1]):(c+=c,t+=t),
2==g.mode_gr&&(c=Math.max(c,t)),c=.5*c*g.ATH.aaSensitivityP,.03125<c?(1<=g.ATH.adjust?g.ATH.adjust=1:g.ATH.adjust<g.ATH.adjustLimit&&(g.ATH.adjust=g.ATH.adjustLimit),g.ATH.adjustLimit=1):(t=31.98*c+6.25E-4,g.ATH.adjust>=t?(g.ATH.adjust*=.075*t+.925,g.ATH.adjust<t&&(g.ATH.adjust=t)):g.ATH.adjustLimit>=t?g.ATH.adjust=t:g.ATH.adjust<g.ATH.adjustLimit&&(g.ATH.adjust=g.ATH.adjustLimit),g.ATH.adjustLimit=t));fa.mdct_sub48(g,q[0],q[1]);g.mode_ext=f.MPG_MD_LR_LR;if(d.force_ms)g.mode_ext=f.MPG_MD_MS_LR;else if(d.mode==
ka.JOINT_STEREO){for(w=h=t=0;w<g.mode_gr;w++)for(c=0;c<g.channels_out;c++)t+=n[w][c],h+=m[w][c];t<=1*h&&(t=g.l3_side.tt[0],c=g.l3_side.tt[g.mode_gr-1],t[0].block_type==t[1].block_type&&c[0].block_type==c[1].block_type&&(g.mode_ext=f.MPG_MD_MS_LR))}g.mode_ext==k&&(e=l,m=n);if(d.analysis&&null!=g.pinfo)for(w=0;w<g.mode_gr;w++)for(c=0;c<g.channels_out;c++)g.pinfo.ms_ratio[w]=g.ms_ratio[w],g.pinfo.ms_ener_ratio[w]=D[w],g.pinfo.blocktype[w][c]=g.l3_side.tt[w][c].block_type,g.pinfo.pe[w][c]=m[w][c],S.arraycopy(g.l3_side.tt[w][c].xr,
0,g.pinfo.xr[w][c],0,576),g.mode_ext==k&&(g.pinfo.ers[w][c]=g.pinfo.ers[w][c+2],S.arraycopy(g.pinfo.energy[w][c+2],0,g.pinfo.energy[w][c],0,g.pinfo.energy[w][c].length));if(d.VBR==J.vbr_off||d.VBR==J.vbr_abr){for(l=0;18>l;l++)g.nsPsy.pefirbuf[l]=g.nsPsy.pefirbuf[l+1];for(w=n=0;w<g.mode_gr;w++)for(c=0;c<g.channels_out;c++)n+=m[w][c];g.nsPsy.pefirbuf[18]=n;n=g.nsPsy.pefirbuf[9];for(l=0;9>l;l++)n+=(g.nsPsy.pefirbuf[l]+g.nsPsy.pefirbuf[18-l])*f.fircoef[l];n=3350*g.mode_gr*g.channels_out/n;for(w=0;w<g.mode_gr;w++)for(c=
0;c<g.channels_out;c++)m[w][c]*=n}g.iteration_loop.iteration_loop(d,m,D,e);r.format_bitstream(d);a=r.copy_buffer(g,a,b,B,1);d.bWriteVbrTag&&C.addVbrFrame(d);if(d.analysis&&null!=g.pinfo){for(c=0;c<g.channels_out;c++){for(b=0;b<x;b++)g.pinfo.pcmdata[c][b]=g.pinfo.pcmdata[c][b+d.framesize];for(b=x;1600>b;b++)g.pinfo.pcmdata[c][b]=q[c][b-x]}u.set_frame_pinfo(d,e)}g.bitrate_stereoMode_Hist[g.bitrate_index][4]++;g.bitrate_stereoMode_Hist[15][4]++;2==g.channels_out&&(g.bitrate_stereoMode_Hist[g.bitrate_index][g.mode_ext]++,
g.bitrate_stereoMode_Hist[15][g.mode_ext]++);for(d=0;d<g.mode_gr;++d)for(q=0;q<g.channels_out;++q)b=g.l3_side.tt[d][q].block_type|0,0!=g.l3_side.tt[d][q].mixed_block_flag&&(b=4),g.bitrate_blockType_Hist[g.bitrate_index][b]++,g.bitrate_blockType_Hist[g.bitrate_index][5]++,g.bitrate_blockType_Hist[15][b]++,g.bitrate_blockType_Hist[15][5]++;return a}}function Gc(){this.size=this.pos=this.want=this.seen=this.sum=0;this.bag=null;this.TotalFrameSize=this.nBytesWritten=this.nVbrNumFrames=0}function Hc(){this.tt=
[[null,null],[null,null]];this.resvDrain_post=this.resvDrain_pre=this.private_bits=this.main_data_begin=0;this.scfsi=[U(4),U(4)];for(var f=0;2>f;f++)for(var k=0;2>k;k++)this.tt[f][k]=new rb}function tb(){this.l=G(f.SBMAX_l);this.s=ha([f.SBMAX_s,3]);var x=this;this.assign=function(k){S.arraycopy(k.l,0,x.l,0,f.SBMAX_l);for(var r=0;r<f.SBMAX_s;r++)for(var v=0;3>v;v++)x.s[r][v]=k.s[r][v]}}function Ic(){this.last_en_subshort=ha([4,9]);this.lastAttacks=U(4);this.pefirbuf=G(19);this.longfact=G(f.SBMAX_l);
this.shortfact=G(f.SBMAX_s);this.attackthre_s=this.attackthre=0}function ia(){function x(){this.ptr=this.write_timing=0;this.buf=new Int8Array(40)}this.fill_buffer_resample_init=this.iteration_init_init=this.lame_encode_frame_init=this.Class_ID=0;this.mfbuf=ha([2,ia.MFSIZE]);this.full_outer_loop=this.use_best_huffman=this.subblock_gain=this.noise_shaping_stop=this.psymodel=this.substep_shaping=this.noise_shaping_amp=this.noise_shaping=this.highpass2=this.highpass1=this.lowpass2=this.lowpass1=this.mode_ext=
this.samplerate_index=this.bitrate_index=this.VBR_max_bitrate=this.VBR_min_bitrate=this.mf_size=this.mf_samples_to_encode=this.resample_ratio=this.channels_out=this.channels_in=this.mode_gr=0;this.l3_side=new Hc;this.ms_ratio=G(2);this.slot_lag=this.frac_SpF=this.padding=0;this.tag_spec=null;this.nMusicCRC=0;this.OldValue=U(2);this.CurrentStep=U(2);this.masking_lower=0;this.bv_scf=U(576);this.pseudohalf=U(ra.SFBMAX);this.sfb21_extra=!1;this.inbuf_old=Array(2);this.blackfilt=Array(2*ia.BPC+1);this.itime=
new Float64Array(2);this.sideinfo_len=0;this.sb_sample=ha([2,2,18,f.SBLIMIT]);this.amp_filter=G(32);this.header=Array(ia.MAX_HEADER_BUF);this.ResvMax=this.ResvSize=this.ancillary_flag=this.w_ptr=this.h_ptr=0;this.scalefac_band=new ma;this.minval_l=G(f.CBANDS);this.minval_s=G(f.CBANDS);this.nb_1=ha([4,f.CBANDS]);this.nb_2=ha([4,f.CBANDS]);this.nb_s1=ha([4,f.CBANDS]);this.nb_s2=ha([4,f.CBANDS]);this.s3_ll=this.s3_ss=null;this.decay=0;this.thm=Array(4);this.en=Array(4);this.tot_ener=G(4);this.loudness_sq=
ha([2,2]);this.loudness_sq_save=G(2);this.mld_l=G(f.SBMAX_l);this.mld_s=G(f.SBMAX_s);this.bm_l=U(f.SBMAX_l);this.bo_l=U(f.SBMAX_l);this.bm_s=U(f.SBMAX_s);this.bo_s=U(f.SBMAX_s);this.npart_s=this.npart_l=0;this.s3ind=Ua([f.CBANDS,2]);this.s3ind_s=Ua([f.CBANDS,2]);this.numlines_s=U(f.CBANDS);this.numlines_l=U(f.CBANDS);this.rnumlines_l=G(f.CBANDS);this.mld_cb_l=G(f.CBANDS);this.mld_cb_s=G(f.CBANDS);this.numlines_l_num1=this.numlines_s_num1=0;this.pe=G(4);this.ms_ener_ratio_old=this.ms_ratio_l_old=this.ms_ratio_s_old=
0;this.blocktype_old=U(2);this.nsPsy=new Ic;this.VBR_seek_table=new Gc;this.PSY=this.ATH=null;this.nogap_current=this.nogap_total=0;this.findPeakSample=this.findReplayGain=this.decode_on_the_fly=!0;this.AudiophileGain=this.RadioGain=this.PeakSample=0;this.rgdata=null;this.noclipScale=this.noclipGainChange=0;this.bitrate_stereoMode_Hist=Ua([16,5]);this.bitrate_blockType_Hist=Ua([16,6]);this.hip=this.pinfo=null;this.in_buffer_nsamples=0;this.iteration_loop=this.in_buffer_1=this.in_buffer_0=null;for(var k=
0;k<this.en.length;k++)this.en[k]=new tb;for(k=0;k<this.thm.length;k++)this.thm[k]=new tb;for(k=0;k<this.header.length;k++)this.header[k]=new x}function Jc(){function x(f,k,d){var c=0;d<<=1;var r=k+d;var a=4;do{var b;var u=a>>1;var e=a;var l=a<<1;var q=l+e;a=l<<1;var g=k;var t=g+u;do{var x=f[g+0]-f[g+e];var m=f[g+0]+f[g+e];var n=f[g+l]-f[g+q];var C=f[g+l]+f[g+q];f[g+l]=m-C;f[g+0]=m+C;f[g+q]=x-n;f[g+e]=x+n;x=f[t+0]-f[t+e];m=f[t+0]+f[t+e];n=Z.SQRT2*f[t+q];C=Z.SQRT2*f[t+l];f[t+l]=m-C;f[t+0]=m+C;f[t+
q]=x-n;f[t+e]=x+n;t+=a;g+=a}while(g<r);var E=v[c+0];var h=v[c+1];for(b=1;b<u;b++){var p=1-2*h*h;var A=2*h*E;g=k+b;t=k+e-b;do{var y=A*f[g+e]-p*f[t+e];C=p*f[g+e]+A*f[t+e];x=f[g+0]-C;m=f[g+0]+C;var O=f[t+0]-y;var G=f[t+0]+y;y=A*f[g+q]-p*f[t+q];C=p*f[g+q]+A*f[t+q];n=f[g+l]-C;C=f[g+l]+C;var R=f[t+l]-y;var X=f[t+l]+y;y=h*C-E*R;C=E*C+h*R;f[g+l]=m-C;f[g+0]=m+C;f[t+q]=O-y;f[t+e]=O+y;y=E*X-h*n;C=h*X+E*n;f[t+l]=G-C;f[t+0]=G+C;f[g+q]=x-y;f[g+e]=x+y;t+=a;g+=a}while(g<r);p=E;E=p*v[c+0]-h*v[c+1];h=p*v[c+1]+h*v[c+
0]}c+=2}while(a<d)}var k=G(f.BLKSIZE),r=G(f.BLKSIZE_s/2),v=[.9238795325112867,.3826834323650898,.9951847266721969,.0980171403295606,.9996988186962042,.02454122852291229,.9999811752826011,.006135884649154475],C=[0,128,64,192,32,160,96,224,16,144,80,208,48,176,112,240,8,136,72,200,40,168,104,232,24,152,88,216,56,184,120,248,4,132,68,196,36,164,100,228,20,148,84,212,52,180,116,244,12,140,76,204,44,172,108,236,28,156,92,220,60,188,124,252,2,130,66,194,34,162,98,226,18,146,82,210,50,178,114,242,10,138,
74,202,42,170,106,234,26,154,90,218,58,186,122,250,6,134,70,198,38,166,102,230,22,150,86,214,54,182,118,246,14,142,78,206,46,174,110,238,30,158,94,222,62,190,126,254];this.fft_short=function(k,v,d,c,w){for(k=0;3>k;k++){var a=f.BLKSIZE_s/2,b=65535&192*(k+1),u=f.BLKSIZE_s/8-1;do{var e=C[u<<2]&255;var l=r[e]*c[d][w+e+b];var q=r[127-e]*c[d][w+e+b+128];var g=l-q;l+=q;var t=r[e+64]*c[d][w+e+b+64];q=r[63-e]*c[d][w+e+b+192];var D=t-q;t+=q;a-=4;v[k][a+0]=l+t;v[k][a+2]=l-t;v[k][a+1]=g+D;v[k][a+3]=g-D;l=r[e+
1]*c[d][w+e+b+1];q=r[126-e]*c[d][w+e+b+129];g=l-q;l+=q;t=r[e+65]*c[d][w+e+b+65];q=r[62-e]*c[d][w+e+b+193];D=t-q;t+=q;v[k][a+f.BLKSIZE_s/2+0]=l+t;v[k][a+f.BLKSIZE_s/2+2]=l-t;v[k][a+f.BLKSIZE_s/2+1]=g+D;v[k][a+f.BLKSIZE_s/2+3]=g-D}while(0<=--u);x(v[k],a,f.BLKSIZE_s/2)}};this.fft_long=function(r,v,d,c,w){r=f.BLKSIZE/8-1;var a=f.BLKSIZE/2;do{var b=C[r]&255;var u=k[b]*c[d][w+b];var e=k[b+512]*c[d][w+b+512];var l=u-e;u+=e;var q=k[b+256]*c[d][w+b+256];e=k[b+768]*c[d][w+b+768];var g=q-e;q+=e;a-=4;v[a+0]=
u+q;v[a+2]=u-q;v[a+1]=l+g;v[a+3]=l-g;u=k[b+1]*c[d][w+b+1];e=k[b+513]*c[d][w+b+513];l=u-e;u+=e;q=k[b+257]*c[d][w+b+257];e=k[b+769]*c[d][w+b+769];g=q-e;q+=e;v[a+f.BLKSIZE/2+0]=u+q;v[a+f.BLKSIZE/2+2]=u-q;v[a+f.BLKSIZE/2+1]=l+g;v[a+f.BLKSIZE/2+3]=l-g}while(0<=--r);x(v,a,f.BLKSIZE/2)};this.init_fft=function(u){for(u=0;u<f.BLKSIZE;u++)k[u]=.42-.5*Math.cos(2*Math.PI*(u+.5)/f.BLKSIZE)+.08*Math.cos(4*Math.PI*(u+.5)/f.BLKSIZE);for(u=0;u<f.BLKSIZE_s/2;u++)r[u]=.5*(1-Math.cos(2*Math.PI*(u+.5)/f.BLKSIZE_s))}}
function $b(){function x(a,b){for(var c=0,d=0;d<f.BLKSIZE/2;++d)c+=a[d]*b.ATH.eql_w[d];return c*=D}function k(a,b,c,f,d,e){if(b>a)if(b<a*n)var g=b/a;else return a+b;else{if(a>=b*n)return a+b;g=a/b}a+=b;if(6>=f+3){if(g>=m)return a;f=0|Z.FAST_LOG10_X(g,16);return a*p[f]}f=0|Z.FAST_LOG10_X(g,16);b=0!=e?d.ATH.cb_s[c]*d.ATH.adjust:d.ATH.cb_l[c]*d.ATH.adjust;return a<z*b?a>b?(c=1,13>=f&&(c=A[f]),b=Z.FAST_LOG10_X(a/b,10/15),a*((h[f]-c)*b+c)):13<f?a:a*A[f]:a*h[f]}function r(a,b,c){0>a&&(a=0);0>b&&(b=0);if(0>=
a)return b;if(0>=b)return a;var f=b>a?b/a:a/b;if(-2<=c&&2>=c){if(f>=m)return a+b;c=0|Z.FAST_LOG10_X(f,16);return(a+b)*y[c]}if(f<n)return a+b;a<b&&(a=b);return a}function v(a,b,c,d,e){var h,g,p=0,k=0;for(h=g=0;h<f.SBMAX_s;++g,++h){for(var l=a.bo_s[h],m=a.npart_s,l=l<m?l:m;g<l;)p+=b[g],k+=c[g],g++;a.en[d].s[h][e]=p;a.thm[d].s[h][e]=k;if(g>=m){++h;break}k=a.PSY.bo_s_weight[h];m=1-k;p=k*b[g];k*=c[g];a.en[d].s[h][e]+=p;a.thm[d].s[h][e]+=k;p=m*b[g];k=m*c[g]}for(;h<f.SBMAX_s;++h)a.en[d].s[h][e]=0,a.thm[d].s[h][e]=
0}function C(a,b,c,d){var e,h,g=0,p=0;for(e=h=0;e<f.SBMAX_l;++h,++e){for(var k=a.bo_l[e],l=a.npart_l,k=k<l?k:l;h<k;)g+=b[h],p+=c[h],h++;a.en[d].l[e]=g;a.thm[d].l[e]=p;if(h>=l){++e;break}p=a.PSY.bo_l_weight[e];l=1-p;g=p*b[h];p*=c[h];a.en[d].l[e]+=g;a.thm[d].l[e]+=p;g=l*b[h];p=l*c[h]}for(;e<f.SBMAX_l;++e)a.en[d].l[e]=0,a.thm[d].l[e]=0}function u(a,b,c){return 1<=c?a:0>=c?b:0<b?Math.pow(a/b,c)*b:0}function W(a,b){for(var c=309.07,d=0;d<f.SBMAX_s-1;d++)for(var e=0;3>e;e++){var h=a.thm.s[d][e];if(0<h){var h=
h*b,g=a.en.s[d][e];g>h&&(c=g>1E10*h?c+23.02585092994046*O[d]:c+O[d]*Z.FAST_LOG10(g/h))}}return c}function d(a,b){for(var c=281.0575,d=0;d<f.SBMAX_l-1;d++){var e=a.thm.l[d];if(0<e){var e=e*b,h=a.en.l[d];h>e&&(c=h>1E10*e?c+23.02585092994046*F[d]:c+F[d]*Z.FAST_LOG10(h/e))}}return c}function c(a,b,c,d,f){var e,h;for(e=h=0;e<a.npart_l;++e){var g=0,p=0,k;for(k=0;k<a.numlines_l[e];++k,++h){var l=b[h],g=g+l;p<l&&(p=l)}c[e]=g;d[e]=p;f[e]=g*a.rnumlines_l[e]}}function w(a,b,c,d){var f=E.length-1,e=0,h=c[e]+
c[e+1];if(0<h){var g=b[e];g<b[e+1]&&(g=b[e+1]);h=20*(2*g-h)/(h*(a.numlines_l[e]+a.numlines_l[e+1]-1));g=0|h;g>f&&(g=f);d[e]=g}else d[e]=0;for(e=1;e<a.npart_l-1;e++)h=c[e-1]+c[e]+c[e+1],0<h?(g=b[e-1],g<b[e]&&(g=b[e]),g<b[e+1]&&(g=b[e+1]),h=20*(3*g-h)/(h*(a.numlines_l[e-1]+a.numlines_l[e]+a.numlines_l[e+1]-1)),g=0|h,g>f&&(g=f),d[e]=g):d[e]=0;h=c[e-1]+c[e];0<h?(g=b[e-1],g<b[e]&&(g=b[e]),h=20*(2*g-h)/(h*(a.numlines_l[e-1]+a.numlines_l[e]-1)),g=0|h,g>f&&(g=f),d[e]=g):d[e]=0}function a(a,b,c,e,d,f,h){var g=
2*f;d=0<f?Math.pow(10,d):1;for(var p,k,l=0;l<h;++l){var m=a[2][l],q=a[3][l],A=b[0][l],r=b[1][l],n=b[2][l],t=b[3][l];A<=1.58*r&&r<=1.58*A?(p=c[l]*m,k=Math.max(n,Math.min(t,c[l]*q)),p=Math.max(t,Math.min(n,p))):(k=n,p=t);0<f&&(t=e[l]*d,A=Math.min(Math.max(A,t),Math.max(r,t)),n=Math.max(k,t),t=Math.max(p,t),r=n+t,0<r&&A*g<r&&(A=A*g/r,n*=A,t*=A),k=Math.min(n,k),p=Math.min(t,p));k>m&&(k=m);p>q&&(p=q);b[2][l]=k;b[3][l]=p}}function b(a,b){a=0<=a?27*-a:a*b;return-72>=a?0:Math.exp(.2302585093*a)}function B(a){0>
a&&(a=0);a*=.001;return 13*Math.atan(.76*a)+3.5*Math.atan(a*a/56.25)}function e(a,b,c,e,d,h,g,p,k,l,m,q){var A=G(f.CBANDS+1),r=p/(15<q?1152:384),t=U(f.HBLKSIZE),n;p/=k;var u=0,Q=0;for(n=0;n<f.CBANDS;n++){var w;var y=B(p*u);A[n]=p*u;for(w=u;.34>B(p*w)-y&&w<=k/2;w++);a[n]=w-u;for(Q=n+1;u<w;)t[u++]=n;if(u>k/2){u=k/2;++n;break}}A[n]=p*u;for(y=0;y<q;y++)n=l[y],u=l[y+1],n=0|Math.floor(.5+m*(n-.5)),0>n&&(n=0),w=0|Math.floor(.5+m*(u-.5)),w>k/2&&(w=k/2),c[y]=(t[n]+t[w])/2,b[y]=t[w],g[y]=(r*u-A[b[y]])/(A[b[y]+
1]-A[b[y]]),0>g[y]?g[y]=0:1<g[y]&&(g[y]=1),u=B(p*l[y]*m),u=Math.min(u,15.5)/15.5,h[y]=Math.pow(10,1.25*(1-Math.cos(Math.PI*u))-2.5);for(b=u=0;b<Q;b++)c=a[b],y=B(p*u),h=B(p*(u+c-1)),e[b]=.5*(y+h),y=B(p*(u-.5)),h=B(p*(u+c-.5)),d[b]=h-y,u+=c;return Q}function l(a,c,e,d,h,g){var p=ha([f.CBANDS,f.CBANDS]),k=0;if(g)for(var l=0;l<c;l++)for(g=0;g<c;g++){var m=e[l]-e[g];m=0<=m?3*m:1.5*m;if(.5<=m&&2.5>=m){var q=m-.5;q=8*(q*q-2*q)}else q=0;m+=.474;m=15.811389+7.5*m-17.5*Math.sqrt(1+m*m);-60>=m?q=0:(m=Math.exp(.2302585093*
(q+m)),q=m/.6609193);m=q*d[g];p[l][g]=m*h[l]}else for(g=0;g<c;g++){q=15+Math.min(21/e[g],12);var A;var r,l=q;for(A=0;1E-20<b(A,l);--A);var n=A;for(r=0;1E-12<Math.abs(r-n);)A=(r+n)/2,0<b(A,l)?r=A:n=A;m=n;for(A=0;1E-20<b(A,l);A+=1);n=0;for(r=A;1E-12<Math.abs(r-n);)A=(r+n)/2,0<b(A,l)?n=A:r=A;var t=0;for(n=0;1E3>=n;++n)A=m+n*(r-m)/1E3,A=b(A,l),t+=A;A=1001/(t*(r-m));for(l=0;l<c;l++)m=A*b(e[l]-e[g],q)*d[g],p[l][g]=m*h[l]}for(l=0;l<c;l++){for(g=0;g<c&&!(0<p[l][g]);g++);a[l][0]=g;for(g=c-1;0<g&&!(0<p[l][g]);g--);
a[l][1]=g;k+=a[l][1]-a[l][0]+1}e=G(k);for(l=d=0;l<c;l++)for(g=a[l][0];g<=a[l][1];g++)e[d++]=p[l][g];return e}function q(a){a=B(a);a=Math.min(a,15.5)/15.5;return Math.pow(10,1.25*(1-Math.cos(Math.PI*a))-2.5)}function g(a,b){-.3>a&&(a=3410);a=Math.max(.1,a/1E3);return 3.64*Math.pow(a,-.8)-6.8*Math.exp(-.6*Math.pow(a-3.4,2))+6*Math.exp(-.15*Math.pow(a-8.7,2))+.001*(.6+.04*b)*Math.pow(a,4)}var t=new Jc,D=1/217621504/(f.BLKSIZE/2),m,n,z,E=[1,.79433,.63096,.63096,.63096,.63096,.63096,.25119,.11749],h=[3.3246*
3.3246,3.23837*3.23837,9.9500500969,9.0247369744,8.1854926609,7.0440875649,2.46209*2.46209,2.284*2.284,4.4892710641,1.96552*1.96552,1.82335*1.82335,1.69146*1.69146,2.4621061921,2.1508568964,1.37074*1.37074,1.31036*1.31036,1.5691069696,1.4555939904,1.16203*1.16203,1.2715945225,1.09428*1.09428,1.0659*1.0659,1.0779838276,1.0382591025,1],p=[1.7782755904,1.35879*1.35879,1.38454*1.38454,1.39497*1.39497,1.40548*1.40548,1.3537*1.3537,1.6999465924,1.22321*1.22321,1.3169398564,1],A=[5.5396212496,2.29259*2.29259,
4.9868695969,2.12675*2.12675,2.02545*2.02545,1.87894*1.87894,1.74303*1.74303,1.61695*1.61695,2.2499700001,1.39148*1.39148,1.29083*1.29083,1.19746*1.19746,1.2339655056,1.0779838276],y=[1.7782755904,1.35879*1.35879,1.38454*1.38454,1.39497*1.39497,1.40548*1.40548,1.3537*1.3537,1.6999465924,1.22321*1.22321,1.3169398564,1],O=[11.8,13.6,17.2,32,46.5,51.3,57.5,67.1,71.5,84.6,97.6,130],F=[6.8,5.8,5.8,6.4,6.5,9.9,12.1,14.4,15,18.9,21.6,26.9,34.2,40.2,46.8,56.5,60.7,73.9,85.7,93.4,126.1],R=[-1.730326E-17,-.01703172,
-1.349528E-17,.0418072,-6.73278E-17,-.0876324,-3.0835E-17,.1863476,-1.104424E-16,-.627638];this.L3psycho_anal_ns=function(a,b,e,h,g,p,l,m,q,A){var r,n=a.internal_flags,y=ha([2,f.BLKSIZE]),Q=ha([2,3,f.BLKSIZE_s]),I=G(f.CBANDS+1),H=G(f.CBANDS+1),V=G(f.CBANDS+2),O=U(2),D=U(2),z,M,B,X,F,N,K,la=ha([2,576]),S=U(f.CBANDS+2),ca=U(f.CBANDS+2);qa.fill(ca,0);var fa=n.channels_out;a.mode==ka.JOINT_STEREO&&(fa=4);var T=a.VBR==J.vbr_off?0==n.ResvMax?0:n.ResvSize/n.ResvMax*.5:a.VBR==J.vbr_rh||a.VBR==J.vbr_mtrh||
a.VBR==J.vbr_mt?.6:1;for(z=0;z<n.channels_out;z++){var Qa=b[z],L=e+576-350-21+192;for(B=0;576>B;B++){var ba;var ia=Qa[L+B+10];for(X=ba=0;9>X;X+=2)ia+=R[X]*(Qa[L+B+X]+Qa[L+B+21-X]),ba+=R[X+1]*(Qa[L+B+X+1]+Qa[L+B+21-X-1]);la[z][B]=ia+ba}g[h][z].en.assign(n.en[z]);g[h][z].thm.assign(n.thm[z]);2<fa&&(p[h][z].en.assign(n.en[z+2]),p[h][z].thm.assign(n.thm[z+2]))}for(z=0;z<fa;z++){var Ka=G(12),da=[0,0,0,0],Da=G(12),Y=1,ac=G(f.CBANDS),ra=G(f.CBANDS),ya=[0,0,0,0],xa=G(f.HBLKSIZE),ma=ha([3,f.HBLKSIZE_s]);for(B=
0;3>B;B++)Ka[B]=n.nsPsy.last_en_subshort[z][B+6],Da[B]=Ka[B]/n.nsPsy.last_en_subshort[z][B+4],da[0]+=Ka[B];if(2==z)for(B=0;576>B;B++){var za=la[0][B];var Ub=la[1][B];la[0][B]=za+Ub;la[1][B]=za-Ub}var Za=la[z&1],bc=0;for(B=0;9>B;B++){var Ua=bc+64;for(r=1;bc<Ua;bc++)r<Math.abs(Za[bc])&&(r=Math.abs(Za[bc]));n.nsPsy.last_en_subshort[z][B]=Ka[B+3]=r;da[1+B/3]+=r;r=r>Ka[B+3-2]?r/Ka[B+3-2]:Ka[B+3-2]>10*r?Ka[B+3-2]/(10*r):0;Da[B+3]=r}if(a.analysis){var Fa=Da[0];for(B=1;12>B;B++)Fa<Da[B]&&(Fa=Da[B]);n.pinfo.ers[h][z]=
n.pinfo.ers_save[z];n.pinfo.ers_save[z]=Fa}var sb=3==z?n.nsPsy.attackthre_s:n.nsPsy.attackthre;for(B=0;12>B;B++)0==ya[B/3]&&Da[B]>sb&&(ya[B/3]=B%3+1);for(B=1;4>B;B++)1.7>(da[B-1]>da[B]?da[B-1]/da[B]:da[B]/da[B-1])&&(ya[B]=0,1==B&&(ya[0]=0));0!=ya[0]&&0!=n.nsPsy.lastAttacks[z]&&(ya[0]=0);if(3==n.nsPsy.lastAttacks[z]||0!=ya[0]+ya[1]+ya[2]+ya[3])Y=0,0!=ya[1]&&0!=ya[0]&&(ya[1]=0),0!=ya[2]&&0!=ya[1]&&(ya[2]=0),0!=ya[3]&&0!=ya[2]&&(ya[3]=0);2>z?D[z]=Y:0==Y&&(D[0]=D[1]=0);q[z]=n.tot_ener[z];var ub=void 0,
Vb=void 0,ob=void 0,P=void 0,jc=a,Ia=xa,Gb=ma,Ra=y,La=z&1,Ma=Q,Sa=z&1,va=h,Na=z,fb=b,gb=e,Va=jc.internal_flags;if(2>Na)t.fft_long(Va,Ra[La],Na,fb,gb),t.fft_short(Va,Ma[Sa],Na,fb,gb);else if(2==Na){for(var ja=f.BLKSIZE-1;0<=ja;--ja)ub=Ra[La+0][ja],Vb=Ra[La+1][ja],Ra[La+0][ja]=(ub+Vb)*Z.SQRT2*.5,Ra[La+1][ja]=(ub-Vb)*Z.SQRT2*.5;for(var Ca=2;0<=Ca;--Ca)for(ja=f.BLKSIZE_s-1;0<=ja;--ja)ub=Ma[Sa+0][Ca][ja],Vb=Ma[Sa+1][Ca][ja],Ma[Sa+0][Ca][ja]=(ub+Vb)*Z.SQRT2*.5,Ma[Sa+1][Ca][ja]=(ub-Vb)*Z.SQRT2*.5}Ia[0]=
Ra[La+0][0];Ia[0]*=Ia[0];for(ja=f.BLKSIZE/2-1;0<=ja;--ja)ob=Ra[La+0][f.BLKSIZE/2-ja],P=Ra[La+0][f.BLKSIZE/2+ja],Ia[f.BLKSIZE/2-ja]=.5*(ob*ob+P*P);for(Ca=2;0<=Ca;--Ca)for(Gb[Ca][0]=Ma[Sa+0][Ca][0],Gb[Ca][0]*=Gb[Ca][0],ja=f.BLKSIZE_s/2-1;0<=ja;--ja)ob=Ma[Sa+0][Ca][f.BLKSIZE_s/2-ja],P=Ma[Sa+0][Ca][f.BLKSIZE_s/2+ja],Gb[Ca][f.BLKSIZE_s/2-ja]=.5*(ob*ob+P*P);for(var rb=0,ja=11;ja<f.HBLKSIZE;ja++)rb+=Ia[ja];Va.tot_ener[Na]=rb;if(jc.analysis){for(ja=0;ja<f.HBLKSIZE;ja++)Va.pinfo.energy[va][Na][ja]=Va.pinfo.energy_save[Na][ja],
Va.pinfo.energy_save[Na][ja]=Ia[ja];Va.pinfo.pe[va][Na]=Va.pe[Na]}2==jc.athaa_loudapprox&&2>Na&&(Va.loudness_sq[va][Na]=Va.loudness_sq_save[Na],Va.loudness_sq_save[Na]=x(Ia,Va));c(n,xa,I,ac,ra);w(n,ac,ra,S);for(K=0;3>K;K++){for(var Hb,aa,sa=void 0,ta=void 0,Wb=void 0,Ab=ma,Ta=H,$a=V,vb=z,Fb=K,Ga=a.internal_flags,sa=ta=0;sa<Ga.npart_s;++sa){for(var Ib=0,tb=0,hb=Ga.numlines_s[sa],zb=0;zb<hb;++zb,++ta){var Bb=Ab[Fb][ta],Ib=Ib+Bb;tb<Bb&&(tb=Bb)}Ta[sa]=Ib}for(ta=sa=0;sa<Ga.npart_s;sa++){var wb=Ga.s3ind_s[sa][0],
ab=Ga.s3_ss[ta++]*Ta[wb];for(++wb;wb<=Ga.s3ind_s[sa][1];)ab+=Ga.s3_ss[ta]*Ta[wb],++ta,++wb;Wb=2*Ga.nb_s1[vb][sa];$a[sa]=Math.min(ab,Wb);Ga.blocktype_old[vb&1]==f.SHORT_TYPE&&(Wb=16*Ga.nb_s2[vb][sa],$a[sa]=Math.min(Wb,$a[sa]));Ga.nb_s2[vb][sa]=Ga.nb_s1[vb][sa];Ga.nb_s1[vb][sa]=ab}for(;sa<=f.CBANDS;++sa)Ta[sa]=0,$a[sa]=0;v(n,H,V,z,K);for(N=0;N<f.SBMAX_s;N++){aa=n.thm[z].s[N][K];aa*=.8;if(2<=ya[K]||1==ya[K+1]){var ib=0!=K?K-1:2;r=u(n.thm[z].s[N][ib],aa,.6*T);aa=Math.min(aa,r)}if(1==ya[K])ib=0!=K?K-1:
2,r=u(n.thm[z].s[N][ib],aa,.3*T),aa=Math.min(aa,r);else if(0!=K&&3==ya[K-1]||0==K&&3==n.nsPsy.lastAttacks[z])ib=2!=K?K+1:0,r=u(n.thm[z].s[N][ib],aa,.3*T),aa=Math.min(aa,r);Hb=Ka[3*K+3]+Ka[3*K+4]+Ka[3*K+5];6*Ka[3*K+5]<Hb&&(aa*=.5,6*Ka[3*K+4]<Hb&&(aa*=.5));n.thm[z].s[N][K]=aa}}n.nsPsy.lastAttacks[z]=ya[2];for(M=F=0;M<n.npart_l;M++){for(var xb=n.s3ind[M][0],Qb=I[xb]*E[S[xb]],jb=n.s3_ll[F++]*Qb;++xb<=n.s3ind[M][1];)Qb=I[xb]*E[S[xb]],jb=k(jb,n.s3_ll[F++]*Qb,xb,xb-M,n,0);jb*=.158489319246111;V[M]=n.blocktype_old[z&
1]==f.SHORT_TYPE?jb:u(Math.min(jb,Math.min(2*n.nb_1[z][M],16*n.nb_2[z][M])),jb,T);n.nb_2[z][M]=n.nb_1[z][M];n.nb_1[z][M]=jb}for(;M<=f.CBANDS;++M)I[M]=0,V[M]=0;C(n,I,V,z)}if((a.mode==ka.STEREO||a.mode==ka.JOINT_STEREO)&&0<a.interChRatio){var Cb=a.interChRatio,Oa=a.internal_flags;if(1<Oa.channels_out){for(var Aa=0;Aa<f.SBMAX_l;Aa++){var oa=Oa.thm[0].l[Aa];var Jb=Oa.thm[1].l[Aa];Oa.thm[0].l[Aa]+=Jb*Cb;Oa.thm[1].l[Aa]+=oa*Cb}for(Aa=0;Aa<f.SBMAX_s;Aa++)for(var kb=0;3>kb;kb++)oa=Oa.thm[0].s[Aa][kb],Jb=
Oa.thm[1].s[Aa][kb],Oa.thm[0].s[Aa][kb]+=Jb*Cb,Oa.thm[1].s[Aa][kb]+=oa*Cb}}if(a.mode==ka.JOINT_STEREO){for(var Db,Kb,Ja,Xa,ea=0;ea<f.SBMAX_l;ea++)n.thm[0].l[ea]>1.58*n.thm[1].l[ea]||n.thm[1].l[ea]>1.58*n.thm[0].l[ea]||(Xa=n.mld_l[ea]*n.en[3].l[ea],Ja=Math.max(n.thm[2].l[ea],Math.min(n.thm[3].l[ea],Xa)),Xa=n.mld_l[ea]*n.en[2].l[ea],Kb=Math.max(n.thm[3].l[ea],Math.min(n.thm[2].l[ea],Xa)),n.thm[2].l[ea]=Ja,n.thm[3].l[ea]=Kb);for(ea=0;ea<f.SBMAX_s;ea++)for(var Ba=0;3>Ba;Ba++)n.thm[0].s[ea][Ba]>1.58*n.thm[1].s[ea][Ba]||
n.thm[1].s[ea][Ba]>1.58*n.thm[0].s[ea][Ba]||(Xa=n.mld_s[ea]*n.en[3].s[ea][Ba],Ja=Math.max(n.thm[2].s[ea][Ba],Math.min(n.thm[3].s[ea][Ba],Xa)),Xa=n.mld_s[ea]*n.en[2].s[ea][Ba],Kb=Math.max(n.thm[3].s[ea][Ba],Math.min(n.thm[2].s[ea][Ba],Xa)),n.thm[2].s[ea][Ba]=Ja,n.thm[3].s[ea][Ba]=Kb);Db=a.msfix;if(0<Math.abs(Db)){for(var Lb=Db,Ha,bb,cb,db,Eb,ga=Lb,lb=Math.pow(10,a.ATHlower*n.ATH.adjust),Lb=2*Lb,ga=2*ga,wa=0;wa<f.SBMAX_l;wa++)bb=n.ATH.cb_l[n.bm_l[wa]]*lb,Eb=Math.min(Math.max(n.thm[0].l[wa],bb),Math.max(n.thm[1].l[wa],
bb)),db=Math.max(n.thm[2].l[wa],bb),cb=Math.max(n.thm[3].l[wa],bb),Eb*Lb<db+cb&&(Ha=Eb*ga/(db+cb),db*=Ha,cb*=Ha),n.thm[2].l[wa]=Math.min(db,n.thm[2].l[wa]),n.thm[3].l[wa]=Math.min(cb,n.thm[3].l[wa]);lb*=f.BLKSIZE_s/f.BLKSIZE;for(wa=0;wa<f.SBMAX_s;wa++)for(var mb=0;3>mb;mb++)bb=n.ATH.cb_s[n.bm_s[wa]]*lb,Eb=Math.min(Math.max(n.thm[0].s[wa][mb],bb),Math.max(n.thm[1].s[wa][mb],bb)),db=Math.max(n.thm[2].s[wa][mb],bb),cb=Math.max(n.thm[3].s[wa][mb],bb),Eb*Lb<db+cb&&(Ha=Eb*Lb/(db+cb),db*=Ha,cb*=Ha),n.thm[2].s[wa][mb]=
Math.min(n.thm[2].s[wa][mb],db),n.thm[3].s[wa][mb]=Math.min(n.thm[3].s[wa][mb],cb)}}var Wa=a.internal_flags;a.short_blocks!=pa.short_block_coupled||0!=D[0]&&0!=D[1]||(D[0]=D[1]=0);for(var ua=0;ua<Wa.channels_out;ua++)O[ua]=f.NORM_TYPE,a.short_blocks==pa.short_block_dispensed&&(D[ua]=1),a.short_blocks==pa.short_block_forced&&(D[ua]=0),0!=D[ua]?Wa.blocktype_old[ua]==f.SHORT_TYPE&&(O[ua]=f.STOP_TYPE):(O[ua]=f.SHORT_TYPE,Wa.blocktype_old[ua]==f.NORM_TYPE&&(Wa.blocktype_old[ua]=f.START_TYPE),Wa.blocktype_old[ua]==
f.STOP_TYPE&&(Wa.blocktype_old[ua]=f.SHORT_TYPE)),A[ua]=Wa.blocktype_old[ua],Wa.blocktype_old[ua]=O[ua];for(z=0;z<fa;z++){var Mb=0;if(1<z){var cc=m;Mb=-2;var Ea=f.NORM_TYPE;if(A[0]==f.SHORT_TYPE||A[1]==f.SHORT_TYPE)Ea=f.SHORT_TYPE;var Nb=p[h][z-2]}else cc=l,Mb=0,Ea=A[z],Nb=g[h][z];cc[Mb+z]=Ea==f.SHORT_TYPE?W(Nb,n.masking_lower):d(Nb,n.masking_lower);a.analysis&&(n.pinfo.pe[h][z]=cc[Mb+z])}return 0};var X=[-1.730326E-17,-.01703172,-1.349528E-17,.0418072,-6.73278E-17,-.0876324,-3.0835E-17,.1863476,
-1.104424E-16,-.627638];this.L3psycho_anal_vbr=function(b,e,h,g,p,l,k,m,n,q){for(var A,y,Q,z=b.internal_flags,B,H,V=G(f.HBLKSIZE),I=ha([3,f.HBLKSIZE_s]),O=ha([2,f.BLKSIZE]),D=ha([2,3,f.BLKSIZE_s]),M=ha([4,f.CBANDS]),R=ha([4,f.CBANDS]),F=ha([4,3]),N=[[0,0,0,0],[0,0,0,0],[0,0,0,0],[0,0,0,0]],K=U(2),J=b.mode==ka.JOINT_STEREO?4:z.channels_out,la=ha([2,576]),S=b.internal_flags,fa=S.channels_out,ca=b.mode==ka.JOINT_STEREO?4:fa,T=0;T<fa;T++){firbuf=e[T];for(var Qa=h+576-350-21+192,L=0;576>L;L++){var ba;
var ia=firbuf[Qa+L+10];for(var da=ba=0;9>da;da+=2)ia+=X[da]*(firbuf[Qa+L+da]+firbuf[Qa+L+21-da]),ba+=X[da+1]*(firbuf[Qa+L+da+1]+firbuf[Qa+L+21-da-1]);la[T][L]=ia+ba}p[g][T].en.assign(S.en[T]);p[g][T].thm.assign(S.thm[T]);2<ca&&(l[g][T].en.assign(S.en[T+2]),l[g][T].thm.assign(S.thm[T+2]))}for(T=0;T<ca;T++){var Y=G(12),Da=G(12),ra=[0,0,0,0],xa=la[T&1],qa=0,ya=3==T?S.nsPsy.attackthre_s:S.nsPsy.attackthre,ma=1;if(2==T)for(L=0,da=576;0<da;++L,--da){var ac=la[0][L],Fa=la[1][L];la[0][L]=ac+Fa;la[1][L]=ac-
Fa}for(L=0;3>L;L++)Da[L]=S.nsPsy.last_en_subshort[T][L+6],Y[L]=Da[L]/S.nsPsy.last_en_subshort[T][L+4],ra[0]+=Da[L];for(L=0;9>L;L++){for(var za=qa+64,Za=1;qa<za;qa++)Za<Math.abs(xa[qa])&&(Za=Math.abs(xa[qa]));S.nsPsy.last_en_subshort[T][L]=Da[L+3]=Za;ra[1+L/3]+=Za;Za=Za>Da[L+3-2]?Za/Da[L+3-2]:Da[L+3-2]>10*Za?Da[L+3-2]/(10*Za):0;Y[L+3]=Za}for(L=0;3>L;++L){var Ua=Da[3*L+3]+Da[3*L+4]+Da[3*L+5],Ub=1;6*Da[3*L+5]<Ua&&(Ub*=.5,6*Da[3*L+4]<Ua&&(Ub*=.5));F[T][L]=Ub}if(b.analysis){for(var fb=Y[0],L=1;12>L;L++)fb<
Y[L]&&(fb=Y[L]);S.pinfo.ers[g][T]=S.pinfo.ers_save[T];S.pinfo.ers_save[T]=fb}for(L=0;12>L;L++)0==N[T][L/3]&&Y[L]>ya&&(N[T][L/3]=L%3+1);for(L=1;4>L;L++){var gb=ra[L-1],ub=ra[L];4E4>Math.max(gb,ub)&&gb<1.7*ub&&ub<1.7*gb&&(1==L&&N[T][0]<=N[T][L]&&(N[T][0]=0),N[T][L]=0)}N[T][0]<=S.nsPsy.lastAttacks[T]&&(N[T][0]=0);if(3==S.nsPsy.lastAttacks[T]||0!=N[T][0]+N[T][1]+N[T][2]+N[T][3])ma=0,0!=N[T][1]&&0!=N[T][0]&&(N[T][1]=0),0!=N[T][2]&&0!=N[T][1]&&(N[T][2]=0),0!=N[T][3]&&0!=N[T][2]&&(N[T][3]=0);2>T?K[T]=ma:
0==ma&&(K[0]=K[1]=0);n[T]=S.tot_ener[T]}var sb=b.internal_flags;b.short_blocks!=pa.short_block_coupled||0!=K[0]&&0!=K[1]||(K[0]=K[1]=0);for(var ob=0;ob<sb.channels_out;ob++)b.short_blocks==pa.short_block_dispensed&&(K[ob]=1),b.short_blocks==pa.short_block_forced&&(K[ob]=0);for(var P=0;P<J;P++){Q=P&1;B=O;var rb=b,Ia=P,Gb=g,Ra=V,La=B,Ma=Q,Sa=rb.internal_flags;if(2>Ia)t.fft_long(Sa,La[Ma],Ia,e,h);else if(2==Ia)for(var va=f.BLKSIZE-1;0<=va;--va){var Na=La[Ma+0][va],tb=La[Ma+1][va];La[Ma+0][va]=(Na+tb)*
Z.SQRT2*.5;La[Ma+1][va]=(Na-tb)*Z.SQRT2*.5}Ra[0]=La[Ma+0][0];Ra[0]*=Ra[0];for(va=f.BLKSIZE/2-1;0<=va;--va){var zb=La[Ma+0][f.BLKSIZE/2-va],Va=La[Ma+0][f.BLKSIZE/2+va];Ra[f.BLKSIZE/2-va]=.5*(zb*zb+Va*Va)}for(var ja=0,va=11;va<f.HBLKSIZE;va++)ja+=Ra[va];Sa.tot_ener[Ia]=ja;if(rb.analysis){for(va=0;va<f.HBLKSIZE;va++)Sa.pinfo.energy[Gb][Ia][va]=Sa.pinfo.energy_save[Ia][va],Sa.pinfo.energy_save[Ia][va]=Ra[va];Sa.pinfo.pe[Gb][Ia]=Sa.pe[Ia]}var Ca=P,Fb=V,Hb=b.internal_flags;2==b.athaa_loudapprox&&2>Ca&&
(Hb.loudness_sq[g][Ca]=Hb.loudness_sq_save[Ca],Hb.loudness_sq_save[Ca]=x(Fb,Hb));if(0!=K[Q]){var aa=void 0,sa=void 0,ta=z,Wb=V,Ab=M[P],Ta=R[P],$a=P,vb=G(f.CBANDS),Tb=G(f.CBANDS),Ga=U(f.CBANDS+2);c(ta,Wb,Ab,vb,Tb);w(ta,vb,Tb,Ga);for(var Ib=0,aa=0;aa<ta.npart_l;aa++){var $b,hb=ta.s3ind[aa][0],fc=ta.s3ind[aa][1],Bb=0,wb=0,Bb=Ga[hb],wb=wb+1;var ab=ta.s3_ll[Ib]*Ab[hb]*E[Ga[hb]];++Ib;for(++hb;hb<=fc;){Bb+=Ga[hb];wb+=1;var ib=ta.s3_ll[Ib]*Ab[hb]*E[Ga[hb]];ab=$b=r(ab,ib,hb-aa);++Ib;++hb}Bb=(1+2*Bb)/(2*wb);
var xb=.5*E[Bb];ab*=xb;if(ta.blocktype_old[$a&1]==f.SHORT_TYPE)sa=2*ta.nb_1[$a][aa],Ta[aa]=0<sa?Math.min(ab,sa):Math.min(ab,.3*Ab[aa]);else{var Qb=16*ta.nb_2[$a][aa],jb=2*ta.nb_1[$a][aa];0>=Qb&&(Qb=ab);0>=jb&&(jb=ab);sa=ta.blocktype_old[$a&1]==f.NORM_TYPE?Math.min(jb,Qb):jb;Ta[aa]=Math.min(ab,sa)}ta.nb_2[$a][aa]=ta.nb_1[$a][aa];ta.nb_1[$a][aa]=ab;ib=vb[aa];ib*=ta.minval_l[aa];ib*=xb;Ta[aa]>ib&&(Ta[aa]=ib);1<ta.masking_lower&&(Ta[aa]*=ta.masking_lower);Ta[aa]>Ab[aa]&&(Ta[aa]=Ab[aa]);1>ta.masking_lower&&
(Ta[aa]*=ta.masking_lower)}for(;aa<f.CBANDS;++aa)Ab[aa]=0,Ta[aa]=0}else for(var Cb=z,Oa=P,Aa=0;Aa<Cb.npart_l;Aa++)Cb.nb_2[Oa][Aa]=Cb.nb_1[Oa][Aa],Cb.nb_1[Oa][Aa]=0}2==K[0]+K[1]&&b.mode==ka.JOINT_STEREO&&a(M,R,z.mld_cb_l,z.ATH.cb_l,b.ATHlower*z.ATH.adjust,b.msfix,z.npart_l);for(P=0;P<J;P++)Q=P&1,0!=K[Q]&&C(z,M[P],R[P],P);for(var oa=0;3>oa;oa++){for(P=0;P<J;++P)if(Q=P&1,0!=K[Q]){var Jb=z,kb=P;if(0==oa)for(var Db=0;Db<Jb.npart_s;Db++)Jb.nb_s2[kb][Db]=Jb.nb_s1[kb][Db],Jb.nb_s1[kb][Db]=0}else{H=D;var Kb=
P,Ja=oa,Xa=I,ea=H,Ba=Q,Lb=b.internal_flags;0==Ja&&2>Kb&&t.fft_short(Lb,ea[Ba],Kb,e,h);if(2==Kb)for(var Ha=f.BLKSIZE_s-1;0<=Ha;--Ha){var bb=ea[Ba+0][Ja][Ha],cb=ea[Ba+1][Ja][Ha];ea[Ba+0][Ja][Ha]=(bb+cb)*Z.SQRT2*.5;ea[Ba+1][Ja][Ha]=(bb-cb)*Z.SQRT2*.5}Xa[Ja][0]=ea[Ba+0][Ja][0];Xa[Ja][0]*=Xa[Ja][0];for(Ha=f.BLKSIZE_s/2-1;0<=Ha;--Ha){var db=ea[Ba+0][Ja][f.BLKSIZE_s/2-Ha],Eb=ea[Ba+0][Ja][f.BLKSIZE_s/2+Ha];Xa[Ja][f.BLKSIZE_s/2-Ha]=.5*(db*db+Eb*Eb)}for(var ga=void 0,lb=void 0,wa=void 0,mb=I,Wa=M[P],ua=R[P],
Mb=P,cc=oa,Ea=b.internal_flags,Nb=new float[f.CBANDS],kc=G(f.CBANDS),dc=new int[f.CBANDS],ga=lb=0;ga<Ea.npart_s;++ga){for(var gc=0,lc=0,sc=Ea.numlines_s[ga],wa=0;wa<sc;++wa,++lb){var mc=mb[cc][lb],gc=gc+mc;lc<mc&&(lc=mc)}Wa[ga]=gc;Nb[ga]=lc;kc[ga]=gc/sc}for(;ga<f.CBANDS;++ga)Nb[ga]=0,kc[ga]=0;var Ya=void 0,nb=void 0,Ob=Ea,pb=Nb,Rb=kc,Xb=dc,Yb=E.length-1,na=0,Pa=Rb[na]+Rb[na+1];0<Pa?(Ya=pb[na],Ya<pb[na+1]&&(Ya=pb[na+1]),Pa=20*(2*Ya-Pa)/(Pa*(Ob.numlines_s[na]+Ob.numlines_s[na+1]-1)),nb=0|Pa,nb>Yb&&
(nb=Yb),Xb[na]=nb):Xb[na]=0;for(na=1;na<Ob.npart_s-1;na++)Pa=Rb[na-1]+Rb[na]+Rb[na+1],0<Pa?(Ya=pb[na-1],Ya<pb[na]&&(Ya=pb[na]),Ya<pb[na+1]&&(Ya=pb[na+1]),Pa=20*(3*Ya-Pa)/(Pa*(Ob.numlines_s[na-1]+Ob.numlines_s[na]+Ob.numlines_s[na+1]-1)),nb=0|Pa,nb>Yb&&(nb=Yb),Xb[na]=nb):Xb[na]=0;Pa=Rb[na-1]+Rb[na];0<Pa?(Ya=pb[na-1],Ya<pb[na]&&(Ya=pb[na]),Pa=20*(2*Ya-Pa)/(Pa*(Ob.numlines_s[na-1]+Ob.numlines_s[na]-1)),nb=0|Pa,nb>Yb&&(nb=Yb),Xb[na]=nb):Xb[na]=0;for(lb=ga=0;ga<Ea.npart_s;ga++){var yb=Ea.s3ind_s[ga][0],
ic=Ea.s3ind_s[ga][1];var hc=dc[yb];var tc=1;var ec=Ea.s3_ss[lb]*Wa[yb]*E[dc[yb]];++lb;for(++yb;yb<=ic;){hc+=dc[yb];tc+=1;var Zb=Ea.s3_ss[lb]*Wa[yb]*E[dc[yb]];ec=r(ec,Zb,yb-ga);++lb;++yb}hc=(1+2*hc)/(2*tc);var uc=.5*E[hc];ec*=uc;ua[ga]=ec;Ea.nb_s2[Mb][ga]=Ea.nb_s1[Mb][ga];Ea.nb_s1[Mb][ga]=ec;Zb=Nb[ga];Zb*=Ea.minval_s[ga];Zb*=uc;ua[ga]>Zb&&(ua[ga]=Zb);1<Ea.masking_lower&&(ua[ga]*=Ea.masking_lower);ua[ga]>Wa[ga]&&(ua[ga]=Wa[ga]);1>Ea.masking_lower&&(ua[ga]*=Ea.masking_lower)}for(;ga<f.CBANDS;++ga)Wa[ga]=
0,ua[ga]=0}0==K[0]+K[1]&&b.mode==ka.JOINT_STEREO&&a(M,R,z.mld_cb_s,z.ATH.cb_s,b.ATHlower*z.ATH.adjust,b.msfix,z.npart_s);for(P=0;P<J;++P)Q=P&1,0==K[Q]&&v(z,M[P],R[P],P,oa)}for(P=0;P<J;P++)if(Q=P&1,0==K[Q])for(var Sb=0;Sb<f.SBMAX_s;Sb++){for(var vc=G(3),oa=0;3>oa;oa++){var eb=z.thm[P].s[Sb][oa],eb=.8*eb;if(2<=N[P][oa]||1==N[P][oa+1])y=0!=oa?oa-1:2,A=u(z.thm[P].s[Sb][y],eb,.36),eb=Math.min(eb,A);else if(1==N[P][oa])y=0!=oa?oa-1:2,A=u(z.thm[P].s[Sb][y],eb,.18),eb=Math.min(eb,A);else if(0!=oa&&3==N[P][oa-
1]||0==oa&&3==z.nsPsy.lastAttacks[P])y=2!=oa?oa+1:0,A=u(z.thm[P].s[Sb][y],eb,.18),eb=Math.min(eb,A);eb*=F[P][oa];vc[oa]=eb}for(oa=0;3>oa;oa++)z.thm[P].s[Sb][oa]=vc[oa]}for(P=0;P<J;P++)z.nsPsy.lastAttacks[P]=N[P][2];for(var Pb=b.internal_flags,qb=0;qb<Pb.channels_out;qb++){var nc=f.NORM_TYPE;0!=K[qb]?Pb.blocktype_old[qb]==f.SHORT_TYPE&&(nc=f.STOP_TYPE):(nc=f.SHORT_TYPE,Pb.blocktype_old[qb]==f.NORM_TYPE&&(Pb.blocktype_old[qb]=f.START_TYPE),Pb.blocktype_old[qb]==f.STOP_TYPE&&(Pb.blocktype_old[qb]=f.SHORT_TYPE));
q[qb]=Pb.blocktype_old[qb];Pb.blocktype_old[qb]=nc}for(P=0;P<J;P++){if(1<P){var oc=m;var pc=-2;var qc=f.NORM_TYPE;if(q[0]==f.SHORT_TYPE||q[1]==f.SHORT_TYPE)qc=f.SHORT_TYPE;var rc=l[g][P-2]}else oc=k,pc=0,qc=q[P],rc=p[g][P];oc[pc+P]=qc==f.SHORT_TYPE?W(rc,z.masking_lower):d(rc,z.masking_lower);b.analysis&&(z.pinfo.pe[g][P]=oc[pc+P])}return 0};this.psymodel_init=function(a){var b,c,d=a.internal_flags,h,g=!0,p=13;var k=b=0;var A=-8.25,r=-4.5,u=G(f.CBANDS),y=G(f.CBANDS),w=G(f.CBANDS),x=a.out_samplerate;
switch(a.experimentalZ){default:case 0:g=!0;break;case 1:g=a.VBR==J.vbr_mtrh||a.VBR==J.vbr_mt?!1:!0;break;case 2:g=!1;break;case 3:p=8,b=-1.75,k=-.0125,A=-8.25,r=-2.25}d.ms_ener_ratio_old=.25;d.blocktype_old[0]=d.blocktype_old[1]=f.NORM_TYPE;for(h=0;4>h;++h){for(var v=0;v<f.CBANDS;++v)d.nb_1[h][v]=1E20,d.nb_2[h][v]=1E20,d.nb_s1[h][v]=d.nb_s2[h][v]=1;for(c=0;c<f.SBMAX_l;c++)d.en[h].l[c]=1E20,d.thm[h].l[c]=1E20;for(v=0;3>v;++v){for(c=0;c<f.SBMAX_s;c++)d.en[h].s[c][v]=1E20,d.thm[h].s[c][v]=1E20;d.nsPsy.lastAttacks[h]=
0}for(v=0;9>v;v++)d.nsPsy.last_en_subshort[h][v]=10}d.loudness_sq_save[0]=d.loudness_sq_save[1]=0;d.npart_l=e(d.numlines_l,d.bo_l,d.bm_l,u,y,d.mld_l,d.PSY.bo_l_weight,x,f.BLKSIZE,d.scalefac_band.l,f.BLKSIZE/1152,f.SBMAX_l);for(h=0;h<d.npart_l;h++)c=b,u[h]>=p&&(c=k*(u[h]-p)/(24-p)+b*(24-u[h])/(24-p)),w[h]=Math.pow(10,c/10),d.rnumlines_l[h]=0<d.numlines_l[h]?1/d.numlines_l[h]:0;d.s3_ll=l(d.s3ind,d.npart_l,u,y,w,g);for(h=v=0;h<d.npart_l;h++){k=sb.MAX_VALUE;for(c=0;c<d.numlines_l[h];c++,v++)b=x*v/(1E3*
f.BLKSIZE),b=this.ATHformula(1E3*b,a)-20,b=Math.pow(10,.1*b),b*=d.numlines_l[h],k>b&&(k=b);d.ATH.cb_l[h]=k;k=-20+20*u[h]/10;6<k&&(k=100);-15>k&&(k=-15);k-=8;d.minval_l[h]=Math.pow(10,k/10)*d.numlines_l[h]}d.npart_s=e(d.numlines_s,d.bo_s,d.bm_s,u,y,d.mld_s,d.PSY.bo_s_weight,x,f.BLKSIZE_s,d.scalefac_band.s,f.BLKSIZE_s/384,f.SBMAX_s);for(h=v=0;h<d.npart_s;h++){c=A;u[h]>=p&&(c=r*(u[h]-p)/(24-p)+A*(24-u[h])/(24-p));w[h]=Math.pow(10,c/10);k=sb.MAX_VALUE;for(c=0;c<d.numlines_s[h];c++,v++)b=x*v/(1E3*f.BLKSIZE_s),
b=this.ATHformula(1E3*b,a)-20,b=Math.pow(10,.1*b),b*=d.numlines_s[h],k>b&&(k=b);d.ATH.cb_s[h]=k;k=-7+7*u[h]/12;12<u[h]&&(k*=1+3.1*Math.log(1+k));12>u[h]&&(k*=1+2.3*Math.log(1-k));-15>k&&(k=-15);k-=8;d.minval_s[h]=Math.pow(10,k/10)*d.numlines_s[h]}d.s3_ss=l(d.s3ind_s,d.npart_s,u,y,w,g);m=Math.pow(10,.5625);n=Math.pow(10,1.5);z=Math.pow(10,1.5);t.init_fft(d);d.decay=Math.exp(-2.302585092994046/(.01*x/192));h=3.5;0!=(a.exp_nspsytune&2)&&(h=1);0<Math.abs(a.msfix)&&(h=a.msfix);a.msfix=h;for(g=0;g<d.npart_l;g++)d.s3ind[g][1]>
d.npart_l-1&&(d.s3ind[g][1]=d.npart_l-1);d.ATH.decay=Math.pow(10,576*d.mode_gr/x*-1.2);d.ATH.adjust=.01;d.ATH.adjustLimit=1;if(-1!=a.ATHtype){v=a.out_samplerate/f.BLKSIZE;for(h=b=g=0;h<f.BLKSIZE/2;++h)b+=v,d.ATH.eql_w[h]=1/Math.pow(10,this.ATHformula(b,a)/10),g+=d.ATH.eql_w[h];g=1/g;for(h=f.BLKSIZE/2;0<=--h;)d.ATH.eql_w[h]*=g}for(g=v=0;g<d.npart_s;++g)for(h=0;h<d.numlines_s[g];++h)++v;for(g=v=0;g<d.npart_l;++g)for(h=0;h<d.numlines_l[g];++h)++v;for(h=v=0;h<d.npart_l;h++)b=x*(v+d.numlines_l[h]/2)/(1*
f.BLKSIZE),d.mld_cb_l[h]=q(b),v+=d.numlines_l[h];for(;h<f.CBANDS;++h)d.mld_cb_l[h]=1;for(h=v=0;h<d.npart_s;h++)b=x*(v+d.numlines_s[h]/2)/(1*f.BLKSIZE_s),d.mld_cb_s[h]=q(b),v+=d.numlines_s[h];for(;h<f.CBANDS;++h)d.mld_cb_s[h]=1;return 0};this.ATHformula=function(a,b){switch(b.ATHtype){case 0:a=g(a,9);break;case 1:a=g(a,-1);break;case 2:a=g(a,0);break;case 3:a=g(a,1)+6;break;case 4:a=g(a,b.ATHcurve);break;default:a=g(a,0)}return a}}function W(){function x(){this.mask_adjust_short=this.mask_adjust=0;
this.bo_l_weight=G(f.SBMAX_l);this.bo_s_weight=G(f.SBMAX_s)}function k(){this.lowerlimit=0}function r(a,b){this.lowpass=b}function K(a){return 1<a?0:0>=a?1:Math.cos(Math.PI/2*a)}function C(a,b){switch(a){case 44100:return b.version=1,0;case 48E3:return b.version=1;case 32E3:return b.version=1,2;case 22050:return b.version=0;case 24E3:return b.version=0,1;case 16E3:return b.version=0,2;case 11025:return b.version=0;case 12E3:return b.version=0,1;case 8E3:return b.version=0,2;default:return b.version=
0,-1}}function u(a,b,c){16E3>c&&(b=2);c=v.bitrate_table[b][1];for(var d=2;14>=d;d++)0<v.bitrate_table[b][d]&&Math.abs(v.bitrate_table[b][d]-a)<Math.abs(c-a)&&(c=v.bitrate_table[b][d]);return c}function U(a,b,c){16E3>c&&(b=2);for(c=0;14>=c;c++)if(0<v.bitrate_table[b][c]&&v.bitrate_table[b][c]==a)return c;return-1}function d(a,b){var c=[new r(8,2E3),new r(16,3700),new r(24,3900),new r(32,5500),new r(40,7E3),new r(48,7500),new r(56,1E4),new r(64,11E3),new r(80,13500),new r(96,15100),new r(112,15600),
new r(128,17E3),new r(160,17500),new r(192,18600),new r(224,19400),new r(256,19700),new r(320,20500)];b=e.nearestBitrateFullIndex(b);a.lowerlimit=c[b].lowpass}function c(a){var b=f.BLKSIZE+a.framesize-f.FFTOFFSET;return b=Math.max(b,512+a.framesize-32)}function w(d,g,k,m,n,r,t){var h=d.internal_flags,p=0,A=[null,null],u=[null,null];if(4294479419!=h.Class_ID)return-3;if(0==m)return 0;var y=q.copy_buffer(h,n,r,t,0);if(0>y)return y;r+=y;p+=y;u[0]=g;u[1]=k;if(da.NEQ(d.scale,0)&&da.NEQ(d.scale,1))for(y=
0;y<m;++y)u[0][y]*=d.scale,2==h.channels_out&&(u[1][y]*=d.scale);if(da.NEQ(d.scale_left,0)&&da.NEQ(d.scale_left,1))for(y=0;y<m;++y)u[0][y]*=d.scale_left;if(da.NEQ(d.scale_right,0)&&da.NEQ(d.scale_right,1))for(y=0;y<m;++y)u[1][y]*=d.scale_right;if(2==d.num_channels&&1==h.channels_out)for(y=0;y<m;++y)u[0][y]=.5*(u[0][y]+u[1][y]),u[1][y]=0;g=c(d);A[0]=h.mfbuf[0];A[1]=h.mfbuf[1];for(k=0;0<m;){var v=[null,null];v[0]=u[0];v[1]=u[1];y=new a;var w=d;var x=A;var z=k,C=m,O=y,D=w.internal_flags;if(.9999>D.resample_ratio||
1.0001<D.resample_ratio)for(var E=0;E<D.channels_out;E++){var R=new b,K=O,F,J=x[E],S=D.mf_size,W=w.framesize,U=v[E],Z=z,ba=C,fa=R,ka=E,Y=w.internal_flags,ha=0,ra=w.out_samplerate/B(w.out_samplerate,w.in_samplerate);ra>ia.BPC&&(ra=ia.BPC);var pa=1E-4>Math.abs(Y.resample_ratio-Math.floor(.5+Y.resample_ratio))?1:0;var qa=1/Y.resample_ratio;1<qa&&(qa=1);var ma=31;0==ma%2&&--ma;ma+=pa;pa=ma+1;if(0==Y.fill_buffer_resample_init){Y.inbuf_old[0]=G(pa);Y.inbuf_old[1]=G(pa);for(F=0;F<=2*ra;++F)Y.blackfilt[F]=
G(pa);Y.itime[0]=0;for(ha=Y.itime[1]=0;ha<=2*ra;ha++){var xa=0;var T=(ha-ra)/(2*ra);for(F=0;F<=ma;F++){var Fa=Y.blackfilt[ha],L=F,za=F-T,Ua=Math.PI*qa,za=za/ma;0>za&&(za=0);1<za&&(za=1);var Ka=za-.5,za=.42-.5*Math.cos(2*za*Math.PI)+.08*Math.cos(4*za*Math.PI),xa=xa+(Fa[L]=1E-9>Math.abs(Ka)?Ua/Math.PI:za*Math.sin(ma*Ua*Ka)/(Math.PI*ma*Ka))}for(F=0;F<=ma;F++)Y.blackfilt[ha][F]/=xa}Y.fill_buffer_resample_init=1}xa=Y.inbuf_old[ka];for(qa=0;qa<W;qa++){F=qa*Y.resample_ratio;ha=0|Math.floor(F-Y.itime[ka]);
if(ma+ha-ma/2>=ba)break;T=F-Y.itime[ka]-(ha+ma%2*.5);T=0|Math.floor(2*T*ra+ra+.5);for(F=Fa=0;F<=ma;++F)L=F+ha-ma/2,Fa+=(0>L?xa[pa+L]:U[Z+L])*Y.blackfilt[T][F];J[S+qa]=Fa}fa.num_used=Math.min(ba,ma+ha-ma/2);Y.itime[ka]+=fa.num_used-qa*Y.resample_ratio;if(fa.num_used>=pa)for(F=0;F<pa;F++)xa[F]=U[Z+fa.num_used+F-pa];else{J=pa-fa.num_used;for(F=0;F<J;++F)xa[F]=xa[F+fa.num_used];for(ha=0;F<pa;++F,++ha)xa[F]=U[Z+ha]}K.n_out=qa;O.n_in=R.num_used}else for(O.n_out=Math.min(w.framesize,C),O.n_in=O.n_out,w=
0;w<O.n_out;++w)x[0][D.mf_size+w]=v[0][z+w],2==D.channels_out&&(x[1][D.mf_size+w]=v[1][z+w]);x=y.n_in;y=y.n_out;if(h.findReplayGain&&!h.decode_on_the_fly&&l.AnalyzeSamples(h.rgdata,A[0],h.mf_size,A[1],h.mf_size,y,h.channels_out)==ca.GAIN_ANALYSIS_ERROR)return-6;m-=x;k+=x;h.mf_size+=y;1>h.mf_samples_to_encode&&(h.mf_samples_to_encode=f.ENCDELAY+f.POSTDELAY);h.mf_samples_to_encode+=y;if(h.mf_size>=g){x=t-p;0==t&&(x=0);y=d;x=e.enc.lame_encode_mp3_frame(y,A[0],A[1],n,r,x);y.frameNum++;y=x;if(0>y)return y;
r+=y;p+=y;h.mf_size-=d.framesize;h.mf_samples_to_encode-=d.framesize;for(x=0;x<h.channels_out;x++)for(y=0;y<h.mf_size;y++)A[x][y]=A[x][y+d.framesize]}}return p}function a(){this.n_out=this.n_in=0}function b(){this.num_used=0}function B(a,b){return 0!=b?B(b,a%b):a}var e=this;W.V9=410;W.V8=420;W.V7=430;W.V6=440;W.V5=450;W.V4=460;W.V3=470;W.V2=480;W.V1=490;W.V0=500;W.R3MIX=1E3;W.STANDARD=1001;W.EXTREME=1002;W.INSANE=1003;W.STANDARD_FAST=1004;W.EXTREME_FAST=1005;W.MEDIUM=1006;W.MEDIUM_FAST=1007;W.LAME_MAXMP3BUFFER=
147456;var l,q,g,t,D,m=new $b,n,z,E;this.enc=new f;this.setModules=function(a,b,c,d,e,f,k,r,u){l=a;q=b;g=c;t=d;D=e;n=f;z=r;E=u;this.enc.setModules(q,m,t,n)};this.lame_init=function(){var a=new Ac;a.class_id=4294479419;var b=a.internal_flags=new ia;a.mode=ka.NOT_SET;a.original=1;a.in_samplerate=44100;a.num_channels=2;a.num_samples=-1;a.bWriteVbrTag=!0;a.quality=-1;a.short_blocks=null;b.subblock_gain=-1;a.lowpassfreq=0;a.highpassfreq=0;a.lowpasswidth=-1;a.highpasswidth=-1;a.VBR=J.vbr_off;a.VBR_q=4;
a.ATHcurve=-1;a.VBR_mean_bitrate_kbps=128;a.VBR_min_bitrate_kbps=0;a.VBR_max_bitrate_kbps=0;a.VBR_hard_min=0;b.VBR_min_bitrate=1;b.VBR_max_bitrate=13;a.quant_comp=-1;a.quant_comp_short=-1;a.msfix=-1;b.resample_ratio=1;b.OldValue[0]=180;b.OldValue[1]=180;b.CurrentStep[0]=4;b.CurrentStep[1]=4;b.masking_lower=1;b.nsPsy.attackthre=-1;b.nsPsy.attackthre_s=-1;a.scale=-1;a.athaa_type=-1;a.ATHtype=-1;a.athaa_loudapprox=-1;a.athaa_sensitivity=0;a.useTemporal=null;a.interChRatio=-1;b.mf_samples_to_encode=f.ENCDELAY+
f.POSTDELAY;a.encoder_padding=0;b.mf_size=f.ENCDELAY-f.MDCTDELAY;a.findReplayGain=!1;a.decode_on_the_fly=!1;b.decode_on_the_fly=!1;b.findReplayGain=!1;b.findPeakSample=!1;b.RadioGain=0;b.AudiophileGain=0;b.noclipGainChange=0;b.noclipScale=-1;a.preset=0;a.write_id3tag_automatic=!0;a.lame_allocated_gfp=1;return a};this.nearestBitrateFullIndex=function(a){var b=[8,16,24,32,40,48,56,64,80,96,112,128,160,192,224,256,320];var c=b[16];var d=16;var e=b[16];var h=16;for(var f=0;16>f;f++)if(Math.max(a,b[f+
1])!=a){c=b[f+1];d=f+1;e=b[f];h=f;break}return c-a>a-e?h:d};this.lame_init_params=function(a){var b=a.internal_flags;b.Class_ID=0;null==b.ATH&&(b.ATH=new zc);null==b.PSY&&(b.PSY=new x);null==b.rgdata&&(b.rgdata=new Cc);b.channels_in=a.num_channels;1==b.channels_in&&(a.mode=ka.MONO);b.channels_out=a.mode==ka.MONO?1:2;b.mode_ext=f.MPG_MD_MS_LR;a.mode==ka.MONO&&(a.force_ms=!1);a.VBR==J.vbr_off&&128!=a.VBR_mean_bitrate_kbps&&0==a.brate&&(a.brate=a.VBR_mean_bitrate_kbps);a.VBR!=J.vbr_off&&a.VBR!=J.vbr_mtrh&&
a.VBR!=J.vbr_mt&&(a.free_format=!1);a.VBR==J.vbr_off&&0==a.brate&&da.EQ(a.compression_ratio,0)&&(a.compression_ratio=11.025);a.VBR==J.vbr_off&&0<a.compression_ratio&&(0==a.out_samplerate&&(a.out_samplerate=map2MP3Frequency(int(.97*a.in_samplerate))),a.brate=0|16*a.out_samplerate*b.channels_out/(1E3*a.compression_ratio),b.samplerate_index=C(a.out_samplerate,a),a.free_format||(a.brate=u(a.brate,a.version,a.out_samplerate)));0!=a.out_samplerate&&(16E3>a.out_samplerate?(a.VBR_mean_bitrate_kbps=Math.max(a.VBR_mean_bitrate_kbps,
8),a.VBR_mean_bitrate_kbps=Math.min(a.VBR_mean_bitrate_kbps,64)):32E3>a.out_samplerate?(a.VBR_mean_bitrate_kbps=Math.max(a.VBR_mean_bitrate_kbps,8),a.VBR_mean_bitrate_kbps=Math.min(a.VBR_mean_bitrate_kbps,160)):(a.VBR_mean_bitrate_kbps=Math.max(a.VBR_mean_bitrate_kbps,32),a.VBR_mean_bitrate_kbps=Math.min(a.VBR_mean_bitrate_kbps,320)));if(0==a.lowpassfreq){switch(a.VBR){case J.vbr_off:var c=new k;d(c,a.brate);c=c.lowerlimit;break;case J.vbr_abr:c=new k;d(c,a.VBR_mean_bitrate_kbps);c=c.lowerlimit;break;
case J.vbr_rh:var e=[19500,19E3,18600,18E3,17500,16E3,15600,14900,12500,1E4,3950];if(0<=a.VBR_q&&9>=a.VBR_q){c=e[a.VBR_q];var h=e[a.VBR_q+1];e=a.VBR_q_frac;c=linear_int(c,h,e)}else c=19500;break;default:e=[19500,19E3,18500,18E3,17500,16500,15500,14500,12500,9500,3950],0<=a.VBR_q&&9>=a.VBR_q?(c=e[a.VBR_q],h=e[a.VBR_q+1],e=a.VBR_q_frac,c=linear_int(c,h,e)):c=19500}a.mode!=ka.MONO||a.VBR!=J.vbr_off&&a.VBR!=J.vbr_abr||(c*=1.5);a.lowpassfreq=c|0}0==a.out_samplerate&&(2*a.lowpassfreq>a.in_samplerate&&(a.lowpassfreq=
a.in_samplerate/2),c=a.lowpassfreq|0,e=a.in_samplerate,h=44100,48E3<=e?h=48E3:44100<=e?h=44100:32E3<=e?h=32E3:24E3<=e?h=24E3:22050<=e?h=22050:16E3<=e?h=16E3:12E3<=e?h=12E3:11025<=e?h=11025:8E3<=e&&(h=8E3),-1==c?c=h:(15960>=c&&(h=44100),15250>=c&&(h=32E3),11220>=c&&(h=24E3),9970>=c&&(h=22050),7230>=c&&(h=16E3),5420>=c&&(h=12E3),4510>=c&&(h=11025),3970>=c&&(h=8E3),c=e<h?44100<e?48E3:32E3<e?44100:24E3<e?32E3:22050<e?24E3:16E3<e?22050:12E3<e?16E3:11025<e?12E3:8E3<e?11025:8E3:h),a.out_samplerate=c);a.lowpassfreq=
Math.min(20500,a.lowpassfreq);a.lowpassfreq=Math.min(a.out_samplerate/2,a.lowpassfreq);a.VBR==J.vbr_off&&(a.compression_ratio=16*a.out_samplerate*b.channels_out/(1E3*a.brate));a.VBR==J.vbr_abr&&(a.compression_ratio=16*a.out_samplerate*b.channels_out/(1E3*a.VBR_mean_bitrate_kbps));a.bWriteVbrTag||(a.findReplayGain=!1,a.decode_on_the_fly=!1,b.findPeakSample=!1);b.findReplayGain=a.findReplayGain;b.decode_on_the_fly=a.decode_on_the_fly;b.decode_on_the_fly&&(b.findPeakSample=!0);if(b.findReplayGain&&l.InitGainAnalysis(b.rgdata,
a.out_samplerate)==ca.INIT_GAIN_ANALYSIS_ERROR)return a.internal_flags=null,-6;b.decode_on_the_fly&&!a.decode_only&&(null!=b.hip&&E.hip_decode_exit(b.hip),b.hip=E.hip_decode_init());b.mode_gr=24E3>=a.out_samplerate?1:2;a.framesize=576*b.mode_gr;a.encoder_delay=f.ENCDELAY;b.resample_ratio=a.in_samplerate/a.out_samplerate;switch(a.VBR){case J.vbr_mt:case J.vbr_rh:case J.vbr_mtrh:a.compression_ratio=[5.7,6.5,7.3,8.2,10,11.9,13,14,15,16.5][a.VBR_q];break;case J.vbr_abr:a.compression_ratio=16*a.out_samplerate*
b.channels_out/(1E3*a.VBR_mean_bitrate_kbps);break;default:a.compression_ratio=16*a.out_samplerate*b.channels_out/(1E3*a.brate)}a.mode==ka.NOT_SET&&(a.mode=ka.JOINT_STEREO);0<a.highpassfreq?(b.highpass1=2*a.highpassfreq,b.highpass2=0<=a.highpasswidth?2*(a.highpassfreq+a.highpasswidth):2*a.highpassfreq,b.highpass1/=a.out_samplerate,b.highpass2/=a.out_samplerate):(b.highpass1=0,b.highpass2=0);0<a.lowpassfreq?(b.lowpass2=2*a.lowpassfreq,0<=a.lowpasswidth?(b.lowpass1=2*(a.lowpassfreq-a.lowpasswidth),
0>b.lowpass1&&(b.lowpass1=0)):b.lowpass1=2*a.lowpassfreq,b.lowpass1/=a.out_samplerate,b.lowpass2/=a.out_samplerate):(b.lowpass1=0,b.lowpass2=0);c=a.internal_flags;var r=32,w=-1;if(0<c.lowpass1){var B=999;for(e=0;31>=e;e++)h=e/31,h>=c.lowpass2&&(r=Math.min(r,e)),c.lowpass1<h&&h<c.lowpass2&&(B=Math.min(B,e));c.lowpass1=999==B?(r-.75)/31:(B-.75)/31;c.lowpass2=r/31}0<c.highpass2&&c.highpass2<.75/31*.9&&(c.highpass1=0,c.highpass2=0,S.err.println("Warning: highpass filter disabled.  highpass frequency too small\n"));
if(0<c.highpass2){r=-1;for(e=0;31>=e;e++)h=e/31,h<=c.highpass1&&(w=Math.max(w,e)),c.highpass1<h&&h<c.highpass2&&(r=Math.max(r,e));c.highpass1=w/31;c.highpass2=-1==r?(w+.75)/31:(r+.75)/31}for(e=0;32>e;e++)h=e/31,w=c.highpass2>c.highpass1?K((c.highpass2-h)/(c.highpass2-c.highpass1+1E-20)):1,h=c.lowpass2>c.lowpass1?K((h-c.lowpass1)/(c.lowpass2-c.lowpass1+1E-20)):1,c.amp_filter[e]=w*h;b.samplerate_index=C(a.out_samplerate,a);if(0>b.samplerate_index)return a.internal_flags=null,-1;if(a.VBR==J.vbr_off)if(a.free_format)b.bitrate_index=
0;else{if(a.brate=u(a.brate,a.version,a.out_samplerate),b.bitrate_index=U(a.brate,a.version,a.out_samplerate),0>=b.bitrate_index)return a.internal_flags=null,-1}else b.bitrate_index=1;a.analysis&&(a.bWriteVbrTag=!1);null!=b.pinfo&&(a.bWriteVbrTag=!1);q.init_bit_stream_w(b);c=b.samplerate_index+3*a.version+6*(16E3>a.out_samplerate?1:0);for(e=0;e<f.SBMAX_l+1;e++)b.scalefac_band.l[e]=t.sfBandIndex[c].l[e];for(e=0;e<f.PSFB21+1;e++)h=(b.scalefac_band.l[22]-b.scalefac_band.l[21])/f.PSFB21,h=b.scalefac_band.l[21]+
e*h,b.scalefac_band.psfb21[e]=h;b.scalefac_band.psfb21[f.PSFB21]=576;for(e=0;e<f.SBMAX_s+1;e++)b.scalefac_band.s[e]=t.sfBandIndex[c].s[e];for(e=0;e<f.PSFB12+1;e++)h=(b.scalefac_band.s[13]-b.scalefac_band.s[12])/f.PSFB12,h=b.scalefac_band.s[12]+e*h,b.scalefac_band.psfb12[e]=h;b.scalefac_band.psfb12[f.PSFB12]=192;b.sideinfo_len=1==a.version?1==b.channels_out?21:36:1==b.channels_out?13:21;a.error_protection&&(b.sideinfo_len+=2);c=a.internal_flags;a.frameNum=0;a.write_id3tag_automatic&&z.id3tag_write_v2(a);
c.bitrate_stereoMode_Hist=Ua([16,5]);c.bitrate_blockType_Hist=Ua([16,6]);c.PeakSample=0;a.bWriteVbrTag&&n.InitVbrTag(a);b.Class_ID=4294479419;for(c=0;19>c;c++)b.nsPsy.pefirbuf[c]=700*b.mode_gr*b.channels_out;-1==a.ATHtype&&(a.ATHtype=4);switch(a.VBR){case J.vbr_mt:a.VBR=J.vbr_mtrh;case J.vbr_mtrh:null==a.useTemporal&&(a.useTemporal=!1);g.apply_preset(a,500-10*a.VBR_q,0);0>a.quality&&(a.quality=LAME_DEFAULT_QUALITY);5>a.quality&&(a.quality=0);5<a.quality&&(a.quality=5);b.PSY.mask_adjust=a.maskingadjust;
b.PSY.mask_adjust_short=a.maskingadjust_short;b.sfb21_extra=a.experimentalY?!1:44E3<a.out_samplerate;b.iteration_loop=new VBRNewIterationLoop(D);break;case J.vbr_rh:g.apply_preset(a,500-10*a.VBR_q,0);b.PSY.mask_adjust=a.maskingadjust;b.PSY.mask_adjust_short=a.maskingadjust_short;b.sfb21_extra=a.experimentalY?!1:44E3<a.out_samplerate;6<a.quality&&(a.quality=6);0>a.quality&&(a.quality=LAME_DEFAULT_QUALITY);b.iteration_loop=new VBROldIterationLoop(D);break;default:b.sfb21_extra=!1,0>a.quality&&(a.quality=
LAME_DEFAULT_QUALITY),c=a.VBR,c==J.vbr_off&&(a.VBR_mean_bitrate_kbps=a.brate),g.apply_preset(a,a.VBR_mean_bitrate_kbps,0),a.VBR=c,b.PSY.mask_adjust=a.maskingadjust,b.PSY.mask_adjust_short=a.maskingadjust_short,b.iteration_loop=c==J.vbr_off?new Bc(D):new ABRIterationLoop(D)}if(a.VBR!=J.vbr_off){b.VBR_min_bitrate=1;b.VBR_max_bitrate=14;16E3>a.out_samplerate&&(b.VBR_max_bitrate=8);if(0!=a.VBR_min_bitrate_kbps&&(a.VBR_min_bitrate_kbps=u(a.VBR_min_bitrate_kbps,a.version,a.out_samplerate),b.VBR_min_bitrate=
U(a.VBR_min_bitrate_kbps,a.version,a.out_samplerate),0>b.VBR_min_bitrate)||0!=a.VBR_max_bitrate_kbps&&(a.VBR_max_bitrate_kbps=u(a.VBR_max_bitrate_kbps,a.version,a.out_samplerate),b.VBR_max_bitrate=U(a.VBR_max_bitrate_kbps,a.version,a.out_samplerate),0>b.VBR_max_bitrate))return-1;a.VBR_min_bitrate_kbps=v.bitrate_table[a.version][b.VBR_min_bitrate];a.VBR_max_bitrate_kbps=v.bitrate_table[a.version][b.VBR_max_bitrate];a.VBR_mean_bitrate_kbps=Math.min(v.bitrate_table[a.version][b.VBR_max_bitrate],a.VBR_mean_bitrate_kbps);
a.VBR_mean_bitrate_kbps=Math.max(v.bitrate_table[a.version][b.VBR_min_bitrate],a.VBR_mean_bitrate_kbps)}a.tune&&(b.PSY.mask_adjust+=a.tune_value_a,b.PSY.mask_adjust_short+=a.tune_value_a);c=a.internal_flags;switch(a.quality){default:case 9:c.psymodel=0;c.noise_shaping=0;c.noise_shaping_amp=0;c.noise_shaping_stop=0;c.use_best_huffman=0;c.full_outer_loop=0;break;case 8:a.quality=7;case 7:c.psymodel=1;c.noise_shaping=0;c.noise_shaping_amp=0;c.noise_shaping_stop=0;c.use_best_huffman=0;c.full_outer_loop=
0;break;case 6:c.psymodel=1;0==c.noise_shaping&&(c.noise_shaping=1);c.noise_shaping_amp=0;c.noise_shaping_stop=0;-1==c.subblock_gain&&(c.subblock_gain=1);c.use_best_huffman=0;c.full_outer_loop=0;break;case 5:c.psymodel=1;0==c.noise_shaping&&(c.noise_shaping=1);c.noise_shaping_amp=0;c.noise_shaping_stop=0;-1==c.subblock_gain&&(c.subblock_gain=1);c.use_best_huffman=0;c.full_outer_loop=0;break;case 4:c.psymodel=1;0==c.noise_shaping&&(c.noise_shaping=1);c.noise_shaping_amp=0;c.noise_shaping_stop=0;-1==
c.subblock_gain&&(c.subblock_gain=1);c.use_best_huffman=1;c.full_outer_loop=0;break;case 3:c.psymodel=1;0==c.noise_shaping&&(c.noise_shaping=1);c.noise_shaping_amp=1;c.noise_shaping_stop=1;-1==c.subblock_gain&&(c.subblock_gain=1);c.use_best_huffman=1;c.full_outer_loop=0;break;case 2:c.psymodel=1;0==c.noise_shaping&&(c.noise_shaping=1);0==c.substep_shaping&&(c.substep_shaping=2);c.noise_shaping_amp=1;c.noise_shaping_stop=1;-1==c.subblock_gain&&(c.subblock_gain=1);c.use_best_huffman=1;c.full_outer_loop=
0;break;case 1:c.psymodel=1;0==c.noise_shaping&&(c.noise_shaping=1);0==c.substep_shaping&&(c.substep_shaping=2);c.noise_shaping_amp=2;c.noise_shaping_stop=1;-1==c.subblock_gain&&(c.subblock_gain=1);c.use_best_huffman=1;c.full_outer_loop=0;break;case 0:c.psymodel=1,0==c.noise_shaping&&(c.noise_shaping=1),0==c.substep_shaping&&(c.substep_shaping=2),c.noise_shaping_amp=2,c.noise_shaping_stop=1,-1==c.subblock_gain&&(c.subblock_gain=1),c.use_best_huffman=1,c.full_outer_loop=0}b.ATH.useAdjust=0>a.athaa_type?
3:a.athaa_type;b.ATH.aaSensitivityP=Math.pow(10,a.athaa_sensitivity/-10);null==a.short_blocks&&(a.short_blocks=pa.short_block_allowed);a.short_blocks!=pa.short_block_allowed||a.mode!=ka.JOINT_STEREO&&a.mode!=ka.STEREO||(a.short_blocks=pa.short_block_coupled);0>a.quant_comp&&(a.quant_comp=1);0>a.quant_comp_short&&(a.quant_comp_short=0);0>a.msfix&&(a.msfix=0);a.exp_nspsytune|=1;0>a.internal_flags.nsPsy.attackthre&&(a.internal_flags.nsPsy.attackthre=$b.NSATTACKTHRE);0>a.internal_flags.nsPsy.attackthre_s&&
(a.internal_flags.nsPsy.attackthre_s=$b.NSATTACKTHRE_S);0>a.scale&&(a.scale=1);0>a.ATHtype&&(a.ATHtype=4);0>a.ATHcurve&&(a.ATHcurve=4);0>a.athaa_loudapprox&&(a.athaa_loudapprox=2);0>a.interChRatio&&(a.interChRatio=0);null==a.useTemporal&&(a.useTemporal=!0);b.slot_lag=b.frac_SpF=0;a.VBR==J.vbr_off&&(b.slot_lag=b.frac_SpF=72E3*(a.version+1)*a.brate%a.out_samplerate|0);t.iteration_init(a);m.psymodel_init(a);return 0};this.lame_encode_flush=function(a,b,d,e){var g=a.internal_flags,h=fc([2,1152]),k=0,
l=g.mf_samples_to_encode-f.POSTDELAY,m=c(a);if(1>g.mf_samples_to_encode)return 0;var n=0;a.in_samplerate!=a.out_samplerate&&(l+=16*a.out_samplerate/a.in_samplerate);var p=a.framesize-l%a.framesize;576>p&&(p+=a.framesize);a.encoder_padding=p;for(p=(l+p)/a.framesize;0<p&&0<=k;){var r=m-g.mf_size,l=a.frameNum,r=r*a.in_samplerate,r=r/a.out_samplerate;1152<r&&(r=1152);1>r&&(r=1);k=e-n;0==e&&(k=0);k=this.lame_encode_buffer(a,h[0],h[1],r,b,d,k);d+=k;n+=k;p-=l!=a.frameNum?1:0}g.mf_samples_to_encode=0;if(0>
k)return k;k=e-n;0==e&&(k=0);q.flush_bitstream(a);k=q.copy_buffer(g,b,d,k,1);if(0>k)return k;d+=k;n+=k;k=e-n;0==e&&(k=0);if(a.write_id3tag_automatic){z.id3tag_write_v1(a);k=q.copy_buffer(g,b,d,k,0);if(0>k)return k;n+=k}return n};this.lame_encode_buffer=function(a,b,c,d,e,f,g){var h=a.internal_flags,k=[null,null];if(4294479419!=h.Class_ID)return-3;if(0==d)return 0;if(null==h.in_buffer_0||h.in_buffer_nsamples<d)h.in_buffer_0=G(d),h.in_buffer_1=G(d),h.in_buffer_nsamples=d;k[0]=h.in_buffer_0;k[1]=h.in_buffer_1;
for(var l=0;l<d;l++)k[0][l]=b[l],1<h.channels_in&&(k[1][l]=c[l]);return w(a,k[0],k[1],d,e,f,g)}}function Kc(){this.setModules=function(f,k){}}function Lc(){this.setModules=function(f,k,r){}}function Mc(){}function Nc(){this.setModules=function(f,k){}}function xa(){this.sampleRate=this.channels=this.dataLen=this.dataOffset=0}function zb(f){return f.charCodeAt(0)<<24|f.charCodeAt(1)<<16|f.charCodeAt(2)<<8|f.charCodeAt(3)}var qa={fill:function(f,k,r,v){if(2==arguments.length)for(var x=0;x<f.length;x++)f[x]=
arguments[1];else for(x=k;x<r;x++)f[x]=v}},S={arraycopy:function(f,k,r,v,C){for(C=k+C;k<C;)r[v++]=f[k++]}},Z={SQRT2:1.4142135623730951,FAST_LOG10:function(f){return Math.log10(f)},FAST_LOG10_X:function(f,k){return Math.log10(f)*k}};pa.short_block_allowed=new pa(0);pa.short_block_coupled=new pa(1);pa.short_block_dispensed=new pa(2);pa.short_block_forced=new pa(3);var sb={MAX_VALUE:3.4028235E38};J.vbr_off=new J(0);J.vbr_mt=new J(1);J.vbr_rh=new J(2);J.vbr_abr=new J(3);J.vbr_mtrh=new J(4);J.vbr_default=
J.vbr_mtrh;ka.STEREO=new ka(0);ka.JOINT_STEREO=new ka(1);ka.DUAL_CHANNEL=new ka(2);ka.MONO=new ka(3);ka.NOT_SET=new ka(4);ca.STEPS_per_dB=100;ca.MAX_dB=120;ca.GAIN_NOT_ENOUGH_SAMPLES=-24601;ca.GAIN_ANALYSIS_ERROR=0;ca.GAIN_ANALYSIS_OK=1;ca.INIT_GAIN_ANALYSIS_ERROR=0;ca.INIT_GAIN_ANALYSIS_OK=1;ca.YULE_ORDER=10;ca.MAX_ORDER=ca.YULE_ORDER;ca.MAX_SAMP_FREQ=48E3;ca.RMS_WINDOW_TIME_NUMERATOR=1;ca.RMS_WINDOW_TIME_DENOMINATOR=20;ca.MAX_SAMPLES_PER_WINDOW=ca.MAX_SAMP_FREQ*ca.RMS_WINDOW_TIME_NUMERATOR/ca.RMS_WINDOW_TIME_DENOMINATOR+
1;gb.NUMTOCENTRIES=100;gb.MAXFRAMESIZE=2880;da.EQ=function(f,k){return Math.abs(f)>Math.abs(k)?Math.abs(f-k)<=1E-6*Math.abs(f):Math.abs(f-k)<=1E-6*Math.abs(k)};da.NEQ=function(f,k){return!da.EQ(f,k)};var v={t1HB:[1,1,1,0],t2HB:[1,2,1,3,1,1,3,2,0],t3HB:[3,2,1,1,1,1,3,2,0],t5HB:[1,2,6,5,3,1,4,4,7,5,7,1,6,1,1,0],t6HB:[7,3,5,1,6,2,3,2,5,4,4,1,3,3,2,0],t7HB:[1,2,10,19,16,10,3,3,7,10,5,3,11,4,13,17,8,4,12,11,18,15,11,2,7,6,9,14,3,1,6,4,5,3,2,0],t8HB:[3,4,6,18,12,5,5,1,2,16,9,3,7,3,5,14,7,3,19,17,15,13,
10,4,13,5,8,11,5,1,12,4,4,1,1,0],t9HB:[7,5,9,14,15,7,6,4,5,5,6,7,7,6,8,8,8,5,15,6,9,10,5,1,11,7,9,6,4,1,14,4,6,2,6,0],t10HB:[1,2,10,23,35,30,12,17,3,3,8,12,18,21,12,7,11,9,15,21,32,40,19,6,14,13,22,34,46,23,18,7,20,19,33,47,27,22,9,3,31,22,41,26,21,20,5,3,14,13,10,11,16,6,5,1,9,8,7,8,4,4,2,0],t11HB:[3,4,10,24,34,33,21,15,5,3,4,10,32,17,11,10,11,7,13,18,30,31,20,5,25,11,19,59,27,18,12,5,35,33,31,58,30,16,7,5,28,26,32,19,17,15,8,14,14,12,9,13,14,9,4,1,11,4,6,6,6,3,2,0],t12HB:[9,6,16,33,41,39,38,26,
7,5,6,9,23,16,26,11,17,7,11,14,21,30,10,7,17,10,15,12,18,28,14,5,32,13,22,19,18,16,9,5,40,17,31,29,17,13,4,2,27,12,11,15,10,7,4,1,27,12,8,12,6,3,1,0],t13HB:[1,5,14,21,34,51,46,71,42,52,68,52,67,44,43,19,3,4,12,19,31,26,44,33,31,24,32,24,31,35,22,14,15,13,23,36,59,49,77,65,29,40,30,40,27,33,42,16,22,20,37,61,56,79,73,64,43,76,56,37,26,31,25,14,35,16,60,57,97,75,114,91,54,73,55,41,48,53,23,24,58,27,50,96,76,70,93,84,77,58,79,29,74,49,41,17,47,45,78,74,115,94,90,79,69,83,71,50,59,38,36,15,72,34,56,95,
92,85,91,90,86,73,77,65,51,44,43,42,43,20,30,44,55,78,72,87,78,61,46,54,37,30,20,16,53,25,41,37,44,59,54,81,66,76,57,54,37,18,39,11,35,33,31,57,42,82,72,80,47,58,55,21,22,26,38,22,53,25,23,38,70,60,51,36,55,26,34,23,27,14,9,7,34,32,28,39,49,75,30,52,48,40,52,28,18,17,9,5,45,21,34,64,56,50,49,45,31,19,12,15,10,7,6,3,48,23,20,39,36,35,53,21,16,23,13,10,6,1,4,2,16,15,17,27,25,20,29,11,17,12,16,8,1,1,0,1],t15HB:[7,12,18,53,47,76,124,108,89,123,108,119,107,81,122,63,13,5,16,27,46,36,61,51,42,70,52,83,
65,41,59,36,19,17,15,24,41,34,59,48,40,64,50,78,62,80,56,33,29,28,25,43,39,63,55,93,76,59,93,72,54,75,50,29,52,22,42,40,67,57,95,79,72,57,89,69,49,66,46,27,77,37,35,66,58,52,91,74,62,48,79,63,90,62,40,38,125,32,60,56,50,92,78,65,55,87,71,51,73,51,70,30,109,53,49,94,88,75,66,122,91,73,56,42,64,44,21,25,90,43,41,77,73,63,56,92,77,66,47,67,48,53,36,20,71,34,67,60,58,49,88,76,67,106,71,54,38,39,23,15,109,53,51,47,90,82,58,57,48,72,57,41,23,27,62,9,86,42,40,37,70,64,52,43,70,55,42,25,29,18,11,11,118,68,
30,55,50,46,74,65,49,39,24,16,22,13,14,7,91,44,39,38,34,63,52,45,31,52,28,19,14,8,9,3,123,60,58,53,47,43,32,22,37,24,17,12,15,10,2,1,71,37,34,30,28,20,17,26,21,16,10,6,8,6,2,0],t16HB:[1,5,14,44,74,63,110,93,172,149,138,242,225,195,376,17,3,4,12,20,35,62,53,47,83,75,68,119,201,107,207,9,15,13,23,38,67,58,103,90,161,72,127,117,110,209,206,16,45,21,39,69,64,114,99,87,158,140,252,212,199,387,365,26,75,36,68,65,115,101,179,164,155,264,246,226,395,382,362,9,66,30,59,56,102,185,173,265,142,253,232,400,388,
378,445,16,111,54,52,100,184,178,160,133,257,244,228,217,385,366,715,10,98,48,91,88,165,157,148,261,248,407,397,372,380,889,884,8,85,84,81,159,156,143,260,249,427,401,392,383,727,713,708,7,154,76,73,141,131,256,245,426,406,394,384,735,359,710,352,11,139,129,67,125,247,233,229,219,393,743,737,720,885,882,439,4,243,120,118,115,227,223,396,746,742,736,721,712,706,223,436,6,202,224,222,218,216,389,386,381,364,888,443,707,440,437,1728,4,747,211,210,208,370,379,734,723,714,1735,883,877,876,3459,865,2,377,
369,102,187,726,722,358,711,709,866,1734,871,3458,870,434,0,12,10,7,11,10,17,11,9,13,12,10,7,5,3,1,3],t24HB:[15,13,46,80,146,262,248,434,426,669,653,649,621,517,1032,88,14,12,21,38,71,130,122,216,209,198,327,345,319,297,279,42,47,22,41,74,68,128,120,221,207,194,182,340,315,295,541,18,81,39,75,70,134,125,116,220,204,190,178,325,311,293,271,16,147,72,69,135,127,118,112,210,200,188,352,323,306,285,540,14,263,66,129,126,119,114,214,202,192,180,341,317,301,281,262,12,249,123,121,117,113,215,206,195,185,
347,330,308,291,272,520,10,435,115,111,109,211,203,196,187,353,332,313,298,283,531,381,17,427,212,208,205,201,193,186,177,169,320,303,286,268,514,377,16,335,199,197,191,189,181,174,333,321,305,289,275,521,379,371,11,668,184,183,179,175,344,331,314,304,290,277,530,383,373,366,10,652,346,171,168,164,318,309,299,287,276,263,513,375,368,362,6,648,322,316,312,307,302,292,284,269,261,512,376,370,364,359,4,620,300,296,294,288,282,273,266,515,380,374,369,365,361,357,2,1033,280,278,274,267,264,259,382,378,
372,367,363,360,358,356,0,43,20,19,17,15,13,11,9,7,6,4,7,5,3,1,3],t32HB:[1,10,8,20,12,20,16,32,14,12,24,0,28,16,24,16],t33HB:[15,28,26,48,22,40,36,64,14,24,20,32,12,16,8,0],t1l:[1,4,3,5],t2l:[1,4,7,4,5,7,6,7,8],t3l:[2,3,7,4,4,7,6,7,8],t5l:[1,4,7,8,4,5,8,9,7,8,9,10,8,8,9,10],t6l:[3,4,6,8,4,4,6,7,5,6,7,8,7,7,8,9],t7l:[1,4,7,9,9,10,4,6,8,9,9,10,7,7,9,10,10,11,8,9,10,11,11,11,8,9,10,11,11,12,9,10,11,12,12,12],t8l:[2,4,7,9,9,10,4,4,6,10,10,10,7,6,8,10,10,11,9,10,10,11,11,12,9,9,10,11,12,12,10,10,11,11,
13,13],t9l:[3,4,6,7,9,10,4,5,6,7,8,10,5,6,7,8,9,10,7,7,8,9,9,10,8,8,9,9,10,11,9,9,10,10,11,11],t10l:[1,4,7,9,10,10,10,11,4,6,8,9,10,11,10,10,7,8,9,10,11,12,11,11,8,9,10,11,12,12,11,12,9,10,11,12,12,12,12,12,10,11,12,12,13,13,12,13,9,10,11,12,12,12,13,13,10,10,11,12,12,13,13,13],t11l:[2,4,6,8,9,10,9,10,4,5,6,8,10,10,9,10,6,7,8,9,10,11,10,10,8,8,9,11,10,12,10,11,9,10,10,11,11,12,11,12,9,10,11,12,12,13,12,13,9,9,9,10,11,12,12,12,9,9,10,11,12,12,12,12],t12l:[4,4,6,8,9,10,10,10,4,5,6,7,9,9,10,10,6,6,7,
8,9,10,9,10,7,7,8,8,9,10,10,10,8,8,9,9,10,10,10,11,9,9,10,10,10,11,10,11,9,9,9,10,10,11,11,12,10,10,10,11,11,11,11,12],t13l:[1,5,7,8,9,10,10,11,10,11,12,12,13,13,14,14,4,6,8,9,10,10,11,11,11,11,12,12,13,14,14,14,7,8,9,10,11,11,12,12,11,12,12,13,13,14,15,15,8,9,10,11,11,12,12,12,12,13,13,13,13,14,15,15,9,9,11,11,12,12,13,13,12,13,13,14,14,15,15,16,10,10,11,12,12,12,13,13,13,13,14,13,15,15,16,16,10,11,12,12,13,13,13,13,13,14,14,14,15,15,16,16,11,11,12,13,13,13,14,14,14,14,15,15,15,16,18,18,10,10,11,
12,12,13,13,14,14,14,14,15,15,16,17,17,11,11,12,12,13,13,13,15,14,15,15,16,16,16,18,17,11,12,12,13,13,14,14,15,14,15,16,15,16,17,18,19,12,12,12,13,14,14,14,14,15,15,15,16,17,17,17,18,12,13,13,14,14,15,14,15,16,16,17,17,17,18,18,18,13,13,14,15,15,15,16,16,16,16,16,17,18,17,18,18,14,14,14,15,15,15,17,16,16,19,17,17,17,19,18,18,13,14,15,16,16,16,17,16,17,17,18,18,21,20,21,18],t15l:[3,5,6,8,8,9,10,10,10,11,11,12,12,12,13,14,5,5,7,8,9,9,10,10,10,11,11,12,12,12,13,13,6,7,7,8,9,9,10,10,10,11,11,12,12,13,
13,13,7,8,8,9,9,10,10,11,11,11,12,12,12,13,13,13,8,8,9,9,10,10,11,11,11,11,12,12,12,13,13,13,9,9,9,10,10,10,11,11,11,11,12,12,13,13,13,14,10,9,10,10,10,11,11,11,11,12,12,12,13,13,14,14,10,10,10,11,11,11,11,12,12,12,12,12,13,13,13,14,10,10,10,11,11,11,11,12,12,12,12,13,13,14,14,14,10,10,11,11,11,11,12,12,12,13,13,13,13,14,14,14,11,11,11,11,12,12,12,12,12,13,13,13,13,14,15,14,11,11,11,11,12,12,12,12,13,13,13,13,14,14,14,15,12,12,11,12,12,12,13,13,13,13,13,13,14,14,15,15,12,12,12,12,12,13,13,13,13,14,
14,14,14,14,15,15,13,13,13,13,13,13,13,13,14,14,14,14,15,15,14,15,13,13,13,13,13,13,13,14,14,14,14,14,15,15,15,15],t16_5l:[1,5,7,9,10,10,11,11,12,12,12,13,13,13,14,11,4,6,8,9,10,11,11,11,12,12,12,13,14,13,14,11,7,8,9,10,11,11,12,12,13,12,13,13,13,14,14,12,9,9,10,11,11,12,12,12,13,13,14,14,14,15,15,13,10,10,11,11,12,12,13,13,13,14,14,14,15,15,15,12,10,10,11,11,12,13,13,14,13,14,14,15,15,15,16,13,11,11,11,12,13,13,13,13,14,14,14,14,15,15,16,13,11,11,12,12,13,13,13,14,14,15,15,15,15,17,17,13,11,12,12,
13,13,13,14,14,15,15,15,15,16,16,16,13,12,12,12,13,13,14,14,15,15,15,15,16,15,16,15,14,12,13,12,13,14,14,14,14,15,16,16,16,17,17,16,13,13,13,13,13,14,14,15,16,16,16,16,16,16,15,16,14,13,14,14,14,14,15,15,15,15,17,16,16,16,16,18,14,15,14,14,14,15,15,16,16,16,18,17,17,17,19,17,14,14,15,13,14,16,16,15,16,16,17,18,17,19,17,16,14,11,11,11,12,12,13,13,13,14,14,14,14,14,14,14,12],t16l:[1,5,7,9,10,10,11,11,12,12,12,13,13,13,14,10,4,6,8,9,10,11,11,11,12,12,12,13,14,13,14,10,7,8,9,10,11,11,12,12,13,12,13,13,
13,14,14,11,9,9,10,11,11,12,12,12,13,13,14,14,14,15,15,12,10,10,11,11,12,12,13,13,13,14,14,14,15,15,15,11,10,10,11,11,12,13,13,14,13,14,14,15,15,15,16,12,11,11,11,12,13,13,13,13,14,14,14,14,15,15,16,12,11,11,12,12,13,13,13,14,14,15,15,15,15,17,17,12,11,12,12,13,13,13,14,14,15,15,15,15,16,16,16,12,12,12,12,13,13,14,14,15,15,15,15,16,15,16,15,13,12,13,12,13,14,14,14,14,15,16,16,16,17,17,16,12,13,13,13,13,14,14,15,16,16,16,16,16,16,15,16,13,13,14,14,14,14,15,15,15,15,17,16,16,16,16,18,13,15,14,14,14,
15,15,16,16,16,18,17,17,17,19,17,13,14,15,13,14,16,16,15,16,16,17,18,17,19,17,16,13,10,10,10,11,11,12,12,12,13,13,13,13,13,13,13,10],t24l:[4,5,7,8,9,10,10,11,11,12,12,12,12,12,13,10,5,6,7,8,9,10,10,11,11,11,12,12,12,12,12,10,7,7,8,9,9,10,10,11,11,11,11,12,12,12,13,9,8,8,9,9,10,10,10,11,11,11,11,12,12,12,12,9,9,9,9,10,10,10,10,11,11,11,12,12,12,12,13,9,10,9,10,10,10,10,11,11,11,11,12,12,12,12,12,9,10,10,10,10,10,11,11,11,11,12,12,12,12,12,13,9,11,10,10,10,11,11,11,11,12,12,12,12,12,13,13,10,11,11,
11,11,11,11,11,11,11,12,12,12,12,13,13,10,11,11,11,11,11,11,11,12,12,12,12,12,13,13,13,10,12,11,11,11,11,12,12,12,12,12,12,13,13,13,13,10,12,12,11,11,11,12,12,12,12,12,12,13,13,13,13,10,12,12,12,12,12,12,12,12,12,12,13,13,13,13,13,10,12,12,12,12,12,12,12,12,13,13,13,13,13,13,13,10,13,12,12,12,12,12,12,13,13,13,13,13,13,13,13,10,9,9,9,9,9,9,9,9,9,9,9,10,10,10,10,6],t32l:[1,5,5,7,5,8,7,9,5,7,7,9,7,9,9,10],t33l:[4,5,5,6,5,6,6,7,5,6,6,7,6,7,7,8]};v.ht=[new ba(0,0,null,null),new ba(2,0,v.t1HB,v.t1l),new ba(3,
0,v.t2HB,v.t2l),new ba(3,0,v.t3HB,v.t3l),new ba(0,0,null,null),new ba(4,0,v.t5HB,v.t5l),new ba(4,0,v.t6HB,v.t6l),new ba(6,0,v.t7HB,v.t7l),new ba(6,0,v.t8HB,v.t8l),new ba(6,0,v.t9HB,v.t9l),new ba(8,0,v.t10HB,v.t10l),new ba(8,0,v.t11HB,v.t11l),new ba(8,0,v.t12HB,v.t12l),new ba(16,0,v.t13HB,v.t13l),new ba(0,0,null,v.t16_5l),new ba(16,0,v.t15HB,v.t15l),new ba(1,1,v.t16HB,v.t16l),new ba(2,3,v.t16HB,v.t16l),new ba(3,7,v.t16HB,v.t16l),new ba(4,15,v.t16HB,v.t16l),new ba(6,63,v.t16HB,v.t16l),new ba(8,255,
v.t16HB,v.t16l),new ba(10,1023,v.t16HB,v.t16l),new ba(13,8191,v.t16HB,v.t16l),new ba(4,15,v.t24HB,v.t24l),new ba(5,31,v.t24HB,v.t24l),new ba(6,63,v.t24HB,v.t24l),new ba(7,127,v.t24HB,v.t24l),new ba(8,255,v.t24HB,v.t24l),new ba(9,511,v.t24HB,v.t24l),new ba(11,2047,v.t24HB,v.t24l),new ba(13,8191,v.t24HB,v.t24l),new ba(0,0,v.t32HB,v.t32l),new ba(0,0,v.t33HB,v.t33l)];v.largetbl=[65540,327685,458759,589832,655369,655370,720906,720907,786443,786444,786444,851980,851980,851980,917517,655370,262149,393222,
524295,589832,655369,720906,720906,720907,786443,786443,786444,851980,917516,851980,917516,655370,458759,524295,589832,655369,720905,720906,786442,786443,851979,786443,851979,851980,851980,917516,917517,720905,589832,589832,655369,720905,720906,786442,786442,786443,851979,851979,917515,917516,917516,983052,983052,786441,655369,655369,720905,720906,786442,786442,851978,851979,851979,917515,917516,917516,983052,983052,983053,720905,655370,655369,720906,720906,786442,851978,851979,917515,851979,917515,
917516,983052,983052,983052,1048588,786441,720906,720906,720906,786442,851978,851979,851979,851979,917515,917516,917516,917516,983052,983052,1048589,786441,720907,720906,786442,786442,851979,851979,851979,917515,917516,983052,983052,983052,983052,1114125,1114125,786442,720907,786443,786443,851979,851979,851979,917515,917515,983051,983052,983052,983052,1048588,1048589,1048589,786442,786443,786443,786443,851979,851979,917515,917515,983052,983052,983052,983052,1048588,983053,1048589,983053,851978,786444,
851979,786443,851979,917515,917516,917516,917516,983052,1048588,1048588,1048589,1114125,1114125,1048589,786442,851980,851980,851979,851979,917515,917516,983052,1048588,1048588,1048588,1048588,1048589,1048589,983053,1048589,851978,851980,917516,917516,917516,917516,983052,983052,983052,983052,1114124,1048589,1048589,1048589,1048589,1179661,851978,983052,917516,917516,917516,983052,983052,1048588,1048588,1048589,1179661,1114125,1114125,1114125,1245197,1114125,851978,917517,983052,851980,917516,1048588,
1048588,983052,1048589,1048589,1114125,1179661,1114125,1245197,1114125,1048589,851978,655369,655369,655369,720905,720905,786441,786441,786441,851977,851977,851977,851978,851978,851978,851978,655366];v.table23=[65538,262147,458759,262148,327684,458759,393222,458759,524296];v.table56=[65539,262148,458758,524296,262148,327684,524294,589831,458757,524294,589831,655368,524295,524295,589832,655369];v.bitrate_table=[[0,8,16,24,32,40,48,56,64,80,96,112,128,144,160,-1],[0,32,40,48,56,64,80,96,112,128,160,
192,224,256,320,-1],[0,8,16,24,32,40,48,56,64,-1,-1,-1,-1,-1,-1,-1]];v.samplerate_table=[[22050,24E3,16E3,-1],[44100,48E3,32E3,-1],[11025,12E3,8E3,-1]];v.scfsi_band=[0,6,11,16,21];Y.Q_MAX=257;Y.Q_MAX2=116;Y.LARGE_BITS=1E5;Y.IXMAX_VAL=8206;var ra={};ra.SFBMAX=3*f.SBMAX_s;f.ENCDELAY=576;f.POSTDELAY=1152;f.MDCTDELAY=48;f.FFTOFFSET=224+f.MDCTDELAY;f.DECDELAY=528;f.SBLIMIT=32;f.CBANDS=64;f.SBPSY_l=21;f.SBPSY_s=12;f.SBMAX_l=22;f.SBMAX_s=13;f.PSFB21=6;f.PSFB12=6;f.BLKSIZE=1024;f.HBLKSIZE=f.BLKSIZE/2+1;f.BLKSIZE_s=
256;f.HBLKSIZE_s=f.BLKSIZE_s/2+1;f.NORM_TYPE=0;f.START_TYPE=1;f.SHORT_TYPE=2;f.STOP_TYPE=3;f.MPG_MD_LR_LR=0;f.MPG_MD_LR_I=1;f.MPG_MD_MS_LR=2;f.MPG_MD_MS_I=3;f.fircoef=[-.1039435,-.1892065,-.0432472*5,-.155915,3.898045E-17,.0467745*5,.50455,.756825,.187098*5];ia.MFSIZE=3456+f.ENCDELAY-f.MDCTDELAY;ia.MAX_HEADER_BUF=256;ia.MAX_BITS_PER_CHANNEL=4095;ia.MAX_BITS_PER_GRANULE=7680;ia.BPC=320;xa.RIFF=zb("RIFF");xa.WAVE=zb("WAVE");xa.fmt_=zb("fmt ");xa.data=zb("data");xa.readHeader=function(f){var k=new xa,
r=f.getUint32(0,!1);if(xa.RIFF==r&&(f.getUint32(4,!0),xa.WAVE==f.getUint32(8,!1)&&xa.fmt_==f.getUint32(12,!1))){var v=f.getUint32(16,!0),x=20;switch(v){case 16:case 18:k.channels=f.getUint16(x+2,!0);k.sampleRate=f.getUint32(x+4,!0);break;default:throw"extended fmt chunk not implemented";}for(var x=x+v,v=xa.data,u=0;v!=r;){r=f.getUint32(x,!1);u=f.getUint32(x+4,!0);if(v==r)break;x+=u+8}k.dataLen=u;k.dataOffset=x+8;return k}};ra.SFBMAX=3*f.SBMAX_s;lamejs.Mp3Encoder=function(f,k,r){3!=arguments.length&&
(console.error("WARN: Mp3Encoder(channels, samplerate, kbps) not specified"),f=1,k=44100,r=128);var v=new W,x=new Kc,u=new ca,G=new da,d=new wc,c=new Y,w=new Ec,a=new gb,b=new ic,B=new Nc,e=new xc,l=new fb,q=new Lc,g=new Mc;v.setModules(u,G,d,c,w,a,b,B,g);G.setModules(u,g,b,a);B.setModules(G,b);d.setModules(v);w.setModules(G,e,c,l);c.setModules(l,e,v.enc.psy);e.setModules(G);l.setModules(c);a.setModules(v,G,b);x.setModules(q,g);q.setModules(b,B,d);var t=v.lame_init();t.num_channels=f;t.in_samplerate=
k;t.brate=r;t.mode=ka.STEREO;t.quality=3;t.bWriteVbrTag=!1;t.disable_reservoir=!0;t.write_id3tag_automatic=!1;v.lame_init_params(t);var D=1152,m=0|1.25*D+7200,n=new Int8Array(m);this.encodeBuffer=function(a,b){1==f&&(b=a);a.length>D&&(D=a.length,m=0|1.25*D+7200,n=new Int8Array(m));a=v.lame_encode_buffer(t,a,b,a.length,n,0,m);return new Int8Array(n.subarray(0,a))};this.flush=function(){var a=v.lame_encode_flush(t,n,0,m);return new Int8Array(n.subarray(0,a))}};lamejs.WavHeader=xa}lamejs();
