# Milkdown 自定义语法完整指南

本文档以 code-block 代码块的自定义实现为例，详细介绍如何在 Milkdown 中自定义语法节点的完整流程。

## 目录
1. 项目技术架构
2. 性能优化方案
3. 自定义语法流程
4. 代码块复制功能实现

## 项目技术架构

### Milkdown 编辑器架构
本项目使用 Milkdown 作为 Markdown 编辑器的核心框架。Milkdown 是一个基于 ProseMirror 的插件化 Markdown 编辑器，具有以下特点：
- Schema 驱动: 通过定义节点 Schema 来描述文档结构
- 插件化架构: 功能通过插件系统进行扩展
- 双向转换: 支持 Markdown ↔ DOM ↔ ProseMirror 的双向转换
- 自定义渲染: 支持通过 NodeView 自定义节点的渲染和交互

### 自定义Crepe 核心组件结构
```bash
src/components/Crepe/
├── core/
│   └── crepe.ts              # Crepe 编辑器核心类
├── schema/
│   └── code-block.ts         # 自定义 Schema 定义
├── components/
│   └── code-block/           # 自定义组件实现
└── feature/
    └── code-mirror/          # 功能集成配置
```

## 性能优化方案

### 异步渲染问题
由于 Milkdown 实例的渲染是异步的，在虚拟列表等高性能场景下会出现渲染延迟问题。为了解决这个问题，项目采用了以下优化策略：

1. 提前初始化和资源预加载
```javascript
// 提前初始化 Milkdown 资源
const crepeInstance = new Crepe({
  root: containerRef.current,
  defaultValue: '',
  features: FEATURES,
  featureConfigs,
});
```

2. 单实例 + toDom 渲染方案
为了保证渲染消息的同步性，采用单 Milkdown 实例后使用 toDom 方法转换成 HTML 再渲染：
再parserMdToHtml中作了一些toDom后的语法兼容处理。
```javascript
// 每次 renderMd 都先 toDom 转成 HTML 后才渲染
const htmlContent = {
    __html: parserMdToHtml(value, {
      hasKnowledgeRetrieve,
      hasNetworkSearch,
    }),
  };
```
这种方案的优势：
- 同步渲染: 避免异步渲染导致的闪烁和延迟
- 性能优化: 减少多实例创建的开销
- 一致性: 保证所有消息使用相同的渲染逻辑

## 自定义语法流程

### 步骤 1: 修改组件来源
在 /src/components/Crepe/feature/code-mirror/index.ts 中修改组件来源：
```javascript
// 原来从官方组件库导入
// import {
//   codeBlockComponent,
//   codeBlockConfig,
// } from '@milkdown/kit/component/code-block';

// 修改为使用自定义组件
import {
  codeBlockComponent,
  codeBlockConfig,
} from '../../components/code-block';
```

修改原因: 官方组件库 @milkdown/kit/component 无法满足自定义需求，需要使用本地自定义的组件实现。

### 步骤 2: 避免 Web Components 冲突
在组件注册时使用自定义标签名避免冲突：
```javascript
// 使用自定义标签名避免与官方组件冲突
defIfNotExists('milkdown-code-block-custom', CodeElement);
```

关键点说明:
1. Web Component 注册机制
- 浏览器的自定义元素注册（customElements.define）要求：
  - 每个标签名（如 'milkdown-link-preview'）只能注册一次。
  - 注册时会将标签名与构造器（类）绑定。
  - 之后通过 new 或 DOM 解析创建该标签时，必须使用注册时的类。

2. 多次注册或类引用不一致的后果
- 如果同一个标签名被不同的类注册，浏览器只认第一次注册的类。
- 如果 new 的类不是注册时的类，会报 Illegal constructor。
- 这种情况常见于：
  - 依赖包（如 @milkdown/components）和本地代码都注册了同名自定义元素，但用的是不同的类。
  - 多个包或多处代码各自注册，导致类引用不一致。

```javascript
export function defIfNotExists(
  tagName: string,
  element: CustomElementConstructor
) {
  const current = customElements.get(tagName);

  if (current == null) {
    customElements.define(tagName, element);
    return;
  }

  if (current === element) {
    return;
  }

  // 避免重复注册警告
  console.warn(`Custom element ${tagName} has been defined before.`);
}
```

### 步骤 3: 自定义 toDom 结构
在 Schema 定义中自定义 toDom 方法，增加代码块皮肤：
```javascript
toDOM: (node) => {
  const attr = ctx.get(codeBlockAttr.key)(node);
  const language = node.attrs.language || 'text';
  const code = node.textContent || '';

  // 创建包装容器
  const wrapper = document.createElement('div');
  wrapper.className = 'code-block-formate-wrapper';
  wrapper.setAttribute('data-language', node.attrs.language);

  // 添加头部（语言标识 + 复制按钮）
  wrapper.appendChild(createHeader(language, code));

  // 添加 CodeMirror 容器
  wrapper.appendChild(createCodeMirrorContainer(code, language));

  return wrapper;
},
```

自定义结构包含:
- 头部区域: 显示编程语言图标、语言名称和复制按钮
- 代码区域: 使用 CodeMirror 进行语法高亮显示
- 样式定制: 自定义边框、背景色、圆角等视觉效果

### 步骤 4: 移除原有组件
在 Crepe 核心配置中移除官方组件，使用自定义组件：
```javascript
// 移除官方的 code-block 组件
this.#editor.remove(codeBlockAttrOrigin);
this.#editor.remove(codeBlockSchemaOrigin);

// 使用自定义的 code-block 组件
this.#editor.use(codeBlockAttr).use(codeBlockSchema);
```
## 总结
通过以上四个步骤，我们成功实现了 Milkdown 代码块的完全自定义：
1. 组件替换: 从官方组件切换到自定义组件
2. 避免冲突: 使用不同的 Web Component 标签名
3. 自定义渲染: 通过 toDom 实现个性化的代码块样式
4. 复制兼容: 通过巧妙的 DOM 结构设计保证复制粘贴的兼容性

这种方法不仅适用于代码块，也可以应用到其他 Milkdown 语法节点的自定义中，为编辑器提供更丰富的功能和更好的用户体验。