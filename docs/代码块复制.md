# 代码块复制功能完整文档

## 概述

本文档详细描述了项目中代码块复制功能的实现逻辑，包括不同场景下的复制处理、问题分析和解决方案。

## 核心文件

- `src/utils/message.ts` - 复制功能的核心工具函数
- `src/components/MdEditor/RenderMd.tsx` - 渲染组件中的复制按钮处理

## 功能架构

### 1. 复制入口函数

#### `handleCopy(message, copyAll)`
- **位置**: `src/utils/message.ts`
- **功能**: 处理消息复制的主入口
- **参数**:
  - `message`: 消息对象
  - `copyAll`: 是否复制全部内容

```typescript
export const handleCopy = (message: MessageItemType, copyAll = false) => {
  const messageText = getMessageContent(message);
  let content = '';
  if (copyAll) {
    content = parserMdToHtml(messageText.replace(mentionRegex, '@$1') || '');
  } else {
    content = selectedTextRef.current;
  }

  // 处理代码块的复制逻辑
  content = handleCodeBlockCopy(content);
  copyContent(content);
};
```

### 2. 代码块特殊处理

#### `handleCodeBlockCopy(content)`
- **位置**: `src/utils/message.ts`
- **功能**: 处理包含 `code-block-formate-wrapper` 类的代码块
- **核心逻辑**: 提取 `pre` 标签内容并确保可见性

```typescript
export const handleCodeBlockCopy = (content: string): string => {
  if (!content || typeof content !== 'string') {
    return content;
  }

  const tempElement = document.createElement('div');
  tempElement.innerHTML = content;

  const codeBlockWrappers = tempElement.querySelectorAll('.code-block-formate-wrapper');
  
  if (codeBlockWrappers.length === 0) {
    return content;
  }

  codeBlockWrappers.forEach((wrapper) => {
    const preElement = wrapper.querySelector('pre');
    if (preElement) {
      // 克隆pre元素并确保它是可见的
      const clonedPre = preElement.cloneNode(true) as HTMLElement;
      if (clonedPre.style.display === 'none') {
        clonedPre.style.display = 'block';
      }
      wrapper.parentNode?.replaceChild(clonedPre, wrapper);
    }
  });

  return tempElement.innerHTML;
};
```

### 3. 复制执行函数

#### `copyContent(content)`
- **位置**: `src/utils/message.ts`
- **功能**: 执行实际的复制操作
- **策略**: 优先使用现代 API，失败时降级到传统方法

```typescript
export const copyContent = (content: string) => {
  try {
    navigator.clipboard.write([
      new ClipboardItem({
        'text/html': new Blob([content], { type: 'text/html' }),
        'text/plain': new Blob([content.replace(/<[^>]+>/g, '')], {
          type: 'text/plain',
        }),
      }),
    ]);
    feedbackToast({ msg: '复制成功' });
  } catch (error) {
    console.log('catch', error);
    unsecuredCopyToClipboard(content);
  }
};
```

### 4. 降级复制方案

#### `unsecuredCopyToClipboard(text)`
- **位置**: `src/utils/message.ts`
- **功能**: 使用传统 `execCommand` 方法的降级方案
- **关键**: 确保临时元素可选择但不可见

```typescript
const unsecuredCopyToClipboard = (text: string) => {
  const tempElement = document.createElement('div');
  tempElement.innerHTML = text;
  
  // 设置样式确保元素可选择但不可见
  tempElement.style.position = 'absolute';
  tempElement.style.left = '-9999px';
  tempElement.style.top = '-9999px';
  tempElement.style.width = '1px';
  tempElement.style.height = '1px';
  tempElement.style.opacity = '0';
  tempElement.style.overflow = 'hidden';
  tempElement.style.whiteSpace = 'pre';
  
  document.body.appendChild(tempElement);

  const range = document.createRange();
  range.selectNodeContents(tempElement);

  const selection = window.getSelection();
  selection?.removeAllRanges();
  selection?.addRange(range);

  const successful = document.execCommand('copy');
  if (!successful) {
    console.error('Failed to copy text using execCommand');
    return;
  }

  selection?.removeAllRanges();
  document.body.removeChild(tempElement);
  feedbackToast({ msg: '复制成功' });
};
```

### 5. 组件中的复制按钮处理

#### `handleCopyCodeClick` (RenderMd.tsx)
- **位置**: `src/components/MdEditor/RenderMd.tsx`
- **功能**: 处理代码块复制按钮的点击事件
- **特点**: 临时修改 `display` 属性确保复制成功

```typescript
const handleCopyCodeClick = (e: React.MouseEvent<HTMLDivElement>) => {
  if (e.target instanceof HTMLElement && e.target.closest('.copy-button-click')) {
    const copyButton = e.target.closest('.copy-button-click');
    if (copyButton) {
      const hiddenPre = copyButton.querySelector('pre');
      if (hiddenPre) {
        // 临时设置为可见
        const originalDisplay = hiddenPre.style.display;
        hiddenPre.style.display = 'block';
        
        // 复制内容
        copyContent(hiddenPre.outerHTML);
        
        // 恢复原始状态
        hiddenPre.style.display = originalDisplay;
      }
    }
  }
};
```

## 问题分析与解决方案

### 核心问题
当复制内容包含 `style="display: none;"` 的元素时，传统的 `document.execCommand('copy')` 方法会失败。

### 问题原因
1. **DOM 选择限制**: `execCommand` 依赖 DOM 选择，隐藏元素无法被正确选择
2. **浏览器安全策略**: 现代浏览器通常不允许复制不可见的内容
3. **API 差异**: `navigator.clipboard.write` 直接操作系统剪贴板，不受元素可见性影响
4. **Secure Context 限制**: Clipboard API (`navigator.clipboard`) 只能在安全上下文（HTTPS）中使用。如果生产环境是 HTTP 而非 HTTPS，则 `navigator.clipboard.write()` 方法会失败，代码会自动进入 catch 块执行降级方案

### 解决策略

#### 1. 双重 API 策略
- **优先**: 使用 `navigator.clipboard.write` (现代 API)
- **降级**: 使用 `document.execCommand` (传统方法)

#### 2. 可见性处理
- **组件层面**: 在复制按钮点击时临时修改 `display` 属性
- **工具层面**: 在 `handleCodeBlockCopy` 中确保克隆的元素可见
- **降级方案**: 优化临时元素样式，确保可选择性

#### 3. 状态恢复
- 复制完成后立即恢复原始的 `display` 状态
- 避免影响页面显示效果

## 使用场景

### 1. 直接点击复制按钮
- 触发: `handleCopyCodeClick`
- 处理: 临时修改 `display` 属性
- 复制: 调用 `copyContent`

### 2. 选中文本复制
- 触发: `handleCopy`
- 处理: `handleCodeBlockCopy` 处理代码块
- 复制: 调用 `copyContent`

### 3. 全文复制
- 触发: `handleCopy(message, true)`
- 处理: 解析 Markdown 并处理代码块
- 复制: 调用 `copyContent`

## 最佳实践

1. **优先使用现代 API**: `navigator.clipboard.write` 提供更好的用户体验
2. **保持降级兼容**: 确保在不支持现代 API 的环境中正常工作
3. **状态管理**: 临时修改样式后及时恢复
4. **错误处理**: 提供清晰的错误日志和用户反馈
5. **性能优化**: 避免不必要的 DOM 操作和查询

## 环境兼容性

### Secure Context 要求
现代 Clipboard API 有严格的安全要求：

- **HTTPS 必需**: `navigator.clipboard` 只能在安全上下文中使用
- **本地开发**: `localhost` 和 `127.0.0.1` 被视为安全上下文
- **生产环境**: 必须使用 HTTPS 协议

### 环境检测与降级
```typescript
// 检测是否支持现代 Clipboard API
const isSecureContext = window.isSecureContext && navigator.clipboard;

if (isSecureContext) {
  // 使用现代 API
  navigator.clipboard.write([...]);
} else {
  // 降级到传统方法
  unsecuredCopyToClipboard(content);
}
```

### 不同环境的行为
| 环境 | navigator.clipboard 可用性 | 降级行为 |
|------|---------------------------|----------|
| HTTPS 生产环境 | ✅ 可用 | 使用现代 API |
| HTTP 生产环境 | ❌ 不可用 | 自动降级到 execCommand |
| localhost 开发 | ✅ 可用 | 使用现代 API |
| 127.0.0.1 开发 | ✅ 可用 | 使用现代 API |

## 注意事项

1. **API 废弃状态**: `document.execCommand` 已被标记为废弃，但仍需保留作为降级方案
2. **安全上下文要求**: 复制功能需要在安全上下文 (HTTPS) 中才能正常使用现代 API
3. **浏览器兼容性**: 不同浏览器对剪贴板 API 的支持程度可能不同
4. **样式处理**: 临时元素的样式设置需要确保既不可见又可选择
5. **生产环境部署**: 确保生产环境使用 HTTPS 以获得最佳用户体验
