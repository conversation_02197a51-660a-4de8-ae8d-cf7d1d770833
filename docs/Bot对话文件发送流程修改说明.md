# Bot对话文件发送流程修改说明（最新版本）

## 修改概述

针对发送给bot的多轮对话场景，实现了**文件选择时立即上传**的新流程，其他场景保持现状不变。

## 主要改动

### 核心变化
1. **文件上传时机提前**：从 `enterToSend` 时上传改为文件选择时立即上传
2. **增加上传状态显示**：实时显示文件上传进度和结果
3. **发送时直接使用已上传文件**：`enterToSend` 时直接发送已上传好的文件消息

## 修改内容

### 1. 扩展 FileListType 类型

添加了Bot对话场景下的文件上传相关字段：

```typescript
export interface FileListType {
  type: string;
  file?: File;
  id: string;
  url: string;
  docInfo?: any;
  isImage?: boolean;
  isClouDoc?: boolean;
  // Bot对话场景下的文件上传相关字段
  message?: MessageItem; // 创建的消息对象
  s3Path?: string; // 上传后的S3路径
  uploadStatus?: 'uploading' | 'success' | 'failed'; // 上传状态
}
```

### 2. 修改 uploadMsg 方法

在文件选择时立即进行上传处理（仅在Bot对话场景下）：

```typescript
const uploadMsg = async (uploadData: any, type: string, path: any) => {
  // 检查是否为bot对话场景
  const isBotConversation = currentConversation?.userID && userDetail?.ex &&
    JSON.parse(userDetail?.ex || '{}')?.stream === true;

  // ... 文件验证逻辑

  // 如果是Bot对话场景，立即进行文件上传处理
  if (isBotConversation) {
    // 先添加文件到列表，显示上传中状态
    setFileList((pre) => [
      ...pre,
      {
        ...baseFileItem,
        uploadStatus: 'uploading',
      },
    ]);

    try {
      // 1. 创建文件消息
      const message = await createFileMessageForBot(fileEl);

      // 2. 上传文件获取URL
      const uploadResult = await IMSDK.uploadFileByMessage(message);

      // 3. 更新文件列表，添加消息和s3Path信息
      setFileList((pre) =>
        pre.map(item =>
          item.id === fileId
            ? {
                ...item,
                message,
                s3Path: uploadResult.data?.url,
                uploadStatus: 'success' as const,
              }
            : item
        )
      );
    } catch (error) {
      // 更新为失败状态
      setFileList((pre) =>
        pre.map(item =>
          item.id === fileId
            ? {
                ...item,
                uploadStatus: 'failed' as const,
              }
            : item
        )
      );
    }
  }
};
```

### 3. 新增 sendUploadedFiles 方法

专门用于发送已经上传好的文件消息：

```typescript
// Bot对话场景：发送已经上传好的文件消息
const sendUploadedFiles = async (list: FileListType[]) => {
  for (const mesItem of list) {
    if (!mesItem.isClouDoc && mesItem.message) {
      try {
        // 如果文件已经成功上传，直接发送带有s3Path的消息
        if (mesItem.uploadStatus === 'success' && mesItem.s3Path) {
          const messageWithS3Path = {
            ...mesItem.message,
            s3Path: mesItem.s3Path,
            subConversationId: conversation?.subConversationId || '',
          };
          await sendMessage({ message: messageWithS3Path });
        } else if (mesItem.uploadStatus === 'failed') {
          // 如果上传失败，发送原始消息
          await sendMessage({ message: mesItem.message });
        } else if (mesItem.uploadStatus === 'uploading') {
          // 如果还在上传中，发送原始消息或等待上传完成
          await sendMessage({ message: mesItem.message });
        }
      } catch (error) {
        console.error('发送文件消息失败:', error);
      }
    }
  }
};
```

### 3. 新增 `createFileMessageForBot` 方法

在 `useFileMessage.ts` 中添加专门为Bot对话场景创建文件消息的方法：

```typescript
// 为Bot对话场景创建文件消息的方法
const createFileMessageForBot = async (
  file: FileWithPath
): Promise<MessageItem> => {
  const isImage = canSendImageTypeList.includes(getFileType(file.name));
  
  if (isImage) {
    // 对于图片，调用 createImageMessage
    return getImageMessage(file);
  } else {
    // 对于其他文件，调用 createFileMessage
    return getFileMessage(file);
  }
};
```

### 4. 修改 enterToSend 流程

更新了消息发送的主流程，使用已上传的文件：

```typescript
if (isBotConversation) {
  // Bot对话场景：发送已上传的文件，再发送流式消息
  if (fileList && fileList.length > 0) {
    await sendUploadedFiles(fileList);
  }

  if (cleanText) {
    message = (await IMSDK.createTextMessage(cleanText)).data;
    sendStreamMessage({
      recvUser: userDetail,
      curMsg: message,
    });
  }

  // 如果有文件或有文本内容，清除输入框
  if (cleanText || (fileList && fileList.length > 0)) {
    clear();
    return;
  }
} else {
  // 非Bot对话场景：保持原有逻辑
  // ... 原有逻辑不变
}
```

### 5. 添加上传状态显示

在 `FileRender` 组件中添加了上传状态的可视化显示：

#### 图片文件状态显示
```typescript
{/* 显示上传状态 */}
{item.uploadStatus === 'uploading' && (
  <div className={styles.uploadStatus}>
    <div className={styles.uploadingText}>上传中...</div>
  </div>
)}
{item.uploadStatus === 'failed' && (
  <div className={styles.uploadStatus}>
    <div className={styles.failedText}>上传失败</div>
  </div>
)}
{item.uploadStatus === 'success' && (
  <div className={styles.uploadStatus}>
    <div className={styles.successText}>✓</div>
  </div>
)}
```

#### 普通文件状态显示
```typescript
{item.uploadStatus === 'uploading' && (
  <span style={{ color: '#1890ff' }}>上传中...</span>
)}
{item.uploadStatus === 'failed' && (
  <span style={{ color: '#ff4d4f' }}>上传失败</span>
)}
{item.uploadStatus === 'success' && (
  <span style={{ color: '#52c41a' }}>上传成功</span>
)}
```

## 新的流程说明

### Bot对话场景下的文件发送流程（新版本）：

#### 阶段1：文件选择时立即上传
1. **文件选择**: 用户在messageInput中选择文件（不支持云文档）
2. **判断场景**: 检测到Bot对话场景（`stream=true`）
3. **添加到列表**: 文件添加到列表，状态设为 `uploading`
4. **创建消息**: 调用 `createFileMessageForBot` 方法
   - 图片文件：调用 `getImageMessage` (对应 `createImageMessage`)
   - 其他文件：调用 `getFileMessage` (对应 `createFileMessage`)
5. **文件上传**: 调用 `IMSDK.uploadFileByMessage` 方法上传文件
6. **更新状态**:
   - 成功：状态改为 `success`，保存 `s3Path` 和 `message`
   - 失败：状态改为 `failed`
7. **状态显示**: 在UI中实时显示上传状态

#### 阶段2：发送消息时使用已上传文件
1. **用户点击发送**: 触发 `enterToSend` 方法
2. **发送已上传文件**: 调用 `sendUploadedFiles` 方法
   - 成功上传的文件：发送带 `s3Path` 的消息
   - 失败的文件：发送原始消息
   - 还在上传的文件：发送原始消息或等待
3. **发送流式消息**: 调用 `sendStreamMessage` 发送文本消息
4. **清理界面**: 清除输入框和文件列表

### 非Bot对话场景：

保持原有的文件发送逻辑不变，确保向后兼容。文件在 `enterToSend` 时才进行处理和发送。

## 错误处理

- 如果文件上传失败，会fallback到发送原始文件消息
- 所有错误都会被捕获并记录到控制台
- 不会影响文本消息的正常发送

## 兼容性

- 非Bot对话场景完全保持原有逻辑
- 只有在检测到Bot对话场景（`stream=true`）时才会使用新流程
- 云文档发送在Bot场景下被跳过（按需求不支持）

## API依赖

新流程依赖以下API：
- `IMSDK.uploadFileByMessage(message)` - 文件上传接口
- 返回格式：`{ data: { url: string } }` 或 `{ url: string }`

## 新流程的优势

### 1. 用户体验提升
- **即时反馈**: 文件选择后立即开始上传，用户可以看到实时状态
- **并行处理**: 用户可以在文件上传的同时继续编辑文本
- **状态可视化**: 清晰的上传状态显示（上传中/成功/失败）

### 2. 性能优化
- **提前上传**: 避免发送时的等待时间
- **减少阻塞**: 发送操作更加流畅
- **错误处理**: 提前发现上传问题，避免发送时失败

### 3. 技术优势
- **状态管理**: 完善的文件状态跟踪
- **错误恢复**: 上传失败时的fallback机制
- **向后兼容**: 非Bot场景完全不受影响

## 测试建议

### 基础功能测试
1. **Bot场景图片上传**: 选择图片后立即显示上传状态
2. **Bot场景文件上传**: 选择文档后立即显示上传状态
3. **上传状态显示**: 验证上传中/成功/失败状态的正确显示
4. **发送流程**: 确认发送时使用已上传的文件

### 异常情况测试
1. **网络异常**: 模拟网络中断时的上传失败处理
2. **大文件上传**: 测试大文件的上传状态和超时处理
3. **多文件上传**: 测试多个文件同时上传的状态管理
4. **上传中发送**: 测试文件还在上传时点击发送的处理

### 兼容性测试
1. **非Bot场景**: 确认普通用户对话的文件发送不受影响
2. **群聊文件**: 确认群聊中的文件发送功能正常
3. **云文档**: 确认云文档在Bot场景下被正确跳过
4. **Electron环境**: 测试桌面端的文件上传功能

### 用户体验测试
1. **状态切换**: 验证上传状态的平滑切换
2. **取消操作**: 测试上传过程中删除文件的处理
3. **重复选择**: 测试重复选择同一文件的处理
4. **界面响应**: 确认上传过程中界面不卡顿

``` mermaid
flowchart TD
    A[用户选择文件] --> B{是否为Bot对话场景?}
    
    B -->|否| C[添加到文件列表<br/>保持原有逻辑]
    B -->|是| D[添加到文件列表<br/>状态: uploading]
    
    D --> E[调用 createFileMessageForBot]
    E --> F{文件类型判断}
    F -->|图片| G[调用 getImageMessage]
    F -->|其他文件| H[调用 getFileMessage]
    
    G --> I[调用 IMSDK.uploadFileByMessage]
    H --> I
    
    I --> J{上传是否成功?}
    J -->|成功| K[更新文件列表<br/>状态: success<br/>添加 s3Path]
    J -->|失败| L[更新文件列表<br/>状态: failed]
    
    K --> M[显示上传成功状态]
    L --> N[显示上传失败状态]
    
    M --> O[用户点击发送按钮]
    N --> O
    C --> P[用户点击发送按钮<br/>非Bot场景]
    
    O --> Q[调用 enterToSend]
    Q --> R{是否为Bot对话场景?}
    
    R -->|是| S[调用 sendUploadedFiles]
    R -->|否| T[执行原有发送逻辑]
    
    S --> U{遍历文件列表}
    U --> V{文件上传状态}
    V -->|success| W[发送带s3Path的消息]
    V -->|failed| X[发送原始消息]
    V -->|uploading| Y[发送原始消息<br/>或等待上传完成]
    
    W --> Z[发送流式消息]
    X --> Z
    Y --> Z
    
    Z --> AA[清理输入框]
    T --> BB[原有逻辑处理]
    BB --> CC[清理输入框]
    
    style A fill:#e1f5fe
    style D fill:#fff3e0
    style I fill:#f3e5f5
    style S fill:#e8f5e8
    style Z fill:#ffebee
```