export const sampleStreamMessage = {
  content: {
    id: "msg_123456",
    start: 1699500000,
    end: 1699500010,
    answer: "这是一个综合回答，基于知识库检索和网络搜索的结果。是一个综合回答，基于知识库检索和网络搜索的结果。是一个综合回答，基于知识库检索和网络搜索的结果。[1]是一个综合回答，基于知识库检索和网络搜索的结果。是一个综合回答，基于知识库检索和网络搜索的结果。是一个综合回答，基于知识库检索和网络搜索的结果。\n是一个综合回答，基于知识库检索和网络搜索的结果。[2]",
    input_tokens: 50,
    output_tokens: 150,
    latency: 2.5,
    think_message: {
      answer: "让我思考一下这个问题..."
    },
    knowledge_retrieve: {
      start: 1699500001,
      end: 1699500003,
      latency: 0.8,
      results: [
        {
          index: 0,
          score: 0.95,
          doc_name: "产品说明文档",
          doc_type: 1,
          doc_url: "https://example.com/docs/1",
          content: "这是从知识库中检索到的相关内容"
        }
      ]
    },
    qa_retrieve: {
      start: 1699500004,
      end: 1699500005,
      latency: 0.5,
      results: [
        {
          index: 0,
          score: 0.88,
          question: "相似问题示例",
          answer: "相似问题的标准答案"
        }
      ]
    },
    network_search: {
      start: 1699500006,
      end: 1699500008,
      latency: 1.2,
      results: [
        {
          index: 0,
          title: "搜索结果标题",
          url: "https://example.com/search/1",
          content: "从网络搜索到的相关内容摘要"
        }
      ]
    },
    suggestion: {
      questions: [
        "您还想了解更多相关信息吗？",
        "需要我详细解释某个部分吗？",
        "您对这个回答满意吗？"
      ],
      clientMsgId:'si_6c73216bdd10f61ad55d47f0e4d675a731_6c73216bdd10f61ad55d47f0e4d675a731'
    }
  },
  type: "stream"
};
