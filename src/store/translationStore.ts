import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { TranslationStore } from '@/store/type';

export type TranslationState = 'finish' | 'loading';

export interface SingleTranslation {
  translatedContent: string;
  translationState: TranslationState;
}

export const useTranslationStore = create<TranslationStore>()(
  devtools(
    (set, get) => ({
      translations: {},
      setTranslation: (clientMsgID: string, content: string) => {
        set((state) => ({
          translations: {
            ...state.translations,
            [clientMsgID]: {
              translatedContent: content,
              translationState: 'finish',
            },
          },
        }));
      },
      updateTranslatedContent: (clientMsgID: string, content: string) => {
        set((state) => ({
          translations: {
            ...state.translations,
            [clientMsgID]: {
              ...state.translations[clientMsgID],
              translatedContent: content,
            },
          },
        }));
      },
      updateTranslationState: (
        clientMsgID: string,
        stateValue: TranslationState
      ) => {
        set((state) => ({
          translations: {
            ...state.translations,
            [clientMsgID]: {
              ...state.translations[clientMsgID],
              translationState: stateValue,
            },
          },
        }));
      },
      clearTranslation: (clientMsgID: string) => {
        set((state) => {
          const newTranslations = { ...state.translations };
          delete newTranslations[clientMsgID];
          return { translations: newTranslations };
        });
      },
      clearAllTranslations: () => {
        set(() => ({
          translations: {},
        }));
      },
    }),
    {
      name: 'translationStore',
      enabled: true,
    }
  )
);
