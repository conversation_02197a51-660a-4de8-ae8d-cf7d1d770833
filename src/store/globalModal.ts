import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { GlobalModalStore } from './type';

export const useGlobalModalStore = create<GlobalModalStore>()(
  devtools(
    (set, get) => ({
      isStatusModalOpen: false,
      isEditModalOpen: false,
      isNetErrorTooltipOpen: false,

      setStatusModalOpen: (open) => set({ isStatusModalOpen: open }),
      setEditModalOpen: (open) => set({ isEditModalOpen: open }),
      setNetErrorTooltipOpen: (open) => set({ isNetErrorTooltipOpen: open }),
    }),
    {
      name: 'globalModalStore',
      enabled: true,
    }
  )
);
