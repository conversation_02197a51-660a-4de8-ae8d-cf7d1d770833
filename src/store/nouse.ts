// @ts-expect-error
import initSqlJs, { Database, SqlJsStatic } from '@jlongster/sql.js';
// @ts-expect-error
import { SQLiteFS } from 'absurd-sql-optimized';
// @ts-expect-error
import IndexedDBBackend from 'absurd-sql-optimized/dist/indexeddb-backend';
import { getSDK } from '@ht/openim-wasm-client-sdk';

// 锁机制
class Mutex {
  private readonly queue: any[] = [];

  lock() {
    let unlock: any;
    const promise = new Promise<void>((resolve) => (unlock = resolve));
    this.queue.push(unlock);

    console.error('111');
    return promise;
  }

  unlock() {
    const unlock = this.queue.shift();
    if (unlock) {
      unlock(); // 释放锁
    }
  }
}

const mutex = new Mutex();

let instance: Database | null = null;
let SQL: SqlJsStatic;
let isInitializing = false;

const initializeSQL = async (sqlWasmPath = '/sql-wasm.wasm') => {
  if (!SQL) {
    SQL = await initSqlJs({ locateFile: () => sqlWasmPath });

    const sqlFS = new SQLiteFS(SQL.FS, new IndexedDBBackend());
    SQL.register_for_idb(sqlFS);

    SQL.FS.mkdir('/sql');

    SQL.FS.mount(sqlFS, {}, '/sql');
  }
};

const InitializeDB = async (
  filePath: string,
  sqlWasmPath = '/sql-wasm.wasm'
) => {
  await initializeSQL(sqlWasmPath); // 确保 SQL 被初始化

  const path = `/sql/${filePath}`;
  const db = new SQL.Database(path, { filename: true });

  if (typeof SharedArrayBuffer === 'undefined') {
    const stream = SQL.FS.open(path, 'a+');
    await stream.node.contents.readIfFallback();
    SQL.FS.close(stream);
  }

  db.exec(`
    PRAGMA page_size=8192;
    PRAGMA journal_mode=MEMORY;
  `);

  return db;
};

export const execSql: any = async (sqlString?: string) => {
  try {
    // await mutex.lock();
    const IMSDK = getSDK();
    const user = await IMSDK.getSelfUserInfo();

    if (!instance) {
      if (isInitializing) {
        // 等待初始化完成
        await new Promise((resolve) => setTimeout(resolve, 100));
        return execSql(sqlString);
      }
      isInitializing = true;
      instance = await InitializeDB(
        `${user?.data?.userID}.sqlite`,
        '/PortalStatics/sql-wasm.wasm'
      );
      isInitializing = false;
    }

    // 执行 SQL 查询
    const result = instance.exec(sqlString);

    return JSON.stringify(result);
  } catch (e) {
    mutex.unlock(); // 错误时释放锁
    return '';
  }
};

export const getDBInstance = async () => {
  const IMSDK = getSDK();
  const user = await IMSDK.getSelfUserInfo();
  instance = await InitializeDB(
    `${user?.data?.userID}.sqlite`,
    '/PortalStatics/sql-wasm.wasm'
  );

  return instance;
};
