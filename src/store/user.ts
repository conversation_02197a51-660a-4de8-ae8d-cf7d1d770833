/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable promise/no-nesting */
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

import { IMSDK } from '@/layouts/BasicLayout';
import { clearIMProfile, getLocale, setLocale } from '@/utils/storage';
import { feedbackToast } from '@/utils/common';
import { t } from 'i18next';
import { SelfUserInfo } from '@ht/openim-wasm-client-sdk';
import { useContactStore } from './contact';
import { useConversationStore } from './conversation';
import { AppSettings, IMConnectState, UserStore } from './type';

export const useUserStore = create<UserStore>()(
  devtools(
    (set, get) => ({
      syncState: 'loading',
      progress: 0,
      reinstall: true,
      isLogining: false,
      connectState: 'success',
      selfInfo: {} as SelfUserInfo,
      selfState: undefined,
      appSettings: {
        locale: getLocale(),
        closeAction: 'miniSize',
      },
      newVersion: {
        available: false,
        data: {},
      },
      updateSyncState: (syncState: IMConnectState) => {
        set({ syncState });
      },
      updateProgressState: (progress: number) => {
        set({ progress });
      },
      updateReinstallState: (reinstall: boolean) => {
        set({ reinstall });
      },
      updateIsLogining: (isLogining: boolean) => {
        set({ isLogining });
      },
      updateConnectState: (connectState: IMConnectState) => {
        set({ connectState });
      },
      getSelfInfoByReq: () => {
        IMSDK.getSelfUserInfo()
          .then(({ data }) => {
            set(() => ({
              selfInfo: {
                ...(data as unknown as SelfUserInfo),
                employeeCode: JSON.parse(data?.ex || '{}')?.employeeCode, // 先临时这么加一下，还是要看下为啥返回的工号字段有时不生效，by zaa 0421
              },
            }));
            if (data.ex != null && data.ex !== '') {
              set(() => ({
                selfStatus: {
                  code: JSON.parse(data.ex || '{}')?.userState?.code,
                  desc: JSON.parse(data.ex || '{}')?.userState?.desc,
                  // duration: JSON.stringify(data.ex)?.userState?.duration,
                  duration: '',
                },
              }));
            }
          })
          .catch((error) => {
            console.error('查询个人信息失败', error);
            feedbackToast({ error, msg: t('toast.getSelfInfoFailed') });
            // get().userLogout();
          });
      },
      updateSelfInfo: (info: Partial<SelfUserInfo>) => {
        set((state) => ({ selfInfo: { ...state.selfInfo, ...info } }));
      },
      updateSelfState: (ex?: any) => {
        if (ex != null && ex !== '') {
          set(() => ({
            selfStatus: {
              code: JSON.parse(ex)?.userState?.code,
              desc: JSON.parse(ex)?.userState?.desc,
              duration: '',
            },
          }));
        } else {
          set(() => ({
            selfStatus: undefined,
          }));
        }
      },
      clearSelfState: (ex?: any) => {
        get().updateSelfState();
      },
      updateAppSettings: (settings: Partial<AppSettings>) => {
        if (settings.locale) {
          setLocale(settings.locale);
        }
        set((state) => ({
          appSettings: { ...state.appSettings, ...settings },
        }));
      },
      userLogout: async (force?: boolean) => {
        if (!force) {
          await IMSDK.logout();
        }

        clearIMProfile();
        set({ selfInfo: {} as SelfUserInfo, progress: 0 });

        set({ selfStatus: undefined });
        useContactStore.getState().clearContactStore();
        useConversationStore.getState().clearConversationStore();

        // 先随便加一下，这样的话在用户登出的时候能醒目的发现
        // if (
        //   window.location.hostname === 'localhost' ||
        //   window.location.hostname === '127.0.0.1'
        // ) {
        //   window.location.replace('http://eipsit.htsc.com.cn/htscPortal/home');
        // } else {
        //   window.location.replace('/htscPortal/home');
        // }
      },
      updateNewVersion: (newVersion: any) => {
        set({ newVersion });
      },
    }),
    {
      name: 'userStore',
      enabled: true,
    }
  )
);
