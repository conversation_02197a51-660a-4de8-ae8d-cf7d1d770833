import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { useConversationStore } from '.';
import {
  SearchInfoType,
  CHANGE_RIGHT_ARE_ACTION,
  rightAreaInGroupListItem,
} from './type';

export const useSearchInfoStore = create<SearchInfoType>()(
  devtools(
    (set, get) => ({
      globalSearchModalVisible: false,
      searchValue: '',
      searchConversationInfo: null,
      needShowRightArea: false,
      rightAreaInSearchList: [],
      rightAreaInSearch: null,
      rightPayload: '',
      updateSearchInfo: (fields: Partial<SearchInfoType>) => {
        set(() => ({ ...fields }));
      },
      changeRightArea: async (
        changeRightAction: CHANGE_RIGHT_ARE_ACTION,
        payload: any
      ) => {
        if (changeRightAction === 'OPEN_CHANNEL') {
          set((state) => {
            let newRightAreaInGroupList = [...state.rightAreaInSearchList];
            newRightAreaInGroupList = newRightAreaInGroupList.filter(
              (i: rightAreaInGroupListItem) => i.type !== 'channel'
            );
            newRightAreaInGroupList.push({
              type: 'channel',
              payload,
            });
            return {
              needShowRightArea: true,
              rightAreaInSearch: 'channel',
              rightPayload: payload,
              rightAreaInSearchList: newRightAreaInGroupList,
            };
          });
        } else if (changeRightAction === 'OPEN_PERSON_DETAIL') {
          set((state) => {
            let newRightAreaInGroupList = [...state.rightAreaInSearchList];
            newRightAreaInGroupList = newRightAreaInGroupList.filter(
              (i: rightAreaInGroupListItem) => i.type !== 'personDetail'
            );
            newRightAreaInGroupList.push({
              type: 'personDetail',
              payload,
            });
            return {
              needShowRightArea: true,
              rightAreaInSearch: 'personDetail',
              rightPayload: payload,
              rightAreaInSearchList: newRightAreaInGroupList,
            };
          });
        } else if (changeRightAction === 'OPEN_FILE_AREA') {
          set((state) => {
            let newRightAreaInGroupList = [...state.rightAreaInSearchList];
            newRightAreaInGroupList = newRightAreaInGroupList.filter(
              (i: rightAreaInGroupListItem) => i.type !== 'file'
            );
            newRightAreaInGroupList.push({
              type: 'file',
              payload,
            });
            return {
              needShowRightArea: true,
              rightAreaInSearch: 'file',
              rightPayload: payload,
              rightAreaInSearchList: newRightAreaInGroupList,
            };
          });
        } else if (changeRightAction === 'CLEAR_RIGHT_AREA') {
          set(() => ({
            needShowRightArea: false,
            rightAreaInSearch: null,
            rightAreaInSearchList: [],
          }));
        }
      },
      changeRightAreaBack: async () => {
        set((state) => {
          const newRightAreaInSearchList = state.rightAreaInSearchList;
          newRightAreaInSearchList.pop();
          const lastRightAreaInfo =
            newRightAreaInSearchList[newRightAreaInSearchList.length - 1];
          if (lastRightAreaInfo.type === 'channel') {
            useConversationStore
              .getState()
              .updateChannelHeaderCurTab('message');
            useConversationStore.getState().updateCurrentConversation({
              ...lastRightAreaInfo.payload,
            });
          }
          return {
            needShowRightArea: true,
            rightAreaInSearch: lastRightAreaInfo.type,
            rightPayload: lastRightAreaInfo.payload,
            rightAreaInSearchList: newRightAreaInSearchList,
          };
        });
      },
    }),
    {
      name: 'searchStore',
      enabled: true,
    }
  )
);
