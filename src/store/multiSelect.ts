import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { message as MessageTip } from '@ht/sprite-ui';
import { findIndex, sortedIndexBy, concat, slice } from 'lodash';
import { MultiSelectStore } from '@/store/type';
import { MessageItem } from '@ht/openim-wasm-client-sdk';

export const useMultiSelectStore = create<MultiSelectStore>()(
  devtools(
    (set, get) => ({
      multiSelectState: false,
      multiSelectList: [],
      updateMultiSelectInfo: (fields: Partial<MultiSelectStore>) => {
        set(() => ({ ...fields }));
      },
      updateMultiSelectList: (msg: MessageItem) => {
        const currentList = get().multiSelectList;

        const existingIndex = findIndex(currentList, {
          clientMsgID: msg.clientMsgID,
        });

        if (existingIndex > -1) {
          const newList = currentList.filter(
            (item) => item.clientMsgID !== msg.clientMsgID
          );
          set({ multiSelectList: newList });
        } else {
          if (currentList?.length >= 50) {
            return MessageTip.warn('最多选中50条记录');
          }
          const insertIndex = sortedIndexBy(currentList, msg, 'seq');
          const newList = concat(
            slice(currentList, 0, insertIndex),
            msg,
            slice(currentList, insertIndex)
          );
          set({ multiSelectList: newList });
        }
      },
      cancelMultiSelect: () => {
        set(() => ({ multiSelectList: [], multiSelectState: false }));
      },
      deleteMultiSelect: (msg: MessageItem) => {
        const currentList = get().multiSelectList;
        const newList = currentList.filter(
          (item) => item.clientMsgID !== msg.clientMsgID
        );
        set({ multiSelectList: newList });
      },
    }),
    {
      name: 'multiSelectStore',
      enabled: true,
    }
  )
);
