import { IMSDK } from '@/layouts/BasicLayout';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { DepartmentInfoType } from './type';

export const useDepartmentInfoStore = create<DepartmentInfoType>()(
  devtools(
    (set, get) => ({
      departmentInfoIniting: true,
      departmentInfo: [],
      startSyncDepartmentInfo: () => {
        set(() => ({
          departmentInfoIniting: true,
        }));
      },
      endSyncDepartmentInfo: () => {
        set(() => ({
          departmentInfoIniting: false,
        }));

        get().initDepartmentInfo();
      },
      initDepartmentInfo: async () => {
        try {
          const { data } = await IMSDK.getDepartmentList(0);

          set(() => ({
            departmentInfo: data,
          }));
        } catch (e) {
          console.error('初始化部门信息的接口失败', e);
          set(() => ({
            departmentInfo: [],
          }));
        }
      },
    }),
    {
      name: 'departmentStore',
      enabled: true,
    }
  )
);
