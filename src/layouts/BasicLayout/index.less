.layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  .themeBg {
    z-index: -1;
    position: absolute;
    width: 100vw;
    height: 100vh;
    border-radius: 5px;
    // filter: brightness(0.9);
    // background: radial-gradient(
    //   circle at 50% 50%,
    //   rgb(var(--link-color-plt-ocean-70)) 20%,
    //   transparent 80%
    // ),
    //   conic-gradient(
    //   from 45deg at 50% 50%,
    //   rgb(var(--link-color-plt-ocean-90)) 0%,
    //   rgb(var(--link-color-plt-ocean-70)) 25%,
    //   rgb(var(--link-color-plt-ocean-90)) 50%,
    //   rgb(var(--link-color-plt-ocean-70)) 75%,
    //   rgb(var(--link-color-plt-ocean-90)) 100%
    // );
    // background: url("../../assets/themeBg.png");
    background: linear-gradient(
      90deg,
      #187aba 0%,
      rgb(24 122 186 / 100%) 75%,
      #2e7fc9 100%
    );
    // background-size: cover;
  }

  .offlineTip {
    position: fixed;
    top: 145px;
    left: calc(50vw);
    z-index: 999;
    width: 300px;
    height: 28px;
    box-shadow: 0 2px 4px 0 #f2f2f3;
    border-radius: 20px;
    border: 1px solid var(--primary-background-color-5);
    background: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 600;
    color: #1d1c1d;
    line-height: 20px;

    img {
      margin-left: 6px;
      width: 14px;
      height: 14px;
    }
  }

  .netFailedInfo {
    position: fixed;
    display: flex;
    align-items: center;
    top: 84px;
    right: 10px;
    z-index: 999;
    width: 350px;
    height: 66px;
    padding: 0 35px 0 17px;
    background: #000000;
    box-shadow: 0 6px 16px 0 rgba(29, 34, 44, 8%),
      0 3px 6px -4px rgba(29, 34, 44, 12%);
    border-radius: 8px;

    span {
      height: 22px;
      font-size: 15px;
      font-weight: 400;
      color: #ffffff;
      line-height: 22px;
      flex: 1;
    }
  }

  .mainContent {
    flex: 1;
    display: flex;
    justify-content: space-between;
    background: #fff;
    border-radius: 3px 0 0;
    overflow: hidden;
  }
  .clearOriginalBg {
    background: transparent !important;
    aside {
      background: transparent !important;
    }
  }
  .upgradeBanner {
    height: 20px;
    position: fixed;
    color: var(--primary-text-color-7);
    cursor: pointer;
    font-size: 14px;
    line-height: 20px;
    display: flex;
    align-items: center;
    top: 12px;

    .dot {
      background: #d6363f;
      width: 7px;
      height: 7px;
      border-radius: 50%;
      position: absolute;
      top: 1px;
      left: 11px;
    }
    img {
      width: 20px;
      height: 20px;
      margin-right: 8px;
    }
  }

  .singleSpin {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }
}
