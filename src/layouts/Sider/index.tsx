import React, { useState, useMemo } from 'react';
import classNames from 'classnames';
import { Link } from '@oula/oula';
import { useUserStore, useConversationStore } from '@/store';
import { Popover } from '@ht/sprite-ui';
import { MessageReceiveOptType } from '@ht/openim-wasm-client-sdk';
import CurrentUserToolBar from '@/components/CurrentUserToolBar';
import {
  getShowDescByStatus,
  getStatusImgSrcByStatus,
} from '@/components/UserState/SetStateModal/const';
import OnlineOrTypingStatus from '@/pages/contact/components/OnlineOrTypingStatus';
import chatIcon from '@/assets/sider/chat.svg';
import chatActiveIcon from '@/assets/sider/chatActive.svg';
import contactIcon from '@/assets/sider/contact.svg';
import contactActiveIcon from '@/assets/sider/contactActive.svg';
import OIMAvatar from '@/components/OIMAvatar';
import styles from './index.less';

const getIcon = (path: string, isActive: boolean) => {
  if (path === 'index') {
    return isActive ? chatActiveIcon : chatIcon;
  } else if (path === 'contact') {
    return isActive ? contactActiveIcon : contactIcon;
  } else {
    return '';
  }
};

export interface Propstype {
  menuData: any;
}

const Sider: React.FC<Propstype> = ({ menuData = [] }) => {
  const { userID, faceURL, nickname } = useUserStore((state) => state.selfInfo);
  const changeRightArea = useConversationStore(
    (state) => state.changeRightArea
  );
  const { selfStatus } = useUserStore();

  const [hoverVisible, setHoverVisible] = useState(false);
  const [clickVisible, setClickVisible] = useState(false);
  const conversationList = useConversationStore(
    (state) => state.conversationList
  );

  const renderHover = () => {
    return (
      <div className={styles.hoverArea}>
        <div className={styles.name}>
          <span>{nickname}</span>
          <OnlineOrTypingStatus userID={userID} size={10} />
        </div>
        {(selfStatus?.code || selfStatus?.desc) && (
          <div className={styles.state}>
            <img src={getStatusImgSrcByStatus(selfStatus)} />
            <span>{getShowDescByStatus(selfStatus)}</span>
          </div>
        )}
        {selfStatus?.duration && (
          <div className={styles.duration}>{selfStatus?.duration}</div>
        )}
      </div>
    );
  };

  const unreadCount = useMemo(() => {
    const allChatItems =
      conversationList?.filter(
        (conversation) => conversation?.parentId == null
      ) ?? [];
    let count = 0;
    allChatItems?.forEach((conversation) => {
      if (
        conversation?.unreadCount &&
        conversation?.unreadCount > 0 &&
        conversation.recvMsgOpt === MessageReceiveOptType.Normal
      ) {
        count += conversation?.unreadCount;
      }
    });
    return count;
  }, [conversationList]);

  return (
    <>
      <div className={styles.siderWrapper}>
        <div>
          {menuData.map((item: any) => {
            if (item.path !== '/' && !item.hideInMenu) {
              return (
                <Link
                  to={item.path}
                  key={item.path}
                  onClick={() => changeRightArea('CLEAR_RIGHT_AREA')}
                >
                  <div
                    className={classNames(
                      styles.menuItem,
                      location.pathname === item.path ? styles.active : ''
                    )}
                  >
                    <div className={styles.icons}>
                      {item.icon && (
                        // eslint-disable-next-line import/no-dynamic-require
                        <img
                          src={getIcon(
                            item.icon,
                            location.pathname === item.path
                          )}
                        /> // eslint-disable-line @typescript-eslint/no-require-imports
                      )}
                    </div>
                    <div
                      onClick={() => {
                        console.warn(item.path, 'item.path');
                      }}
                    >
                      {item.name}
                    </div>
                    {item.key === 'chat' && unreadCount > 0 && (
                      <div
                        className={classNames(
                          styles.unreadCount,
                          unreadCount >= 100 && styles.unreadCountMore
                        )}
                      >
                        {unreadCount < 100 ? unreadCount : '99+'}
                      </div>
                    )}
                  </div>
                </Link>
              );
            } else {
              return '';
            }
          })}
        </div>
        <div style={{ flex: '1 1 0%' }}></div>

        <Popover
          overlayClassName={styles.popPersonCardContainer}
          overlayInnerStyle={{
            padding: 0,
            borderRadius: '10px',
          }}
          content={<CurrentUserToolBar onHide={() => setClickVisible(false)} />}
          placement="rightTop"
          destroyTooltipOnHide={true}
          trigger={'click'}
          showArrow={false}
          open={clickVisible}
          onOpenChange={(open) => {
            setClickVisible(open);
            setHoverVisible(false);
          }}
        >
          <Popover
            overlayClassName={styles.popStateContainer}
            overlayInnerStyle={{
              borderRadius: '5px',
              background: 'black',
            }}
            content={renderHover()}
            placement="rightTop"
            trigger={'hover'}
            showArrow={true}
            open={hoverVisible}
            onOpenChange={(open) => {
              setHoverVisible(!clickVisible && open);
            }}
          >
            <div className={styles.menuItem} style={{ marginBottom: '20px' }}>
              {(selfStatus?.code || selfStatus?.desc) && (
                <div className={styles.state}>
                  <img src={getStatusImgSrcByStatus(selfStatus)}></img>
                </div>
              )}
              <div className={classNames(styles.selfbtn)}>
                <OIMAvatar
                  userID={userID}
                  src={faceURL}
                  size={39}
                  stateSize={11}
                />
              </div>
            </div>
          </Popover>
        </Popover>
      </div>
    </>
  );
};

export default Sider;
