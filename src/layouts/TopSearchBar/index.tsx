import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Input, Popover } from '@ht/sprite-ui';
import GlobalSearchModal from '@/components/GlobalSearchModal';
import { useSearchInfoStore } from '@/store';
import { isEmpty, throttle } from 'lodash';
import { SessionType } from '@ht/openim-wasm-client-sdk';
import searchIcon from '@/assets/images/topSearchBar/search.svg';
import styles from './index.less';

const TopSearchBar = () => {
  const [dynamicInputWidth, setDynamicInputWidth] = useState();
  const searchInputRef = useRef<{ current: React.ReactElement }>();
  const {
    searchValue,
    searchConversationInfo,
    globalSearchModalVisible,
    updateSearchInfo,
  } = useSearchInfoStore();

  useEffect(() => {
    const computeInputWidth = () => {
      if (searchInputRef?.current) {
        // 计算输入框宽度的逻辑
        const width = searchInputRef.current?.offsetWidth + 16;
        setDynamicInputWidth(width);
      }
    };
    // 初始化时计算一次宽度
    computeInputWidth();

    const throttledComputeInputWidth = throttle(computeInputWidth, 100);

    window.addEventListener('resize', throttledComputeInputWidth);

    return () => {
      window.removeEventListener('resize', throttledComputeInputWidth);
    };
  }, []);

  const openGlobalSearchModal = () => {
    updateSearchInfo({ globalSearchModalVisible: true });
  };

  const closeGlobalSearchModal = () => {
    setTimeout(() => {
      updateSearchInfo({ globalSearchModalVisible: false });
    }, 100);
  };

  const getPlaceholderStr = useMemo(() => {
    if (isEmpty(searchValue) && isEmpty(searchConversationInfo)) {
      return '搜索 Linkflow';
    } else {
      const conversationName = searchConversationInfo?.showName || '';
      let showName = '';
      if (!isEmpty(conversationName)) {
        showName = `in:${
          searchConversationInfo?.conversationType === SessionType.Single
            ? '@'
            : ''
        }${conversationName}`;
      }
      return `搜索：${showName}${searchValue}`;
    }
  }, [searchValue, searchConversationInfo]);

  return (
    <div className={styles.topBarContainer}>
      <div></div>
      <div className={styles.searchInput} ref={searchInputRef}>
        <Input
          placeholder={getPlaceholderStr}
          onFocus={openGlobalSearchModal}
          // onBlur={closeGlobalSearchModal}
        />
        <div className={styles.searchIconBox}>
          <img src={searchIcon} />
        </div>
      </div>

      <GlobalSearchModal
        open={globalSearchModalVisible}
        onClose={closeGlobalSearchModal}
        width={dynamicInputWidth}
      />
    </div>
  );
};
export default TopSearchBar;
