{"me": "I", "you": "You", "yes": "Yes", "no": "No", "confirm": "Confirm", "retry": "Retry", "cancel": "Cancel", "canceled": "Canceled", "pieces": "Pieces", "open": "Open", "close": "Close", "clear": "Clear", "answer": "Answer", "hangUp": "Hang up", "notStart": "Not started", "waitingStart": "Awaiting", "alreadyStarted": "Already", "finished": "Finished", "clickToView": "Click to view", "jumpToMessage": "Jump to message", "connect": {"syncing": "Syncing", "syncFailed": "Sync Failed", "connecting": "Connecting", "connectFailed": "Connect Failed"}, "toast": {"accessSuccess": "Operation successful!", "accessFailed": "Operation failed!", "getConversationFailed": "Failed to get conversation list!", "getThreadGroupFailed": "Failed to get threadGroup list!", "getThreadMessageFailed": "Failed to get threadMessage list!", "getGroupInfoFailed": "Failed to get group information!", "getGroupMemberFailed": "Failed to get group member information!", "getHistoryMessageFailed": "Failed to get history messages!", "getSelfInfoFailed": "Failed to get personal information!", "getFriendListFailed": "Failed to get friend list!", "getBlackListFailed": "Failed to get blacklist!", "getGroupListFailed": "Failed to get group list!", "getOnlineStatusFailed": "Failed to get online status!", "getMemberListFailed": "Failed to get group members!", "getUserInfoFailed": "Failed to get user information!", "pinConversationFailed": "Failed to modify pinned conversation status!", "setConversationRecvMessageOptFailed": "Failed to modify conversation message reception status!", "hideConversationFailed": "Failed to hide conversation!", "clearConversationMessagesFailed": "Failed to delete conversation messages!", "deleteConversationFailed": "Failed to delete conversation!", "updateGroupVerificationFailed": "Failed to modify group verification method!", "updateGroupInfoFailed": "Failed to modify group information!", "updateConversationPrivateStateFailed": "Failed to modify after-reading burn status!", "updateBlackStateFailed": "Failed to modify blacklist status!", "unfriendFailed": "Failed to unfriend!", "getMessageListFailed": "Failed to get message list!", "sendApplicationFailed": "Failed to send request!", "loading": "Logging in...", "syncing": "Syncing...", "syncFailed": "Sync failed!", "appUpdate": "App update", "updateVersion": "New version {{version}} found. Update now?", "updateContent": "Update content:", "isCancelUpdate": "Are you sure you want to cancel the update?", "clearChatHistory": "Clear chat history", "confirmClearChatHistory": "Are you sure you want to clear chat history?", "downloading": "Downloading...", "applyUpdateSuccess": "Download complete, effective after restart, are you sure you want to restart now?", "later": "Later", "applyDownloadSuccess": "Download complete, follow the installer to update!", "applyDownloadFailed": "Download failed!", "loginExpiration": "Current login has expired, please log in again!", "accountKicked": "Your account has logged in on another device, please log in again!", "updatePasswordSuccess": "Password updated successfully, please log in again!", "inputOldPassword": "Please enter your old password!", "passwordRules": "6-20 characters ,at least including numbers and letters", "reconfirmPassword": "Please confirm your password again", "passwordsDifferent": "Passwords do not match!", "confirmlogOut": "Are you sure you want to log out of the current account?", "updateAvatarFailed": "Failed to change avatar!", "updateAvatarSuccess": "Update avatar successfully", "notCanSendMessage": "Cannot send messages in a group chat that you have quit!", "groupMuted": "The administrator or group owner has muted everyone!", "currentMuted": "You have been muted by the administrator or group owner!", "messagesDeleteFailed": "Some messages failed to delete!", "confirmkickMember": "Are you sure you want to kick {{name}} from the group chat?", "addSuccess": "Added successfully!", "copySuccess": "Copied successfully!", "copyFailed": "Copy failed!", "confirmAllMuted": "Are you sure you want to mute everyone?", "confirmDisbandGroup": "Are you sure you want to disband this channel?", "confirmExitGroup": "Are you sure you want to exit this channel?", "confirmMoveBlacklist": "Are you sure you want to add this friend to the blacklist?", "userBlacked": "This user has been added to the blacklist!", "confirmUnfriend": "Are you sure you want to unfriend?", "selectLeastOne": "Please select at least one option!", "inputGroupName": "Please enter a group name", "inputContent": "Please enter a content", "sendSuccess": "Sent successfully!", "beyondSelectionLimit": "Selection limit exceeded!", "sendJoinGroupRequestSuccess": "Sent group join request successfully!", "inputNickName": "Please enter a nickname", "inputPhoneNumber": "Please enter phone number", "inputCorrectPhoneNumber": "Please enter a valid phone number!", "inputEmail": "Please enter email address", "inputCorrectEmail": "Please enter a valid email address!", "inputVerifyCode": "Please enter your verification code", "inputPassword": "Please enter your password", "sendFreiendRequestSuccess": "Friend request sent successfully!", "registerSuccess": "Registration successful!", "inputInvitationCode": "Please enter your invitation code", "downloadFailed": "Download failed!", "beingBlacklist": "The other party has added you to the blacklist!", "usingFriendVerification": "Friend verification is enabled, you are not yet their friend!", "confirmDeleteMoment": "Are you sure you want to delete this moment?", "uploadFailed": "Upload failed!", "somethingError": "Seems like there was a small issue~", "getLogFailed": "Failed to retrieve the log file!", "publishFailed": "Failed to publish!", "notCanAddFriend": "The other party has been set up not to be added as a friend!", "fileTypeError": "Unsupported file type or folder!", "setSelfInfoFailed": "Failed to set personal information!"}, "placeholder": {"conversation": "Conversation", "leaveMessage": "Leave a message", "recover": "<PERSON><PERSON> to recover", "someInCall": "{{count}} people are on a call", "compere": "Host", "watchUser": "Watch him only", "removeWatchUser": "Stop watching him only", "fold": "Fold", "participantCanUnmuteSelf": "Allow members to unmute themselves", "participantCanEnableVideo": "Allow members to enable video", "onlyHostShareScreen": "Only the host can share screen", "onlyHostInviteUser": "Only the host can invite participants", "joinDisableMicrophone": "Mute members upon joining", "member": "Member", "microphone": "Microphone", "camera": "Camera", "sharedScreen": "Share screen", "endSharing": "End sharing", "connecting": "Connecting...", "uploadSuccess": "Upload successful", "reportLog": "Report log", "reportSpecificLog": "Report specific log", "viewingLocalLogs": "View local logs", "totalNumPhoto": "Total of {{num}} photos", "somePersonLike": "{{num}} likes", "whoCanWatch": "Who can see", "remindWhoToWatch": "Remind who to see", "publishMomentsToast": "Thoughts at this moment...", "public": "Public", "publicToast": "Visible to everyone", "privacy": "Private", "privacyToast": "Visible to you only", "partlyVisible": "Visible to selected", "partlyVisibleToast": "Only selected can see", "hiddenSome": "Hide from selected", "hiddenSomeToast": "Selected cannot see", "comment": "Comment", "hint": "Hint", "toLike": "Like", "byLike": "Liked your post", "mentioned": "Mentioned", "atYou": "Mentioned you", "replied": "Replied", "publishImages": "Post photos", "publishVideo": "Post video", "details": "Details", "messageDestruct": "Automatically delete messages regularly", "messageDestructTime": "Set time to auto-delete messages", "messageDestructToast": "Messages sent after this will be deleted after the set time", "moments": "Moments", "somePerson": "{{num}} people", "selectMember": "Select member", "setInfo": "Set information", "regain": "Retrieve again", "optional": "(Optional)", "invitationCode": "Invitation code", "getBack": "Return", "nextStep": "Next step", "register": "Register new user", "registerToast": "Don't have an account yet?", "toRegister": "Register now", "login": "<PERSON><PERSON>", "pleaseEnterSendTo": "Please enter sent to", "verifyCode": "Verification code", "verifyValidity": "6-digit verification code, valid for 10 minutes", "sendVerifyCode": "Send verification code", "qrCodeLogin": "QR code login", "qrCodeLoginTitle": "Please scan the QR code using OpenCorp mobile", "welcome": "Welcome to OpenCorp", "title": "Online office collaboration", "subTitle": "Collaborative work for efficient office tasks", "myCreated": "I created", "myJoined": "I joined", "newFriends": "New friends", "groupNotification": "Group notification", "friendVerification": "Friend verification", "groupInfo": "Group information", "personalInfo": "Personal information", "inviteToGroup": " Invite to group", "qrCodeToGroup": "Join group by scanning QR code", "selectIDToGroup": "Search group ID", "remark": "Remark", "email": "Email", "phoneNumber": "Phone Number", "verifyPhoneNumber": "Verify phone number", "verifyEmail": "Verify email", "birth": "Birthday", "gender": "Gender", "man": "Male", "female": "Female", "unknown": "Unknown", "nickName": "Nickname", "groupNickName": "Group Nickname", "joinGroupTime": "Joined Time", "joinGroupMode": "Joined Method", "editInfo": "Edit profile", "message": "Chat information", "relevantMessage": "{{count}} related chat records", "overview": "Overview", "latestChat": "Recent Chats", "myFriend": "My Friends", "myGroup": "My Groups", "groupName": "Group Name", "groupAvatar": "Group Avatar", "groupMember": "Group Members", "selected": "Selected", "moveBlacklist": "Add to Blacklist", "willFilterThisUserMessage": "You will not receive messages from this friend.", "unfriend": "Unfriend", "setStarFriend": "Set as <PERSON><PERSON> Friend", "shieldConversation": "Block this Conversation", "privateChat": "Burn after Reading", "privateChatTime": "Burn after Reading Time", "isRead": "Read", "isReadList": "Message Read List", "allIsRead": "All Read", "isReadNum": "{{num}} have read", "unreadNum": "{{num}} unread", "unread": "Unread", "revoke": "Revoke", "copy": "Copy", "check": "Multiple Selection", "sendMessage": "Send Message", "save": "Save", "group": "Group", "groupTppe": "Group Type", "workGroup": "Work Group", "myGroupNickname": "My Group Nickname", "notNotify": "Mute Notifications", "allMuted": "Mute All", "allMutedClose": "Unmute All", "groupVerification": "Group Verification", "groupMemberPermissions": "Group Member Permissions", "transferGroup": "Transfer Channel", "exitGroup": "Leave Channel", "exitGroupFailed": "Failed to leave channel", "exitGroupToast": "After exiting, you will no longer receive messages from this channel.", "disbandGroup": "Disband Channel", "disbandGroupToast": "All group members will be removed.", "and": "{{someone}} and {{otherone}}", "groupOwner": "Group Owner", "administrator": "Administrator", "setAdministrator": "Set as Administrator", "mute": "Mute", "cancelMute": "Unmute", "setMute": "<PERSON>", "onlyManageCanSend": "Only group owner and administrators can send messages.", "publish": "Publish", "edit": "Edit", "call": "Call", "file": "File", "document": "Documents", "emoji": "<PERSON><PERSON><PERSON>", "image": "Image", "video": "Video", "card": "Card", "personalCard": "Personal Card", "screenshot": "Screenshot", "sticky": "<PERSON>y on Top", "removeSticky": "Remove from Top", "draft": "Draft", "pleaseEnter": "Please Enter", "enterGroupName": "Choose a group name for easy to searches", "clickToModify": "Click to Modify", "search": "Search", "about": "About Us", "password": "Password", "forgetPassword": "Forget Password", "changePassword": "Change Password", "checkNewVersion": "Check for New Version", "viewMore": "View More", "add": "Add", "remove": "Remove", "delete": "Delete", "close": "Close", "finder": "View", "blackList": "Contact Blacklist", "oldPassword": "Old Password:", "newPassword": "New Password:", "confirmPassword": "Confirm Password:", "chat": "Messages", "contact": "Contacts", "contacts": "Contacts", "myInfo": "My Information", "accountSetting": "Account <PERSON><PERSON>", "logOut": "Log Out", "personalSetting": "Personal Settings", "chooseLanguage": "Choose Language", "closeButtonEvent": "Event when clicking the close button", "exitApplication": "Exit Application", "minimize": "Minimize to Tray", "messageToast": "Message Alert", "messageAllowBeep": "New Message Sound Alert", "messageNotNotify": "Do Not Disturb Mode", "addFriends": "Add Friends", "verifyAdd": "Add", "addGroup": "Add Group", "createGroup": "Create Group Chat", "createNow": "Create Now", "createGroupToast": "Create a group to start online collaboration.", "addFriendsSetting": "Add Friend Settings", "refuseAddFriend": "Disallow Adding Me as a Friend", "markAsRead": "<PERSON> <PERSON>", "removeConversation": "Remove Conversation", "send": "Send", "sendTo": "Send To:", "groupAnnouncement": "Group Announcement", "noGroupAnnouncement": "No group announcements yet~", "needManageEdit": "Only the group owner and administrators can edit", "loosenToSend": "Release to Send", "reEdit": "Re-edit", "multipleNewMessages": "{{count}} new messages", "reply": "Reply", "sendShortcutkey": "Enter to Send/Shift+Enter for New Line", "sendWithEnter": "Send with <PERSON><PERSON>", "sendWithShiftEnter": "Send with Shift+Enter", "mentionAll": "Everyone", "forward": "Forward", "mergeForward": "Merge and Forward", "messageHistory": "Chat History", "whosMessageHistory": "Chat History for {{who}} ", "launch": "Launched by", "launchPerson": "Launcher", "inviteYou": "Invites you for", "videoCall": "Video Call", "voiceCall": "Voice Call", "meeting": "Video Meeting", "meetingSetting": "Meeting Settings", "someInMeeting": "{{count}} people are in the meeting", "finishMeeting": "End meeting", "leaveMeeting": "Leave meeting", "meetingName": "Meeting topic", "initiatedMeeting": "Initiated meeting", "meetingTime": "Meeting duration", "meetingID": "Meeting ID", "meetingDetail": "Meeting details", "changeMeetingDetail": "Change meeting details", "underWay": "In progress", "joinCall": "Join call", "joinMeeting": "Join meeting", "quickMeeting": "Quick meeting", "orderMeeting": "Book a meeting", "orderSuccess": "Scheduled successfully", "callTimeout": "Unanswered after timeout", "callTime": "Call duration", "handleByOtherDevice": "Handled on another device", "screenshotHideWindow": "Hide current window screenshot", "historyList": "History", "memberList": "Group member list", "meetingInvitation": "Call invitation", "invitation": "Invite", "share": "Share contact card", "selectUser": "Select user", "setting": "Settings", "offLine": "Offline", "online": "Online", "typing": "Typing...", "kick": "Kick out", "kickMember": "Kick out member", "forbidLookMemberInfo": "Forbidden to view other group member's details", "forbidAddMember": "Forbidden to add group members as friends", "applyNeedInvite": "Group member invitation doesn't need verification", "applyNeedVerification": "Verification message required", "applyAll": "Allow everyone to join the group"}, "empty": {"noMoments": "No moments available", "noMoreMessage": "No more messages", "showAllResults": "All results displayed!", "noSearchResults": "No relevant results found!", "fileContentEmpty": "File is empty!"}, "messageDescription": {"imageMessage": "[Image]", "voiceMessage": "[Voice]", "fileMessage": "[File]{{file}}", "locationMessage": "[Location]{{location}}", "videoMessage": "[Video]", "cardMessage": "[Card]", "faceMessage": "[Emoji]", "rtcMessage": "[Call]", "quoteMessage": "[Quoted Message]", "massMessage": "[Group Notification]", "mergeMessage": "[Chat History]", "callMessage": "[Call]", "meetingMessage": "[Meeting Invitation]", "customMessage": "[Custom Message]", "notSupMessage": "[Unsupported Message Type]", "drftPrefix": "[Draft]", "atAllPrefix": "[Everyone]", "atYouPrefix": "[Someone @you]", "unreadCount": "[{{count}} messages]", "catchMessage": "[Unsupported Message Type]", "forwardMessage": "[Forward] {{additional}}", "addtionalCardMessage": "[Card] {{additional}}", "quoteMessageRevoke": "The cited content has been deleted", "groupAnnouncementPrefix": "[Group Announcement]", "revokeMessage": "{{revoker}} recalled a message", "advanceRevokeMessage": "{{operator}} recalled a message sent by {{revoker}}", "alreadyFriendMessage": "You are now friends and can start chatting!", "createGroupMessage": "{{creator}} created a group chat", "updateGroupInfoMessage": "{{operator}} updated the group info", "updateGroupNameMessage": "{{operator}} changed the group name to {{name}}", "updateGroupAnnouncementMessage": "{{operator}} updated the group announcement", "transferGroupMessage": "{{owner}} transferred group ownership to {{newOwner}}", "quitGroupMessage": "{{name}} quit the group chat", "invitedToGroupMessage": "{{operator}} invited {{invitedUser}} to join the group chat", "kickInGroupMessage": "{{operator}} was removed from the group by {{kickedUser}}", "joinGroupMessage": "{{name}} joined the group chat", "disbanedGroupMessage": "{{operator}} disbanded the group chat", "allMuteMessage": "{{operator}} enabled group mute", "cancelAllMuteMessage": "{{operator}} disabled group mute", "singleMuteMessage": "{{name}} was muted by {{operator}} for {{muteTime}}", "cancelSingleMuteMessage": "{{name}} had their mute canceled by {{operator}}", "burnReadStatus": "Burn after reading is {{status}}"}, "time": {"day": "Day", "week": "Week", "month": "Month", "today": "Today", "yesterday": "Yesterday", "startTime": "Start Time"}, "date": {"second": "{{num}} second(s)", "minute": "{{num}} minute(s)", "hour": "{{num}} hour(s)", "day": "{{num}} day(s)", "weeks": "{{num}} week(s)", "month": "{{num}} month(s)", "justNow": "Just now", "thisWeek": "This week", "thisMonth": "This month", "earlier": "Earlier", "custom": "Custom"}, "application": {"agree": "Agree", "refuse": "Refuse", "agreed": "Agreed", "refused": "Refused", "information": "Verification Information", "pending": "Pending Verification", "applyToJoin": "Apply to Join", "applyToAdd": "Apply to Add", "applyToFriend": "Applied to Add You as a Friend"}, "errCode": {"passwordError": "Password error", "accountNotExist": "Account doesn't exist", "phoneNumberRegistered": "Phone already register", "accountRegistered": "Account already registered", "operationTooFrequent": "Operation too frequent, please try again later", "verificationCodeError": "Incorrect verification code", "verificationCodeExpired": "Verification code expired", "verificationCodeErrorLimitExceed": "Verification code error limit exceeded, please try again later", "verificationCodeUsed": "Verification code already used", "invitationCodeUsed": "Invitation code already used", "invitationCodeNotExist": "Invitation code does not exist", "operationRestriction": "Operation restricted"}}