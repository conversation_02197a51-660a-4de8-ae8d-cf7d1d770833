import PCMPlayer from 'pcm-player';

export const playAudio = (text: string | undefined, doneCb?: () => any) => {
  const returnValue = new Promise<void>((resolve, reject) => {
    const player = new PCMPlayer({
      inputCodec: 'Int16',
      channels: 1,
      sampleRate: 16000,
      flushTime: 1000,
      fftSize: 2048,
      onstatechange: (node, e, type) => {
        console.log('sentences onstatechange', node, e, type);
      },
      onended: (node, e) => {
        // tts.stop();
        console.log('sentences onended', node, e);

        resolve();
      },
    });

    const pcmPlay = (isEnd: boolean, audio: any) => {
      if (!isEnd) {
        const segment = new Int8Array(audio.length); // 生成二进制8位数组
        for (let i = 0; i < audio.length; i++) {
          segment[i] = audio.charCodeAt(i); // 获取字符的Unicode 编码
        }
        // const audioBuffer = new AudioBuffer(segment.buffer);
        player.feed(segment.buffer);
      }
    };
    // @ts-expect-error
    // const tts = new Tts(audioSave);
    const tts = new Tts(pcmPlay);
    tts.onError(function (obj: any) {
      tts.stop();
      //   uninitMediaSource();
      //   audioPlayOver();
      //   isStart = false;
      console.log(obj);
      //   alert(JSON.stringify(obj));
    });
    tts.start(
      `${location.origin}/htscai/ws3/tts`,
      {
        svc: 'tts',
        auf: '4',
        vid: '500861',
        aue: 'raw',
        type: '1',
        uid: '660Y5r',
        appid: 'pc20onli',
        extend_params: '{"params":"spd=0,token=anhui,ability=ab_tts"}',
      },
      text,
      false
    );
  });

  return returnValue;
};
