export const KeyboardKeys = {
  ENTER: 'Enter',
  ARROW_UP: 'ArrowUp',
  ARROW_DOWN: 'ArrowDown',
} as const;

export const KeyboardEventSource = {
  MESSAGE_INPUT: 'messageInput',
  COMMAND_LIST: 'commandList',
  ROBOT_COMMAND: 'robotCommand',
  COMMAND_FORM: 'commandForm',
} as const;

// 所有能打出字符的按键code
export const charKeyCodes = [
  9, // Tab
  32, // 空格 (Space)
  48, // 0
  49, // 1
  50, // 2
  51, // 3
  52, // 4
  53, // 5
  54, // 6
  55, // 7
  56, // 8
  57, // 9
  65, // A
  66, // B
  67, // C
  68, // D
  69, // E
  70, // F
  71, // G
  72, // H
  73, // I
  74, // J
  75, // K
  76, // L
  77, // M
  78, // N
  79, // O
  80, // P
  81, // Q
  82, // R
  83, // S
  84, // T
  85, // U
  86, // V
  87, // W
  88, // X
  89, // Y
  90, // Z
  186, // ; (分号)
  187, // = (等号)
  188, // , (逗号)
  189, // - (减号/连字符)
  190, // . (句号)
  191, // / (斜杠)
  192, // ` (反引号)
  219, // [ (左方括号)
  220, // \ (反斜杠)
  221, // ] (右方括号)
  222, // ' (单引号)
  96, // 小键盘 0
  97, // 小键盘 1
  98, // 小键盘 2
  99, // 小键盘 3
  100, // 小键盘 4
  101, // 小键盘 5
  102, // 小键盘 6
  103, // 小键盘 7
  104, // 小键盘 8
  105, // 小键盘 9
  106, // 小键盘 *
  107, // 小键盘 +
  109, // 小键盘 -
  110, // 小键盘 .
  111, // 小键盘
  229, // 中文输入法按键
];

export const AILoadingText = 'AI正在生成中...';
