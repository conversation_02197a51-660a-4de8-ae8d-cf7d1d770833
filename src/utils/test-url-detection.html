<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试URL自动识别功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .test-case {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-case h3 {
            margin-top: 0;
            color: #333;
        }
        .before, .after {
            margin: 10px 0;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 3px;
            word-break: break-all;
        }
        .before {
            background: #fff3cd;
        }
        .after {
            background: #d4edda;
        }
        span {
            background: #e7f3ff;
            padding: 2px 4px;
            border-radius: 2px;
            margin: 0 2px;
        }
        a {
            color: #007bff;
            text-decoration: underline;
            margin: 0 2px;
        }
        a[style*="display: none"] {
            background: #ffcccc;
        }
    </style>
</head>
<body>
    <h1>URL自动识别功能测试</h1>
    
    <div class="test-case">
        <h3>测试用例 1: 原始复杂文本场景</h3>
        <div class="before">
            <strong>处理前:</strong>
            <div id="test1-before">
                我没办法直接登录平台去查找和复制报告URL呢。你可以按照上述步骤在devInsight平台（http://eip.htsc.com.cn/devinsight）啦你好你好你好http://eipdev.htsc.com.cn/linkflow/chat上找到符合要求分析指标在个以下的洞察报告然后把报告的提供给我我就能调用报告解读工具为你解读报告啦你好你好你好
            </div>
        </div>
        <div class="after">
            <strong>处理后:</strong>
            <div id="test1-after"></div>
        </div>
    </div>

    <div class="test-case">
        <h3>测试用例 2: 多个URL混合文本</h3>
        <div class="before">
            <strong>处理前:</strong>
            <div id="test2-before">
                请访问 https://example.com/中文路径 查看详情，或者访问 http://google.com 搜索更多信息。还可以查看 https://test.com/测试页面 获取帮助。
            </div>
        </div>
        <div class="after">
            <strong>处理后:</strong>
            <div id="test2-after"></div>
        </div>
    </div>

    <div class="test-case">
        <h3>测试用例 3: 已有链接和文本URL混合</h3>
        <div class="before">
            <strong>处理前:</strong>
            <div id="test3-before">
                访问 <a href="https://existing.com">现有链接</a> 或者 https://new.com/新链接 查看更多。
            </div>
        </div>
        <div class="after">
            <strong>处理后:</strong>
            <div id="test3-after"></div>
        </div>
    </div>

    <div class="test-case">
        <h3>测试用例 4: 纯英文URL</h3>
        <div class="before">
            <strong>处理前:</strong>
            <div id="test4-before">
                Check out https://github.com/example/repo for more details.
            </div>
        </div>
        <div class="after">
            <strong>处理后:</strong>
            <div id="test4-after"></div>
        </div>
    </div>

    <div class="test-case">
        <h3>测试用例 5: 包含中文的URL</h3>
        <div class="before">
            <strong>处理前:</strong>
            <div id="test5-before">
                访问 https://中文网站.com/路径 了解更多信息。
            </div>
        </div>
        <div class="after">
            <strong>处理后:</strong>
            <div id="test5-after"></div>
        </div>
    </div>

    <script>
        // 复制优化后的函数
        const containsChinese = (text) => {
            return /[\u4e00-\u9fff]/.test(text);
        };

        const extractChinese = (text) => {
            return text.replace(/[^\u4e00-\u9fff\s]/g, '').trim();
        };

        const removeChinese = (text) => {
            return text.replace(/[\u4e00-\u9fff]/g, '').trim();
        };

        const URL_REGEX = /(https?:\/\/[^\s<>"']+)/gi;

        const containsURL = (text) => {
            return URL_REGEX.test(text);
        };

        const convertTextURLsToLinks = (container) => {
            const walker = document.createTreeWalker(
                container,
                NodeFilter.SHOW_TEXT,
                {
                    acceptNode: (node) => {
                        const parent = node.parentNode;
                        if (parent && parent.tagName.toLowerCase() === 'a') {
                            return NodeFilter.FILTER_REJECT;
                        }
                        return NodeFilter.FILTER_ACCEPT;
                    }
                }
            );

            const textNodes = [];
            let currentNode;
            
            while ((currentNode = walker.nextNode())) {
                if (currentNode.textContent && containsURL(currentNode.textContent)) {
                    textNodes.push(currentNode);
                }
            }

            textNodes.forEach(textNode => {
                const text = textNode.textContent || '';
                const parent = textNode.parentNode;
                
                if (!parent) return;

                URL_REGEX.lastIndex = 0;
                
                const parts = [];
                let lastIndex = 0;
                let match;

                while ((match = URL_REGEX.exec(text)) !== null) {
                    if (match.index > lastIndex) {
                        parts.push(text.substring(lastIndex, match.index));
                    }
                    
                    parts.push({ type: 'url', url: match[0] });
                    lastIndex = match.index + match[0].length;
                }
                
                if (lastIndex < text.length) {
                    parts.push(text.substring(lastIndex));
                }

                if (parts.length > 1) {
                    const fragment = document.createDocumentFragment();
                    
                    parts.forEach(part => {
                        if (typeof part === 'string') {
                            if (part) {
                                fragment.appendChild(document.createTextNode(part));
                            }
                        } else {
                            const link = document.createElement('a');
                            link.href = part.url;
                            link.textContent = part.url;
                            link.setAttribute('target', '_blank');
                            link.setAttribute('rel', 'noopener noreferrer');
                            fragment.appendChild(link);
                        }
                    });
                    
                    parent.replaceChild(fragment, textNode);
                }
            });
        };

        const processLinks = (container) => {
            // 第一步：将文本中的URL转换为链接标签
            convertTextURLsToLinks(container);
            
            // 第二步：处理所有链接标签
            const links = Array.from(container.querySelectorAll('a'));
            
            links.forEach((a) => {
                try {
                    if (!a.getAttribute('target')) {
                        a.setAttribute('target', '_blank');
                        a.setAttribute('rel', 'noopener noreferrer');
                    }

                    const linkText = a.textContent?.trim() || '';
                    const href = a.getAttribute('href')?.trim() || '';

                    if (linkText === href && containsChinese(linkText)) {
                        const chinesePart = extractChinese(linkText);
                        const nonChinesePart = removeChinese(linkText);
                        const cleanHref = removeChinese(href);

                        if (chinesePart && a.parentNode) {
                            const chineseSpan = document.createElement('span');
                            chineseSpan.textContent = chinesePart;

                            if (a.nextSibling) {
                                a.parentNode.insertBefore(chineseSpan, a.nextSibling);
                            } else {
                                a.parentNode.appendChild(chineseSpan);
                            }

                            a.textContent = nonChinesePart;
                            if (cleanHref !== href && cleanHref) {
                                a.setAttribute('href', cleanHref);
                            }

                            if (!nonChinesePart) {
                                a.style.display = 'none';
                            }
                        }
                    }
                } catch (error) {
                    console.warn('处理链接时出现错误:', error, a);
                }
            });
        };

        // 运行测试
        function runTests() {
            for (let i = 1; i <= 5; i++) {
                const beforeElement = document.getElementById(`test${i}-before`);
                const afterElement = document.getElementById(`test${i}-after`);
                afterElement.innerHTML = beforeElement.innerHTML;
                processLinks(afterElement);
            }
        }

        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
