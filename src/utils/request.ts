import { extend } from 'umi-request';
import { t } from 'i18next';
import { v4 as uuidv4 } from 'uuid';

import { useUserStore } from '@/store';
import { message } from '@ht/sprite-ui';
import { ErrCodeMap } from '@/constants';

import { getChatToken, getIMToken } from './storage';
import { feedbackToast } from './common';

const tokenErrorCodeList = [1501, 1503, 1504, 1505];

interface ErrorData {
  errCode: number;
  errMsg?: string;
}
const errorHandler = (err: unknown) => {
  const errData = err as ErrorData;
  if (errData.errMsg) {
    message.error(ErrCodeMap[errData.errCode] || errData.errMsg);
  }
};

const request = extend({
  errorHandler, // 默认错误处理
  credentials: 'include', // 默认请求是否带上cookie
  timeout: 25000,
  prefix: '',
});

request.interceptors.request.use((url, options) => {
  let newHeaders = {};
  if (
    location.href.includes('localhost') ||
    location.href.includes('127.0.0.1')
  ) {
    newHeaders = {
      ...newHeaders,
      'X-Eipgw-Token': localStorage.getItem('eipgw-token'),
    };
  }

  return {
    url,
    options: {
      ...options,
      headers: {
        ...options.headers,
        ...newHeaders,
      },
    },
  };
});

export default request;
