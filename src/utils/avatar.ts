import { useUserStore } from '@/store';
import { MessageItem } from '@ht/openim-wasm-client-sdk';

export const avatarList = [
  {
    src: new URL('@/assets/avatar/ic_avatar_01.png', import.meta.url).href,
    name: 'ic_avatar_01',
  },
  {
    src: new URL('@/assets/avatar/ic_avatar_02.png', import.meta.url).href,
    name: 'ic_avatar_02',
  },
  {
    src: new URL('@/assets/avatar/ic_avatar_03.png', import.meta.url).href,
    name: 'ic_avatar_03',
  },
  {
    src: new URL('@/assets/avatar/ic_avatar_04.png', import.meta.url).href,
    name: 'ic_avatar_04',
  },
  {
    src: new URL('@/assets/avatar/ic_avatar_05.png', import.meta.url).href,
    name: 'ic_avatar_05',
  },
  {
    src: new URL('@/assets/avatar/ic_avatar_06.png', import.meta.url).href,
    name: 'ic_avatar_06',
  },
];

export const getDefaultAvatar = (name: string) => {
  return avatarList.find((avator) => avator.name === name)?.src;
};

export const getRandomDefaultAvatar = (userID?: string) => {
  let propLength = 0;
  if (userID != null) {
    propLength = userID.length;
  }
  const randomIndex = Math.floor(propLength % avatarList.length);

  return avatarList[randomIndex].src;
};

// 查的时候加点缓存，这样就不用通过img的onError来试错了，减少点404
export const getAvatarUrl = (userID?: string) => {
  if (userID == null || userID === '') {
    return getRandomDefaultAvatar();
  } else if (hasRandomAvatarUserList.includes(userID)) {
    return getRandomDefaultAvatar(userID);
  } else if (hasValidAvatarUserList.includes(userID)) {
    return `/linkFlowService/openim/avatar/${userID}?timestamp=${
      window.syncFinshTimeStamp || ''
    }`;
  } else {
    return `/linkFlowService/openim/avatar/${userID}?timestamp=${
      window.syncFinshTimeStamp || ''
    }`;
  }
};

export const getAvatarUrlFromMessage = (message: MessageItem) => {
  return getAvatarUrl(message?.sendID);
};

// 规避一些头像取随机时的闪烁问题
const hasValidAvatarUserList: string[] = [];

// 规避一些头像取随机时的闪烁问题
const hasRandomAvatarUserList: string[] = [];

const insertNewUserIdList = (list: string[], userID?: string) => {
  if (userID == null || userID === '') {
    return;
  }
  if (list.includes(userID)) {
  } else if (list.length <= 1000) {
    list.push(userID);
  } else {
    list.shift();
    list.push(userID);
  }
};

export const insertNewValidUserIdList = (userID?: string) => {
  return insertNewUserIdList(hasValidAvatarUserList, userID);
};

export const insertNewRandomUserIdList = (userID?: string) => {
  return insertNewUserIdList(hasRandomAvatarUserList, userID);
};

export enum userRolesType {
  employees = 'employees',
  developers = 'developers',
  robot = 'robot',
}

/**
 * 根据用户id判断当前角色
 * @param userId 用户id
 * @returns 用户角色
 */
export const getUserRoles = (userId: string) => {
  // 自有人员ID规则：34位长度字符串 比如：2deecdbee704205b7ed61bd985e79b9b08
  // 其他账号ID规则：36位长度字符串
  // 其中：
  // bot以2000开头 例如：200010893c3e933e5518878cf34fb63c7714
  // 开发商以100开头 例如：100010893c3e933e5518878cf34fb63c7714
  if (userId && userId?.length === 34) {
    return userRolesType.employees;
  } else if (userId?.startsWith('2000')) {
    return userRolesType.robot;
  } else {
    return userRolesType.developers;
  }
};

// 是否机器人用户
export const isBotUser = (userId: string) => {
  const currentRole = getUserRoles(userId);
  return currentRole === userRolesType.robot;
};
