// 运行时代码。使用 spriteui prefix 时，对于 notification, message 需要手动设置 prefixCls; 如果希望全局设置，可以放在这里

import { notification, message, ConfigProvider } from '@ht/sprite-ui';
import BaseSettings from '@config/BaseSettings';
import '@/components/Crepe/theme/common/style.css';
import '@/components/Crepe/theme/frame/style.css';
import './global.less';

notification.config({
  prefixCls: `${BaseSettings.appName}-notification`,
});

message.config({
  prefixCls: `${BaseSettings.appName}-message`,
});

ConfigProvider.config({
  prefixCls: BaseSettings.appName,
});

let fonts = decodeURIComponent(
  location.search
    .substring(1)
    .toLowerCase()
    .split('&')
    .filter((e) => e.startsWith('font='))[0] || ''
).replace('font=', '');
// if (!fonts) {
//   fonts = localStorage.getItem('linkim.props.fonts') || '';
// } else {
//   localStorage.setItem('linkim.props.fonts', fonts);
// }
if (!fonts) {
  fonts = 'PingFangSC-Regular';
}
document.getElementsByTagName('body')[0].style.fontFamily = fonts;
