/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-lines */
import { IMSDK } from '@/layouts/BasicLayout';
import { MessageItem } from '@ht/openim-wasm-client-sdk';
import { base64toFile, canSendImageTypeList } from '@/utils/common';
import { v4 as uuidv4 } from 'uuid';

export interface FileWithPath extends File {
  path?: string;
}

export function useFileMessage() {
  const getImageMessage = async (file: FileWithPath) => {
    const { width, height } = await getPicInfo(file);
    const baseInfo = {
      uuid: uuidv4(),
      type: file.type,
      size: file.size,
      width,
      height,
      url: URL.createObjectURL(file),
    };

    const options = {
      sourcePicture: baseInfo,
      bigPicture: baseInfo,
      snapshotPicture: baseInfo,
      sourcePath: '',
      file,
    };

    return (await IMSDK.createImageMessageByFile(options)).data;
  };

  const getVideoMessage = async (
    file: FileWithPath,
    snapShotFile: FileWithPath
  ) => {
    const { width, height } = await getPicInfo(snapShotFile);

    const options = {
      videoFile: file,
      snapshotFile: snapShotFile,
      videoPath: '',
      duration: await getMediaDuration(URL.createObjectURL(file)),
      videoType: file.type,
      snapshotPath: '',
      videoUUID: uuidv4(),
      videoUrl: '',
      videoSize: file.size,
      snapshotUUID: uuidv4(),
      snapshotSize: snapShotFile.size,
      snapshotUrl: URL.createObjectURL(snapShotFile),
      snapshotWidth: width,
      snapshotHeight: height,
      snapShotType: snapShotFile.type,
    };
    return (await IMSDK.createVideoMessageByFile(options)).data;
  };

  const getFileMessage = async (file: FileWithPath) => {
    const options = {
      file,
      filePath: '',
      fileName: file.name,
      uuid: uuidv4(),
      sourceUrl: '',
      fileSize: file.size,
      fileType: file.type,
    };
    return (await IMSDK.createFileMessageByFile(options)).data;
  };

  const createFileMessage = async (
    file: FileWithPath
  ): Promise<MessageItem> => {
    const isImage = canSendImageTypeList.includes(getFileType(file.name));
    // const isVideo = file?.type.includes('video');
    // const isSound = file?.type.includes('audio');
    if (window.electronAPI) {
      if (isImage) {
        return (await IMSDK.createImageMessageFromFullPath(file.path || ''))
          .data;
      } else {
        return (await IMSDK.createFileMessageFromFullPath(file.path || ''))
          .data;
      }
    }
    if (isImage) {
      return getImageMessage(file);
    }
    // if (isVideo) {
    //   const snapShotFile = await getVideoSnshotFile(file);
    //   return getVideoMessage(file, snapShotFile);
    // }
    // if (isSound) {
    //   return getSoundMessage(file);
    // }
    return getFileMessage(file);
  };

  // 为Bot对话场景创建文件消息的方法
  const createFileMessageForBot = async (
    file: FileWithPath
  ): Promise<MessageItem> => {
    const isImage = canSendImageTypeList.includes(getFileType(file.name));

    if (isImage) {
      // 对于图片，调用 createImageMessage
      return getImageMessage(file);
    } else {
      // 对于其他文件，调用 createFileMessage
      return getFileMessage(file);
    }
  };

  const getFileType = (name: string) => {
    const idx = name.lastIndexOf('.');
    return name.slice(idx + 1);
  };

  const getPicInfo = (file: File): Promise<HTMLImageElement> =>
    new Promise((resolve, reject) => {
      const _URL = window.URL || window.webkitURL;
      const img = new Image();
      img.onload = function () {
        resolve(img);
      };
      img.src = _URL.createObjectURL(file);
    });

  const getVideoSnshotFile = (file: File): Promise<File> => {
    const url = URL.createObjectURL(file);
    // eslint-disable-next-line promise/param-names
    return new Promise((reslove, reject) => {
      const video = document.createElement('VIDEO') as HTMLVideoElement;
      video.setAttribute('autoplay', 'autoplay');
      video.setAttribute('muted', 'muted');
      video.innerHTML = `<source src="${url}" type="audio/mp4">`;
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      video.addEventListener('canplay', () => {
        const anw = document.createAttribute('width');
        // @ts-expect-error
        anw.nodeValue = video.videoWidth;
        const anh = document.createAttribute('height');
        // @ts-expect-error
        anh.nodeValue = video.videoHeight;
        canvas.setAttributeNode(anw);
        canvas.setAttributeNode(anh);
        // @ts-expect-error
        ctx.drawImage(video, 0, 0, video.videoWidth, video.videoHeight);
        const base64 = canvas.toDataURL('image/png');
        video.pause();
        const file = base64toFile(base64);
        reslove(file);
      });
    });
  };

  const getMediaDuration = (path: string): Promise<number> =>
    new Promise((resolve) => {
      const vel = new Audio(path);
      vel.onloadedmetadata = function () {
        resolve(Number(vel.duration.toFixed()));
      };
    });

  const getSoundMessage = async (file: FileWithPath) => {
    const options = {
      soundPath: '',
      duration: 6,
      uuid: uuidv4(),
      sourceUrl: '',
      dataSize: file.size,
      soundType: file.type,
      file,
    };
    return (await IMSDK.createSoundMessageByFile(options)).data;
  };

  return {
    getImageMessage,
    getVideoMessage,
    getFileMessage,
    getSoundMessage,
    createFileMessage,
    createFileMessageForBot,
    getVideoSnshotFile,
  };
}
