import { useCallback, useRef, useState } from 'react';
import { MessageItem, ConversationItem } from '@ht/openim-wasm-client-sdk';
import { debounce } from 'lodash';
import { VirtuosoHandle } from '@ht/react-virtuoso';

interface UseMessageScrollProps {
  virtuosoRef: React.RefObject<VirtuosoHandle>;
  conversation?: ConversationItem;
  onMarkAsRead?: () => void;
}

export const useMessageScroll = ({
  virtuosoRef,
  conversation,
  onMarkAsRead,
}: UseMessageScrollProps) => {
  const [hasScrolled, setHasScrolled] = useState(false);
  const prevListLengthRef = useRef<number>(0);

  // 初始化时立即滚动到底部
  const scrollToBottomImmediate = useCallback(() => {
    virtuosoRef.current?.scrollToIndex({
      index: 'LAST',
      align: 'end',
      behavior: 'auto',
    });
    onMarkAsRead?.();
  }, [onMarkAsRead, virtuosoRef]);
  // }, [virtuosoRef]);
  const scrollToBottom = useCallback(() => {
    virtuosoRef.current?.scrollToIndex({
      index: 'LAST',
      align: 'end',
      behavior: 'smooth',
    });
    onMarkAsRead?.();
  }, [onMarkAsRead, virtuosoRef]);

  // 平滑滚动到底部
  const scrollToBottomSmooth = useCallback(() => {
    let count = 0;
    const interval = setInterval(() => {
      scrollToBottom();
      count++;
      if (count >= 2) {
        clearInterval(interval);
      }
    }, 500);

    onMarkAsRead?.();
  }, [onMarkAsRead, scrollToBottom]);
  // }, [virtuosoRef]);

  // 处理消息列表变化
  const handleMessagesChange = useCallback(
    (messages: MessageItem[], isSelfMessage = false) => {
      if (!messages.length) {
        return;
      }

      const lengthDiff = messages.length - prevListLengthRef.current;

      // 新消息条件：消息数量变化不超过2条 且 (未向上滚动 或 是自己发送的消息)
      if (
        lengthDiff > 0 &&
        lengthDiff <= 2 &&
        (!hasScrolled || isSelfMessage)
      ) {
        setTimeout(() => {
          scrollToBottom();
        }, 30);
      }
      prevListLengthRef.current = messages.length;
    },
    [hasScrolled, scrollToBottomSmooth]
  );

  // 接收到新消息时，会立刻触发一次不触底，scrollToBottom后再触发滚到底，导致button会闪现，因此这里加一个debounce
  const updateScrollState = useCallback(
    debounce((atBottomState: boolean) => {
      setHasScrolled(!atBottomState);
    }, 500),
    []
  );

  const handleNearBottom = useCallback(
    (atBottomState) => {
      console.debug('handleNearBottom', atBottomState);
      updateScrollState(atBottomState);
      if (atBottomState) {
        onMarkAsRead?.();
      }
    },
    [updateScrollState, onMarkAsRead]
  );

  return {
    hasScrolled,
    scrollToBottomImmediate,
    scrollToBottomSmooth,
    handleMessagesChange,
    handleNearBottom,
  };
};
