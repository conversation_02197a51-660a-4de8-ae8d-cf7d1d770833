import { IMSDK } from '@/layouts/BasicLayout';
import { CbEvents, UserOnlineState, WSEvent } from '@ht/openim-wasm-client-sdk';
import { isEmpty } from 'lodash';

// 与useronlinestatus组合使用，合并一些订阅请求，在页面代码中仅使用useronlineStatus即可，不要直接引入该hook

const SUBSCRIBE_FLUSH_WINDOW = 100; // 订阅的时间窗口
const UNSUBSCRIBE_DELAY = 5000; // 取消订阅的延迟时间窗口

type Callback = (data: UserOnlineState) => void;

// 订阅回调列表
const subscribers = new Map<string, Set<Callback>>();
// 引用计数
const refCount = new Map<string, number>();
// 最终操作意图和时间戳
const finalOps = new Map<
  string,
  { action: 'subscribe' | 'unsubscribe'; timestamp: number }
>();
// 最新在线状态缓存
const latestState = new Map<string, UserOnlineState>();

let isWSBound = false;
let subscribeFlushTimer: ReturnType<typeof setTimeout> | null = null;

const handler = ({ data }: WSEvent<UserOnlineState>) => {
  const { userID } = data;
  latestState.set(userID, data);
  const subs = subscribers.get(userID);
  if (subs?.size) {
    subs.forEach((cb) => cb(data));
  } else {
    console.error('No subscriber for userID:', userID);
  }
};

const scheduleSubscribeFlush = () => {
  if (subscribeFlushTimer != null) {
    return;
  }
  subscribeFlushTimer = setTimeout(() => {
    subscribeFlushTimer = null;
    flushFinalOps();
  }, SUBSCRIBE_FLUSH_WINDOW);
};

const flushFinalOps = async () => {
  const toSubscribe: string[] = [];
  const toUnsubscribe: string[] = [];

  for (const [userID, { action }] of finalOps.entries()) {
    if (action === 'subscribe') {
      toSubscribe.push(userID);
    } else if (action === 'unsubscribe') {
      toUnsubscribe.push(userID);
    }
  }

  finalOps.clear();

  if (toSubscribe.length) {
    const resData = await IMSDK.subscribeUsersStatus(toSubscribe);

    console.debug({ resData });
    if (resData.data != null && !isEmpty(resData.data)) {
      resData.data?.forEach((item) => {
        latestState.set(item.userID, item);
        const subs = subscribers.get(item.userID);
        if (subs?.size) {
          subs.forEach((cb) => cb(item));
        } else {
          console.error('No subscriber for userID:', item.userID);
        }
      });
    }
  }

  if (toUnsubscribe.length) {
    IMSDK.unsubscribeUsersStatus(toUnsubscribe).catch(console.warn);
  }
};

export const userStatusManager = {
  subscribe(userID: string, callback: Callback) {
    if (!userID) {
      return;
    }

    if (!subscribers.has(userID)) {
      subscribers.set(userID, new Set());
    }
    subscribers.get(userID)!.add(callback);

    const current = refCount.get(userID) || 0;
    refCount.set(userID, current + 1);

    if (current === 0) {
      const now = Date.now();
      finalOps.set(userID, { action: 'subscribe', timestamp: now });
      scheduleSubscribeFlush();
    }

    // 首次绑定 WS
    if (!isWSBound) {
      IMSDK.on(CbEvents.OnUserStatusChanged, handler);
      isWSBound = true;
    }

    const cached = latestState.get(userID);
    if (cached) {
      callback(cached);
    }
  },

  unsubscribe(userID: string, callback: Callback) {
    if (!userID) {
      return;
    }

    const subs = subscribers.get(userID);
    subs?.delete(callback);
    if (subs?.size === 0) {
      subscribers.delete(userID);
    }

    const current = (refCount.get(userID) || 1) - 1;

    if (current <= 0) {
      refCount.delete(userID);
      const now = Date.now();
      finalOps.set(userID, { action: 'unsubscribe', timestamp: now });

      setTimeout(() => {
        const latest = finalOps.get(userID);
        if (latest?.action === 'unsubscribe' && latest.timestamp === now) {
          flushFinalOps();
        }
      }, UNSUBSCRIBE_DELAY);
    } else {
      refCount.set(userID, current);
    }

    if (subscribers.size === 0 && isWSBound) {
      IMSDK.off(CbEvents.OnUserStatusChanged, handler);
      isWSBound = false;
    }
  },
};
