// eslint-disable-next-line eslint-comments/disable-enable-pair
/* eslint-disable max-lines */
import {
  MessageItem as OriginalMessageItem,
  MessageType,
  MessageStatus,
} from '@ht/openim-wasm-client-sdk';
import { useLatest, useRequest } from 'ahooks';
import { message as Message } from '@ht/sprite-ui';
import { useCallback, useEffect, useRef, useState } from 'react';
import { IMSDK } from '@/layouts/BasicLayout';
import emitter, { emit, UpdateMessaggeBaseInfoParams } from '@/utils/events';
import { useConversationStore, useUserStore } from '@/store';
import { cloneDeep } from 'lodash';
import { getDateContent, dateISDifferenceFiveMinutes } from '@/utils/date';
import {
  getConversationIDByMsg,
  mergeMessagesByClientMsgID,
} from '@/utils/utils';
import { shallow } from 'zustand/shallow';
import { sampleStreamMessage } from '@/mock/streamMessageData';

// 扩展MessageItem类型以包含frontendExtension属性
interface MessageItem extends OriginalMessageItem {
  frontendExtension?: {
    data: string;
    description: string;
    extension: string;
  };
}

const START_INDEX = 10000;
const SPLIT_COUNT = 30;
// eslint-disable-next-line max-statements
export function useHistoryMessageList(conversation: any) {
  const currentConversation = conversation || {};
  const { userID: selfUserID } = useUserStore.getState().selfInfo;

  // useConversationStore((state) => state.currentConversation) || {};
  const targetMsg = useConversationStore((state) => state.targetMsg);

  const updateTargetMsg = useConversationStore(
    (state) => state.updateTargetMsg
  );

  const getCurrentMessageUpInfo = useConversationStore(
    (state) => state.getCurrentMessageUpInfo
  );

  const { conversationID, unreadCount, groupID, userID, ex } =
    currentConversation;

  const [loadState, setLoadState] = useState({
    initLoading: true,
    hasMoreOld: true,
    hasMoreOldReverse: true,
    messageList: [] as MessageItem[],
    firstItemIndex: START_INDEX,
    initialTopMostItemIndex: SPLIT_COUNT - 1,
  });
  const latestLoadState = useLatest(loadState);
  const minSeq = useRef(0);
  const [currentTargetMsg, setCurrentTargetMsg] = useState(targetMsg);

  const addDataSepatorToMessageList = (messageList: MessageItem[]) => {
    try {
      const messageListWithDataSeparator = messageList?.map(
        (item, index, array) => {
          const itemObj = { ...item } as MessageItem;
          // if (index === array.length - 1) {
          //   itemObj.customElem = {
          //     ...itemObj.customElem,
          //     data: JSON.stringify(sampleStreamMessage),
          //   };
          // }

          if (index >= 1) {
            if (
              !dateISDifferenceFiveMinutes(
                item.sendTime,
                array[index - 1].sendTime
              )
            ) {
              const currentDate = new Date(item.sendTime);
              itemObj.frontendExtension = {
                data: 'date-separator',
                description: getDateContent(currentDate),
                extension: '',
              };
            }
            return itemObj;
          } else {
            const currentDate = new Date(item.sendTime);
            itemObj.frontendExtension = {
              data: 'date-separator',
              description: getDateContent(currentDate),
              extension: '',
            };
            return itemObj;
          }
        }
      );
      return messageListWithDataSeparator;
    } catch (e) {
      return messageList;
    }
  };

  useEffect(() => {
    if (targetMsg.clientMsgID && targetMsg.clientMsgID.length > 0) {
      console.warn('setCurrentTargetMsg');
      setCurrentTargetMsg(targetMsg);
    }
  }, [targetMsg]);

  const fetchMessageContext = async (data: any, reqConversationID: string) => {
    let needShowList: MessageItem[] = [];
    const contextData = {
      isEnd: true,
      isReverseEnd: true,
    };

    if (targetMsg.seq != null && targetMsg.seq !== 0) {
      try {
        const resResRes = await IMSDK.getSurroundingMessageList({
          count: SPLIT_COUNT,
          seq: targetMsg.seq,
          conversationID: conversationID ?? '',
          viewType: 0,
        });

        console.debug(
          {
            count: SPLIT_COUNT,
            seq: targetMsg.seq,
            conversationID: conversationID ?? '',
            viewType: 0,
          },
          { resResRes }
        );

        needShowList = addDataSepatorToMessageList(resResRes.data.messageList);

        contextData.isEnd = resResRes.data.isEnd;
        contextData.isReverseEnd = resResRes.data.isReverseEnd;
      } catch (e) {
        console.error('resResRes', e);
      }
    }

    return { needShowList, contextData };
  };

  // 处理消息高亮
  const handleMessageHighlight = (needShowList: MessageItem[], data: any) => {
    let targetItemIndex = needShowList.findIndex(
      (msg) => msg.clientMsgID === data.clientMsgID
    );

    if (data.clientMsgID !== targetMsg.clientMsgID) {
      const currentTargetItemIndex = needShowList.findIndex(
        (msg) => msg.clientMsgID === targetMsg.clientMsgID
      );
      if (currentTargetItemIndex !== -1) {
        targetItemIndex = currentTargetItemIndex;
        needShowList[targetItemIndex].ex = 'needHighLight';
      }
    } else {
      needShowList[targetItemIndex].ex = 'needHighLight';
    }

    return targetItemIndex;
  };

  // 更新消息列表状态
  const updateMessageListState = (
    needShowList: MessageItem[],
    contextData: any,
    targetItemIndex: number
  ) => {
    setLoadState((preState) => ({
      ...preState,
      initLoading: false,
      hasMoreOld: targetMsg.seq === 0 ? false : !contextData.isEnd,
      hasMoreOldReverse: !contextData.isReverseEnd,
      messageList: [...needShowList],
      firstItemIndex: preState.firstItemIndex - needShowList.length,
      initialTopMostItemIndex: targetItemIndex,
    }));
  };
  const handleSeqMessage = async (isGotoFirseMessage = false) => {
    try {
      const { data } = await IMSDK.getSeqMessage({
        conversationID,
        seq: targetMsg.seq || 0,
      });
      if (data.status === MessageStatus.Recalled) {
        updateTargetMsg({});
        Message.info('该消息已删除');
        return;
      }
      const reqConversationID = conversationID;

      // 获取消息上下文
      const { needShowList, contextData } = await fetchMessageContext(
        data,
        reqConversationID
      );

      if (conversationID !== reqConversationID) {
        return;
      }

      // 处理消息高亮
      if (!isGotoFirseMessage) {
        const targetItemIndex = handleMessageHighlight(needShowList, data);
        // 更新状态
        setTimeout(() =>
          updateMessageListState(needShowList, contextData, targetItemIndex)
        );
      } else {
        // 跳转到第一条消息，不需要高亮
        const targetItemIndex = needShowList.findIndex(
          (msg) => msg.clientMsgID === data.clientMsgID
        );
        setTimeout(() =>
          updateMessageListState(needShowList, contextData, targetItemIndex)
        );
      }
    } catch (err) {
      loadHistoryMessages();
      console.error('跳转定位失败', err);
    }
    // updateTargetMsg({});
  };

  useEffect(() => {
    if (targetMsg?.seq != null && targetMsg?.clientMsgID == null) {
      // 跳转到第一条未读
      handleSeqMessage(true);
    } else if (targetMsg?.seq != null && targetMsg?.clientMsgID != null) {
      // 转发、引用类定位数据

      handleSeqMessage();
    } else {
      loadHistoryMessages();
    }
  }, [conversationID, ex]);

  useEffect(() => {
    const pushNewMessage = (message: MessageItem) => {
      // 检查消息是否已存在
      if (
        latestLoadState.current.messageList.find(
          (item) => item.clientMsgID === message.clientMsgID
        )
      ) {
        return;
      }

      // 检查消息是否属于当前会话
      const inCurrentGroupConversation =
        message?.groupID != null && message?.groupID === groupID;

      const inCurrentSingleConversation =
        message?.groupID == null &&
        ((message?.recvID === userID && message.sendID === selfUserID) ||
          (message?.recvID === selfUserID && message?.sendID === userID));

      const itemMessage = { ...message };
      const currentDate = new Date(message.sendTime);
      if (inCurrentGroupConversation || inCurrentSingleConversation) {
        setLoadState((preState) => {
          let result;
          if (preState?.messageList?.length > 0) {
            const previousMessage =
              preState.messageList[preState.messageList.length - 1];
            if (
              !dateISDifferenceFiveMinutes(
                itemMessage.sendTime,
                previousMessage.sendTime
              )
            ) {
              itemMessage.frontendExtension = {
                data: 'date-separator',
                description: getDateContent(currentDate),
                extension: '',
              };
            }
            result = {
              ...preState,
              messageList: [
                ...preState.messageList,
                {
                  ...itemMessage,
                },
              ],
            };
          } else {
            itemMessage.frontendExtension = {
              data: 'date-separator',
              description: getDateContent(currentDate),
              extension: '',
            };
            result = {
              ...preState,
              firstItemIndex: START_INDEX - 1,
              initialTopMostItemIndex: 0,
              hasMoreOld: false,
              hasMoreOldReverse: false,
              initLoading: false,
              messageList: [itemMessage],
            };
          }
          return result;
        });
      }
    };

    // 批量处理新消息
    const batchPushNewMessages = (messages: MessageItem[]) => {
      if (!messages?.length) {
        return;
      }

      // 创建现有消息ID的查找集合，提高查找效率
      const existingMessageIds = new Set(
        latestLoadState.current.messageList.map((item) => item.clientMsgID)
      );

      // 使用Set进行快速查找，过滤已存在的消息
      const newMessages = messages.filter(
        (message) => !existingMessageIds.has(message.clientMsgID)
      );

      if (!newMessages.length) {
        return;
      }

      // 过滤当前会话的消息
      const currentConversationMessages = newMessages.filter((message) => {
        // 提取条件判断为常量，避免重复计算
        const inCurrentGroupConversation =
          message?.groupID != null && message.groupID === groupID;

        const inCurrentSingleConversation =
          message?.groupID == null &&
          ((message.recvID === userID && message.sendID === selfUserID) ||
            (message.recvID === selfUserID && message.sendID === userID));

        return inCurrentGroupConversation || inCurrentSingleConversation;
      });

      if (!currentConversationMessages.length) {
        return;
      }

      // 一次性更新状态
      setLoadState((preState) => {
        // 预先缓存日期计算结果
        const getDateForMessage = (time: number) => {
          const currentDate = new Date(time);
          return {
            data: 'date-separator',
            description: getDateContent(currentDate),
            extension: '',
          };
        };

        if (preState?.messageList?.length > 0) {
          // 获取最后一条消息作为参考
          const lastMessage =
            preState.messageList[preState.messageList.length - 1];

          // 为每条消息添加日期分隔符
          const messagesWithSeparator = currentConversationMessages.map(
            (message, index) => {
              const itemMessage = { ...message };
              const referenceMessage =
                index === 0
                  ? lastMessage
                  : currentConversationMessages[index - 1];

              if (
                !dateISDifferenceFiveMinutes(
                  itemMessage.sendTime,
                  referenceMessage.sendTime
                )
              ) {
                itemMessage.frontendExtension = getDateForMessage(
                  itemMessage.sendTime
                );
              }
              return itemMessage;
            }
          );

          return {
            ...preState,
            messageList: [...preState.messageList, ...messagesWithSeparator],
          };
        } else {
          // 如果是第一条消息，直接添加日期分隔符
          const firstMessage = {
            ...currentConversationMessages[0],
          } as MessageItem;
          firstMessage.frontendExtension = getDateForMessage(
            firstMessage.sendTime
          );

          // 处理剩余消息的日期分隔符
          const restMessages = currentConversationMessages
            .slice(1)
            .map((message, index) => {
              const itemMessage = { ...message } as MessageItem;
              const prevMessage =
                index === 0 ? firstMessage : currentConversationMessages[index];

              if (
                !dateISDifferenceFiveMinutes(
                  itemMessage.sendTime,
                  prevMessage.sendTime
                )
              ) {
                itemMessage.frontendExtension = getDateForMessage(
                  itemMessage.sendTime
                );
              }
              return itemMessage;
            });

          return {
            ...preState,
            firstItemIndex: START_INDEX - 1,
            initialTopMostItemIndex: 0,
            hasMoreOld: false,
            hasMoreOldReverse: false,
            initLoading: false,
            messageList: [firstMessage, ...restMessages],
          };
        }
      });
    };

    const updateOneMessage = (message: MessageItem) => {
      setLoadState((preState) => {
        const tmpList = [...preState.messageList];
        const idx = tmpList.findIndex(
          (msg) => msg.clientMsgID === message.clientMsgID
        );

        if (idx < 0) {
          return preState;
        }
        const attachedInfoElem = {
          ...tmpList[idx].attachedInfoElem,
          ...message.attachedInfoElem,
        };
        tmpList[idx] = { ...tmpList[idx], ...message, attachedInfoElem };
        if (message.contentType === MessageType.RevokeMessage) {
          if (tmpList[idx].groupID) {
            getCurrentMessageUpInfo(tmpList[idx].groupID);
          }
          const newtmpList = tmpList.map((item) => {
            let data = cloneDeep(item);
            if (item.contentType === MessageType.QuoteMessage) {
              const quoteMessage = item.quoteElem?.quoteMessage;
              if (quoteMessage?.clientMsgID === message.clientMsgID) {
                const newQuoteMessage = {
                  ...quoteMessage,
                  contentType: MessageType.RevokeMessage,
                };
                data = {
                  ...item,
                  quoteElem: {
                    quoteMessage: newQuoteMessage,
                    text: item.quoteElem?.text || '',
                  },
                };
              }
            }
            return data;
          });
          return {
            ...preState,
            messageList: newtmpList,
          };
        } else {
          return {
            ...preState,
            messageList: tmpList,
          };
        }
      });
    };

    const updateOrInsertMessage = (message: MessageItem) => {
      const msgConversationID = getConversationIDByMsg(message);
      if (
        msgConversationID !== conversationID ||
        latestLoadState.current?.messageList == null
      ) {
        return;
      }
      const idx = latestLoadState.current?.messageList?.findIndex(
        (msg) => msg.clientMsgID === message.clientMsgID
      );

      if (idx < 0) {
        pushNewMessage(message);
      } else {
        updateOneMessage(message);
      }
    };

    const updateMessageNicknameAndFaceUrl = ({
      sendID,
      senderNickname,
      senderFaceUrl,
    }: UpdateMessaggeBaseInfoParams) => {
      setLoadState((preState) => {
        const tmpList = [...preState.messageList].map((message) => {
          if (message.sendID === sendID) {
            message.senderFaceUrl = senderFaceUrl;
            message.senderNickname = senderNickname;
          }
          return message;
        });
        return {
          ...preState,
          messageList: tmpList,
        };
      });
    };
    const deleteOnewMessage = (clientMsgID: string) => {
      setLoadState((preState) => {
        const tmpList = [...preState.messageList];
        const idx = tmpList.findIndex((msg) => msg.clientMsgID === clientMsgID);
        if (idx < 0) {
          return preState;
        }
        tmpList.splice(idx, 1);

        return {
          ...preState,
          messageList: tmpList,
        };
      });
    };
    const clearMessages = () => {
      setLoadState(() => ({
        initLoading: false,
        hasMoreOld: true,
        hasMoreOldReverse: true,
        hasMoreNew: true,
        messageList: [] as MessageItem[],
        firstItemIndex: START_INDEX,
        initialTopMostItemIndex: SPLIT_COUNT - 1,
      }));
      minSeq.current = 0;
    };

    emitter.on('PUSH_NEW_MSG', pushNewMessage);
    emitter.on('BATCH_PUSH_NEW_MSGS', batchPushNewMessages);
    emitter.on('UPDATE_ONE_MSG', updateOneMessage);
    emitter.on('UPDATE_OR_INSERT_ONE_MSG', updateOrInsertMessage);
    emitter.on('UPDATE_MSG_NICK_AND_FACEURL', updateMessageNicknameAndFaceUrl);
    emitter.on('DELETE_ONE_MSG', deleteOnewMessage);
    emitter.on('CLEAR_MSGS', clearMessages);
    return () => {
      emitter.off('PUSH_NEW_MSG', pushNewMessage);
      emitter.off('BATCH_PUSH_NEW_MSGS', batchPushNewMessages);
      emitter.off('UPDATE_ONE_MSG', updateOneMessage);
      emitter.off('UPDATE_OR_INSERT_ONE_MSG', updateOrInsertMessage);
      emitter.off(
        'UPDATE_MSG_NICK_AND_FACEURL',
        updateMessageNicknameAndFaceUrl
      );
      emitter.off('DELETE_ONE_MSG', deleteOnewMessage);
      emitter.off('CLEAR_MSGS', clearMessages);
    };
  }, [conversationID, groupID, latestLoadState]);

  const loadHistoryMessages = () => {
    if (
      latestLoadState.current.messageList == null ||
      latestLoadState.current.messageList?.length === 0
    ) {
      getMoreOldMessages(false);
    } else {
      getMoreOldMessages(true);
    }
  };

  const { loading: moreOldLoading, runAsync: getMoreOldMessages } = useRequest(
    async (loadMore = true) => {
      const reqConversationID = conversationID;

      try {
        // if (loadState.initLoading) {
        //   return;
        // }
        const startTime = Date.now();

        const { data } = await IMSDK.getAdvancedHistoryMessageList({
          count: SPLIT_COUNT,
          startClientMsgID: loadMore
            ? latestLoadState.current.messageList[0]?.clientMsgID
            : '',
          conversationID: conversationID ?? '',
          viewType: 0,
        });

        if (conversationID !== reqConversationID) {
          return;
        }

        // await initMessageThreadMap(groupID);

        const needShowList = addDataSepatorToMessageList(data.messageList);

        // 移除 setTimeout，直接更新状态
        setLoadState((preState) => {
          return {
            ...preState,
            initLoading: false,
            hasMoreOld: !data.isEnd,
            messageList: mergeMessagesByClientMsgID(
              needShowList,
              loadMore ? preState.messageList : []
            ),
            firstItemIndex: preState.firstItemIndex - needShowList.length,
            initialTopMostItemIndex: loadMore
              ? SPLIT_COUNT - 1
              : needShowList.length - 1,
          };
        });

        minSeq.current = (data as any).lastMinSeq;
      } catch (e) {
        console.error('查询历史消息失败', e);
        setLoadState((preState) => ({
          ...preState,
          initLoading: false,
          hasMoreOld: false,
          messageList: [],
          firstItemIndex: 0,
        }));
      }
    },
    {
      manual: true,
      // throttleLeading: true,
      // throttleWait: 2000,
      // throttleTrailing: false,
    }
  );
  const {
    loading: moreOldLoadingReverse,
    runAsync: getMoreOldMessagesReverse,
  } = useRequest(
    async () => {
      try {
        const startClientMsgID =
          latestLoadState.current.messageList[
            latestLoadState.current.messageList?.length - 1
          ]?.clientMsgID;

        const { data } = await IMSDK.getAdvancedHistoryMessageListReverse({
          count: SPLIT_COUNT,
          startClientMsgID: startClientMsgID || '',
          conversationID: conversationID ?? '',
          viewType: 0,
        });

        // if (conversationID !== reqConversationID) {
        //   return;
        // }
        //
        // 每次更新历史时，需要更新一下messageThreadMap
        // await initMessageThreadMap(groupID);

        const needShowList = addDataSepatorToMessageList(data.messageList);

        setLoadState((preState) => {
          const mergeResult = mergeMessagesByClientMsgID(
            preState.messageList || [],
            needShowList
          );

          if (
            mergeResult?.length !== 0 &&
            mergeResult.length === preState.messageList.length
          ) {
            return {
              ...preState,
              initLoading: false,
              hasMoreOldReverse: false,
              messageList: mergeResult,
              firstItemIndex:
                preState.firstItemIndex === START_INDEX
                  ? preState.firstItemIndex - needShowList.length
                  : preState.firstItemIndex,
            };
          } else {
            return {
              ...preState,
              initLoading: false,
              hasMoreOldReverse: !data.isEnd,
              messageList: mergeMessagesByClientMsgID(
                preState.messageList || [],
                needShowList
              ),
              firstItemIndex:
                preState.firstItemIndex === START_INDEX
                  ? preState.firstItemIndex - needShowList.length
                  : preState.firstItemIndex,
            };
          }
        });

        minSeq.current = (data as any).lastMinSeq;
      } catch (e) {}
    },
    {
      manual: true,
      // throttleLeading: true,
      // throttleWait: 20,
      // throttleTrailing: false,
    }
  );

  return {
    SPLIT_COUNT,
    loadState,
    latestLoadState,
    conversationID,
    moreOldLoading,
    moreOldLoadingReverse,
    currentTargetMsg,
    getMoreOldMessages,
    getMoreOldMessagesReverse,
    loadHistoryMessages,
  };
}

export const pushNewMessage = (message: MessageItem) => {
  emit('PUSH_NEW_MSG', message);
};

export const batchPushNewMessages = (messages: MessageItem[]) => {
  emit('BATCH_PUSH_NEW_MSGS', messages);
};

export const updateOneMessage = (message: MessageItem) => {
  emit('UPDATE_ONE_MSG', message);
};

export const updateOrInsertMessage = (message: MessageItem) => {
  emit('UPDATE_OR_INSERT_ONE_MSG', message);
};
export const updateMessageNicknameAndFaceUrl = (
  params: UpdateMessaggeBaseInfoParams
) => emit('UPDATE_MSG_NICK_AND_FACEURL', params);
export const deleteOneMessage = (clientMsgID: string) =>
  emit('DELETE_ONE_MSG', clientMsgID);
export const clearMessages = () => emit('CLEAR_MSGS');
