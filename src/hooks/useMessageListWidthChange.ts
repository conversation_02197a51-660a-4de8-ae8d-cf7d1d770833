import { useEffect, useRef } from 'react';
import debounce from 'lodash.debounce';

type Callback = (rect: DOMRectReadOnly) => void;

export function useResizeObserverWithThreshold<T extends HTMLElement>(
  callback: Callback,
  options?: {
    debounceDelay?: number;
    widthThreshold?: number;
  }
) {
  const ref = useRef<T | null>(null);
  const lastWidthRef = useRef<number | null>(null);

  const debounceDelay = options?.debounceDelay ?? 200;
  const widthThreshold = options?.widthThreshold ?? 5; // px

  useEffect(() => {
    const element = ref.current;
    if (!element) {
      return;
    }

    const handleResize = debounce((entries: ResizeObserverEntry[]) => {
      const entry = entries[0];
      if (!entry) {
        return;
      }

      const currentWidth = entry.contentRect.width;
      const lastWidth = lastWidthRef.current;

      if (
        lastWidth === null ||
        Math.abs(currentWidth - lastWidth) >= widthThreshold
      ) {
        lastWidthRef.current = currentWidth;
        callback(entry.contentRect);
      }
    }, debounceDelay);

    const observer = new ResizeObserver(handleResize);
    observer.observe(element);

    return () => {
      observer.disconnect();
      handleResize.cancel();
    };
  }, [callback, debounceDelay, widthThreshold]);

  return ref;
}
