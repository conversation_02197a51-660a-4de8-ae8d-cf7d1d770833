/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable no-lonely-if */
/* eslint-disable max-statements */
import { useEffect, useRef } from 'react';

export const selectedTextRef = { current: '' };

export const useSelectedText = () => {
  useEffect(() => {
    const handleSelection = () => {
      const selection = document.getSelection();

      if (selection && selection.type === 'Range') {
        const range = selection.getRangeAt(0);
        const container = document.createElement('div');
        // 获取选区的公共祖先节点
        const commonAncestor = range.commonAncestorContainer;

        // 如果是单行文本节点，需要获取其父元素的完整样式
        if (commonAncestor.nodeType === Node.TEXT_NODE) {
          const currentNode = commonAncestor;
          let currentElement = currentNode.parentElement;
          let resultNode = document.createTextNode(range.toString());

          // 从内到外包装所有样式
          while (
            currentElement &&
            !currentElement.classList.contains('message-content')
          ) {
            const wrapper = currentElement.cloneNode(false);
            wrapper.appendChild(resultNode);
            resultNode = wrapper;
            currentElement = currentElement.parentElement;
          }

          container.appendChild(resultNode);
        } else {
          // 处理列表和其他复杂结构
          const contents = range.cloneContents();
          const firstElement = contents.firstElementChild;

          // 如果是列表项，需要保持完整的列表结构
          if (firstElement && firstElement.tagName === 'LI') {
            const ul = document.createElement('ul');
            ul.className = 'editor-list';
            ul.setAttribute('data-type', 'bullet');
            ul.appendChild(contents);
            container.appendChild(ul);
          } else {
            container.appendChild(contents);
          }
        }

        selectedTextRef.current = container.innerHTML;
      }
      // selection.type === 'Caret' || selection.type === 'None'
      else {
        // 这里需要判断selection?.anchorNode是否是<span>复制</span>
        if (selection?.anchorNode?.textContent !== '复制') {
          selectedTextRef.current = '';
        }
      }
    };

    document.addEventListener('selectionchange', handleSelection);
    return () =>
      document.removeEventListener('selectionchange', handleSelection);
  }, []);

  return selectedTextRef;
};
