/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable indent */
import { KeyboardEventSource, KeyboardKeys } from '@/utils/constants';
import { useCallback } from 'react';

interface KeyboardHandlers {
  onEnter?: () => void;
  onArrowUp?: () => void;
  onArrowDown?: () => void;
  onEvent?: (e: any) => void;
}

export const useKeyboardEvent = (
  source: (typeof KeyboardEventSource)[keyof typeof KeyboardEventSource],
  handlers: KeyboardHandlers,
  conditions?: {
    shouldHandle?: boolean;
    shouldPreventDefault?: boolean;
  }
) => {
  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (handlers.onEvent) {
        handlers.onEvent(e);
      }
      if (conditions?.shouldHandle === false) {
        return;
      }

      const shouldPreventDefault = conditions?.shouldPreventDefault ?? true;
      switch (e.key) {
        // 如果同时按下ctrl和enter，在这里处理，不在enter中处理
        case KeyboardKeys.ENTER:
          if (e.ctrlKey) {
            return;
          }
          // 如果同时按下shift和enter，在这里处理，不在enter中处理
          if (e.shiftKey) {
            return;
          }
          if (handlers.onEnter) {
            shouldPreventDefault && e.preventDefault();
            e.stopPropagation();
            handlers.onEnter();
          }
          break;
        case KeyboardKeys.ARROW_UP:
          if (handlers.onArrowUp) {
            shouldPreventDefault && e.preventDefault();
            handlers.onArrowUp();
          }
          break;
        case KeyboardKeys.ARROW_DOWN:
          if (handlers.onArrowDown) {
            shouldPreventDefault && e.preventDefault();
            handlers.onArrowDown();
          }
          break;
        default:
          // 处理其他按键的情况
          break;
      }
    },
    [handlers, conditions]
  );

  return handleKeyDown;
};
