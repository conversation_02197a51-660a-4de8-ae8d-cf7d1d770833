import { useEffect, useState } from 'react';
import { UserOnlineState } from '@ht/openim-wasm-client-sdk';
import { userStatusManager } from './userStatusManager';

// 依赖于userStatusManager来进行实际的在线状态订阅/取消订阅，以及已订阅用户在线状态的缓存，该Hook仅完成读取
const useOnlineStatus = (userID: string) => {
  const [state, setState] = useState<UserOnlineState>();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    let active = true;

    const cb = (data: UserOnlineState) => {
      if (active && data.userID === userID) {
        setState(data);
      }
    };

    userStatusManager.subscribe(userID, cb);
    setLoading(false);

    return () => {
      active = false;
      userStatusManager.unsubscribe(userID, cb);
    };
  }, [userID]);

  return { onlineState: state, loading };
};

export default useOnlineStatus;
