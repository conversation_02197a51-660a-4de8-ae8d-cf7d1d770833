import { buildTree } from '@/pages/contact/components/LeftTree/util';
import { useDepartmentInfoStore } from '@/store/department';
import { useEffect, useMemo } from 'react';

export default function useDeparmentInfo() {
  const { departmentInfoIniting, departmentInfo, initDepartmentInfo } =
    useDepartmentInfoStore();

  useEffect(() => {
    if (!departmentInfoIniting) {
      // 初次加载完成时加载一次，后续每次切到切到其他页面再切回通讯录时，重新查询一遍
      initDepartmentInfo();
    }
  }, []);

  const treeData = useMemo(() => {
    if (!departmentInfoIniting && departmentInfo != null) {
      return buildTree(departmentInfo);
    } else {
      return [];
    }
  }, [departmentInfoIniting, departmentInfo]);

  return {
    departmentTreeData: treeData,
    departmentInfoIniting,
  };
}
