import { CbEvents } from '@ht/openim-wasm-client-sdk';
import {
  GroupItem,
  GroupMemberItem,
  WSEvent,
} from '@ht/openim-wasm-client-sdk/lib/types/entity';
import { useLatest } from 'ahooks';
import { useCallback, useEffect, useRef, useState } from 'react';

import { useConversationStore } from '@/store';
import { feedbackToast } from '@/utils/common';
import { IMSDK } from '@/layouts/BasicLayout';

export const REACH_SEARCH_FLAG = 'LAST_FLAG';

export interface FetchStateType {
  offset: number;
  searchOffset: number;
  count: number;
  loading: boolean;
  hasMore: boolean;
  groupMemberList: GroupMemberItem[];
  searchMemberList: GroupMemberItem[];
}

interface UseGroupMembersProps {
  groupID?: string;
  notRefresh?: boolean;
}

const GROUPMEMBER_COUNT = 500;

export default function useGroupMembers(props?: UseGroupMembersProps) {
  const { groupID, notRefresh } = props ?? {};
  const [fetchState, setFetchState] = useState<FetchStateType>({
    offset: 0,
    searchOffset: 0,
    count: 20,
    loading: false,
    hasMore: true,
    groupMemberList: [],
    searchMemberList: [],
  });
  const latestFetchState = useLatest(fetchState);
  // const lastKeyword = useRef('');

  const getMemberData = useCallback(
    async (refresh = false) => {
      const sourceID =
        groupID ??
        useConversationStore.getState().currentConversation?.groupID ??
        '';
      if (!sourceID) {
        return;
      }

      setFetchState((state) => ({
        ...state,
        loading: true,
      }));
      try {
        const { data } = await IMSDK.getGroupMemberList({
          groupID: sourceID,
          offset: refresh ? 0 : latestFetchState.current.offset,
          count: GROUPMEMBER_COUNT,
          filter: 0,
        });

        // await new Promise((resolve) => setTimeout(resolve, 10 * 1000));

        setFetchState((state) => ({
          ...state,
          groupMemberList: [...(refresh ? [] : state.groupMemberList), ...data],
          hasMore: data.length === GROUPMEMBER_COUNT,
          offset: state.offset + GROUPMEMBER_COUNT,
        }));
        if (
          data.length === GROUPMEMBER_COUNT &&
          latestFetchState.current.loading
        ) {
          await getMemberData();
        } else {
          setFetchState((state) => ({
            ...state,
            loading: false,
          }));
        }
      } catch (error) {
        // feedbackToast({
        //   msg: 'getMemberFailed',
        //   error,
        // });
        setFetchState((state) => ({
          ...state,
          loading: false,
        }));
      }
    },
    [groupID, latestFetchState]
  );

  useEffect(() => {
    const currentConversationGroupID =
      useConversationStore.getState().currentConversation?.groupID;
    if (!groupID && !currentConversationGroupID) {
      return;
    }
    const groupMemberInfoChangedHandler = ({
      data: member,
    }: WSEvent<GroupMemberItem>) => {
      if (
        member.groupID === latestFetchState.current.groupMemberList[0]?.groupID
      ) {
        const idx = latestFetchState.current.groupMemberList.findIndex(
          (item) => item.userID === member.userID
        );
        const newMembers = [...latestFetchState.current.groupMemberList];
        newMembers[idx] = { ...member };
        setFetchState((state) => ({
          ...state,
          groupMemberList: newMembers,
        }));
      }
    };

    const groupMemberCountHandler = ({
      data,
    }: WSEvent<GroupItem | GroupMemberItem>) => {
      if (notRefresh) {
        return;
      }
      if (
        data.groupID ===
        (groupID || latestFetchState.current.groupMemberList[0]?.groupID)
      ) {
        setTimeout(() => {
          getMemberData(true);
        }, 200);
      }
    };

    const setIMListener = () => {
      IMSDK.on(
        CbEvents.OnGroupMemberInfoChanged,
        groupMemberInfoChangedHandler
      );
      IMSDK.on(CbEvents.OnGroupMemberAdded, groupMemberCountHandler);
      IMSDK.on(CbEvents.OnGroupMemberDeleted, groupMemberCountHandler);
      IMSDK.on(CbEvents.OnJoinedGroupAdded, groupMemberCountHandler);
    };

    const disposeIMListener = () => {
      IMSDK.off(
        CbEvents.OnGroupMemberInfoChanged,
        groupMemberInfoChangedHandler
      );
      IMSDK.off(CbEvents.OnGroupMemberAdded, groupMemberCountHandler);
      IMSDK.off(CbEvents.OnGroupMemberDeleted, groupMemberCountHandler);
      IMSDK.off(CbEvents.OnJoinedGroupAdded, groupMemberCountHandler);
    };
    setIMListener();
    return () => {
      disposeIMListener();
    };
  }, [groupID, getMemberData, latestFetchState, notRefresh]);

  const resetState = () => {
    setFetchState({
      offset: 0,
      searchOffset: 0,
      count: 20,
      loading: false,
      hasMore: true,
      groupMemberList: [],
      searchMemberList: [],
    });
  };

  return {
    fetchState,
    getMemberData,
    resetState,
  };
}
