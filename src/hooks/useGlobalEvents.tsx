/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-lines */
/* eslint-disable max-statements */
import {
  CbEvents,
  MessageReceiveOptType,
  MessageType,
  SessionType,
  GroupStatus,
} from '@ht/openim-wasm-client-sdk';
import {
  ConversationItem,
  FriendApplicationItem,
  GroupApplicationItem,
  GroupItem,
  GroupMemberItem,
  MessageItem,
  RevokedInfo,
  SelfUserInfo,
  WSEvent,
  ReactedInfo,
  ThreadCreateCallback,
  GroupMessageReceiptInfo,
  ReceiptInfo,
  GroupSetUpMessageCallback,
  GroupCancelUpMessageCallback,
} from '@ht/openim-wasm-client-sdk/lib/types/entity';
import { t } from 'i18next';
import { useEffect, useRef } from 'react';
import _ from 'lodash';
// import { useNavigate } from 'react-router-dom';

// import { BusinessAllowType } from '@/api/login';
import { CustomType } from '@/constants';

import {
  batchPushNewMessages,
  pushNewMessage,
  updateMessageNicknameAndFaceUrl,
  updateOneMessage,
  updateOrInsertMessage,
} from '@/hooks/useHistoryMessageList';

import {
  useConversationStore,
  useUserStore,
  useMultiSelectStore,
} from '@/store';
import { useContactStore } from '@/store/contact';
import { feedbackToast } from '@/utils/common';
import { createNotification, isGroupSession } from '@/utils/imCommon';
import { clearIMProfile, getIMToken, getIMUserID } from '@/utils/storage';
import { IMSDK } from '@/layouts/BasicLayout';
// import { appWindow } from '@tauri-apps/api/window';
import { getCurrentWindow } from '@tauri-apps/api/window';
import { platform } from '@tauri-apps/plugin-os';
import { useDepartmentInfoStore } from '@/store/department';
import { flashTray } from '@/tauri';
import BaseSettings from '@config/BaseSettings';
import notificationIcon from '@/assets/notification.svg';
import { setBrowserIcon, isSingleChat } from '@/utils/utils';
import htscIco from '@/assets/htsc.png';
import { Button, message, notification } from '@ht/sprite-ui';
import bell from '@/assets/upgradeBell-black.png';
import { AILoadingText } from '@/utils/constants';
import { useSelectedText } from './useSelectedText';

declare global {
  interface Window {
    __TAURI_INTERNALS__: any;
  }
}

let titleInterval: any = {};

export function useGlobalEvent() {
  const updateNewVersion = useUserStore((state) => state.updateNewVersion);

  // const navigate = useNavigate();
  const resume = useRef(false);
  const firstLoad = useRef(false);
  // user
  const updateSyncState = useUserStore((state) => state.updateSyncState);
  const updateProgressState = useUserStore(
    (state) => state.updateProgressState
  );
  const updateReinstallState = useUserStore(
    (state) => state.updateReinstallState
  );
  const updateConnectState = useUserStore((state) => state.updateConnectState);
  const updateSelfInfo = useUserStore((state) => state.updateSelfInfo);
  const updateSelfState = useUserStore((state) => state.updateSelfState);
  const userLogout = useUserStore((state) => state.userLogout);
  useSelectedText();

  // conversation
  const updateConversationList = useConversationStore(
    (state) => state.updateConversationList
  );
  const updateCurrentConversation = useConversationStore(
    (state) => state.updateCurrentConversation
  );
  const updateUnReadCount = useConversationStore(
    (state) => state.updateUnReadCount
  );
  const updateCurrentGroupInfo = useConversationStore(
    (state) => state.updateCurrentGroupInfo
  );
  const getCurrentGroupInfoByReq = useConversationStore(
    (state) => state.getCurrentGroupInfoByReq
  );
  const setCurrentMemberInGroup = useConversationStore(
    (state) => state.setCurrentMemberInGroup
  );
  const getCurrentMemberInGroupByReq = useConversationStore(
    (state) => state.getCurrentMemberInGroupByReq
  );
  const tryUpdateCurrentMemberInGroup = useConversationStore(
    (state) => state.tryUpdateCurrentMemberInGroup
  );
  const getConversationListByReq = useConversationStore(
    (state) => state.getConversationListByReq
  );
  const getUnReadCountByReq = useConversationStore(
    (state) => state.getUnReadCountByReq
  );
  const updateLlmLoading = useConversationStore(
    (state) => state.updateLlmLoading
  );
  const getCurrentMessageUpInfo = useConversationStore(
    (state) => state.getCurrentMessageUpInfo
  );
  const checkRightArea = useConversationStore((state) => state.checkRightArea);
  const deleteMultiSelect = useMultiSelectStore(
    (state) => state.deleteMultiSelect
  );
  // contact
  const startSyncDepartmentInfo = useDepartmentInfoStore(
    (state) => state.startSyncDepartmentInfo
  );
  const endSyncDepartmentInfo = useDepartmentInfoStore(
    (state) => state.endSyncDepartmentInfo
  );

  const getFriendListByReq = useContactStore(
    (state) => state.getFriendListByReq
  );
  const getGroupListByReq = useContactStore((state) => state.getGroupListByReq);
  const updateGroup = useContactStore((state) => state.updateGroup);
  const pushNewGroup = useContactStore((state) => state.pushNewGroup);

  const updateRecvGroupApplication = useContactStore(
    (state) => state.updateRecvGroupApplication
  );
  const updateSendGroupApplication = useContactStore(
    (state) => state.updateSendGroupApplication
  );

  let cacheConversationList = [] as ConversationItem[];
  const audioEl: HTMLAudioElement | null = null;

  const titleFlashListener = () => {
    const { visibilityState } = document;
    if (visibilityState === 'visible') {
      clearInterval(titleInterval);
      titleInterval = null;
      document.title = BaseSettings.title;
      setBrowserIcon(htscIco);
    } else {
    }
  };
  useEffect(() => {
    loginCheck();
    cacheConversationList = [];
    setIMListener();
    setIpcListener();

    window.addEventListener('online', () => {
      updateConnectState('success');
      IMSDK.networkStatusChanged();
    });
    window.addEventListener('offline', () => {
      updateConnectState('failed');
      IMSDK.networkStatusChanged();
    });
    document.addEventListener('visibilitychange', titleFlashListener);

    return () => {
      document.removeEventListener('visibilitychange', titleFlashListener);
      disposeIMListener();
    };
  }, []);

  const loginCheck = async () => {
    const IMToken = (await getIMToken()) as string;
    const IMUserID = (await getIMUserID()) as string;

    if (!IMToken || !IMUserID) {
      clearIMProfile();
      // navigate('/login');
    }
    // tryLogin();
  };

  const browserNotification = (msg: string) => {
    if (!('Notification' in window)) {
      // 检查浏览器是否支持通知
    } else if (Notification.permission === 'granted') {
      // 检查是否已授予通知权限；如果是的话，创建一个通知
      const notification = new Notification(msg);
      // …
    } else if (Notification.permission !== 'denied') {
      // 我们需要征求用户的许可
      Notification.requestPermission().then((permission) => {
        // 如果用户接受，我们就创建一个通知
        if (permission === 'granted') {
          const notification = new Notification(msg);
          // …
        }
      });
    }

    // 最后，如果用户拒绝了通知，并且你想尊重用户的选择，则无需再打扰他们
  };

  const onUpgradeEvent = (event: any) => {
    const args = {
      message: '系统通知',
      description: event?.data?.content,
      duration: 0,
      icon: (
        <img
          style={{
            width: '20px',
            position: 'relative',
            top: '2px',
          }}
          src={bell}
        />
      ),
      btn: (
        <Button
          type="primary"
          style={{
            backgroundColor: '#1263A3',
            border: 'none',
            color: '#FFFFFF',
            borderRadius: '8px',
            height: '36px',
            width: '92px',
            cursor: 'pointer',
          }}
          onClick={() => {
            localStorage.setItem('linkim.setting.isUpgrade', 'true');
            location.reload();
          }}
        >
          立即更新
        </Button>
      ),
      onClose: () => {
        updateNewVersion({
          available: true,
          data: event,
        });
      },
    };
    notification.open(args);
  };

  // const tryLogin = async () => {
  //   updateIsLogining(true);
  //   const IMToken = (await getIMToken()) as string;
  //   const IMUserID = (await getIMUserID()) as string;
  //   try {
  //     const apiAddr = import.meta.env.VITE_API_URL;
  //     const wsAddr = import.meta.env.VITE_WS_URL;
  //     if (window.electronAPI) {
  //       await IMSDK.initSDK({
  //         platformID: window.electronAPI?.getPlatform() ?? 5,
  //         apiAddr,
  //         wsAddr,
  //         dataDir: window.electronAPI.getDataPath('sdkResources') || './',
  //         logFilePath: window.electronAPI.getDataPath('logsPath') || './',
  //         logLevel: LogLevel.Debug,
  //         isLogStandardOutput: false,
  //         systemType: 'electron',
  //       });
  //       await IMSDK.login({
  //         userID: IMUserID,
  //         token: IMToken,
  //       });
  //     } else {
  //       await IMSDK.login({
  //         userID: IMUserID,
  //         token: IMToken,
  //         platformID: 5,
  //         apiAddr,
  //         wsAddr,
  //         logLevel: LogLevel.Debug,
  //       });
  //     }
  //     initStore();
  //   } catch (error) {
  //     console.error(error);
  //     if ((error as WsResponse).errCode !== 10102) {
  //       navigate('/login');
  //     }
  //   }
  //   updateIsLogining(false);
  // };

  const setIMListener = () => {
    // account
    IMSDK.on(CbEvents.OnSelfInfoUpdated, selfUpdateHandler);
    IMSDK.on(CbEvents.OnConnecting, connectingHandler);
    IMSDK.on(CbEvents.OnConnectFailed, connectFailedHandler);
    IMSDK.on(CbEvents.OnConnectSuccess, connectSuccessHandler);
    IMSDK.on(CbEvents.OnKickedOffline, kickHandler);
    IMSDK.on(CbEvents.OnUserTokenExpired, expiredHandler);
    IMSDK.on(CbEvents.OnUserTokenInvalid, expiredHandler);
    // sync
    IMSDK.on(CbEvents.OnSyncServerStart, syncStartHandler);
    IMSDK.on(CbEvents.OnSyncServerProgress, syncProgressHandler);
    IMSDK.on(CbEvents.OnSyncServerFinish, syncFinishHandler);
    IMSDK.on(CbEvents.OnSyncServerFailed, syncFailedHandler);
    // message
    IMSDK.on(CbEvents.OnRecvNewMessages, newMessagesHandler);
    IMSDK.on(CbEvents.OnRecvNewMessage, newMessageHandler);
    IMSDK.on(CbEvents.OnNewRecvMessageRevoked, revokedMessageHandler);
    IMSDK.on(CbEvents.OnRecvMessageReacted, messageReactedHandler);
    IMSDK.on(CbEvents.OnRecvGroupReadReceipt, groupMessageReadHandler);

    // 单聊已读回调
    IMSDK.on(CbEvents.OnRecvC2CReadReceipt, singleMessageReadHandler);

    // conversation
    IMSDK.on(CbEvents.OnConversationChanged, conversationChangeHandler);
    IMSDK.on(CbEvents.OnNewConversation, newConversationHandler);
    // IMSDK.on(
    //   CbEvents.OnTotalUnreadMessageCountChanged,
    //   totalUnreadChangeHandler
    // );
    // group
    IMSDK.on(CbEvents.OnJoinedGroupAdded, joinedGroupAddedHandler);
    IMSDK.on(CbEvents.OnJoinedGroupDeleted, joinedGroupDeletedHandler);
    IMSDK.on(CbEvents.OnGroupDismissed, joinedGroupDismissHandler);
    IMSDK.on(CbEvents.OnGroupInfoChanged, groupInfoChangedHandler);
    IMSDK.on(CbEvents.OnGroupMemberAdded, groupMemberAddedHandler);
    IMSDK.on(CbEvents.OnGroupMemberDeleted, groupMemberDeletedHandler);
    IMSDK.on(CbEvents.OnGroupMemberInfoChanged, groupMemberInfoChangedHandler);
    IMSDK.on(CbEvents.OnGroupUpMessageSet, groupUpMessageSetHandler);
    IMSDK.on(CbEvents.OnGroupUpMessageCancel, groupUpMessageCancelHandler);
    // application
    IMSDK.on(
      CbEvents.OnFriendApplicationAdded,
      friendApplicationProcessedHandler
    );
    IMSDK.on(
      CbEvents.OnFriendApplicationAccepted,
      friendApplicationProcessedHandler
    );
    IMSDK.on(
      CbEvents.OnFriendApplicationRejected,
      friendApplicationProcessedHandler
    );
    IMSDK.on(
      CbEvents.OnGroupApplicationAdded,
      groupApplicationProcessedHandler
    );
    IMSDK.on(
      CbEvents.OnGroupApplicationAccepted,
      groupApplicationProcessedHandler
    );
    IMSDK.on(
      CbEvents.OnGroupApplicationRejected,
      groupApplicationProcessedHandler
    );
    IMSDK.on(CbEvents.OnNewRecvMessageModified, newRecvMessageModified);

    // contact
    IMSDK.on(CbEvents.OnSyncDepartmentStart, startSyncDepartmentInfo);
    IMSDK.on(CbEvents.OnSyncDepartmentFinish, endSyncDepartmentInfo);

    // thread
    IMSDK.on(CbEvents.OnThreadCreated, onThreadCreated);

    // 升级
    IMSDK.on(CbEvents.OnUserClientRestart, onUpgradeEvent);
  };

  const onThreadCreated = (data: WSEvent<ThreadCreateCallback>) => {
    if (useUserStore.getState().syncState === 'loading' || resume.current) {
      return;
    }

    if (data.data.threadId != null && data?.data?.threadId !== '') {
      updateOneMessage({
        clientMsgID: data.data.startMessageId,
        seq: data.data.startMessageSeq,
        thread: { threadId: data?.data?.threadId },
      } as unknown as MessageItem);
    }
  };
  const selfUpdateHandler = ({ data }: WSEvent<SelfUserInfo>) => {
    updateMessageNicknameAndFaceUrl({
      sendID: data.userID,
      senderNickname: data.nickname,
      senderFaceUrl: data.faceURL,
    });
    updateSelfInfo(data);
    updateSelfState(data?.ex);
  };
  const connectingHandler = async () => {
    // await new Promise((resolve) => setTimeout(resolve, 10 * 1000));

    updateConnectState('loading');
  };

  const connectFailedHandler = ({ errCode, errMsg }: WSEvent) => {
    updateConnectState('failed');
    console.error('connectFailedHandler', errCode, errMsg);

    if (errCode === 705) {
      tryOut(t('toast.loginExpiration'));
    }
  };
  const connectSuccessHandler = () => {
    updateConnectState('success');
  };
  const kickHandler = () => tryOut(t('toast.accountKicked'));
  const expiredHandler = () => tryOut(t('toast.loginExpiration'));

  const tryOut = (msg: string) =>
    feedbackToast({
      msg,
      error: msg,
      onClose: () => {
        userLogout(true);
      },
    });

  // sync
  const syncStartHandler = ({ data }: WSEvent<boolean>) => {
    if (!firstLoad?.current) {
      updateSyncState('loading');
      updateReinstallState(data);
    } else {
    }
  };
  const syncProgressHandler = ({ data }: WSEvent<number>) => {
    updateProgressState(data);
  };
  const syncFinishHandler = () => {
    if (!firstLoad?.current) {
      updateSyncState('success');
      firstLoad.current = true;
    }
    getFriendListByReq();
    getGroupListByReq();
    getConversationListByReq(false, true);
    getUnReadCountByReq();
    // initThreadGroupMap();

    window.syncFinshTimeStamp = Date.now().toString(); // 同步完成的时间戳，头像请求加上这个，确保每次刷新页面拿到最新的
  };
  const syncFailedHandler = () => {
    updateSyncState('failed');
    firstLoad.current = false;
    feedbackToast({ msg: t('toast.syncFailed'), error: t('toast.syncFailed') });
  };
  const pendingMessages = useRef<MessageItem[]>([]);
  // 定时器引用
  const messageTimer = useRef<NodeJS.Timeout | null>(null);
  const newMessagesHandler = ({ data }: WSEvent<MessageItem[]>) => {
    console.debug('收到消息，多条', {
      data: data[0].textElem?.content,
    });

    if (useUserStore.getState().syncState === 'loading' || resume.current) {
      return;
    }
    data.map((message) => handleNewMessage(message));

    // 将新消息添加到待处理队列
    // pendingMessages.current = [...pendingMessages.current, ...data];

    // // 如果定时器已存在，不需要创建新的定时器
    // if (messageTimer.current) {
    //   return;
    // }

    // // 创建定时器，1秒后处理所有收集到的消息
    // messageTimer.current = setTimeout(() => {
    //   if (pendingMessages.current.length > 0) {
    //     handleNewMessages(pendingMessages.current);
    //     pendingMessages.current = [];
    //   }
    //   messageTimer.current = null;
    // }, 1000);
  };
  const newMessageHandler = ({ data }: WSEvent<MessageItem>) => {
    if (useUserStore.getState().syncState === 'loading' || resume.current) {
      return;
    }
    handleNewMessage(data);
  };

  const messageReactedHandler = ({ data }: WSEvent<ReactedInfo>) => {
    if (useUserStore.getState().syncState === 'loading' || resume.current) {
      return;
    }
    updateOneMessage({
      clientMsgID: data.clientMsgID,
      attachedInfoElem: {
        reaction: data.reaction,
      },
    } as unknown as MessageItem);
  };

  const singleMessageReadHandler = ({ data }: WSEvent<ReceiptInfo[]>) => {
    if (useUserStore.getState().syncState === 'loading' || resume.current) {
      return;
    }

    data.map((item) => {
      if (item.userID != null && item.userID !== '') {
        return item?.msgIDList?.map((msgId) =>
          updateOneMessage({
            clientMsgID: msgId,
            isRead: true,
          } as unknown as MessageItem)
        );
      } else {
        return item;
      }
    });
  };

  const groupMessageReadHandler = ({
    data,
  }: WSEvent<GroupMessageReceiptInfo>) => {
    if (useUserStore.getState().syncState === 'loading' || resume.current) {
      return;
    }

    data.groupMessageReadInfos?.map((item) =>
      updateOneMessage({
        clientMsgID: item.clientMsgID,
        attachedInfoElem: {
          groupHasReadInfo: {
            groupMemberCount: item.groupMemberCount,
            hasReadCount: item.hasReadCount,
          },
        },
        isRead: item.isRead,
      } as unknown as MessageItem)
    );
  };

  const revokedMessageHandler = ({ data }: WSEvent<RevokedInfo>) => {
    updateOneMessage({
      clientMsgID: data.clientMsgID,
      contentType: MessageType.RevokeMessage,
      notificationElem: {
        detail: JSON.stringify(data),
      },
    } as MessageItem);
    deleteMultiSelect(data);
    checkRightArea({
      clientMsgID: data.clientMsgID,
      contentType: MessageType.RevokeMessage,
      notificationElem: {
        detail: JSON.stringify(data),
      },
    } as MessageItem);
  };

  const newMessageNotify = async (newServerMsg: MessageItem) => {
    if (useUserStore.getState().syncState === 'loading') {
      return;
    }

    // const { selfInfo } = useUserStore.getState();

    // if (
    //   selfInfo.allowBeep === BusinessAllowType.NotAllow ||
    //   selfInfo.globalRecvMsgOpt !== MessageReceiveOptType.Normal
    // ) {
    //   return;
    // }

    let cveItem = [
      ...useConversationStore.getState().conversationList,
      ...cacheConversationList,
    ].find((conversation) => {
      if (isGroupSession(newServerMsg.sessionType)) {
        return newServerMsg.groupID === conversation.groupID;
      }
      return newServerMsg.sendID === conversation.userID;
    });

    if (!cveItem) {
      try {
        const { data } = await IMSDK.getOneConversation({
          sessionType: newServerMsg.sessionType,
          sourceID: newServerMsg.groupID || newServerMsg.sendID,
        });
        cveItem = data;
        cacheConversationList = [...cacheConversationList, { ...cveItem }];
      } catch (e) {
        return;
      }
    }

    if (cveItem.recvMsgOpt !== MessageReceiveOptType.Normal) {
      return;
    }

    // if (window.__TAURI_INTERNALS__) {
    //   const currentPlatform = platform();

    //   if (currentPlatform === 'windows') {
    //     getCurrentWindow().requestUserAttention(2);
    //   }
    //   if (currentPlatform === 'macos') {
    //     const { unReadCount } = useConversationStore.getState();
    //     getCurrentWindow().setBadgeCount(unReadCount + 1);
    //   }
    //   flashTray(true);
    // }
    const { unReadCount } = useConversationStore.getState();
    if (document.visibilityState === 'hidden' && !isSingleChat()) {
      // titleInterval = flashTitle();
      document.title = `【新消息】${BaseSettings.title}`;
      setBrowserIcon(notificationIcon);
    }

    // createNotification({
    //   message: newServerMsg,
    //   conversation: cveItem,
    //   callback: async (conversation) => {
    //     if (
    //       useConversationStore.getState().currentConversation
    //         ?.conversationID === conversation.conversationID
    //     ) {
    //       return;
    //     }
    //     await updateCurrentConversation({ ...conversation, unreadCount: 1 });
    //     // navigate(`/chat/${conversation.conversationID}`);
    //   },
    // });

    // if (!audioEl) {
    //   audioEl = document.createElement('audio');
    // }
    // audioEl.src = messageRing;
    // audioEl.play();
  };

  const notPushType = [MessageType.TypingMessage, MessageType.RevokeMessage];

  const handleNewMessage = (newServerMsg: MessageItem) => {
    // 新消息
    // if (newServerMsg.contentType === MessageType.InitParentConversation) {
    //   return;
    // }
    const isInCurrentConversation = inCurrentConversation(newServerMsg);

    if (
      newServerMsg.sendID === useUserStore.getState().selfInfo.userID &&
      isInCurrentConversation
    ) {
      // 本人发送的消息，会在seq变化触发，用于更新seq
      updateOrInsertMessage(newServerMsg);
      return;
    } else if (
      // 这里其实要梳理下，为了影响小，先暂时这样了，不太理解为什么之前判断消息是否在当前会话的逻辑没了？
      newServerMsg.sendID === useUserStore.getState().selfInfo.userID &&
      !isInCurrentConversation
    ) {
      return;
    }

    if (newServerMsg.contentType === MessageType.CustomMessage) {
      const customData = JSON.parse(newServerMsg.customElem!.data);
      if (
        CustomType.CallingInvite <= customData.customType &&
        customData.customType <= CustomType.CallingHungup
      ) {
        return;
      }
    }

    const needNotification =
      !notPushType.includes(newServerMsg.contentType) &&
      newServerMsg.sendID !== useUserStore.getState().selfInfo.userID;

    if (needNotification) {
      // if (
      //   document.visibilityState === 'hidden'
      //   // ||
      //   // !inCurrentConversation(newServerMsg)
      // ) {
      newMessageNotify(newServerMsg);
      // }
    }

    // if (!inCurrentConversation(newServerMsg)) {
    //   return;
    // }

    if (!notPushType.includes(newServerMsg.contentType)) {
      pushNewMessage(newServerMsg);
    }
  };

  const handleNewMessages = (newServerMsg: MessageItem[]) => {
    // 批量处理新消息
    // 先找到在当前会话内的消息
    const newServerMsgInCurrentConversation = newServerMsg.filter((msg) =>
      inCurrentConversation(msg)
    );
    if (newServerMsgInCurrentConversation.length > 0) {
      batchPushNewMessages(newServerMsgInCurrentConversation);
    }
  };

  const inCurrentConversation = (newServerMsg: MessageItem) => {
    let result = false;

    switch (newServerMsg.sessionType) {
      case SessionType.Single:
        result =
          (newServerMsg.sendID ===
            useConversationStore.getState().currentConversation?.userID &&
            newServerMsg.recvID === useUserStore.getState().selfInfo.userID) ||
          (newServerMsg.sendID === useUserStore.getState().selfInfo.userID &&
            newServerMsg.recvID ===
              useConversationStore.getState().currentConversation?.userID);
        break;
      case SessionType.Group:
      case SessionType.WorkingGroup:
        result =
          newServerMsg.groupID ===
          useConversationStore.getState().currentConversation?.groupID;
        break;
      case SessionType.Notification:
        result =
          newServerMsg.sendID ===
          useConversationStore.getState().currentConversation?.userID;
        break;
      default:
        result = false;
    }

    return result;
  };

  // conversation
  const conversationChangeHandler = ({ data }: WSEvent<ConversationItem[]>) => {
    console.debug('conversationChangeHandler', { data });
    updateConversationList(data, 'filter');
  };
  const newConversationHandler = ({ data }: WSEvent<ConversationItem[]>) => {
    updateConversationList(data, 'push');
  };
  // const totalUnreadChangeHandler = ({ data }: WSEvent<number>) => {
  //   if (data === useConversationStore.getState().unReadCount) {
  //     return;
  //   }
  //   updateUnReadCount(data);
  // };

  // group
  const joinedGroupAddedHandler = ({ data }: WSEvent<GroupItem>) => {
    if (
      data.groupID ===
      useConversationStore.getState().currentConversation?.groupID
    ) {
      updateCurrentGroupInfo(data);
      getCurrentMemberInGroupByReq(data.groupID);
    }
    IMSDK.getOneConversation({
      sourceID: data.groupID,
      sessionType: 3,
    }).then((res) => {
      IMSDK.markConversationMessageAsRead(res.data.conversationID);
    });
    pushNewGroup(data);
  };
  const joinedGroupDeletedHandler = ({ data }: WSEvent<GroupItem>) => {
    if (
      data.groupID ===
      useConversationStore.getState().currentConversation?.groupID
    ) {
      getCurrentGroupInfoByReq(data.groupID);
      setCurrentMemberInGroup();
    }
    updateGroup(data, true);
    // 清空草稿
    if (data.status === GroupStatus.Muted) {
      try {
        IMSDK.setConversationDraft({
          conversationID: `sg_${data.groupID}`,
          draftText: '',
        });
      } catch (e) {}
    }
  };
  const joinedGroupDismissHandler = ({ data }: WSEvent<GroupItem>) => {
    // 群解散，清空草稿
    try {
      IMSDK.setConversationDraft({
        conversationID: `sg_${data.groupID}`,
        draftText: '',
      });
    } catch (e) {}
    if (
      data.groupID ===
      useConversationStore.getState().currentConversation?.groupID
    ) {
      getCurrentMemberInGroupByReq(data.groupID);
    }
  };
  const groupInfoChangedHandler = ({ data }: WSEvent<GroupItem>) => {
    updateGroup(data);

    // 清空草稿
    if (data.status === GroupStatus.Muted) {
      try {
        IMSDK.setConversationDraft({
          conversationID: `sg_${data.groupID}`,
          draftText: '',
        });
      } catch (e) {}
    }

    if (
      data.groupID ===
      useConversationStore.getState().currentConversation?.groupID
    ) {
      updateCurrentGroupInfo(data);
    }
  };
  const groupMemberAddedHandler = ({ data }: WSEvent<GroupMemberItem>) => {
    if (
      data.groupID ===
        useConversationStore.getState().currentConversation?.groupID &&
      data.userID === useUserStore.getState().selfInfo.userID
    ) {
      getCurrentMemberInGroupByReq(data.groupID);
    }
  };
  const groupMemberDeletedHandler = ({ data }: WSEvent<GroupMemberItem>) => {
    if (
      data.groupID ===
        useConversationStore.getState().currentConversation?.groupID &&
      data.userID === useUserStore.getState().selfInfo.userID
    ) {
      getCurrentMemberInGroupByReq(data.groupID);
    }
  };
  const groupMemberInfoChangedHandler = ({
    data,
  }: WSEvent<GroupMemberItem>) => {
    if (
      data.groupID ===
      useConversationStore.getState().currentConversation?.groupID
    ) {
      updateMessageNicknameAndFaceUrl({
        sendID: data.userID,
        senderNickname: data.nickname,
        senderFaceUrl: data.faceURL,
      });
      tryUpdateCurrentMemberInGroup(data);
    }
  };

  const groupUpMessageSetHandler = ({
    data,
  }: WSEvent<GroupSetUpMessageCallback>) => {
    getCurrentMessageUpInfo(data.groupID);
  };

  const groupUpMessageCancelHandler = ({
    data,
  }: WSEvent<GroupCancelUpMessageCallback>) => {
    getCurrentMessageUpInfo(data.groupID);
  };

  // application
  const friendApplicationProcessedHandler = ({
    data,
  }: WSEvent<FriendApplicationItem>) => {
    const isRecv = data.toUserID === useUserStore.getState().selfInfo.userID;
    // if (isRecv) {
    //   updateRecvFriendApplication(data);
    // } else {
    //   updateSendFriendApplication(data);
    // }
  };
  const groupApplicationProcessedHandler = ({
    data,
  }: WSEvent<GroupApplicationItem>) => {
    const isRecv = data.userID !== useUserStore.getState().selfInfo.userID;
    if (isRecv) {
      updateRecvGroupApplication(data);
    } else {
      updateSendGroupApplication(data);
    }
  };

  const newRecvMessageModified = ({ data }: WSEvent<any>) => {
    console.debug('message modify', data);

    // const content = JSON.parse(data.modifyMsg.content || '{}').content || '';
    const content = JSON.parse(data.modifyMsg.content || '{}') || '';
    const msg: any = {
      clientMsgID: data.clientMsgID,
      contentType: data.modifyMsg.contentType,
    };
    if (data.modifyMsg.contentType === MessageType.TextMessage) {
      msg.textElem = {
        content,
      };
    } else if (data.modifyMsg.contentType === MessageType.CustomMessage) {
      msg.customElem = content;
    }
    updateOneMessage({
      ...msg,
    } as MessageItem);
    if (data.modifyMsg.contentType === MessageType.CustomMessage) {
      const value = JSON.parse(content.data);
      if (
        value?.type === 'stream' &&
        value?.content?.answer !== AILoadingText
      ) {
        updateLlmLoading(false);
      }
    }
  };

  const disposeIMListener = () => {
    IMSDK.off(CbEvents.OnSelfInfoUpdated, selfUpdateHandler);
    IMSDK.off(CbEvents.OnConnecting, connectingHandler);
    IMSDK.off(CbEvents.OnConnectFailed, connectFailedHandler);
    IMSDK.off(CbEvents.OnConnectSuccess, connectSuccessHandler);
    IMSDK.off(CbEvents.OnKickedOffline, kickHandler);
    IMSDK.off(CbEvents.OnUserTokenExpired, expiredHandler);
    IMSDK.off(CbEvents.OnUserTokenInvalid, expiredHandler);
    // sync
    IMSDK.off(CbEvents.OnSyncServerStart, syncStartHandler);
    IMSDK.off(CbEvents.OnSyncServerProgress, syncProgressHandler);
    IMSDK.off(CbEvents.OnSyncServerFinish, syncFinishHandler);
    IMSDK.off(CbEvents.OnSyncServerFailed, syncFailedHandler);
    // message
    IMSDK.off(CbEvents.OnRecvNewMessages, newMessagesHandler);
    IMSDK.off(CbEvents.OnRecvNewMessage, newMessageHandler);
    // conversation
    IMSDK.off(CbEvents.OnConversationChanged, conversationChangeHandler);
    IMSDK.off(CbEvents.OnNewConversation, newConversationHandler);
    // IMSDK.off(
    //   CbEvents.OnTotalUnreadMessageCountChanged,
    //   totalUnreadChangeHandler
    // );
    // group
    IMSDK.off(CbEvents.OnJoinedGroupAdded, joinedGroupAddedHandler);
    IMSDK.off(CbEvents.OnJoinedGroupDeleted, joinedGroupDeletedHandler);
    IMSDK.off(CbEvents.OnGroupDismissed, joinedGroupDismissHandler);
    IMSDK.off(CbEvents.OnGroupInfoChanged, groupInfoChangedHandler);
    IMSDK.off(CbEvents.OnGroupMemberAdded, groupMemberAddedHandler);
    IMSDK.off(CbEvents.OnGroupMemberDeleted, groupMemberDeletedHandler);
    IMSDK.off(CbEvents.OnGroupMemberInfoChanged, groupMemberInfoChangedHandler);
    IMSDK.off(CbEvents.OnGroupUpMessageSet, groupUpMessageSetHandler);
    IMSDK.off(CbEvents.OnGroupUpMessageCancel, groupUpMessageCancelHandler);
    // application
    IMSDK.off(
      CbEvents.OnFriendApplicationAdded,
      friendApplicationProcessedHandler
    );
    IMSDK.off(
      CbEvents.OnFriendApplicationAccepted,
      friendApplicationProcessedHandler
    );
    IMSDK.off(
      CbEvents.OnFriendApplicationRejected,
      friendApplicationProcessedHandler
    );
    IMSDK.off(
      CbEvents.OnGroupApplicationAdded,
      groupApplicationProcessedHandler
    );
    IMSDK.off(
      CbEvents.OnGroupApplicationAccepted,
      groupApplicationProcessedHandler
    );
    IMSDK.off(
      CbEvents.OnGroupApplicationRejected,
      groupApplicationProcessedHandler
    );
    IMSDK.off(CbEvents.OnNewRecvMessageModified, newRecvMessageModified);

    // contact
    IMSDK.off(CbEvents.OnSyncDepartmentStart, startSyncDepartmentInfo);
    IMSDK.off(CbEvents.OnSyncDepartmentFinish, endSyncDepartmentInfo);

    // thread
    IMSDK.off(CbEvents.OnThreadCreated, onThreadCreated);

    IMSDK.off(CbEvents.OnUserClientRestart, onUpgradeEvent);
  };

  const setIpcListener = () => {
    window.electronAPI?.subscribe('appResume', () => {
      if (resume.current) {
        return;
      }
      resume.current = true;
      setTimeout(() => {
        resume.current = false;
      }, 5000);
    });
  };

  return {
    onUpgradeEvent,
  };
}
