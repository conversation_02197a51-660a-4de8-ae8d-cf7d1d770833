import { useCallback, useEffect, useMemo, useState } from 'react';
import { IMSDK } from '@/layouts/BasicLayout';
import { feedbackToast } from '@/utils/common';
import { BotCommandItem } from '@ht/openim-wasm-client-sdk';

export function useRobot(groupId: string) {
  const [robotCommandList, setRobotCommandList] = useState<BotCommandItem[]>();

  const fetchRobotCommands = useCallback(
    async ({ groupID, botID }: { groupID?: string; botID?: string }) => {
      try {
        const { data } = await IMSDK.getBotCommandList({ groupID, botID });
        setRobotCommandList(data);
      } catch (error) {
        feedbackToast({ error: error as Error });
        return [];
      }
    },
    []
  );

  const submitRobotCommand = useCallback(async (params) => {
    try {
      const { data } = await IMSDK.invokeBotInteraction(params);
      // throw new Error(data.errMsg || 'Command execution failed');
    } catch (error) {
      feedbackToast({ error: error as Error });
      return false;
    }
  }, []);

  const groupedCommands = useMemo(() => {
    if (!robotCommandList) {
      return new Map();
    }

    return robotCommandList.reduce((acc, command) => {
      if (!acc.has(command.botID)) {
        acc.set(command.botID, {
          botName: command.botName!,
          commands: [],
        });
      }
      acc.get(command.botID)!.commands.push(command);
      return acc;
    }, new Map<string, { botName: string; commands: BotCommandItem[] }>());
  }, [robotCommandList]);

  return {
    robotCommandList,
    groupedCommands,
    fetchRobotCommands,
    submitRobotCommand,
  };
}
