import type { SessionType } from '@ht/openim-wasm-client-sdk';
import { ConversationItem } from '@ht/openim-wasm-client-sdk/lib/types/entity';
import { useHistory } from 'react-router-dom';

import { IMSDK } from '@/layouts/BasicLayout';
import { useConversationStore } from '@/store';
import { feedbackToast } from '@/utils/common';

export type ToSpecifiedConversationParams = {
  sourceID: string;
  sessionType: SessionType;
  isJump?: boolean;
  isChildWindow?: boolean;
};

// 聊天框右侧显示内容
export type ToggleShowThreadParams = {
  rightArea: 'personDetail' | 'thread';
};

export function useConversationToggle() {
  const navigate = useHistory();

  const updateCurrentConversation = useConversationStore(
    (state) => state.updateCurrentConversation
  );
  const updateChannelHeaderCurTab = useConversationStore(
    (state) => state.updateChannelHeaderCurTab
  );
  const isMultiSession = useConversationStore((state) => state.isMultiSession);

  const changeRightArea = useConversationStore(
    (state) => state.changeRightArea
  );

  const getConversation = async ({
    sourceID,
    sessionType,
  }: {
    sourceID: string;
    sessionType: SessionType;
  }): Promise<ConversationItem | undefined> => {
    let conversation = useConversationStore
      .getState()
      .conversationList.find(
        (item) => item.userID === sourceID || item.groupID === sourceID
      );
    if (!conversation) {
      try {
        conversation = (
          await IMSDK.getOneConversation({
            sourceID,
            sessionType,
          })
        ).data;

        return conversation;
      } catch (error) {
        feedbackToast({ error });
      }
    }
    return conversation;
  };

  const toSpecifiedConversation = async (
    params: ToSpecifiedConversationParams,
    toChat = true,
    callback?: () => void
  ) => {
    const { sourceID, sessionType, isJump } = params;
    const conversation = await getConversation({ sourceID, sessionType });

    if (!conversation) {
      console.error('会话不存在');
      return;
    }
    if (
      useConversationStore.getState().currentConversation?.conversationID !==
      conversation.conversationID
    ) {
      await updateCurrentConversation({ ...conversation }, isJump);
    } else if (isMultiSession) {
      // 如果currentConversation相同并且是多会话，打开右侧会话列表
      changeRightArea(
        'OPEN_ROBOT_CONVERSATION_AREA',
        conversation.conversationID
      );
    }
    // 切换到消息页面
    updateChannelHeaderCurTab('message');
    if (toChat) {
      navigate.push(`./chat`);
    }
    if (callback) {
      callback();
    }
  };
  return {
    toSpecifiedConversation,
  };
}
