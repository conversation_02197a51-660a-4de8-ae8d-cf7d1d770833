import { useHistory } from 'react-router-dom';

// Recreate React Router v6's useNavigate hook using v5's useHistory
export function useNavigate() {
  const history = useHistory();

  return (
    to: string | number,
    options?: { replace?: boolean; state?: any }
  ) => {
    if (typeof to === 'number') {
      history.go(to);
      return;
    }

    if (typeof options === 'undefined') {
      history.push(to);
      return;
    }

    if (options.replace) {
      history.replace(to, options.state);
    } else {
      history.push(to, options.state);
    }
  };
}

// Recreate useSearchParams hook from v6
export function useSearchParams() {
  const history = useHistory();
  const { search } = history.location;
  const searchParams = new URLSearchParams(search);

  const setSearchParams = (
    nextParams: URLSearchParams | ((prev: URLSearchParams) => URLSearchParams),
    navigateOptions?: { replace?: boolean }
  ) => {
    const newSearchParams =
      typeof nextParams === 'function' ? nextParams(searchParams) : nextParams;

    const newSearch = newSearchParams.toString();
    const newPath = `${history.location.pathname}?${newSearch}`;

    if (navigateOptions?.replace) {
      history.replace(newPath);
    } else {
      history.push(newPath);
    }
  };

  return [searchParams, setSearchParams] as const;
}
