import { IMSDK } from '@/layouts/BasicLayout';
import { useUserStore } from '@/store';
import { UserStatusType } from '@/store/type';
import { CbEvents, PublicUserItem, WSEvent } from '@ht/openim-wasm-client-sdk';
import { useState, useEffect, useMemo } from 'react';

const useUserInfo = (userID?: string) => {
  const { selfInfo, selfStatus } = useUserStore();
  const [userDetail, setUserDetail] = useState<PublicUserItem>();

  useEffect(() => {
    const getUserDetailData = async () => {
      try {
        const userDetailDataResponse = await IMSDK.getUsersInfo([userID!]);

        if (userDetailDataResponse?.data?.[0] != null) {
          setUserDetail({
            ...userDetailDataResponse.data[0],
            faceURL: '',
            employeeCode: JSON.parse(userDetailDataResponse.data[0]?.ex || '{}')
              ?.employeeCode,
            positionInfos:
              JSON.parse(userDetailDataResponse.data[0]?.ex || '{}')
                ?.positionInfos ||
              userDetailDataResponse.data[0].positionInfos ||
              [],
          });
        } else {
          throw new Error(`接口返回${JSON.stringify(userDetailDataResponse)}`);
        }
      } catch (e: any) {
        setUserDetail({
          userID: userID!,
          nickname: '--',
          faceURL: '',
          ex: '',
          employeeCode: '',
          positionInfos: [],
        });
      }
    };

    if (userID === selfInfo.userID) {
      setUserDetail(selfInfo);
    } else if (userID != null && userID !== '') {
      getUserDetailData();
    } else {
      setUserDetail(undefined);
    }
  }, [selfInfo, userID]);

  useEffect(() => {
    // 查询后回调触发
    const updateUserDetail = ({ data }: WSEvent<PublicUserItem>) => {
      if (data.userID === userID) {
        setUserDetail({ ...userDetail, ...data });
      }
    };

    if (userID != null && userID !== '' && userID !== selfInfo.userID) {
      IMSDK.on(CbEvents.OnUserInfoUpdated, updateUserDetail);
    }

    return () => {
      if (userID != null && userID !== '' && userID !== selfInfo.userID) {
        IMSDK.off(CbEvents.OnUserInfoUpdated, updateUserDetail);
      }
    };
  }, [selfInfo.userID, userDetail, userID]);

  const userState: UserStatusType = useMemo(() => {
    if (userID === selfInfo.userID) {
      return selfStatus;
    } else {
      return JSON.parse(userDetail?.ex || '{}')?.userState;
    }
  }, [selfInfo.userID, selfStatus, userDetail?.ex, userID]);

  const userIsBot: any = useMemo(() => {
    if (userID === selfInfo.userID) {
      return JSON.parse(selfInfo?.ex || '{}')?.isBot;
    } else {
      return JSON.parse(userDetail?.ex || '{}')?.isBot;
    }
  }, [selfInfo?.ex, selfInfo.userID, userDetail?.ex, userID]);

  const multiSession: any = useMemo(() => {
    if (userID === selfInfo.userID) {
      return JSON.parse(selfInfo?.ex || '{}')?.multiSession;
    } else {
      return JSON.parse(userDetail?.ex || '{}')?.multiSession;
    }
  }, [selfInfo?.ex, selfInfo.userID, userDetail?.ex, userID]);

  return { userDetail, userState, userIsBot, multiSession };
};

export default useUserInfo;
