import { IMSDK } from '@/layouts/BasicLayout';
import { useConversationStore, useUserStore } from '@/store';
import _ from 'lodash';
import { message } from '@ht/sprite-ui';
import {
  GroupJoinSource,
  GroupVerificationType,
  MessageItem,
} from '@ht/openim-wasm-client-sdk';
import { useState } from 'react';
import { parseThreadIdFromMessage } from '@/utils/utils';
import { shallow } from 'zustand/shallow';

type createThreadType = {
  parentId: string;
  startClientMsg: MessageItem;
  groupName?: string;
};

export default function useThreadState() {
  const [loading, setLoading] = useState(false);
  const {
    currentConversation,
    currentThreadGroupInfo,
    currentThreadConversation,
    setCurrentThreadConversation,
    openThreadConversation,
    currentThreadFirstMessage,
  } = useConversationStore(
    (state) => ({
      currentConversation: state.currentConversation,
      currentThreadGroupInfo: state.currentThreadGroupInfo,
      currentThreadConversation: state.currentThreadConversation,
      setCurrentThreadConversation: state.setCurrentThreadConversation,
      openThreadConversation: state.openThreadConversation,
      currentThreadFirstMessage: state.currentThreadFirstMessage,
    }),
    shallow
  );

  // 加入thread
  const joinThread = async (threadId: string) => {
    try {
      setLoading(true);
      const isJonGroup = await IMSDK.isJoinGroup(threadId);
      // 未加群的需要先加入群
      if (isJonGroup.data === false) {
        await IMSDK.joinGroup({
          groupID: threadId,
          reqMsg: '',
          joinSource: GroupJoinSource.Search,
        });

        return true;
      }
      setLoading(false);

      return true;
    } catch (e) {
      console.error('joinThread--失败', threadId, e);
      return false;
    }
  };

  const createThread = async () => {
    const parentID = currentThreadGroupInfo?.parentId || '';
    const startClientMsg = currentThreadFirstMessage;
    let tetx = startClientMsg?.textElem?.content;
    if (tetx && tetx.length > 80) {
      tetx = tetx.substring(0, 80);
    }
    const groupName =
      tetx || `${parentID}-的子群组-${startClientMsg?.clientMsgID || ''}`;

    // step 1、创建子群聊
    try {
      setLoading(true);

      const ResonseData = await IMSDK.createGroup({
        groupInfo: {
          groupName,
          groupType: 5,
          parentID, // 这里先不要改，目前SDK给的类型有点问题，不是写错
          startClientMsgSeq: startClientMsg?.seq, // 创建群组时带上原始消息的seq，方便后续查询thread的第一条消息
          startClientMsgID: startClientMsg?.clientMsgID,
          needVerification: GroupVerificationType.AllNot, // 务必设置thread为直接进群模式
        },

        memberUserIDs:
          startClientMsg?.sendID &&
          startClientMsg?.sendID !== useUserStore.getState().selfInfo.userID
            ? [startClientMsg?.sendID]
            : [], // 创建群组时把原消息的发送人作为群成员加进去
      });

      setLoading(false);
      // step 2、设置CurrentThreadConversation
      const threadId = ResonseData.data?.groupID;

      await setCurrentThreadConversation(threadId, parentID, startClientMsg!);
      return threadId;
    } catch (e: any) {
      message.error('createThread--失败', e);
      setLoading(false);

      return null;
    }
  };

  const getThreadFristMessage = async (groupID?: string, seq?: number) => {
    try {
      if (groupID == null || seq == null) {
        return;
      }
      const conversationID = `sg_${groupID}`;

      const seqRes = await IMSDK.getSeqMessage({ conversationID, seq });

      const startClientMsg = seqRes.data;

      if (startClientMsg != null) {
        return startClientMsg;
      }
      return null;
    } catch (e) {
      console.error('getFristMessageByThreadID', e);
      return null;
    }
  };

  const openThread = async ({ parentId, startClientMsg }: createThreadType) => {
    try {
      if (
        currentThreadConversation?.groupID === parentId &&
        currentThreadFirstMessage === startClientMsg
      ) {
        return;
      }
      // 当前非群聊，不支持打开
      if (currentConversation?.groupID == null) {
        console.error('currentConversation非群聊，暂时不支持打开thread');
        return;
      }

      const threadId = parseThreadIdFromMessage(startClientMsg);

      // 存在当前消息开头的thread时，打开已有thread
      if (threadId != null && threadId !== '') {
        // 需要把当前人加到thread里，否则不在thread里的人点开时看不到历史消息
        const joinThreadResult = await joinThread(threadId);
        if (joinThreadResult) {
          setCurrentThreadConversation(
            threadId,
            startClientMsg.groupID,
            startClientMsg
          );
        }
      } else {
        // 无thread时，走新建thread逻辑
        openThreadConversation(parentId, startClientMsg);
      }
    } catch (e) {
      console.error('openThread--失败', e);
    }
  };

  return {
    loading,
    currentThreadConversation,
    createThread,
    openThread,
    getThreadFristMessage,
    joinThread,
  };
}
