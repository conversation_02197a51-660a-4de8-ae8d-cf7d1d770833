import { IMSDK } from '@/layouts/BasicLayout';
import {
  CbEvents,
  ConversationInputStatus,
  ConversationItem,
  WSEvent,
} from '@ht/openim-wasm-client-sdk';
import { useEffect, useState } from 'react';

// 私聊用户的状态
const useTypingStatus = (currentConversation?: ConversationItem) => {
  const [typing, setTyping] = useState(false);

  useEffect(() => {
    // 仅私聊需要
    if (
      currentConversation?.userID != null &&
      currentConversation?.userID !== ''
    ) {
      const conversationUserInputStatusChangedHandler = ({
        data,
      }: WSEvent<ConversationInputStatus>) => {
        if (
          data.conversationID !== currentConversation?.conversationID ||
          data.userID !== currentConversation.userID
        ) {
          return;
        }
        setTyping(Boolean(data.platformIDs.length));
      };
      IMSDK.on(
        CbEvents.OnConversationUserInputStatusChanged,
        conversationUserInputStatusChangedHandler
      );
    }
  }, [currentConversation?.conversationID, currentConversation?.userID]);

  return {
    typing,
  };
};
export default useTypingStatus;
