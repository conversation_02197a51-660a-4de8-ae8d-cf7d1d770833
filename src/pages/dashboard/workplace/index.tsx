import { Ava<PERSON>, Card, Col, List, Row } from '@ht/sprite-ui';
import React, { useState, useEffect } from 'react';
import type { Dispatch } from '@oula/oula';
import { Link, connect } from '@oula/oula';
import moment from 'moment';
import { getSDK, CbEvents } from '@ht/openim-wasm-client-sdk';
import type { ModalState } from './model';
import EditableLinkGroup from './components/EditableLinkGroup';
import styles from './style.less';
import type { ActivitiesType, NoticeType } from './data.d';
import { getAdminToken, getUserToken } from '../../../services/service';

const links = [
  {
    title: '列表页',
    href: '/list',
  },
];

interface WorkplaceProps {
  projectNotice: NoticeType[];
  activities: ActivitiesType[];
  dispatch: Dispatch;
  projectLoading: boolean;
  activitiesLoading: boolean;
}

const Workplace = () => {
  const [friends, setFriends] = useState<any>([]);
  const [conversations, setConversations] = useState<any>([]);
  const [text, setText] = useState<any>('');
  const [newMsg, setNewMsg] = useState<any>([]);
  useEffect(() => {
    const IMSDK = getSDK({
      coreWasmPath: '/openIM.021711.wasm',
      sqlWasmPath: '/sql-wasm.wasm',
      // debug: true,
    });
    const userID = '2924991196';

    IMSDK.on(CbEvents.OnRecvNewMessage, ({ data }) => {
      // data 新消息
      // let oldmsg = JSON.parse(JSON.stringify(newMsg))
      // oldmsg.push(data)
      // setNewMsg(oldmsg)
    IMSDK.on(CbEvents.OnConversationChanged, ({ data }) => {
      // data 会话信息
      const oldmsg = JSON.parse(JSON.stringify(newMsg));
      oldmsg.push(data);
      setNewMsg(oldmsg);
    });

    IMSDK.on(CbEvents.OnConnectSuccess, () => {
      // 好友列表
      IMSDK.getFriendList()
        .then(({ data }) => {
          // 调用成功
          setFriends(data);
        })
        .catch(({ errCode, errMsg }) => {

          // 调用失败
        });
      // 会话列表
      IMSDK.getAllConversationList()
        .then(({ data }) => {
          // 调用成功
          setConversations(data);
        })
        .catch(({ errCode, errMsg }) => {
          // 调用失败
        });
    });

    getAdminToken().then((res) => {
      getUserToken(userID, res?.data?.token).then((res2) => {
        const token = res2?.data?.token;
        const config: any = {
          userID: '2924991196', // IM 用户 userID
          token, // IM 用户令牌
          platformID: 5, // 当前登录平台号
          apiAddr: 'http://168.64.9.42:10002', // IM api 地址，一般为`http://xxx:10002`或`https://xxx/api
          wsAddr: 'ws://168.64.9.42:10001', // IM ws 地址，一般为`ws://xxx:10001`或`wss://xxx/message_gateway`
        };
        IMSDK.login(config)
          .then(() => {
            // setConnectSuccess(true)
          })
          .catch(({ errCode, errMsg }) => {
            // 登录失败
          });
      });
    });
  }, []);

  const sendMessage = async () => {
    const IMSDK = getSDK();
    const msg = await IMSDK.createTextMessage(text);
    const cardMsg = await IMSDK.createCardMessage({
      userID: '2924991196',
      nickname: '华泰证券',
      faceURL:
        'https://tse4-mm.cn.bing.net/th/id/OIP-C.JCEcaQJVR_vC2kgt6BGZlAAAAA?rs=1&pid=ImgDetMain',
      ex: '测试ex字段',
    });
    const locMsg = await IMSDK.createLocationMessage({
      description: '华泰证券广场',
      longitude: 118.730256,
      latitude: 32.007549,
    });
  };

  return (
    <div>
      <input onChange={(e) => setText(e.target.value)}></input>
      <button type="button" onClick={sendMessage}>
        发送
      </button>
      <br />

      <div>
        好友列表：
        <br />
        {friends.map((f: any) => (
          <div key={f.userID}>
            {f.nickname}({f.userID})
          </div>
        ))}
      </div>
      <div>
        会话列表：
        <br />
        {conversations.map((f: any) => (
          <div key={f.conversationID}>
            {f.showName}({JSON.stringify(f)})
          </div>
        ))}
      </div>
      <div>
        新消息：
        <br />
        {JSON.stringify(newMsg)}
      </div>
    </div>
  );
};

export default connect(
  ({
    dashboard: { projectNotice, activities },
    loading,
  }: {
    dashboard: ModalState;
    loading: {
      effects: Record<string, boolean>;
    };
  }) => ({
    projectNotice,
    activities,
    projectLoading: loading.effects['dashboard/fetchProjectNotice'],
    activitiesLoading: loading.effects['dashboard/fetchActivitiesList'],
  })
)(Workplace);
