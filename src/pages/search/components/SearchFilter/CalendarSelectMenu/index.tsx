import classNames from 'classnames';
import { isEmpty } from 'lodash';
import { Dropdown } from '@ht/sprite-ui';
import { useState, useMemo } from 'react';
import dayjs from 'dayjs';
import selectedIcon from '@/assets/images/searchFilter/selected.svg';
import CalendarSelect from './CalendarSelect';
import styles from './index.less';

interface CalendarSelectMenuProps {
  handleClose: () => void;
  onSearchFilterParamsChange: (params: any) => void;
  searchFilterParams: any;
}
const CalendarSelectMenu = ({
  onSearchFilterParamsChange,
  handleClose,
  searchFilterParams,
}: CalendarSelectMenuProps) => {
  const { dateSelect = '', customizeDate = [] } = searchFilterParams;
  const [customizeDateVisible, setCustomizeDateVisible] = useState(false);

  const MenuList = [
    {
      label: '最近一周',
      key: 'week',
    },
    {
      label: '最近一个月',
      key: 'month',
    },
    {
      label: '最近三个月',
      key: 'threeMonth',
    },
    {
      label: '自定义时间',
      key: 'customizeTime',
    },
  ];

  const getPopupContainer = useMemo(() => {
    return document.getElementById('CalendarSelectMenuId') || document.body;
  }, []);
  return (
    <div
      id="CalendarSelectMenuId"
      className={styles.calendarSelectMenuWrapper}
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
      }}
    >
      {MenuList.map((item) => {
        return item.key !== 'customizeTime' ? (
          <div
            key={item.key}
            className={classNames(
              styles.menuItemButton,
              dateSelect.key === item.key && styles.menuItemButtonActived
            )}
            onClick={() => {
              handleClose();
              const positionTime = dayjs().unix();
              let periodTime = 0;
              const now = dayjs();
              let periodDate = dayjs();
              if (item.key === 'week') {
                periodDate = now.subtract(7, 'day').startOf('day');
              } else if (item.key === 'month') {
                periodDate = now.subtract(30, 'day').startOf('day');
              } else if (item.key === 'threeMonth') {
                periodDate = now.subtract(90, 'day').startOf('day');
              }
              periodTime = Math.abs(periodDate.diff(now, 'second'));
              if (dateSelect.key === item.key) {
                return;
              }
              onSearchFilterParamsChange({
                dateSelect: item,
                searchTimePosition: positionTime,
                searchTimePeriod: periodTime,
                customizeDate: [],
              });
            }}
          >
            {item.label}
            <img src={selectedIcon} />
          </div>
        ) : (
          <Dropdown
            trigger={['click']}
            getPopupContainer={() => getPopupContainer}
            open={customizeDateVisible}
            onOpenChange={(open) => {
              setCustomizeDateVisible(open);
            }}
            placement="right"
            overlay={
              <CalendarSelect
                handleClose={() => {
                  setCustomizeDateVisible(false);
                }}
                isOpen={customizeDateVisible}
                dateValue={customizeDate}
                onSelect={(val) => {
                  handleClose();
                  if (isEmpty(val)) {
                    onSearchFilterParamsChange({
                      dateSelect: {},
                      searchTimePosition: 0,
                      searchTimePeriod: 0,
                      customizeDate: [],
                    });
                  } else {
                    const startDate = val[0];
                    const endDate = val[1];

                    const positionDate = endDate.endOf('day').unix();
                    const periodDate = startDate.startOf('day').unix();
                    const periodTime = positionDate - periodDate;

                    onSearchFilterParamsChange({
                      dateSelect: item,
                      searchTimePosition: positionDate,
                      searchTimePeriod: periodTime,
                      customizeDate: val,
                    });
                  }
                }}
              />
            }
          >
            <div
              onClick={() => {
                setCustomizeDateVisible(true);
              }}
              className={classNames(
                styles.menuItemButton,
                dateSelect.key === item.key && styles.menuItemButtonActived
              )}
            >
              {item.label}
              <img src={selectedIcon} />
            </div>
          </Dropdown>
        );
      })}
    </div>
  );
};
export default CalendarSelectMenu;
