.calendarSelectMenuWrapper {
  width: 220px;
  background: #fff;
  box-shadow: 0 2px 4px 2px rgba(0, 0, 0, 4%);
  border-radius: 8px;
  padding: 6px;
  color: #1d1c1d;
  display: flex;
  flex-direction: column;

  .menuItemButton {
    display: flex;
    align-content: center;
    align-items: center;
    justify-content: space-between;
    color: #1d1c1d;
    padding: 0 18px;
    cursor: pointer;
    line-height: 36px;
    height: 36px;
    font-size: 14px;
    font-weight: 400;

    > img {
      display: none;
    }

    &:hover {
      background: var(--msg-qute-backgroud-color);
      border-radius: 6px;
    }
  }

  .menuItemButtonActived {
    color: var(--primary-text-color-9);

    > img {
      display: block;
    }
  }
}
