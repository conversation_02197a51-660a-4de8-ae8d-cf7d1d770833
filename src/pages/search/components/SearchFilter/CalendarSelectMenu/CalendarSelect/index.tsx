/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-lines */
/* eslint-disable max-statements */
/* eslint-disable indent */
import { useState, useRef, useEffect, useMemo, FC } from 'react';
import classNames from 'classnames';
import { isEmpty } from 'lodash';
import { Dropdown } from '@ht/sprite-ui';
import moment, { Moment } from 'moment';
import arrowIcon from '@/assets/images/searchFilter/arrow.svg';
import selectedIcon from '@/assets/images/searchFilter/selected.svg';
import styles from './index.less';

interface CustomDateRangePickerProps {
  onSelect: (val: Moment[]) => void;
  dateValue: Moment[];
  isOpen: boolean;
  handleClose: () => void;
}

const CustomDateRangePicker: FC<CustomDateRangePickerProps> = ({
  onSelect,
  dateValue,
  isOpen,
  handleClose,
}) => {
  const [currentDate, setCurrentDate] = useState(moment());
  const [selectedDates, setSelectedDates] = useState<Moment[]>([]);
  const [isSelecting, setIsSelecting] = useState(false);
  const [monthMenuOpen, setMonthMenuOpen] = useState(false);
  const daysContainerRef = useRef(null);

  useEffect(() => {
    if (isOpen && !isEmpty(dateValue)) {
      setSelectedDates(dateValue);
    }
  }, [isOpen, dateValue]);

  useEffect(() => {
    if (!isOpen && !isSelecting && !isEmpty(selectedDates)) {
      onSelect(selectedDates);
    }
  }, [isOpen, isSelecting, selectedDates]);

  const getPopupContainer = useMemo(() => {
    return document.getElementById('customDateRangePickerId') || document.body;
  }, []);

  // 生成12个月的选项
  const generateMonthOptions = () => {
    const months = [];
    const currentYear = currentDate.year();

    for (let i = 0; i < 12; i++) {
      const monthDate = moment().year(currentYear).month(i).startOf('month');
      months.push({
        value: i,
        label: monthDate.format('M月'),
      });
    }

    return months;
  };

  // 处理年切换
  const changeYear = (amount: number) => {
    const newYear = currentDate.clone().add(amount, 'years');
    setCurrentDate(newYear);
  };

  // 处理月份切换
  const changeMonth = (amount: number) => {
    const newMonth = currentDate.clone().add(amount, 'months');
    setCurrentDate(newMonth);
  };

  // 处理下拉选择月份
  const handleMonthSelect = (monthIndex: number) => {
    setCurrentDate(currentDate.clone().month(monthIndex));
    setMonthMenuOpen(false);
  };

  // 处理日期选择
  const handleDateSelect = (date: Moment) => {
    if (!date) {
      return;
    }

    if (!isSelecting) {
      // 开始选择 - 高亮单个日期
      setSelectedDates([date]);
      setIsSelecting(true);
    } else if (moment(date).isBefore(selectedDates[0])) {
      // 如果选择的日期比第一个早，则重置选择
      setSelectedDates([date]);
    } else {
      // 完成选择
      setSelectedDates([selectedDates[0], date]);
      setIsSelecting(false);
      handleClose();
    }
  };

  // 添加滚轮事件监听
  useEffect(() => {
    const container: any = daysContainerRef.current;

    const handleWheel = (e: any) => {
      e.preventDefault();
      if (e.deltaY > 0) {
        changeMonth(1);
      } else if (e.deltaY < 0) {
        changeMonth(-1);
      }
    };
    if (container) {
      container.addEventListener('wheel', handleWheel);
    }
    return () => {
      if (container) {
        container.removeEventListener('wheel', handleWheel);
      }
    };
  }, [currentDate]);

  const renderHeader = () => {
    return (
      <div className={styles.calendarHeader}>
        <div>
          <span>{moment(currentDate).format('YYYY年MM月')}</span>
          <Dropdown
            trigger={['click']}
            getPopupContainer={() => getPopupContainer}
            open={monthMenuOpen}
            onOpenChange={(open) => {
              setMonthMenuOpen(open);
            }}
            placement="bottom"
            overlay={
              monthMenuOpen ? (
                <div
                  className={styles.monthMenuWrapper}
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                >
                  {generateMonthOptions().map((item, index) => {
                    return (
                      <div
                        key={item.value}
                        className={classNames(
                          styles.menuItemButton,
                          currentDate.month() === item.value &&
                            styles.menuItemButtonActived
                        )}
                        onClick={() => {
                          handleMonthSelect(index);
                        }}
                      >
                        {item.label}
                        <img src={selectedIcon} />
                      </div>
                    );
                  })}
                </div>
              ) : (
                <></>
              )
            }
          >
            <img
              src={arrowIcon}
              onClick={() => setMonthMenuOpen(true)}
              className={styles.arrowIcon}
            />
          </Dropdown>
        </div>
        <div>
          <img
            src={arrowIcon}
            className={styles.leftArrow}
            onClick={() => {
              changeYear(-1);
            }}
          />
          <img
            src={arrowIcon}
            className={styles.rightArrow}
            onClick={() => {
              changeYear(1);
            }}
          />
        </div>
      </div>
    );
  };

  // 获取要显示的所有日期
  const getDisplayDays = () => {
    const startOfMonth = currentDate.clone().startOf('month');
    const endOfMonth = currentDate.clone().endOf('month');

    // 当前月份的所有日期
    const daysInMonth = endOfMonth.diff(startOfMonth, 'days') + 1;
    const currentMonthDays = Array.from({ length: daysInMonth }, (_, i) =>
      startOfMonth.clone().add(i, 'days')
    );

    // 上个月的最后几天
    const firstDayOfWeek = startOfMonth.day();
    const prevMonthDays =
      firstDayOfWeek > 0
        ? Array.from({ length: firstDayOfWeek }, (_, i) =>
            currentDate
              .clone()
              .subtract(1, 'month')
              .endOf('month')
              .subtract(firstDayOfWeek - i - 1, 'days')
          )
        : [];

    // 下个月的前几天
    const lastDayOfWeek = endOfMonth.day();
    const nextMonthDays =
      lastDayOfWeek < 6
        ? Array.from({ length: 6 - lastDayOfWeek }, (_, i) =>
            currentDate.clone().add(1, 'month').startOf('month').add(i, 'days')
          )
        : [];

    return [...prevMonthDays, ...currentMonthDays, ...nextMonthDays];
  };

  // 渲染所有日期
  const renderAllDays = () => {
    const days = getDisplayDays();
    const today = moment();

    // 判断日期是否属于当前月份
    const isCurrentMonth = (date: Moment) =>
      moment(date).month() === currentDate.month();

    // 判断是否是今天
    const isToday = (date: Moment) => moment(date).isSame(today, 'day');

    // 判断日期是否在今天之后
    const isMomentAfterToday = (inputMoment: Moment) => {
      const startOfToday = moment().startOf('day');
      const inputDate = inputMoment.clone().startOf('day');
      return inputDate.isAfter(startOfToday);
    };

    // 判断今天是周几
    const weekNum = moment().format('d');

    // 判断是否是选中的单个日期
    const isSingleSelected = (date: Moment) =>
      selectedDates.length === 1 &&
      moment(date).isSame(selectedDates[0], 'day');

    // 判断日期是否在选中范围内
    const isInRange = (date: Moment) => {
      if (selectedDates.length < 2) {
        return false;
      }

      return moment(date).isBetween(
        selectedDates[0],
        selectedDates[1],
        null,
        '[]'
      );
    };

    // 判断是否是范围起点或终点
    const isRangeEdge = (date: Moment) => {
      if (selectedDates.length < 2) {
        return false;
      }
      return (
        moment(date).isSame(selectedDates[0], 'day') ||
        moment(date).isSame(selectedDates[1], 'day')
      );
    };

    // 判断日期在范围内的位置（左、中、右）
    const getRangePosition = (date: Moment, index: number) => {
      if (!isInRange(date)) {
        return null;
      }
      if (isRangeEdge(date)) {
        return 'edge';
      }

      const prevDay = index > 0 ? days[index - 1] : null;
      const nextDay = index < days.length - 1 ? days[index + 1] : null;

      if (prevDay && !isInRange(prevDay)) {
        return 'left';
      }
      if (nextDay && !isInRange(nextDay)) {
        return 'right';
      }
      return 'middle';
    };

    return (
      <div ref={daysContainerRef} className={styles.daysContainer}>
        {['日', '一', '二', '三', '四', '五', '六'].map((day, index) => (
          <div
            key={day}
            className={classNames(
              styles.dayTitle,
              Number(weekNum) === index && styles.todayTitle
            )}
          >
            {day}
          </div>
        ))}

        {days.map((day, index) => {
          const rangePos = getRangePosition(day, index);
          const isEdge = isRangeEdge(day);
          const ifInRange = rangePos !== null;
          const isSingle = isSingleSelected(day);
          const isAfterTody = isMomentAfterToday(day);

          let backgroundColor = '';
          let color = isCurrentMonth(day) ? '#1D1C1D' : '#999BA0';
          let opacity = 1;
          if (isAfterTody) {
            opacity = 0.5;
          } else if (isToday(day)) {
            backgroundColor = 'var(--primary-text-color-9)';
            color = 'white';
          } else if (isSingle) {
            backgroundColor = 'var(--primary-text-color-9)';
            color = 'white';
          } else if (isEdge) {
            backgroundColor = 'var(--primary-text-color-9)';
            color = 'white';
          } else if (ifInRange) {
            backgroundColor = '#E6F1FC';
          }

          let classname = '';
          if (ifInRange) {
            if (
              rangePos === 'left' ||
              (isEdge && day.isSame(selectedDates[0], 'day'))
            ) {
              classname = 'leftDate';
            } else if (
              rangePos === 'right' ||
              (isEdge && day.isSame(selectedDates[1], 'day'))
            ) {
              classname = 'rightDate';
            } else if (rangePos === 'middle') {
              classname = 'rangeDate';
            }
          }

          return (
            <div
              key={day.format('YYYY-MM-DD')}
              className={classNames(
                styles.dateWrapper,
                styles[classname],
                isAfterTody && styles.noHover
              )}
              onClick={() => {
                if (!isAfterTody) {
                  handleDateSelect(day);
                }
              }}
            >
              <div
                className={styles.dateBox}
                style={{
                  backgroundColor,
                  color,
                  opacity,
                }}
              >
                {isToday(day) ? '今' : day.date()}
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div
      className={styles.customDateRangePickerWrapper}
      id="customDateRangePickerId"
    >
      {renderHeader()}
      {renderAllDays()}
    </div>
  );
};

export default CustomDateRangePicker;
