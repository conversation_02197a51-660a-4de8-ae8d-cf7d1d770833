.customDateRangePickerWrapper {
  border: 1px solid #dddee0;
  width: 340px;
  background: #ffffff;
  box-shadow: 0 2px 4px 2px rgba(0, 0, 0, 4%);
  border-radius: 12px;
  padding: 0;
  overflow: hidden;

  .calendarHeader {
    padding: 18px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 16px;
    font-weight: 600;
    color: rgba(29, 28, 29, 85%);
    line-height: 22px;

    > div {
      display: flex;
      align-items: center;
    }

    .arrowIcon,
    .leftArrow,
    .rightArrow {
      width: 18px;
      height: 18px;
      padding: 3px;
      border-radius: 4px;

      &:hover {
        background: rgba(107, 107, 108, 8%);
        cursor: pointer;
      }
    }

    .arrowIcon {
      margin-left: 2px;
    }
    .rightArrow,
    .leftArrow {
      margin-left: 6px;
    }

    .leftArrow {
      transform: rotate(90deg);
    }

    .rightArrow {
      transform: rotate(-90deg);
    }
  }

  .daysContainer {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 14px 0;
    max-height: 400px;
    overflow-y: auto;
    padding: 0 8px 24px;
    cursor: default;
    position: relative;

    .dayTitle {
      text-align: center;
      height: 26px;
      font-size: 14px;
      font-weight: 600;
      color: #999ba0;
    }

    .todayTitle {
      color: #0074e2;
    }

    .dateWrapper {
      height: 26px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      width: 100%;
      position: relative;

      .dateBox {
        width: 26px;
        height: 26px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        z-index: 1;
        font-size: 14px;
        font-weight: 600;
        line-height: 20px;
        border-radius: 8px;

        &:hover {
          background-color: rgba(107, 107, 108, 8%);
        }
      }

      &:nth-of-type(7n + 1) {
        &::before {
          content: "";
          display: block;
          width: 50%;
          height: 100%;
          position: absolute;
          background-color: #fff;
          left: 0;
        }
      }
      &:nth-of-type(7n + 7) {
        &::before {
          content: "";
          display: block;
          width: 50%;
          height: 100%;
          position: absolute;
          background-color: #fff;
          right: 0;
        }
      }
    }
    .noHover {
      cursor: not-allowed;
      .dateBox {
        &:hover {
          background-color: none;
        }
      }
    }

    .leftDate {
      &::after {
        content: "";
        width: 50%;
        height: 100%;
        position: absolute;
        background-color: #e6f1fc;
        display: block;
        right: 0;
      }
    }

    .rightDate {
      &::after {
        content: "";
        width: 50%;
        height: 100%;
        position: absolute;
        background-color: #e6f1fc;
        display: block;
        left: 0;
      }
    }

    .rangeDate {
      background-color: #e6f1fc;
    }
  }
}

.monthMenuWrapper {
  width: 100px;
  height: 238px;
  background: #fff;
  box-shadow: 0 2px 4px 2px rgba(0, 0, 0, 4%);
  border-radius: 8px;
  padding: 6px;
  color: #1d1c1d;
  display: flex;
  flex-direction: column;
  overflow-y: auto;

  .menuItemButton {
    display: flex;
    align-content: center;
    align-items: center;
    justify-content: space-between;
    color: #1d1c1d;
    padding: 0 12px 0 18px;
    cursor: pointer;
    line-height: 36px;
    height: 36px;
    font-size: 14px;
    font-weight: 400;
    white-space: nowrap;

    > img {
      display: none;
    }

    &:hover {
      background: var(--msg-qute-backgroud-color);
      border-radius: 6px;
    }
  }

  .menuItemButtonActived {
    color: var(--primary-text-color-9);

    > img {
      display: block;
    }
  }
}
