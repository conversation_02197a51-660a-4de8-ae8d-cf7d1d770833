.conversationSelectMenuWrapper {
  width: 270px;
  background: #ffffff;
  box-shadow: 0 2px 4px 2px rgba(0, 0, 0, 4%);
  border-radius: 8px;
  border: 1px solid var(--primary-border-color);
  padding: 6px 0;
  display: flex;
  flex-direction: column;

  .menuItemButton {
    display: flex;
    align-items: center;
    height: 52px;
    padding: 0 10px;
    font-size: 15px;
    font-weight: 600;
    color: var(--primary-text-color-1);
    line-height: 21px;

    .checkIcon {
      margin-right: 17px;
    }

    .groupIcon {
      width: 32px;
      border-radius: 6px;
      margin-right: 10px;
    }

    .highlightClassName {
      background-color: transparent;
      color: var(--primary-text-color-9);
    }
  }

  .selectedList {
    .selectedDesc {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 10px 4px;
      font-size: 12px;
      color: var(--primary-text-color-7);
      line-height: 16px;

      .deleteAll {
        cursor: pointer;
      }
    }

    .menuItemButton {
      justify-content: space-between;
      padding: 0 0 0 10px;
      color: var(--primary-text-color-9);

      .searchValue {
        display: flex;
        align-items: center;
        flex: 1;
        overflow: hidden;
      }

      .deleteIcon {
        width: 32px;
        height: 32px;
        border-radius: 6px;
        margin-left: 2px;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          background: var(--msg-qute-backgroud-color);
          cursor: pointer;
        }
      }

      &:hover {
        background: transparent !important;
        border-radius: 6px;
        cursor: default !important;
      }
    }
  }

  .line {
    width: 100%;
    height: 1px;
    background: var(--primary-border-color);
    margin: 2px;
  }

  .virtuosoListContainer {
    max-height: 55vh;
    overflow-x: hidden;
    .menuItemButton:hover {
      background: var(--msg-qute-backgroud-color);
      border-radius: 6px;
      cursor: pointer;
    }

    .checkIcon {
      cursor: pointer;
    }

    > div {
      display: flex;
      flex-direction: column;
      padding: 0 6px;
    }
  }

  :global {
    .linkflow-typography {
      color: inherit;
    }
  }
}
