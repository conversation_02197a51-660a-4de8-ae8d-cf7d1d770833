import classNames from 'classnames';
import selectedIcon from '@/assets/images/searchFilter/selected.svg';
import styles from './index.less';

interface CustomTypeMenuProps {
  handleClose: () => void;
  onSearchFilterParamsChange: (params: any) => void;
  searchFilterParams: any;
}
const SortSelectMenu = ({
  onSearchFilterParamsChange,
  handleClose,
  searchFilterParams,
}: CustomTypeMenuProps) => {
  const { sort = {} } = searchFilterParams;

  const MenuList = [
    {
      label: '最相关',
      key: '_score',
    },
    {
      label: '发送时间',
      key: 'send_time',
    },
  ];
  const SortList = [
    {
      label: '升序',
      key: true,
    },
    {
      label: '降序',
      key: false,
    },
  ];
  return (
    <div
      className={styles.sortSelectMenuWrapper}
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
      }}
    >
      {MenuList.map((item) => {
        return (
          <div
            key={item.key}
            className={classNames(
              styles.menuItemButton,
              sort.field === item.key && styles.menuItemButtonActived
            )}
            onClick={() => {
              handleClose();
              onSearchFilterParamsChange({
                sort: {
                  field: item.key,
                  ascending: false,
                },
              });
            }}
          >
            {item.label}
            <img src={selectedIcon} />
          </div>
        );
      })}
      <div className={styles.line}></div>
      {SortList.map((item) => {
        return (
          <div
            key={item.label}
            className={classNames(
              styles.menuItemButton,
              sort.ascending === item.key && styles.menuItemButtonActived
            )}
            onClick={() => {
              handleClose();
              onSearchFilterParamsChange({
                sort: {
                  field: sort.field,
                  ascending: item.key,
                },
              });
            }}
          >
            {item.label}
            <img src={selectedIcon} />
          </div>
        );
      })}
    </div>
  );
};
export default SortSelectMenu;
