import classNames from 'classnames';
import selectedIcon from '@/assets/images/searchFilter/selected.svg';
import styles from './index.less';

interface CustomTypeMenuProps {
  handleClose: () => void;
  onSearchFilterParamsChange: (params: any) => void;
  searchFilterParams: any;
}
const CustomTypeMenu = ({
  onSearchFilterParamsChange,
  handleClose,
  searchFilterParams,
}: CustomTypeMenuProps) => {
  const { customType = {} } = searchFilterParams;

  const MenuList = [
    {
      label: '文本',
      key: '101, 106, 107, 114',
    },
    {
      label: '文件',
      key: '105, 103, 104',
    },
    {
      label: '云文档',
      key: '110/clouddocument',
    },
  ];
  return (
    <div
      className={styles.customTypeMenuWrapper}
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
      }}
    >
      {MenuList.map((item) => {
        return (
          <div
            key={item.key}
            className={classNames(
              styles.menuItemButton,
              customType.key === item.key && styles.menuItemButtonActived
            )}
            onClick={() => {
              handleClose();
              if (customType.key === item.key) {
                return;
              }
              onSearchFilterParamsChange({
                customType: item,
              });
            }}
          >
            {item.label}
            <img src={selectedIcon} />
          </div>
        );
      })}
    </div>
  );
};
export default CustomTypeMenu;
