import classNames from 'classnames';
import { Spin } from '@ht/sprite-ui';
import { FC, useEffect, useMemo, useState } from 'react';
import { EmployeeItem } from '@ht/openim-wasm-client-sdk';
import { isEmpty } from 'lodash';
import { useUserStore } from '@/store';
import { Virtuoso } from '@ht/react-virtuoso';
import Highlighter from 'react-highlight-words';
import { IMSDK } from '@/layouts/BasicLayout';
import checkIcon from '@/assets/images/searchFilter/check.png';
import checkedIcon from '@/assets/images/searchFilter/checked.svg';
import deleteIcon from '@/assets/images/searchFilter/delete.svg';
import OIMAvatar from '@/components/OIMAvatar';
import FilterSearchInput from '../FilterSearchInput';
import SelectSearchEmpty from '../SelectSearchEmpty';
import styles from './index.less';

const MemberPageSize = 20;

interface ContactSelectMenuProps {
  handleClose: () => void;
  onSearchFilterParamsChange: (params: any) => void;
  searchFilterParams: any;
}
const ContactSelectMenu: FC<ContactSelectMenuProps> = ({
  onSearchFilterParamsChange,
  handleClose,
  searchFilterParams,
}) => {
  const { contactSelect = [] } = searchFilterParams;
  const { employeeCode, nickname, userID } = useUserStore.getState().selfInfo;
  const [searchValue, setSearchValue] = useState('');
  const [searchInfo, setSearchInfo] = useState<{
    list: EmployeeItem[];
    loading: boolean;
    page: number;
    isEnd: boolean;
  }>({
    list: [],
    loading: false,
    page: 0,
    isEnd: false,
  });

  useEffect(() => {
    handleContactSearch();
  }, [searchValue]);

  const handleContactSearch = async (page = 1) => {
    if (page === 1) {
      setSearchInfo((pre) => {
        return { ...pre, loading: true };
      });
    }
    try {
      const { data } = await IMSDK.searchEmployeeListPage(
        searchValue,
        (page - 1) * MemberPageSize,
        MemberPageSize
      );
      const currentList = data.list || [];
      setSearchInfo((pre) => {
        return {
          list: page === 1 ? currentList : [...pre.list, ...currentList],
          page,
          loading: false,
          isEnd: currentList.length < MemberPageSize,
        };
      });
    } catch (e) {
      setSearchInfo({
        list: [],
        page: 1,
        loading: false,
        isEnd: false,
      });
    }
  };

  const currentSelectList = useMemo(() => {
    if (!isEmpty(searchValue)) {
      return searchInfo.list.filter((item) => {
        return (
          contactSelect.findIndex(
            (i: EmployeeItem) => item.employeeCode === i.employeeCode
          ) === -1
        );
      }, []);
    } else if (
      !isEmpty(userID) &&
      contactSelect.findIndex(
        (i: EmployeeItem) => employeeCode === i.employeeCode
      ) === -1
    ) {
      return [
        {
          employeeName: nickname,
          employeeCode,
          employeeID: userID,
          departmentID: '',
          departmentName: '',
          position: '',
          faceURL: '',
          employeeChannel: 0,
          sortNum: 0,
          customSortNum: 0,
        },
      ];
    } else {
      return [];
    }
  }, [searchValue, searchInfo.list, contactSelect, nickname, employeeCode]);

  // 是否展示列表
  const ifShowList = useMemo(() => {
    //   搜索数据全部选中后不展示
    return (
      currentSelectList.filter((item) => {
        return (
          contactSelect.findIndex(
            (i: EmployeeItem) => i.employeeCode === item.employeeCode
          ) === -1
        );
      })?.length > 0 ||
      (searchInfo.list.length === 0 && !isEmpty(searchValue))
    );
  }, [currentSelectList, searchInfo.list.length, searchValue, contactSelect]);

  const virtuosoStyle = useMemo(() => {
    const selectedTitleHeight = contactSelect.length > 0 ? '32px' : '0px';
    const allLength = currentSelectList.length + contactSelect.length;
    const emptyHeight =
      currentSelectList.length === 0 && ifShowList ? '300px' : '0px';
    const lineHeight = contactSelect.length > 0 && ifShowList ? '5px' : '0px';

    return {
      height: `calc(52px * ${allLength} + ${selectedTitleHeight} + ${emptyHeight} + ${lineHeight})`,
    };
  }, [currentSelectList, contactSelect, ifShowList]);

  const handleUncheck = (selectItem: EmployeeItem) => {
    const newList = contactSelect.filter(
      (i: EmployeeItem) => i.employeeCode !== selectItem.employeeCode
    );
    onSearchFilterParamsChange({
      contactSelect: newList,
    });
  };

  return (
    <div
      className={styles.contactSelectMenuWrapper}
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
      }}
    >
      <div style={{ margin: '6px 10px' }}>
        <FilterSearchInput
          placeholder="输入名称查找发送者"
          onInputChange={(val: string) => setSearchValue(val)}
        />
      </div>

      <Virtuoso
        className={styles.virtuosoListContainer}
        style={virtuosoStyle}
        data={ifShowList ? currentSelectList : []}
        itemContent={(index: number, searchItem: EmployeeItem) => {
          return (
            <div
              key={searchItem.employeeCode}
              className={classNames(styles.menuItemButton)}
              onClick={() => {
                onSearchFilterParamsChange({
                  contactSelect: [...contactSelect, searchItem],
                });
              }}
            >
              <img src={checkIcon} className={styles.checkIcon} />
              <OIMAvatar
                size={32}
                userID={searchItem.employeeID}
                hideOnlineStatus={true}
                style={{ marginRight: '10px' }}
              />

              <Highlighter
                highlightClassName={styles.highlightClassName}
                searchWords={[searchValue]}
                autoEscape={true}
                textToHighlight={searchItem.employeeName}
              />
            </div>
          );
        }}
        endReached={() => {
          if (!searchInfo.isEnd) {
            handleContactSearch(searchInfo.page + 1);
          }
        }}
        increaseViewportBy={500}
        components={{
          EmptyPlaceholder: () => {
            return ifShowList ? <SelectSearchEmpty /> : <></>;
          },
          Header: () => {
            return (
              <>
                {contactSelect.length > 0 && (
                  <div className={styles.selectedList}>
                    <div className={styles.selectedDesc}>
                      <span>已选记录</span>
                      <span
                        className={styles.deleteAll}
                        onClick={() => {
                          onSearchFilterParamsChange({
                            contactSelect: [],
                          });
                        }}
                      >
                        全部删除
                      </span>
                    </div>
                    {contactSelect.map((selectItem: EmployeeItem) => {
                      return (
                        <div
                          key={selectItem.employeeCode}
                          className={classNames(styles.menuItemButton)}
                        >
                          <div className={styles.searchValue}>
                            <img
                              src={checkedIcon}
                              className={styles.checkIcon}
                              onClick={() => handleUncheck(selectItem)}
                            />
                            <OIMAvatar
                              size={32}
                              userID={selectItem.employeeID}
                              hideOnlineStatus={true}
                              style={{ marginRight: '10px' }}
                            />
                            <span className={styles.name}>
                              {selectItem.employeeName}
                            </span>
                          </div>
                          <div
                            className={styles.deleteIcon}
                            onClick={() => handleUncheck(selectItem)}
                          >
                            <img src={deleteIcon} />
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
                {contactSelect.length > 0 && ifShowList && (
                  <div className={styles.line}></div>
                )}
              </>
            );
          },
        }}
      />
    </div>
  );
};
export default ContactSelectMenu;
