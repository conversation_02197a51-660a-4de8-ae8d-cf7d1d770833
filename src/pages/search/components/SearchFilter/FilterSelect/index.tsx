import { CSSProperties, FC } from 'react';
import classNames from 'classnames';
import { Typography, Tooltip } from '@ht/sprite-ui';
import openIcon from '@/assets/images/searchFilter/open.svg';
import calendarDelIcon from '@/assets/images/searchFilter/calendarDel.svg';
import styles from './index.less';

interface FilterSelectProps {
  isOpen: boolean;
  filterTitle: string;
  type?: string;
  wrapperStyle?: CSSProperties;
  onClear?: () => void;
  needCancel?: boolean;
  hasSelectData?: boolean;
  wrapperClassName?: string;
}

const FilterSelect: FC<FilterSelectProps> = ({
  isOpen,
  filterTitle,
  type,
  wrapperStyle = {},
  onClear,
  needCancel = true,
  hasSelectData,
  wrapperClassName = '',
}) => {
  return (
    <div
      style={wrapperStyle}
      className={classNames(
        styles.filterSelectWrapper,
        isOpen && styles.filterSelectWrapperOpen,
        styles[wrapperClassName]
      )}
    >
      <Tooltip
        title={hasSelectData ? filterTitle : null}
        overlayClassName={styles.filterTitleTooltip}
        color="#000000"
      >
        <Typography.Text ellipsis={true}>
          <span
            className={classNames(
              styles.filterName,
              hasSelectData && styles.filterNameHasData
            )}
          >
            {filterTitle}
          </span>
        </Typography.Text>
      </Tooltip>
      <img
        style={hasSelectData && needCancel ? { marginLeft: '2px' } : {}}
        src={hasSelectData && needCancel ? calendarDelIcon : openIcon}
        className={classNames(isOpen && styles.closeIcon)}
        onClick={(e) => {
          if (onClear && hasSelectData) {
            e.stopPropagation();
            onClear();
          }
        }}
      />
    </div>
  );
};

export default FilterSelect;
