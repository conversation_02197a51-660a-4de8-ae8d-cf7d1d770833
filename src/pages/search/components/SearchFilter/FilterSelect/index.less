.filterSelectWrapper {
  padding: 10px;
  height: 32px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid var(--primary-border-color);
  display: flex;
  align-items: center;
  width: 102px;
  justify-content: space-between;

  &:hover {
    background: #eeeeee;
    cursor: pointer;
  }

  .filterName {
    font-size: 14px;
    font-weight: 400;
    color: var(--primary-text-color-1);
    line-height: 20px;
    word-break: keep-all;
    white-space: nowrap;
  }

  .closeIcon {
    transform: rotate(180deg);
  }
}

.history_filterSelect {
  .filterName {
    color: #999ba0;
  }

  .filterNameHasData {
    color: var(--primary-text-color-1);
  }
}

.filterSelectWrapperOpen {
  border: 1px solid var(--primary-text-color-9);
}

.filterTitleTooltip {
  :global {
    .linkflow-tooltip-inner {
      padding: 7px 16px;
      border-radius: 8px;
      box-shadow: 0 6px 16px 0 rgba(29, 34, 44, 8%),
        0 3px 6px -4px rgba(29, 34, 44, 12%);
      font-size: 14px;
      font-weight: 400;
      color: #ffffff;
      line-height: 20px;
    }
    .linkflow-tooltip-arrow {
      bottom: 1px;
    }
  }
}
