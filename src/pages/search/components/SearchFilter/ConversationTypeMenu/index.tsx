import classNames from 'classnames';
import selectedIcon from '@/assets/images/searchFilter/selected.svg';
import styles from './index.less';

interface ConversationTypeMenuProps {
  handleClose: () => void;
  onSearchFilterParamsChange: (params: any) => void;
  searchFilterParams: any;
}

export const ConversationTypeMenuList = [
  {
    label: '私聊',
    key: 1,
  },
  {
    label: '群聊',
    key: 3,
  },
];

const ConversationTypeMenu = ({
  onSearchFilterParamsChange,
  handleClose,
  searchFilterParams,
}: ConversationTypeMenuProps) => {
  const { conversationType = {} } = searchFilterParams;

  return (
    <div
      className={styles.conversationTypeMenuWrapper}
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
      }}
    >
      {ConversationTypeMenuList.map((item) => {
        return (
          <div
            key={item.key}
            className={classNames(
              styles.menuItemButton,
              conversationType.key === item.key && styles.menuItemButtonActived
            )}
            onClick={() => {
              handleClose();
              if (conversationType.key === item.key) {
                return;
              }
              onSearchFilterParamsChange({
                conversationType: item,
              });
            }}
          >
            {item.label}
            <img src={selectedIcon} />
          </div>
        );
      })}
    </div>
  );
};
export default ConversationTypeMenu;
