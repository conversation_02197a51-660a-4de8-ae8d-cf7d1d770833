.searchFilterWrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .leftBox {
    display: flex;
    flex: 1;
  }

  .filterIconWrapper {
    width: 70px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: var(--primary-text-color-1);
    cursor: pointer;
    margin-right: 8px;
    margin-top: 8px;

    > img {
      margin-right: 6px;
    }
  }

  .filterActivedIconWrapper {
    background: var(--tab-actived-background-color);
    color: var(--primary-text-color-9);
  }
  .searchFilter_message {
    display: flex;
    flex: 1;
    flex-wrap: wrap;

    .filterItem {
      margin-right: 8px;
      margin-top: 8px;
    }
  }

  .sortFilterItem {
    margin-left: auto;
    margin-top: 8px;
  }

  .marginSet {
    .sortFilterItem_Left {
      margin-left: 0 !important;
    }
  }
}
