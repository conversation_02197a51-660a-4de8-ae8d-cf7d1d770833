/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-lines */
import { FC, useMemo, useEffect, useState } from 'react';
import classNames from 'classnames';
import { isEmpty, map } from 'lodash';
import moment from 'moment';
import { Dropdown } from '@ht/sprite-ui';
import filterIcon from '@/assets/images/searchFilter/filter.svg';
import filterActivedIcon from '@/assets/images/searchFilter/filterActived.svg';
import ConversationTypeMenu from './ConversationTypeMenu';
import ConversationSelectMenu from './ConversationSelectMenu';
import ContactSelectMenu from './ContactSelectMenu';
import CustomTypeMenu from './CustomTypeMenu';
import CalendarSelectMenu from './CalendarSelectMenu';
import SortSelectMenu from './SortSelectMenu';
import FilterSelect from './FilterSelect';
import { TabType } from '../..';
import styles from './index.less';

interface SearchFilterProps {
  curTab: TabType;
  onSearchFilterParamsChange: (params: any) => void;
  searchFilterParams: any;
  isShowFilterSelect: boolean;
  onFilterSelectChange: (val: boolean) => void;
}

const SearchFilter: FC<SearchFilterProps> = ({
  curTab,
  onSearchFilterParamsChange,
  searchFilterParams,
  isShowFilterSelect,
  onFilterSelectChange,
}) => {
  const [filterDropVisible, setFilterDropVisible] = useState({
    conversationTypeOpen: false,
    conversationSelectOpen: false,
    contactSelectOpen: false,
    customTypeOpen: false,
    calendarOpen: false,
    sortTypeOpen: false,
  });

  const onFilterDropVisibleChange = (obj: any) => {
    setFilterDropVisible((pre) => {
      return {
        ...pre,
        ...obj,
      };
    });
  };

  const getPopupContainer = useMemo(() => {
    return document.getElementById('BasicLayoutId') || document.body;
  }, []);

  const renderFilterTitle = (type: string) => {
    let title = '';
    const showNameList = map(
      searchFilterParams.conversationSelect,
      (i) => i.showName
    );
    const employeeNameList = map(
      searchFilterParams.contactSelect,
      (i) => i.employeeName
    );
    const { customizeDate = [], dateSelect = {} } = searchFilterParams;
    let timeStr = dateSelect.label;
    if (dateSelect.key === 'customizeTime') {
      timeStr = `${moment(customizeDate[0]).format('YYYY/M/D')} ~ ${moment(
        customizeDate[1]
      ).format('YYYY/M/D')}`;
    }

    switch (type) {
      case 'conversationType':
        title = isEmpty(searchFilterParams[type])
          ? '会话类型'
          : searchFilterParams.conversationType.label;
        break;
      case 'conversationSelect':
        title = isEmpty(searchFilterParams[type])
          ? '所在会话'
          : showNameList.join('、');
        break;
      case 'contactSelect':
        title = isEmpty(searchFilterParams[type])
          ? '发送人'
          : employeeNameList.join('、');
        break;
      case 'customType':
        title = isEmpty(searchFilterParams[type])
          ? '消息类型'
          : searchFilterParams.customType.label;
        break;
      case 'dateSelect':
        title = isEmpty(searchFilterParams[type]) ? '日期' : timeStr;
        break;
      case 'sort':
        title = `排序方式：${
          searchFilterParams.sort.field === 'send_time' ? '发送时间' : '最相关'
        }`;
        break;
      default:
        break;
    }
    return title;
  };

  const resizeObserver = new ResizeObserver((entries) => {
    const checkContainer = document.getElementById('checkContainer');
    for (const entry of entries) {
      const { width } = entry.contentRect; // 获取当前宽度
      if (checkContainer) {
        // 4. 判断宽度是否小于阈值
        if (width < 710) {
          checkContainer.classList.add(styles.marginSet); // 添加类名
        } else {
          checkContainer.classList.remove(styles.marginSet); // 移除类名
        }
      }
    }
  });

  useEffect(() => {
    const checkContainer = document.getElementById('checkContainer');
    if (checkContainer) {
      resizeObserver.observe(checkContainer);
    }
    return () => {
      if (checkContainer) {
        resizeObserver.unobserve(checkContainer);
      }
    };
  }, []);

  return (
    <div className={styles.searchFilterWrapper}>
      <div className={styles.leftBox}>
        <div
          className={classNames(
            styles.filterIconWrapper,
            isShowFilterSelect && styles.filterActivedIconWrapper
          )}
          onClick={() => {
            onFilterSelectChange(!isShowFilterSelect);
          }}
        >
          <img src={isShowFilterSelect ? filterActivedIcon : filterIcon} />
          <span>筛选</span>
        </div>

        <div className={styles.searchFilter_message} id="checkContainer">
          {curTab === 'message' && isShowFilterSelect && (
            <>
              <Dropdown
                trigger={['click']}
                getPopupContainer={() => getPopupContainer}
                open={filterDropVisible.conversationTypeOpen}
                onOpenChange={(open) => {
                  onFilterDropVisibleChange({ conversationTypeOpen: open });
                }}
                placement="bottomLeft"
                overlay={
                  filterDropVisible.conversationTypeOpen ? (
                    <ConversationTypeMenu
                      handleClose={() => {
                        onFilterDropVisibleChange({
                          conversationTypeOpen: false,
                        });
                      }}
                      onSearchFilterParamsChange={onSearchFilterParamsChange}
                      searchFilterParams={searchFilterParams}
                    />
                  ) : (
                    <></>
                  )
                }
              >
                <div
                  onClick={() => {
                    onFilterDropVisibleChange({ conversationTypeOpen: true });
                  }}
                  className={styles.filterItem}
                >
                  <FilterSelect
                    filterTitle={renderFilterTitle('conversationType')}
                    isOpen={filterDropVisible.conversationTypeOpen}
                    hasSelectData={
                      !isEmpty(searchFilterParams.conversationType)
                    }
                    onClear={() => {
                      onSearchFilterParamsChange({
                        conversationType: {},
                      });
                    }}
                  />
                </div>
              </Dropdown>
              <Dropdown
                trigger={['click']}
                getPopupContainer={() => getPopupContainer}
                open={filterDropVisible.conversationSelectOpen}
                onOpenChange={(open) => {
                  onFilterDropVisibleChange({ conversationSelectOpen: open });
                }}
                placement="bottomLeft"
                overlay={
                  filterDropVisible.conversationSelectOpen ? (
                    <ConversationSelectMenu
                      handleClose={() => {
                        onFilterDropVisibleChange({
                          conversationSelectOpen: false,
                        });
                      }}
                      onSearchFilterParamsChange={onSearchFilterParamsChange}
                      searchFilterParams={searchFilterParams}
                    />
                  ) : (
                    <></>
                  )
                }
              >
                <div
                  onClick={() => {
                    onFilterDropVisibleChange({ conversationSelectOpen: true });
                  }}
                  className={styles.filterItem}
                >
                  <FilterSelect
                    filterTitle={renderFilterTitle('conversationSelect')}
                    isOpen={filterDropVisible.conversationSelectOpen}
                    hasSelectData={
                      !isEmpty(searchFilterParams.conversationSelect)
                    }
                    onClear={() => {
                      onSearchFilterParamsChange({
                        conversationSelect: [],
                      });
                    }}
                  />
                </div>
              </Dropdown>
              <Dropdown
                trigger={['click']}
                getPopupContainer={() => getPopupContainer}
                open={filterDropVisible.contactSelectOpen}
                onOpenChange={(open) => {
                  onFilterDropVisibleChange({ contactSelectOpen: open });
                }}
                placement="bottomLeft"
                overlay={
                  filterDropVisible.contactSelectOpen ? (
                    <ContactSelectMenu
                      handleClose={() => {
                        onFilterDropVisibleChange({
                          contactSelectOpen: false,
                        });
                      }}
                      onSearchFilterParamsChange={onSearchFilterParamsChange}
                      searchFilterParams={searchFilterParams}
                    />
                  ) : (
                    <></>
                  )
                }
              >
                <div
                  onClick={() => {
                    onFilterDropVisibleChange({ contactSelectOpen: true });
                  }}
                  className={styles.filterItem}
                >
                  <FilterSelect
                    filterTitle={renderFilterTitle('contactSelect')}
                    isOpen={filterDropVisible.contactSelectOpen}
                    hasSelectData={!isEmpty(searchFilterParams.contactSelect)}
                    onClear={() => {
                      onSearchFilterParamsChange({
                        contactSelect: [],
                      });
                    }}
                  />
                </div>
              </Dropdown>
              <Dropdown
                trigger={['click']}
                getPopupContainer={() => getPopupContainer}
                open={filterDropVisible.customTypeOpen}
                onOpenChange={(open) => {
                  onFilterDropVisibleChange({ customTypeOpen: open });
                }}
                placement="bottomLeft"
                overlay={
                  filterDropVisible.customTypeOpen ? (
                    <CustomTypeMenu
                      handleClose={() => {
                        onFilterDropVisibleChange({ customTypeOpen: false });
                      }}
                      onSearchFilterParamsChange={onSearchFilterParamsChange}
                      searchFilterParams={searchFilterParams}
                    />
                  ) : (
                    <></>
                  )
                }
              >
                <div
                  onClick={() => {
                    onFilterDropVisibleChange({ customTypeOpen: true });
                  }}
                  className={styles.filterItem}
                >
                  <FilterSelect
                    filterTitle={renderFilterTitle('customType')}
                    isOpen={filterDropVisible.customTypeOpen}
                    hasSelectData={!isEmpty(searchFilterParams.customType)}
                    onClear={() => {
                      onSearchFilterParamsChange({
                        customType: {},
                      });
                    }}
                  />
                </div>
              </Dropdown>
              <Dropdown
                trigger={['click']}
                getPopupContainer={() => getPopupContainer}
                open={filterDropVisible.calendarOpen}
                onOpenChange={(open) => {
                  onFilterDropVisibleChange({ calendarOpen: open });
                }}
                placement="bottomLeft"
                overlay={
                  filterDropVisible.calendarOpen ? (
                    <CalendarSelectMenu
                      handleClose={() => {
                        onFilterDropVisibleChange({ calendarOpen: false });
                      }}
                      onSearchFilterParamsChange={onSearchFilterParamsChange}
                      searchFilterParams={searchFilterParams}
                    />
                  ) : (
                    <></>
                  )
                }
              >
                <div
                  onClick={() => {
                    onFilterDropVisibleChange({ calendarOpen: true });
                  }}
                  className={styles.filterItem}
                >
                  <FilterSelect
                    filterTitle={renderFilterTitle('dateSelect')}
                    hasSelectData={!isEmpty(searchFilterParams.dateSelect)}
                    isOpen={filterDropVisible.calendarOpen}
                    type="calendar"
                    onClear={() => {
                      onSearchFilterParamsChange({
                        dateSelect: '',
                        searchTimePosition: 0,
                        searchTimePeriod: 0,
                        customizeDate: [],
                      });
                    }}
                  />
                </div>
              </Dropdown>
            </>
          )}
          <Dropdown
            trigger={['click']}
            getPopupContainer={() => getPopupContainer}
            open={filterDropVisible.sortTypeOpen}
            onOpenChange={(open) => {
              onFilterDropVisibleChange({ sortTypeOpen: open });
            }}
            placement="bottomLeft"
            overlay={
              filterDropVisible.sortTypeOpen ? (
                <SortSelectMenu
                  handleClose={() => {
                    onFilterDropVisibleChange({
                      sortTypeOpen: false,
                    });
                  }}
                  onSearchFilterParamsChange={onSearchFilterParamsChange}
                  searchFilterParams={searchFilterParams}
                />
              ) : (
                <></>
              )
            }
          >
            <div
              onClick={() => {
                onFilterDropVisibleChange({ sortTypeOpen: true });
              }}
              className={classNames(
                styles.sortFilterItem,
                curTab === 'message' &&
                  isShowFilterSelect &&
                  styles.sortFilterItem_Left
              )}
            >
              <FilterSelect
                filterTitle={renderFilterTitle('sort')}
                isOpen={filterDropVisible.sortTypeOpen}
                wrapperStyle={{ width: '154px' }}
                hasSelectData={true}
                needCancel={false}
              />
            </div>
          </Dropdown>
        </div>
      </div>
    </div>
  );
};

export default SearchFilter;
