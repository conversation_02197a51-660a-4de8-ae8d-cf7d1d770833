import { FC } from 'react';
import { Input } from '@ht/sprite-ui';
import styles from './index.less';

interface FilterSearchInputProps {
  onInputChange: (val: string) => void;
  placeholder: string;
}

const FilterSearchInput: FC<FilterSearchInputProps> = ({
  onInputChange,
  placeholder,
}) => {
  return (
    <div className={styles.filterSearchInputWrapper}>
      <Input
        placeholder={placeholder}
        className={styles.filterSearchInput}
        onChange={(e) => onInputChange(e.target.value)}
      />
    </div>
  );
};

export default FilterSearchInput;
