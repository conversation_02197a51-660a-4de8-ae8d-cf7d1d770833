import withScrollBar from '@/components/withScrollBar';
import Channel from '@/components/Channel';
import { useSearchInfoStore } from '@/store';
import UserDetail from '@/pages/contact/components/UserDetail';
import FilePreviewModal from '@/components/FilePreviewModal';
import styles from './index.less';

const SearchRightArea = () => {
  const { rightAreaInSearch, changeRightArea, rightPayload } =
    useSearchInfoStore();
  return (
    <div className={styles.rightArea}>
      {rightAreaInSearch === 'channel' && <Channel hasDeleteIcon={true} />}
      {rightAreaInSearch === 'personDetail' && (
        <UserDetail
          userID={rightPayload}
          handleSelectedUserClear={() => {
            changeRightArea('CLEAR_RIGHT_AREA');
          }}
        />
      )}
      {rightAreaInSearch === 'file' && (
        <FilePreviewModal
          message={rightPayload}
          onClose={() => {
            changeRightArea('CLEAR_RIGHT_AREA');
          }}
        />
      )}
    </div>
  );
};

export default withScrollBar(SearchRightArea, {
  domId: 'drag-scroll-bar-right-search',
  direction: 'right',
  defaultWidth: 'calc(100vw * 968 / 2560)',
  minWidth: 435,
  maxWidth: window.innerWidth - (560 / 1536) * window.innerWidth,
  maxWidthCalc: () => {
    return window.innerWidth - (560 / 1536) * window.innerWidth;
  },
  dragBarClassName: 'scrollbarRight',
});
