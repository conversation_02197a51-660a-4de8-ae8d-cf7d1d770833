.memberItemWrapper {
  background-color: var(--link-color-base-pry);
  cursor: pointer;
  border: 1px solid rgba(var(--link-color-plt-gray-100), 0.13);
  border-bottom: 0;
  border-color: var(--link-color-otl-ter);
  padding: 16px;
  display: flex;
  align-items: center;
  position: relative;
  min-height: 69px;
  margin: 0 13px 0 17px;

  .memberContent {
    flex: auto;
    align-self: center;
    margin: -6px -6px -6px 16px;
    padding: 6px;
    overflow: hidden;

    .employeeNameWrapper {
      display: flex;
      align-items: center;

      .employeeName {
        font-size: 15px;
        color: var(--link-color-content-pry);
        margin-right: 10px;
        font-weight: bold;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }

  .actionsWrapper {
    margin: 16px;
    position: absolute;
    top: 0;
    right: 0;
    opacity: 0;
    display: flex;
    align-items: center;

    .actionBtn {
      min-width: 80px;
      height: 36px;
      padding: 0 12px 1px;
      font-size: 15px;
      background-color: var(--link-color-base-pry);
      border: 1px solid var(--link-color-otl-sec);
      color: var(--link-color-content-pry);
      font-weight: bold;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 12px;

      &:hover {
        background-color: var(--link-color-base-sec);
        box-shadow: 0 1px 3px #00000014;
        cursor: pointer;
      }

      &:first-of-type {
        margin-left: 0;
      }
    }
  }

  &:hover {
    .actionsWrapper {
      opacity: 1;
    }
  }
}

.firstMemberItem {
  border-radius: 8px 8px 0 0;
}

.lastMemberItem {
  border-radius: 0 0 8px 8px;
  border-bottom: 1px solid rgba(var(--link-color-plt-gray-100), 0.13);
}

.memberItem {
  border-radius: 8px;
  border-bottom: 1px solid rgba(var(--link-color-plt-gray-100), 0.13);
}

.search_Highlight {
  padding: 0 !important;
  background: #fff5da !important;
  border-radius: 2px;
}
