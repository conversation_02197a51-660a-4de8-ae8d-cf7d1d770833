import { FC, useRef, useEffect } from 'react';
import classNames from 'classnames';
import Mark from 'mark.js';
import { shallow } from 'zustand/shallow';
import { SessionType, EmployeeItem } from '@ht/openim-wasm-client-sdk';
import OIMAvatar, { OnlineStatusComponent } from '@/components/OIMAvatar';
import { IMSDK } from '@/layouts/BasicLayout';
import { useConversationStore, useSearchInfoStore } from '@/store';
import { PopPersonCardContainer } from '@/components/Channel/components/MessageItem/PopPersonCardContainer';
import styles from './index.less';

interface RenderMemberItemProps {
  memberItem: EmployeeItem;
  index: number;
  total: number;
  searchValue: string;
}

const RenderMemberItem: FC<RenderMemberItemProps> = ({
  memberItem,
  index,
  total,
  searchValue,
}) => {
  const { updateCurrentConversation, updateChannelHeaderCurTab } =
    useConversationStore((state) => ({
      updateCurrentConversation: state.updateCurrentConversation,
      updateChannelHeaderCurTab: state.updateChannelHeaderCurTab,
    }));
  const { changeRightArea } = useSearchInfoStore();

  const { changeSearchRightArea } = useSearchInfoStore(
    (state) => ({
      changeSearchRightArea: state.changeRightArea,
    }),
    shallow
  );

  const highLightContentRef = useRef(null);
  const markInstanceRef = useRef<any>(null);

  useEffect(() => {
    if (highLightContentRef.current) {
      markInstanceRef.current = new Mark(highLightContentRef.current);
    }
  }, []);

  useEffect(() => {
    if (markInstanceRef.current) {
      markInstanceRef.current.unmark();
      if (searchValue.trim()) {
        markInstanceRef.current.mark(searchValue.trim(), {
          className: styles.search_Highlight,
        });
      }
    }
  }, [searchValue]);

  const openChart = async (e: any) => {
    e.stopPropagation();
    const conversation = await IMSDK.getOneConversation({
      sessionType: SessionType.Single,
      sourceID: memberItem.employeeID,
    });
    changeRightArea('OPEN_CHANNEL', conversation.data);
    updateChannelHeaderCurTab('message');
    updateCurrentConversation({
      ...conversation.data,
    });
  };

  const openMemberInfo = () => {
    if (location.pathname === '/linkflow/search') {
      changeSearchRightArea('OPEN_PERSON_DETAIL', memberItem.employeeID);
    } else {
      changeRightArea('OPEN_PERSON_DETAIL', memberItem.employeeID);
    }
  };

  return (
    <div
      className={classNames(
        styles.memberItemWrapper,
        total === 1 && styles.memberItem,
        index === 0 && styles.firstMemberItem,
        index === total - 1 && styles.lastMemberItem
      )}
      key={memberItem.employeeID}
      onClick={() => {
        openMemberInfo();
      }}
    >
      <OIMAvatar
        userID={memberItem.employeeID}
        size={36}
        hideOnlineStatus={true}
      />
      <div className={styles.memberContent}>
        <div className={styles.employeeNameWrapper}>
          <PopPersonCardContainer sendID={memberItem.employeeID}>
            <span className={styles.employeeName} ref={highLightContentRef}>
              {memberItem.employeeName}
            </span>
          </PopPersonCardContainer>

          <OnlineStatusComponent
            userID={memberItem.employeeID}
            stateSize={8}
            stateRight={0}
            isPosition={false}
          />
        </div>
      </div>

      <div className={styles.actionsWrapper}>
        <div className={styles.actionBtn} onClick={openChart}>
          消息
        </div>
        <div className={styles.actionBtn} onClick={openMemberInfo}>
          查看个人档案
        </div>
      </div>
    </div>
  );
};

export default RenderMemberItem;
