.fileItemContentWrapper {
  position: relative;
  border: 1px solid rgba(var(--link-color-plt-gray-100), 0.13);
  border-bottom: 0;
  border-color: var(--link-color-otl-ter);

  &:first-of-type {
    border-radius: 8px 8px 0 0;
    .fileItemWrapper {
      border-radius: 8px 8px 0 0;
    }
  }

  &:last-of-type {
    border-radius: 0 0 8px 8px;
    .fileItemWrapper {
      border-radius: 0 0 8px 8px;
    }

    border-bottom: 1px solid rgba(var(--link-color-plt-gray-100), 0.13);
  }

  .fileItemWrapper {
    background-color: var(--link-color-base-pry);
    cursor: pointer;
    padding: 12px;
    display: flex;
    align-items: center;
    position: relative;

    .fileIcon {
      width: 36px;
      height: 36px;
      margin-right: 12px;
    }

    .fileName {
      font-size: 15px;
      font-weight: bold;
      align-items: baseline;
      color: var(--link-color-base-inv-pry);
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      line-height: 22px;
      margin-top: -4px;
    }

    .fileDesc {
      margin-top: 2px;
      font-size: 13px;
      overflow: hidden;
      color: rgba(97, 96, 97, 100%);
      line-height: 16px;

      &:last-of-type {
        display: none;
      }
    }

    .fileBox {
      overflow: hidden;
    }

    &:hover {
      .fileDesc {
        display: none;

        &:last-of-type {
          display: block;
        }
      }
    }
  }

  &:hover {
    .fileBox {
      padding-right: 120px;
    }
  }

  .operationMenu {
    display: none;
    position: absolute;
    top: 0;
    bottom: 0;
    margin: auto 0;
    height: fit-content;
    right: 12px;
    padding: 4px;
    box-shadow: 0 2px 4px 0 var(--file-backgroud-color);
    border-radius: 12px;
    border: 1px solid var(--primary-background-color-5);
    background-color: var(--primary-background-color-6);
    .operationItem {
      width: 32px;
      height: 32px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }
    .operationItem:hover {
      background: var(--file-backgroud-color);
    }
  }

  .moreMenuWarp {
    .menuBox {
      padding: 12px 0;
      .menuItem {
        width: 100%;
        height: 30px;
        display: flex;
        align-items: center;
        padding: 0 24px;
        font-size: 14px;
        font-weight: 400;
        color: var(--primary-text-color);
        cursor: pointer;
      }
      .menuItem:hover {
        // color: var(--primary-text-color-pressed);
        background-color: var(--msg-qute-backgroud-color);
      }
      .deleteBtn {
        color: var(--link-color-content-imp);
      }
      .deleteBtn:hover {
        // color: var(--primary-text-color-pressed);
        background-color: var(--msg-qute-backgroud-color);
      }
    }
    .menuBox:first-child {
      border-bottom: 1px solid var(--primary-background-color-5);
    }
    .menuBox:last-child {
      border-bottom: none;
    }
  }

  .operationMoreMenuWarp {
    width: 220px;
    padding-right: 0;
    border-radius: 8px;
    border: 1px solid var(--primary-background-color-5);
    overflow: hidden;
    background-color: var(--primary-background-color-6);
    padding-left: 0;
    :global {
      .linkflow-popover-arrow {
        display: none;
      }
      .linkflow-popover-inner-content {
        padding: 0;
      }
    }
  }

  :global {
    .linkflow-popover-arrow {
      display: none;
    }
    .linkflow-popover-inner-content {
      padding: 0;
    }
  }

  &:hover {
    .operationMenu {
      display: flex;
    }
  }
}
