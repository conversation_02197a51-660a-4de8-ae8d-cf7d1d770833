import { FC, useState, useRef, useEffect } from 'react';
import classNames from 'classnames';
import { isEmpty } from 'lodash';
import { Popover, message as Message, Tooltip } from '@ht/sprite-ui';
import dayjs from 'dayjs';
import {
  MessageItem as MessageItemType,
  SearchMessageResultItem,
  SearchServerMsgItem,
} from '@ht/openim-wasm-client-sdk';
import { useSearchInfoStore, useUserStore } from '@/store';
import { IMSDK } from '@/layouts/BasicLayout';
import { feedbackToast } from '@/utils/common';
import { deleteOneMessage } from '@/hooks/useHistoryMessageList';
import downloadIcon from '@/assets/channel/messageRender/download.png';
import replyIcon from '@/assets/channel/messageRender/reply.png';
import moreIcon from '@/assets/channel/messageRender/more.png';
import { wpsFileType } from '@/components/Channel/components/MessageItem/FileMessageRender';
import { getFileIcon } from '@/components/Channel/components/MessageInput/FileRender';
import FilePreviewModal from '@/components/FilePreviewModal/FileModal';
import ForwardModal from '@/components/ForwardModal';
import styles from './index.less';

type SearchListType =
  | (MessageItemType & SearchMessageResultItem)
  | SearchServerMsgItem;

interface RenderFileItemProps {
  fileItem: SearchListType;
}

const RenderFileItem: FC<RenderFileItemProps> = ({ fileItem }) => {
  const { changeRightArea } = useSearchInfoStore();
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [open, setOpen] = useState<boolean>(false);
  const [forwardModal, setForwardModal] = useState<boolean>(false);
  const moreMenuRef = useRef<any>();
  const [tooltipOpen, setTooltipOpen] = useState<boolean>(false);

  const { fileElem, senderNickname, sendTime } = fileItem || {};
  const { fileName = '', sourceUrl = '' } = fileElem || {};
  const idx = fileName.lastIndexOf('.');
  const fileType = fileName.slice(idx + 1).toLowerCase();
  const isWps = wpsFileType.some((item: string) => item === fileType);

  const getPreviewUrl = async () => {
    try {
      const { data } = await IMSDK.getPreviewUrl({
        fileName,
        relativeDownloadUrl: sourceUrl,
        clientMsgID: fileItem.clientMsgID,
      });
      return data?.pcPreviewUrl || '';
    } catch (error) {
      console.error('getPreviewUrl', error);
      return '';
    }
  };

  const openModal = async () => {
    if (!isWps) {
      Message.info('该文件暂不支持预览！');
      return;
    }
    try {
      if (!previewUrl) {
        const url = await getPreviewUrl();
        if (url) {
          setPreviewUrl(url);
          setOpen(true);
        } else {
          Message.info('该文件暂不支持预览！');
        }
      } else {
        setOpen(true);
      }
    } catch (error) {
      console.error('openModal', error);
    }
  };

  const openView = async () => {
    try {
      if (!previewUrl) {
        const url = await getPreviewUrl();
        if (url) {
          setPreviewUrl(url);
          window.open(url, '_blank');
        } else {
          Message.info('该文件暂不支持预览！');
        }
      } else {
        window.open(previewUrl, '_blank');
      }
    } catch (error) {
      console.error('openView', error);
    }
  };

  const downloadFile = (url: string, name: string) => {
    const link = document.createElement('a');
    link.href = url;
    link.download = name;
    link.click();
  };

  // 判断文本是否有超长打点
  const isTextTruncated = (id: string) => {
    const currentElement = document.getElementById(id);
    if (currentElement) {
      return currentElement.scrollWidth > currentElement.clientWidth;
    } else {
      return false;
    }
  };

  const renderAction = () => {
    return (
      <div className={styles.operationMenu}>
        <div
          className={styles.operationItem}
          onClick={() => downloadFile(sourceUrl, fileName)}
        >
          <img src={downloadIcon} />
        </div>
        <div
          className={styles.operationItem}
          onClick={() => setForwardModal(true)}
        >
          <img src={replyIcon} />
        </div>
        <Popover
          placement="rightTop"
          title={null}
          content={
            <div className={styles.moreMenuWarp}>
              <div className={styles.menuBox}>
                {isWps ? (
                  <div className={styles.menuItem} onClick={openView}>
                    在新选项卡中打开
                  </div>
                ) : (
                  ''
                )}
                <div
                  className={styles.menuItem}
                  onClick={() => {
                    changeRightArea('OPEN_FILE_AREA', fileItem);
                  }}
                >
                  查看文件详情
                </div>
              </div>
            </div>
          }
          overlayClassName={styles.operationMoreMenuWarp}
          getPopupContainer={() => moreMenuRef.current || document.body}
        >
          <div className={styles.operationItem} ref={moreMenuRef}>
            <img src={moreIcon} />
          </div>
        </Popover>
      </div>
    );
  };

  return (
    <div className={styles.fileItemContentWrapper}>
      <div
        className={styles.fileItemWrapper}
        key={fileItem.clientMsgID}
        onClick={() => {
          openModal();
        }}
      >
        <img src={getFileIcon(fileName, 'icon')} className={styles.fileIcon} />
        <div className={styles.fileBox}>
          <Tooltip
            placement="top"
            title={fileName}
            arrowPointAtCenter={true}
            open={tooltipOpen}
            onOpenChange={(val: boolean) => {
              if (isTextTruncated(`${sendTime}`)) {
                setTooltipOpen(val);
              } else {
                setTooltipOpen(false);
              }
            }}
          >
            <div className={styles.fileName} id={`${sendTime}`}>
              {fileName}
            </div>
          </Tooltip>
          <div className={styles.fileDesc}>
            由 {senderNickname} 共享 在 {dayjs(sendTime).format('MM 月DD 日')}
          </div>
          <div className={styles.fileDesc}>
            查看 {getFileIcon(fileName, 'text')}
          </div>
        </div>
      </div>
      {renderAction()}
      {open && (
        <FilePreviewModal
          open={open}
          onClose={(e) => {
            e.preventDefault();
            e.stopPropagation();
            setOpen(false);
          }}
          url={previewUrl}
          message={fileItem}
          download={() => downloadFile(sourceUrl, fileName)}
          showName={fileName}
          openView={openView}
        />
      )}
      {forwardModal && (
        <ForwardModal
          open={forwardModal}
          onClose={() => setForwardModal(false)}
          message={fileItem}
          isThread={false}
          isSender={false}
          conversationID={fileItem.conversationID}
          showName={fileItem.showName}
        />
      )}
    </div>
  );
};

export default RenderFileItem;
