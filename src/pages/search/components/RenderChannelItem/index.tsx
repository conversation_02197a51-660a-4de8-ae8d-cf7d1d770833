/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable react/no-danger */
import { FC, useEffect, useState, useRef } from 'react';
import Mark from 'mark.js';
import { isEmpty } from 'lodash';
import classNames from 'classnames';
import { CheckOutlined } from '@ht-icons/sprite-ui-react';
import { SessionType, GroupItem } from '@ht/openim-wasm-client-sdk';
import { IMSDK } from '@/layouts/BasicLayout';
import { useConversationStore, useSearchInfoStore } from '@/store';
import { useConversationSettings } from '@/hooks/useConversationSettings';
import { GroupItemType } from '@/components/GlobalSearchModal';
import { handleHighLight } from '@/components/GlobalSearchModal/components/SearchGroupItem';

import styles from './index.less';
import { TabType } from '../..';

interface RenderChannelItemProps {
  channelItem: GroupItemType;
  index: number;
  total: number;
  searchValue: string;
  currentTab: TabType;
}

const RenderChannelItem: FC<RenderChannelItemProps> = ({
  channelItem,
  index,
  total,
  searchValue,
  currentTab,
}) => {
  const { updateCurrentConversation, updateChannelHeaderCurTab } =
    useConversationStore((state) => ({
      updateCurrentConversation: state.updateCurrentConversation,
      updateChannelHeaderCurTab: state.updateChannelHeaderCurTab,
    }));
  const { currentMemberIfJoinedGroup } = useConversationSettings(channelItem);
  const { changeRightArea } = useSearchInfoStore();
  const [groupInfo, setGroupInfo] = useState<GroupItem>();

  const highLightContentRef = useRef(null);
  const markInstanceRef = useRef<any>(null);

  useEffect(() => {
    if (highLightContentRef.current) {
      markInstanceRef.current = new Mark(highLightContentRef.current);
    }
  }, []);

  useEffect(() => {
    if (markInstanceRef.current) {
      markInstanceRef.current.unmark();
      if (searchValue.trim()) {
        markInstanceRef.current.mark(searchValue.trim(), {
          className: styles.search_Highlight,
        });
      }
    }
  }, [searchValue]);

  const onSearchItemClick = async () => {
    const conversation = await IMSDK.getOneConversation({
      sessionType: SessionType.Group,
      sourceID: channelItem.groupID,
    });
    setTimeout(() => {
      changeRightArea('OPEN_CHANNEL', {
        conversation: conversation.data,
      });
    }, 500);
  };

  useEffect(() => {
    if (currentTab === 'channel') {
      IMSDK.getSpecifiedGroupsInfo([channelItem.groupID]).then((res) => {
        const { data } = res;
        setGroupInfo(data[0]);
      });
    }
  }, [channelItem, currentTab]);

  const openChart = async (e: any) => {
    e.stopPropagation();
    const conversation = await IMSDK.getOneConversation({
      sessionType: SessionType.Group,
      sourceID: channelItem.groupID,
    });
    changeRightArea('CLEAR_RIGHT_AREA');
    updateChannelHeaderCurTab('message');
    updateCurrentConversation({
      ...conversation.data,
    });
  };

  return (
    <div
      className={classNames(
        styles.channelItemWrapper,
        total === 1 && styles.channelItem,
        index === 0 && styles.firstChannelItem,
        index === total - 1 && styles.lastChannelItem
      )}
      key={channelItem.groupID}
      onClick={() => {
        onSearchItemClick();
      }}
    >
      <div className={styles.flexBox}>
        <span className={styles.groupName} ref={highLightContentRef}>
          {channelItem.showName}
        </span>
        <span className={styles.tipDesc}>查看群聊</span>
      </div>
      <div className={styles.flexBox}>
        {currentMemberIfJoinedGroup && (
          <>
            <span className={styles.joinDesc}>
              <CheckOutlined className={styles.checkIcon} />
              已加入
            </span>
            &nbsp;&nbsp;·&nbsp;&nbsp;
          </>
        )}
        <span className={styles.memberCount}>
          {groupInfo?.memberCount || 0} 位成员
        </span>
        {!isEmpty(channelItem.nickname) && !isEmpty(channelItem.nickname) && (
          <span className={styles.nicknameBox}>
            包含:
            <span
              dangerouslySetInnerHTML={{
                __html: handleHighLight(
                  channelItem.keyword,
                  channelItem.nickname
                ),
              }}
              className={styles.nickname}
            ></span>
          </span>
        )}
      </div>
      {/* <div className={styles.openChartBtn} onClick={openChart}>
        在主页中打开
      </div> */}
    </div>
  );
};

export default RenderChannelItem;
