.channelItemWrapper {
  background-color: var(--link-color-base-pry);
  cursor: pointer;
  border: 1px solid rgba(var(--link-color-plt-gray-100), 0.13);
  border-bottom: 0;
  border-color: var(--link-color-otl-ter);
  padding: 16px;
  position: relative;
  margin: 0 13px 0 17px;

  .flexBox {
    display: flex;
    align-items: center;
    min-height: 22px;
    line-height: 22px;

    .groupName {
      font-size: 15px;
      font-weight: bold;
      align-items: baseline;
      color: var(--link-color-base-inv-pry);
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      display: block;
    }

    .tipDesc {
      visibility: hidden;
      font-size: 13px;
      color: var(--link-color-base-modal);
      margin: 0 8px;
      word-break: keep-all;
    }

    .checkIcon {
      font-size: 9px;
      margin-right: 5px;
      color: var(--link-color-base-inv-hgl-2);
    }

    .joinDesc {
      font-size: 13px;
      color: var(--link-color-base-inv-hgl-2);
      font-weight: bold;
    }

    .memberCount {
      font-size: 13px;
      color: var(--link-color-base-modal);
    }

    .nicknameBox {
      font-size: 14px;
      color: var(--link-color-base-modal);
      line-height: 20px;
      font-weight: 400;
      margin-left: 8px;

      .nickname {
        padding: 0 4px;
        color: var(--link-color-base-inv-pry);
        margin-left: 2px;
      }
    }
  }

  .openChartBtn {
    position: absolute;
    top: 12px;
    right: 12px;
    border-radius: 8px;
    height: 36px;
    padding: 0 12px 1px;
    font-size: 15px;
    background-color: var(--link-color-base-pry);
    border: 1px solid var(--link-color-otl-sec);
    color: var(--link-color-content-pry);
    font-weight: bold;
    cursor: pointer;
    text-align: center;
    white-space: nowrap;
    display: none;
    align-items: center;
    justify-content: center;

    &:hover {
      box-shadow: 0 1px 3px #00000014;
      background: rgba(248, 248, 248, 100%);
    }
  }

  &:hover {
    .tipDesc {
      visibility: visible !important;
    }
    .openChartBtn {
      display: flex;
    }
  }
}

.firstChannelItem {
  border-radius: 8px 8px 0 0;
}

.lastChannelItem {
  border-radius: 0 0 8px 8px;
  border-bottom: 1px solid rgba(var(--link-color-plt-gray-100), 0.13);
}

.channelItem {
  border-radius: 8px;
  border-bottom: 1px solid rgba(var(--link-color-plt-gray-100), 0.13);
}

.search_Highlight {
  padding: 0 !important;
  background: #fff5da !important;
  border-radius: 2px;
}
