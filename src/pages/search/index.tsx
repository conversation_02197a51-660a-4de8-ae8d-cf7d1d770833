/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-statements */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable max-lines */
import { useEffect, useState, useMemo } from 'react';
import _, { filter, isEmpty } from 'lodash';
import { v4 as uuidv4 } from 'uuid';
import { useUpdateEffect } from 'ahooks';
import classNames from 'classnames';
import { Virtuoso } from '@ht/react-virtuoso';
import { Spin, Empty } from '@ht/sprite-ui';
import MainContainer from '@/components/MainContainer';
import { IMSDK } from '@/layouts/BasicLayout';
import {
  MessageItem as MessageItemType,
  SearchMessageResultItem,
  MessageType,
  EmployeeItem,
  ConversationItem,
  SearchServerMsgItem,
} from '@ht/openim-wasm-client-sdk';
import {
  useConversationStore,
  useSearchInfoStore,
  useUserStore,
} from '@/store';
import { GroupItemType } from '@/components/GlobalSearchModal';
import searchEmpty from '@/assets/images/searchModal/searchEmpty.png';
import RenderMessageItem from './components/RenderMessageItem';
import RenderChannelItem from './components/RenderChannelItem';
import SearchRightArea from './components/SearchRightArea';
import RenderMemberItem from './components/RenderMemberItem';
import SearchFilter from './components/SearchFilter';
import styles from './index.less';

export type TabType = 'message' | 'file' | 'draw' | 'channel' | 'member';
type SearchListType =
  | (MessageItemType & SearchMessageResultItem)
  | SearchServerMsgItem;

interface tabItemProps {
  key: TabType;
  title: string;
  length: number;
  data?: any;
  renderItem?: any;
  loading: boolean;
  action: any;
}

type SearchFilterParamsProps = {
  customType: {
    label?: string;
    key?: string;
  };
  conversationType: {
    label?: string;
    key?: number;
  };
  conversationSelect: ConversationItem[];
  contactSelect: EmployeeItem[];
  dateSelect: {
    label?: string;
    key?: string;
  };
  sort: {
    field: '_score' | 'send_time';
    ascending: boolean;
  };
  searchTimePosition: number;
  searchTimePeriod: number;
};

const Count = 80;
const MessageTypeList = [
  MessageType.TextMessage,
  MessageType.MergeMessage,
  MessageType.QuoteMessage,
  MessageType.FileMessage,
  MessageType.VideoMessage,
  MessageType.VoiceMessage,
  MessageType.AtTextMessage,
  MessageType.CustomMessage,
  MessageType.GroupAnnouncementUpdated,
];
const MemberPageSize = 100;

const Search = () => {
  const searchValue = useSearchInfoStore((state) => state.searchValue);
  const needShowRightArea = useSearchInfoStore(
    (state) => state.needShowRightArea
  );
  const rightAreaInSearch = useSearchInfoStore(
    (state) => state.rightAreaInSearch
  );
  const rightPayload = useSearchInfoStore((state) => state.rightPayload);
  const searchConversationInfo = useSearchInfoStore(
    (state) => state.searchConversationInfo
  );
  const updateTargetMsg = useConversationStore(
    (state) => state.updateTargetMsg
  );
  const updateChannelHeaderCurTab = useConversationStore(
    (state) => state.updateChannelHeaderCurTab
  );
  const updateMultiSessionForSearch = useConversationStore(
    (state) => state.updateMultiSessionForSearch
  );
  const updateCurrentConversation = useConversationStore(
    (state) => state.updateCurrentConversation
  );
  const [latestSearchConversationInfo, setLatestSearchConversationInfo] =
    useState<ConversationItem | null>();
  const { connectState } = useUserStore();
  const [curTab, setCurTab] = useState<TabType>('message');
  const [messageSearchInfo, setMessageSearchInfo] = useState<{
    list: SearchListType[];
    loading: boolean;
    messageTotal: number;
    messagePage: number;
  }>({
    list: [],
    loading: false,
    messageTotal: 0,
    messagePage: 0,
  });
  const [channelSearchInfo, setChannelSearchInfo] = useState<{
    list: GroupItemType[];
    loading: boolean;
    channelTotal: number;
    channelPage: number;
  }>({
    list: [],
    loading: false,
    channelTotal: 0,
    channelPage: 0,
  });
  const [memberSearchInfo, setMemberSearchInfo] = useState<{
    list: EmployeeItem[];
    loading: boolean;
    memberTotal: number;
    memberPage: number;
  }>({
    list: [],
    loading: false,
    memberTotal: 0,
    memberPage: 0,
  });
  const [searchFilterParams, setSearchFilterParams] =
    useState<SearchFilterParamsProps>({
      customType: {},
      conversationType: {},
      conversationSelect: searchConversationInfo?.conversationID
        ? [{ ...searchConversationInfo }]
        : [],
      contactSelect: [],
      dateSelect: {},
      sort: {
        field: '_score',
        ascending: false,
      },
      searchTimePosition: 0,
      searchTimePeriod: 0,
    });
  const [showFilterSelect, setShowFilterSelect] = useState<boolean>(false);

  const handleMessageTypeList = (customType: string) => {
    let typeList = MessageTypeList;
    switch (customType) {
      case '文本':
        typeList = [
          MessageType.TextMessage,
          MessageType.MergeMessage,
          MessageType.QuoteMessage,
          MessageType.AtTextMessage,
        ];
        break;
      case '文件':
        typeList = [
          MessageType.FileMessage,
          MessageType.VideoMessage,
          MessageType.VoiceMessage,
        ];
        break;
      case '云文档':
        typeList = [MessageType.CustomMessage];
        break;
      default:
        break;
    }
    return typeList;
  };

  const handleLocalDataToServer = (localData: SearchMessageResultItem[]) => {
    const resultItems: SearchListType[] = [];
    _.forEach(localData, (searchResultItem: SearchMessageResultItem) => {
      _.forEach(searchResultItem.messageList, (listItem) => {
        const item = {
          ...searchResultItem,
          ...listItem,
        };
        resultItems.push(item);
      });
    });
    return resultItems;
  };

  const handleLocalMessageSearch = async (page = 1) => {
    try {
      const { data } = await IMSDK.searchLocalMessages({
        conversationID: searchConversationInfo?.conversationID || '',
        keywordList: [searchValue],
        keywordListMatchType: 0,
        senderUserIDList: [],
        messageTypeList: handleMessageTypeList(
          searchFilterParams.customType.label || ''
        ),
        searchTimePosition: 0,
        searchTimePeriod: 0,
        pageIndex: page,
        count: Count,
      });
      const resultItems: SearchListType[] =
        (data.searchResultItems &&
          handleLocalDataToServer(data.searchResultItems)) ||
        [];
      setMessageSearchInfo((pre) => {
        return {
          ...pre,
          list: page === 1 ? resultItems : [...pre.list, ...resultItems],
        };
      });
      if (!_.isEmpty(searchConversationInfo) && resultItems.length === Count) {
        await handleLocalMessageSearch(page + 1);
      } else {
        setMessageSearchInfo((pre) => {
          return {
            ...pre,
            loading: false,
            messagePage: page,
            messageTotal: pre.list.length,
          };
        });
      }
    } catch (e) {
      setMessageSearchInfo({
        list: [],
        loading: false,
        messageTotal: 0,
        messagePage: 0,
      });
    }
  };

  const handleMessageSearch = async (page = 0) => {
    if (_.isEmpty(searchValue)) {
      setMessageSearchInfo({
        list: [],
        loading: false,
        messageTotal: 0,
        messagePage: 0,
      });
      return;
    }
    if (page === 0) {
      setMessageSearchInfo((pre) => {
        return { ...pre, loading: true, list: [] };
      });
    }
    try {
      const params = {
        conversationIDs: searchFilterParams.conversationSelect.map(
          (i: ConversationItem) => i.conversationID
        ),
        keywordList: [searchValue],
        keywordListMatchType: 0,
        senderUserIDList: searchFilterParams.contactSelect.map(
          (i: EmployeeItem) => i.employeeID
        ),
        messageTypeList: handleMessageTypeList(
          searchFilterParams.customType.label || ''
        ),
        searchTimePosition: searchFilterParams.searchTimePosition,
        searchTimePeriod: searchFilterParams.searchTimePeriod,
        pageIndex: page,
        count: Count,
        customTypeList:
          searchFilterParams.customType.key === '110/clouddocument'
            ? searchFilterParams.customType.key.split(',')
            : [],
        conversationType: searchFilterParams.conversationType.key,
        sort: [searchFilterParams.sort],
      };
      const { data } = await IMSDK.searchServerMsg(params);

      const resultItems: SearchListType[] = data.searchResultItems || [];
      setMessageSearchInfo((pre) => {
        return {
          list: page === 0 ? resultItems : [...pre.list, ...resultItems],
          loading: false,
          messageTotal: data.totalCount,
          messagePage: page,
        };
      });
    } catch (err) {
      // 兼容本地搜索
      handleLocalMessageSearch(1);
    }
  };

  const handleChannelSearch = async () => {
    if (_.isEmpty(searchValue)) {
      setChannelSearchInfo({
        list: [],
        loading: false,
        channelTotal: 0,
        channelPage: 0,
      });
      return;
    }
    setChannelSearchInfo((pre) => {
      return { ...pre, loading: true };
    });
    try {
      const { data } = await IMSDK.getAllConversationList();
      const filterData = _.filter(data, (item: ConversationItem) => {
        return (
          item.showName.toLowerCase().includes(searchValue.toLowerCase()) &&
          !_.isEmpty(item.groupID)
        );
      });
      const groupData = filterData.map((i) => {
        return {
          groupID: i.groupID,
          showName: i.showName,
          nickname: '',
          keyword: '',
        };
      });
      const res: any = await IMSDK.getGroupsByNickname(searchValue);
      const groupsByNickname: any[] = res.data || [];
      groupsByNickname.forEach((item: any) => {
        const index = groupData.findIndex(
          (i) => i.groupID === item?.group?.groupID
        );
        if (index >= 0) {
          groupData[index] = {
            ...groupData[index],
            nickname: item.nickname,
          };
        } else {
          groupData.push({
            groupID: item.group.groupID,
            showName: item.group.groupName,
            nickname: item.nickname,
            keyword: item.keyword,
          });
        }
      });
      setChannelSearchInfo((pre) => {
        return {
          ...pre,
          list: groupData,
          loading: false,
          channelTotal: groupData.length,
        };
      });
    } catch (e) {
      setChannelSearchInfo({
        list: [],
        loading: false,
        channelTotal: 0,
        channelPage: 0,
      });
    }
  };

  const handleMemberSearch = async (page = 1) => {
    if (_.isEmpty(searchValue)) {
      setMemberSearchInfo({
        list: [],
        loading: false,
        memberTotal: 0,
        memberPage: 0,
      });
      return;
    }
    if (page === 1) {
      setMemberSearchInfo((pre) => {
        return { ...pre, loading: true };
      });
    }
    try {
      const { data } = await IMSDK.searchEmployeeListPage(
        searchValue,
        (page - 1) * MemberPageSize,
        MemberPageSize
      );
      const currentList = data.list || [];
      setMemberSearchInfo((pre) => {
        return {
          list: page === 1 ? currentList : [...pre.list, ...currentList],
          memberTotal: data.total,
          memberPage: page,
          loading: false,
        };
      });
    } catch (e) {
      setMemberSearchInfo({
        list: [],
        memberTotal: 0,
        memberPage: 1,
        loading: false,
      });
    }
  };

  const searchTabArr: tabItemProps[] = useMemo(() => {
    return [
      {
        key: 'message',
        title: '消息',
        length: messageSearchInfo.messageTotal,
        data: messageSearchInfo.list,
        loading: messageSearchInfo.loading,
        action: (page = 0) => handleMessageSearch(page),
        renderItem: (searchItem: SearchListType) => (
          <RenderMessageItem
            messagetItem={searchItem}
            searchValue={searchValue}
          />
        ),
      },
      {
        key: 'channel',
        title: '群聊',
        length: channelSearchInfo.channelTotal,
        data: channelSearchInfo.list,
        loading: channelSearchInfo.loading,
        action: () => handleChannelSearch(),
        renderItem: (searchItem: GroupItemType, index: number) => (
          <RenderChannelItem
            channelItem={searchItem}
            index={index}
            currentTab={curTab}
            total={channelSearchInfo.channelTotal}
            searchValue={searchValue}
          />
        ),
      },
      {
        key: 'member',
        title: '人员',
        length: memberSearchInfo.memberTotal,
        data: memberSearchInfo.list,
        loading: memberSearchInfo.loading,
        action: (page = 1) => handleMemberSearch(page),
        renderItem: (searchItem: EmployeeItem, index: number) => (
          <RenderMemberItem
            memberItem={searchItem}
            index={index}
            total={memberSearchInfo.memberTotal}
            searchValue={searchValue}
          />
        ),
      },
    ];
  }, [
    messageSearchInfo,
    memberSearchInfo,
    channelSearchInfo,
    searchFilterParams,
  ]);

  useEffect(() => {
    handleMessageSearch(0);
    handleChannelSearch();
    handleMemberSearch(1);
  }, [searchValue]);

  const currentTabInfo = useMemo(() => {
    return _.filter(searchTabArr, (i) => i.key === curTab)[0] || {};
  }, [searchTabArr, curTab]);

  const onSearchFilterParamsChange = (params: any) => {
    setSearchFilterParams((pre) => {
      return {
        ...pre,
        ...params,
      };
    });
  };

  useEffect(() => {
    // 全局搜索框变动 不包含指定会话时，将指定会话从所在会话已选列表移除
    if (
      searchFilterParams.conversationSelect.findIndex(
        (item) =>
          item.conversationID === latestSearchConversationInfo?.conversationID
      ) !== -1 &&
      isEmpty(searchConversationInfo?.conversationID)
    ) {
      const newList = filter(
        searchFilterParams.conversationSelect,
        (item) =>
          item.conversationID !== latestSearchConversationInfo?.conversationID
      );
      onSearchFilterParamsChange({
        conversationSelect: newList,
      });
    }
  }, [latestSearchConversationInfo]);

  useEffect(() => {
    // 全局搜索框包含指定会话时，将指定会话加入到所在会话已选列表
    if (
      searchFilterParams.conversationSelect.findIndex(
        (item) => item.conversationID === searchConversationInfo?.conversationID
      ) === -1 &&
      !isEmpty(searchConversationInfo?.conversationID)
    ) {
      onSearchFilterParamsChange({
        conversationSelect: [
          ...searchFilterParams.conversationSelect,
          { ...searchConversationInfo },
        ],
      });
    }
    return () => {
      setLatestSearchConversationInfo(searchConversationInfo);
    };
  }, [searchConversationInfo]);

  useUpdateEffect(() => {
    currentTabInfo.action();
  }, [searchFilterParams]);

  const onFilterSelectChange = (val: boolean) => {
    setShowFilterSelect(val);
    if (!val) {
      setSearchFilterParams({
        customType: {},
        conversationType: {},
        conversationSelect: searchConversationInfo?.conversationID
          ? [{ ...searchConversationInfo }]
          : [],
        contactSelect: [],
        dateSelect: {},
        sort: {
          field: '_score',
          ascending: false,
        },
        searchTimePosition: 0,
        searchTimePeriod: 0,
      });
    }
  };

  useEffect(() => {
    if (rightAreaInSearch === 'channel') {
      // 为了处理从其他右侧页面返回到聊天页面时 定位到原高亮消息处
      const { searchItem, conversation } = rightPayload;

      const updateConversation = (conversationInfo: ConversationItem) => {
        if (conversationInfo?.parentId) {
          updateMultiSessionForSearch(conversationInfo);
        } else {
          updateCurrentConversation(conversationInfo);
        }
      };
      if (searchItem) {
        updateTargetMsg({
          clientMsgID: searchItem.clientMsgID,
          seq: searchItem.seq,
        });
      }
      updateChannelHeaderCurTab('message');
      updateConversation({
        ...conversation,
        ex: `${uuidv4()}`,
      });
    }
  }, [rightAreaInSearch, rightPayload]);

  return (
    <MainContainer>
      {/* 搜索内容 */}
      <div className={styles.searchWrapper}>
        <div className={styles.searchHeader}>
          <div className={styles.searchTabs}>
            {searchTabArr.map((tab: tabItemProps) => {
              return (
                <div
                  className={classNames(
                    styles.searchTabItem,
                    curTab === tab.key && styles.activedTab
                  )}
                  key={tab.key}
                  onClick={() => {
                    setCurTab(tab.key);
                    tab.action();
                  }}
                >
                  <span>{tab.title}</span>
                  <span style={{ fontSize: '12px', marginLeft: '3px' }}>
                    {tab.length || 0}
                  </span>
                </div>
              );
            })}
          </div>
        </div>
        {curTab === 'message' && (
          <div className={styles.searchFilter}>
            <SearchFilter
              curTab={curTab}
              onSearchFilterParamsChange={onSearchFilterParamsChange}
              searchFilterParams={searchFilterParams}
              isShowFilterSelect={showFilterSelect}
              onFilterSelectChange={(val: boolean) => onFilterSelectChange(val)}
            />
          </div>
        )}
        <div className={styles.searchContent}>
          {connectState === 'loading' || currentTabInfo.loading ? (
            <Spin spinning={true} />
          ) : _.isEmpty(currentTabInfo.data) ? (
            <div className={styles.emptyWrapper}>
              <Empty
                image={<img src={searchEmpty} />}
                description={<div>未搜索到相关结果</div>}
              />
            </div>
          ) : (
            <div className={styles.contentWrapper}>
              <Virtuoso
                className={styles.virtuosoListContainer}
                data={currentTabInfo.data}
                itemContent={(index, searchItem: any) => {
                  return currentTabInfo.renderItem(searchItem, index);
                }}
                endReached={() => {
                  if (currentTabInfo.data.length < currentTabInfo.length) {
                    let lastPage = 0;
                    if (currentTabInfo.key === 'member') {
                      lastPage = memberSearchInfo.memberPage + 1;
                    } else if (currentTabInfo.key === 'message') {
                      lastPage = messageSearchInfo.messagePage + 1;
                    }
                    currentTabInfo.action(lastPage);
                  }
                }}
                increaseViewportBy={500}
              />
            </div>
          )}
        </div>
      </div>
      {needShowRightArea && <SearchRightArea />}
    </MainContainer>
  );
};

export default Search;
