.searchWrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
  background: var(--primary-background-color-17);
  border-radius: 8px;

  .searchHeader {
    width: 100%;
    box-shadow: 0 2px 16px 0 rgba(0, 0, 0, 8%);
    z-index: 10;

    .searchTabs {
      padding: 13px;
      max-width: 1000px;
      padding-right: 60px;
      margin: 0 auto;
      display: flex;
      align-items: center;

      .searchTabItem {
        min-width: 50px;
        height: 36px;
        padding: 0 8px;
        font-size: 14px;
        color: var(--primary-text-color-1);
        display: flex;
        align-items: center;
        margin-right: 24px;
        cursor: pointer;
        justify-content: center;
        font-weight: 600;
        border-radius: 6px;
        line-height: 20px;

        &:hover {
          background: var(--primary-background-color-15);
        }
      }

      .activedTab {
        background: var(--tab-actived-background-color);
        color: var(--primary-text-color-9);

        &:hover {
          background: var(--tab-actived-background-color);
        }
      }
    }
  }
  .searchFilter {
    max-width: 1000px;
    padding: 12px 13px 0;
    width: 100%;
    align-self: center;
  }

  .searchContent {
    flex: 1;
    width: 100%;
    position: relative;
    overflow-y: auto;
    overflow-x: hidden;

    .emptyWrapper {
      margin-top: 200px;
    }

    .contentWrapper {
      margin: 0 auto;
      display: flex;
      flex-direction: column;
      height: 100%;

      .searchList {
        flex: 1;
        padding-bottom: 16px;
      }

      .virtuosoListContainer {
        flex: 1;
        overflow-y: scroll !important;
        margin: 20px 0;

        > div {
          left: 0;
          right: 0;
          margin: 0 auto;
          max-width: 1000px;
        }
      }
    }

    :global {
      .linkflow-spin-spinning {
        position: absolute;
        left: 0;
        right: 0;
        top: 40px;
      }
    }
  }
}
