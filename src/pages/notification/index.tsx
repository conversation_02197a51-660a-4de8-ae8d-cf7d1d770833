import { useState, useEffect } from 'react';
import dayjs from 'dayjs';
import { ConversationItem } from '@ht/openim-wasm-client-sdk';
import { initCtx } from '@/utils/parserMdToHtml';
import OIMAvatar from '@/components/OIMAvatar';
import GroupAvatar from '@/components/ChannelList/GroupAvatar';
import ChannelItem from '@/components/ChannelList/ChannelItem';
import styles from './index.less';

interface NotificationObjType {
  list: ConversationItem[];
  num: number;
}

const Notification = () => {
  const [today, setToday] = useState(dayjs());
  const [notificationObj, setNotificationObj] = useState<NotificationObjType>({
    list: [],
    num: 0,
  });

  useEffect(() => {
    initCtx();
    const unsubscribe = window.electronAPI?.subscribe(
      'get-message-notification-data',
      (data) => {
        setNotificationObj(data);
      }
    );

    return () => {
      unsubscribe?.();
    };
  }, []);

  const iconBoxRender = (contact: ConversationItem) =>
    contact.groupID ? (
      <GroupAvatar faceMember={contact?.faceMember} size={'small'} />
    ) : (
      <OIMAvatar
        userID={contact?.userID}
        size={42}
        stateSize={11}
        stateRight={-2}
        hideOnlineStatus={true}
      />
    );

  const onMouseEnter = () => {
    window.electronAPI?.ipcSend('notification-mouse-status', 'enter');
  };

  const onMouseLeave = () => {
    window.electronAPI?.ipcSend('notification-mouse-status', 'leave');
  };

  const cancel = () => {
    window.electronAPI?.ipcSend('notification-cancel-flashing');
  };

  return (
    <div
      className={styles.notificationWarp}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      <div className={styles.header}>新消息·{notificationObj.num}</div>
      <div className={styles.content}>
        {notificationObj.list.map((contact: ConversationItem) => {
          return (
            <ChannelItem
              key={contact.conversationID}
              contact={contact}
              iconBoxRender={iconBoxRender(contact)}
              isLastPinnedItem={false}
              isActive={false}
              today={today}
              isNotification={true}
            />
          );
        })}
      </div>
      <div className={styles.footer}>
        <div onClick={cancel}>取消闪烁</div>
      </div>
    </div>
  );
};

export default Notification;
