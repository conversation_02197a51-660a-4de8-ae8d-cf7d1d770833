.singleChatWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding: 10px;

  .singleThemeBg {
    z-index: -1;
    position: absolute;
    width: 100vw;
    height: 100vh;
    background: url("../../assets/singleThemeBg.jpg");
    background-size: cover;
  }

  .singleChatContent {
    max-width: 1800px;
    height: 100%;
    width: 100%;
    display: flex;
    border-radius: 8px;
    overflow: hidden;
  }

  .spinWrapper {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: #fff;

    .singleSpin {
      position: absolute;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
    }
  }

  .noPermission {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    > img {
      width: 135px;
    }

    .noPermissionDesc {
      margin-top: 37px;
      font-size: 36px;
      font-weight: 600;
      color: #1d1c1d;
      line-height: 50px;
    }
  }
}
