import React, { useState, useEffect, useRef } from 'react';
// @ts-expect-error
import { initBackend } from 'absurd-sql-optimized/dist/indexeddb-main-thread';
import './debugapp.css';

interface WorkerMessage {
  type: string;
  msg?: string;
  timing?: number;
  name?: string;
  value?: string;
  id?: number;
  data?: any;
  on?: boolean;
}

const DebugApp: React.FC = () => {
  const [worker, setWorker] = useState<Worker | null>(null);
  const [sqlOutput, setSqlOutput] = useState<
    { columns: string[]; values: any[][] }[] | string
  >([]);
  const [timings, setTimings] = useState<number[]>([]);
  const [chatlogCount, setChatlogCount] = useState<number>(10000);
  const [outputs, setOutputs] = useState<string[]>([]);
  const [dbname, setDbname] = useState<string>('bench.sqlite');
  const [error, setError] = useState<string>('');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    const workerInstance = new Worker(
      new URL('./main.worker.js', import.meta.url)
    );
    initBackend(workerInstance);
    // listenForPerfData(workerInstance);

    workerInstance.postMessage({ type: 'ui-invoke', name: 'init' });

    const handleMessage = (e: MessageEvent<WorkerMessage>) => {
      switch (e.data.type) {
        case 'output':
          output(e.data.msg || '');
          break;
        case 'clearTimings':
          clearTimings();
          break;
        case 'outputTiming':
          if (e.data.timing !== undefined) {
            outputTiming(e.data.timing);
          }
          break;
        default:
          break;
      }
    };

    workerInstance.addEventListener('message', handleMessage);
    setWorker(workerInstance);

    return () => {
      workerInstance.removeEventListener('message', handleMessage);
      workerInstance.terminate();
    };
  }, []);

  const fixed = (num: number, places: number): number => {
    const factor = 10 ** places;
    const clipped = Math.floor(num * factor);
    return clipped / factor;
  };

  const output = (msg: string): void => {
    // const el = document.createElement('div');
    // el.innerHTML = msg;
    setOutputs((prevOutputs) => [...prevOutputs, msg]);
  };

  const clearTimings = (): void => {
    setTimings([]);
  };

  const clearAll = (): void => {
    clearTimings();
    setOutputs([]);
    setSqlOutput([]);
  };

  const outputTiming = (timing: number): void => {
    setTimings((prevTimings) => [...prevTimings, timing]);
  };

  const handleRadioChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const { name, value } = e.target;
    if (worker) {
      worker.postMessage({ type: 'options', name, value });
    }
  };

  const handleProfileClick = (e: React.ChangeEvent<HTMLInputElement>): void => {
    if (worker) {
      worker.postMessage({ type: 'profiling', on: e.target.checked });
    }
  };

  const handleRawIDBClick = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const disableIfRawIDB = document.querySelector('.disable-if-raw-idb');
    if (disableIfRawIDB) {
      disableIfRawIDB.style.opacity = e.target.checked ? 0.3 : 1;
    }
    if (worker) {
      worker.postMessage({
        type: 'options',
        name: 'raw-idb',
        on: e.target.checked,
      });
    }
  };

  const benchClick = (method: string): void => {
    if (worker) {
      worker.postMessage({
        type: 'ui-invoke',
        name: method,
        prop: { chatlogCount },
      });
    }
  };

  const handleButtonClick = (method: string): void => {
    changeDB('bench.sqlite');
    if (worker) {
      worker.postMessage({ type: 'ui-invoke', name: method });
    }
  };

  const exportDB = (): Promise<any> => {
    if (!worker) {
      return Promise.reject('Worker not initialized');
    }
    const reqId = Math.random();
    return new Promise((resolve) => {
      const handler = (e: MessageEvent<WorkerMessage>) => {
        if (e.data.type === 'fetch-binary' && e.data.id === reqId) {
          worker.removeEventListener('message', handler);
          resolve(e.data.data);
        }
      };
      worker.addEventListener('message', handler);
      worker.postMessage({ type: 'export-db', id: reqId });
    });
  };

  const saveDbToFile = async (): Promise<void> => {
    const { buffer, filename } = await exportDB();
    const blob = new Blob([buffer], { type: 'application/octet-stream' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename.replace('/sq/', '');
    a.click();
    URL.revokeObjectURL(url);
  };

  const runQuery = (sql: string): Promise<any> => {
    if (!worker) {
      return Promise.reject('Worker not initialized');
    }

    const reqId = Math.random();

    return new Promise((resolve) => {
      const handler = (e: MessageEvent<WorkerMessage>) => {
        if (e.data.type === 'query-results' && e.data.id === reqId) {
          worker.removeEventListener('message', handler);
          resolve(e.data.data);
        }
      };
      worker.addEventListener('message', handler);
      worker.postMessage({ type: 'run-query', sql, id: reqId });
    });
  };

  const changeDB = (dbname: string): void => {
    if (worker) {
      worker.postMessage({ type: 'change-db', dbname });
    }
  };

  const loadDbFromFile = (file: File): void => {
    const reader = new FileReader();
    reader.onload = () => {
      const buffer = new Uint8Array(reader.result as ArrayBuffer);
      if (worker) {
        worker.postMessage({ type: 'load-db', buffer });
      }
      setError('');
    };
    reader.readAsArrayBuffer(file);
  };

  const execute = async (): Promise<void> => {
    const sql = textareaRef.current?.value || '';
    try {
      const results = await runQuery(sql);

      if (results.length === 0) {
        setSqlOutput('No results');
        return;
      }

      if (!results.forEach) {
        setSqlOutput(results);
        return;
      }

      // let outputHtml = '';
      // results.forEach((result: any) => {
      //   const table = createTable(result.columns, result.values);
      //   outputHtml += table.outerHTML;
      // });
      setSqlOutput(results);
      setError('');
    } catch (err: any) {
      setError(err.message);
    }
  };

  const renderTable = (columns: string[], values: any[][]) => {
    return (
      <table>
        <thead>
          <tr>
            {columns.map((col, index) => (
              <th key={index}>{col}</th>
            ))}
          </tr>
        </thead>
        <tbody>
          {values.map((row, rowIndex) => (
            <tr key={rowIndex}>
              {row.map((cell, cellIndex) => (
                <td key={cellIndex}>{cell}</td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    );
  };

  const renderOutput = () => {
    if (typeof sqlOutput === 'string') {
      return <div>{sqlOutput}</div>;
    }

    if (sqlOutput.length === 0) {
      return <div>No results</div>;
    }

    return sqlOutput.map((result, index) => (
      <div key={index}>{renderTable(result.columns, result.values)}</div>
    ));
  };

  const createTable = (
    columns: string[],
    values: any[][]
  ): HTMLTableElement => {
    const table = document.createElement('table');
    const thead = document.createElement('thead');
    const tbody = document.createElement('tbody');

    // 表头
    const headerRow = document.createElement('tr');
    columns.forEach((col) => {
      const th = document.createElement('th');
      th.textContent = col;
      headerRow.appendChild(th);
    });
    thead.appendChild(headerRow);

    // 表体
    values.forEach((row) => {
      const tr = document.createElement('tr');
      row.forEach((cell) => {
        const td = document.createElement('td');
        td.textContent = cell;
        tr.appendChild(td);
      });
      tbody.appendChild(tr);
    });

    table.appendChild(thead);
    table.appendChild(tbody);
    return table;
  };

  return (
    <div
      className="debug-container"
      style={{ backgroundColor: 'wheat', padding: '20px', borderRadius: '8px' }}
    >
      <div className="interpreter">
        <label>
          DB Name:
          <input
            type="text"
            value={dbname}
            onChange={(e) => setDbname(e.target.value)}
            placeholder="Enter database name"
          />
        </label>
        <button type="button" onClick={() => changeDB(dbname)}>
          USE DB
        </button>
        <textarea
          ref={textareaRef}
          id="commands"
          rows={10}
          cols={80}
          style={{ display: 'block', width: '100%', margin: '20px 0' }}
        />
        <button type="button" onClick={execute}>
          Execute
        </button>
        <button type="button" onClick={clearAll}>
          Clear
        </button>
        <button type="button" onClick={saveDbToFile}>
          Save the db
        </button>
        <div id="error" style={{ color: 'red', height: error ? '2em' : '0' }}>
          {error}
        </div>

        <div id="output">{renderOutput()}</div>
      </div>

      <hr />

      <div className="text">
        This is sqlite3 running in your browser with a backend that properly
        persists the database in IndexedDB (
        <a href="https://github.com/jlongster/absurd-sql">absurd-sql</a>). It
        stores each page from the db as a separate item, treating IDB like a
        hard disk. It never has to load the full DB into memory and can update
        it with small individual writes.
      </div>

      <div className="text last">
        The below examples are meant to be stress tests, showing that it can
        handle large amounts of data and queries that need to scan all of it.
        With more normal cases and a small cache, it works really well. It
        easily beats IndexedDB up to a factor of 10.
      </div>

      <div>
        <button
          type="button"
          id="populateSmall"
          onClick={() => handleButtonClick('populateSmall')}
        >
          Write a little data
        </button>
        <button
          type="button"
          id="populateLarge"
          onClick={() => handleButtonClick('populateLarge')}
        >
          Write lots of data
        </button>
        <button
          type="button"
          id="sumAll"
          onClick={() => handleButtonClick('sumAll')}
        >
          Sum all values
        </button>
        <button
          type="button"
          id="randomReads"
          onClick={() => handleButtonClick('randomReads')}
        >
          Read chunks of 1000 items
        </button>
        <button
          type="button"
          id="deleteFile"
          onClick={() => handleButtonClick('deleteFile')}
        >
          Delete file
        </button>
        <button
          type="button"
          id="readBench"
          onClick={() => handleButtonClick('readBench')}
        >
          Run read benchmarks
        </button>
        <button
          type="button"
          id="writeBench"
          onClick={() => handleButtonClick('writeBench')}
        >
          Run write benchmarks
        </button>
        <button
          type="button"
          id="mockChat"
          onClick={() => benchClick('mockChat')}
        >
          Mock Chat log
        </button>
        <input
          type="text"
          value={chatlogCount}
          onChange={(e) => setChatlogCount(Number(e.target.value))}
          placeholder="Enter mock count"
        />
      </div>

      <div className="options">
        <label>
          <input type="checkbox" name="profile" onChange={handleProfileClick} />{' '}
          Record performance profile
        </label>
        <label>
          <input
            type="checkbox"
            name="raw-indexeddb"
            onChange={handleRawIDBClick}
          />{' '}
          Use raw IndexedDB
        </label>
      </div>

      <div className="disable-if-raw-idb">
        <div className="options">
          Backend:
          <label>
            <input
              type="radio"
              name="backend"
              value="idb"
              defaultChecked={true}
              onChange={handleRadioChange}
            />{' '}
            IndexedDB
          </label>
          <label>
            <input
              type="radio"
              name="backend"
              value="memory"
              onChange={handleRadioChange}
            />{' '}
            Memory
          </label>
        </div>

        <div className="options">
          Cache size:
          <label>
            <input
              type="radio"
              name="cacheSize"
              value="0"
              defaultChecked={true}
              onChange={handleRadioChange}
            />{' '}
            0MB
          </label>
          <label>
            <input
              type="radio"
              name="cacheSize"
              value="2000"
              onChange={handleRadioChange}
            />{' '}
            2MB
          </label>
          <label>
            <input
              type="radio"
              name="cacheSize"
              value="10000"
              onChange={handleRadioChange}
            />{' '}
            10MB
          </label>
          <label>
            <input
              type="radio"
              name="cacheSize"
              value="60000"
              onChange={handleRadioChange}
            />{' '}
            60MB
          </label>
          <span
            className="warning"
            style={{ fontSize: '13px', color: '#8080a0' }}
          >
            Using a cache will greatly improve perf, but no cache shows the full
            number of read/writes
          </span>
        </div>

        <div className="options pageSize">
          Page size:
          <label>
            <input
              type="radio"
              name="pageSize"
              value="4096"
              onChange={handleRadioChange}
            />{' '}
            4096
          </label>
          <label>
            <input
              type="radio"
              name="pageSize"
              value="8192"
              defaultChecked={true}
              onChange={handleRadioChange}
            />{' '}
            8192
          </label>
          <label>
            <input
              type="radio"
              name="pageSize"
              value="16384"
              onChange={handleRadioChange}
            />{' '}
            16384
          </label>
        </div>
      </div>

      <div className="flex flex-fill">
        <div className="output flex-fill">
          {outputs.map((msg, index) => (
            <div key={index}>{msg}</div>
          ))}
        </div>
        <div className="output timings">
          <strong>timings</strong>
          <div className="timings-data">
            {timings.map((timing, index) => (
              <div key={index}>{timing.toFixed(2)}</div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DebugApp;
