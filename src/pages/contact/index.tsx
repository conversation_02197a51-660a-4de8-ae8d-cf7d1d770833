import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Breadcrumb, Pagination, PaginationProps } from '@ht/sprite-ui';
import MainContainer from '@/components/MainContainer';
import { IMSDK } from '@/layouts/BasicLayout';
import _ from 'lodash';
import { EmployeeItem } from '@ht/openim-wasm-client-sdk';
import { useConversationStore } from '@/store';
import useDeparmentInfo from '@/hooks/useDeparmentInfo';
import loadingIcon from '@/assets/contact/loading.png';
import { shallow } from 'zustand/shallow';
import RightUserDetail from './components/RightUserDetail';
import styles from './index.less';
import LeftTree from './components/LeftTree';
import SearchUser from './components/SearchUser';
import UserCard from './components/UserCard';
import { BotList } from './components/BotList';

export interface selectDepartmentProps {
  key: React.Key;
  departNamePath?: { departmentID: string; departmentName: string }[];
}

interface StateProps<T> {
  loading: boolean;
  data: T;
}

const defaultDepartmentId = 'htzq'; // 集团的ID

// eslint-disable-next-line max-statements
const Contact = () => {
  const { departmentInfoIniting } = useDeparmentInfo();
  const [showSkeleton, setShowSkeleton] = useState<boolean>(true);
  const [selectedDepartmentID, setSelectedDepartmentID] =
    useState<selectDepartmentProps>();

  const changeRightArea = useConversationStore(
    (state) => state.changeRightArea
  );
  const rightAreaInGroupList = useConversationStore(
    (state) => state.rightAreaInGroupList
  );
  const currentRightArea =
    rightAreaInGroupList && rightAreaInGroupList.length > 0
      ? rightAreaInGroupList[rightAreaInGroupList.length - 1]
      : {};
  const selectedUserID =
    currentRightArea?.type === 'personDetail' ? currentRightArea?.payload : '';

  const [pageSizeOptions, setPageSizeOptions] = useState([10, 20, 30, 40, 50]);

  const previousWidthRef = useRef<number>(); // 用于存储上一次的宽度

  const [showUsers, setShowUsers] = useState<StateProps<EmployeeItem[]>>();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalNum, setTotalNum] = useState(0);

  const [columns, setColumns] = useState<string | number>();

  const [showBot, setShowBot] = useState<boolean>(false);

  const handleDepartmentClicked = useCallback(
    (selectedKeysValue: React.Key[], info: any) => {
      setSelectedDepartmentID({
        key: selectedKeysValue[0],
        departNamePath: info.node.namePath,
      });

      changeRightArea('CLEAR_RIGHT_AREA');
      setCurrentPage(1);
    },
    [changeRightArea]
  );

  const clearSelectedDepartment = () => {
    setSelectedDepartmentID(undefined);
    changeRightArea('CLEAR_RIGHT_AREA');
    setCurrentPage(1);
  };

  // 切换部门或者团队时更新users列表
  useEffect(() => {
    async function fetchEmployeeList() {
      if (
        !(selectedDepartmentID?.key == null || selectedDepartmentID?.key === '')
      ) {
        try {
          setShowUsers({
            loading: true,
            data: [],
          });

          const departmentID = selectedDepartmentID?.key as string;

          const { data } = await IMSDK.getEmployeeListPageInDepartment(
            departmentID,
            (currentPage - 1) * pageSize,
            pageSize
          );

          setShowUsers({
            loading: false,
            data: data.list,
          });
          setTotalNum(data.total);
        } catch (e) {
          setShowUsers({
            loading: true,
            data: [],
            totalNum: 0,
          });
        }
      }
    }
    fetchEmployeeList();
  }, [currentPage, pageSize, selectedDepartmentID?.key]);

  // useEffect(() => {
  //   const initBotList = async () => {
  //     if (showBot) {
  //       const { data } = await IMSDK.getEmployeeListPageInDepartment(
  //         'SZYG',
  //         0,
  //         1
  //       );
  //
  //       setShowBotList(data.list);
  //       setBotTotalNum(data.total);
  //     }
  //   };

  //   if (showBot) {
  //     initBotList();
  //   }
  // }, [showBot]);
  const onPaginationChange: PaginationProps['onChange'] = (
    newPageNumber,
    newPageSize
  ) => {
    setCurrentPage(newPageNumber);
    setPageSize(newPageSize);
  };

  const handleUserClicked = useCallback(
    (userID: string, e?: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
      if (e) {
        e.stopPropagation();
      }

      changeRightArea('OPEN_PERSON_DETAIL', userID);
    },
    [changeRightArea]
  );

  useEffect(() => {
    if (!departmentInfoIniting) {
      setTimeout(() => {
        setShowSkeleton(false);
      }, 150);
    } else {
      setShowSkeleton(true);
    }
  }, [departmentInfoIniting]);

  const resetColumns = (containerWidth: number) => {
    const columnWidth = 144; // 最小列宽

    const gap = 16; // 列间距
    const totalColumnWidth = columnWidth + gap;
    if (containerWidth) {
      setColumns(Math.floor(containerWidth / totalColumnWidth));
    }
  };

  useEffect(() => {
    const container = document.getElementById('employeeList');
    const resetPageSizeOptions = (containerWidth: number) => {
      const columnWidth = 144; // 最小列宽

      const gap = 16; // 列间距

      // 计算每列占用的总宽度，包括列宽和间隔
      const totalColumnWidth = columnWidth + gap;

      if (containerWidth) {
        const newParams = Math.floor(containerWidth / totalColumnWidth) * 4;

        setPageSizeOptions([
          newParams,
          newParams * 2,
          newParams * 3,
          newParams * 5,
        ]);
        setPageSize(newParams);
        setCurrentPage(1);
      }
    };
    if (!container) {
      return;
    }
    // 创建 ResizeObserver 实例
    const resizeObserver = new ResizeObserver(() => {
      const containerWidth = container?.offsetWidth;
      if (containerWidth !== previousWidthRef.current) {
        // 获取容器的新宽度并更新状态
        resetColumns(containerWidth);
        resetPageSizeOptions(containerWidth);
        previousWidthRef.current = containerWidth;
      }
    });
    resizeObserver.observe(container);
    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  return (
    <MainContainer>
      {/* 左侧组织架构 */}
      <LeftTree
        selectedDepartmentID={selectedDepartmentID!}
        handleDepartmentClicked={handleDepartmentClicked}
        defaultSelectedDepartmentCode={defaultDepartmentId}
        clearSelectedDepartment={clearSelectedDepartment}
        showBot={showBot}
        showSkeleton={showSkeleton}
        setShowBot={setShowBot}
      />
      {/* 中间部门和员工信息 */}

      <div className={styles.mainArea}>
        {!showSkeleton ? (
          <>
            <div className={styles.mainInfo}>
              {/* 搜索框和分页 */}
              <div className={styles.header}>
                <div className={styles.title}>所有人员</div>
              </div>
              <div className={styles.searchUser}>
                <SearchUser
                  handleUserClicked={(item) =>
                    handleUserClicked(item.employeeID)
                  }
                  style={{
                    height: 38,
                    borderRadius: 8,
                    background: '#fff',
                    border: '1px solid #C6C8CA',
                  }}
                  size={'middle'}
                />
              </div>
              {/* 面包屑 */}

              {!showBot ? (
                <>
                  {selectedDepartmentID && (
                    <div className={styles.breadCrumb}>
                      <Breadcrumb>
                        {selectedDepartmentID?.departNamePath?.map((item) => (
                          <Breadcrumb.Item key={item.departmentID}>
                            {item.departmentName}
                          </Breadcrumb.Item>
                        ))}
                      </Breadcrumb>
                    </div>
                  )}

                  {/* <div
            className={styles.teamArea}
            style={{
              maxHeight: showAllTeams ? `${maxDeaultTeamListHight}px` : 'none',
            }}
          >
            {showTeamList != null && !_.isEmpty(showTeamList) && (
              <ul id="teamListUl" className={styles.teamList}>
                {showTeamList?.map((item: any) => {
                  return (
                    <li
                      className={classnames(
                        styles.teamListItem,
                        selectedTeamID === item.key && styles.selected
                      )}
                      key={item.key}
                      onClick={() => handleTeamClicked(item)}
                    >
                      {item.title}
                    </li>
                  );
                })}
              </ul>
            )}
            {showToggleBtn && (
              <div className={styles.toggleBtn}>
                {showAllTeams ? (
                  <>
                    <div onClick={() => setShowAllTeams(false)}>
                      展开
                      <CaretDownOutlined />
                    </div>
                  </>
                ) : (
                  <>
                    <div onClick={() => setShowAllTeams(true)}>
                      收起
                      <CaretUpOutlined />
                    </div>
                  </>
                )}
              </div>
            )}
          </div> */}

                  <div className={styles.contactPanel} id="employeeList">
                    <div
                      className={styles.employeeList}
                      style={{
                        gridTemplateColumns: `repeat(${
                          columns || 'auto-fit'
                        }, minmax(284px, 284px))`,
                      }}
                    >
                      {showUsers?.data?.map((user) => (
                        <UserCard
                          key={user.employeeID}
                          user={user}
                          handleUserClicked={handleUserClicked}
                        />
                      ))}
                    </div>
                  </div>
                </>
              ) : (
                <div
                  style={{
                    flex: 1,
                    display: 'flex',
                    flexDirection: 'column',
                  }}
                >
                  <BotList />
                </div>
              )}
            </div>

            {/* 底部分页栏 */}
            {!showBot && (
              <div className={styles.footer}>
                {!_.isEmpty(showUsers?.data) && (
                  <Pagination
                    total={totalNum}
                    showSizeChanger={true}
                    current={currentPage}
                    pageSize={pageSize}
                    pageSizeOptions={pageSizeOptions}
                    className={styles.pagination}
                    onChange={onPaginationChange}
                  />
                )}
              </div>
            )}
          </>
        ) : (
          <div className={styles.loadingArea}>
            <img src={loadingIcon}></img>
          </div>
        )}
      </div>
      {selectedUserID != null && selectedUserID !== '' && (
        <RightUserDetail selectedUserID={selectedUserID} />
      )}
    </MainContainer>
  );
};

export default Contact;
