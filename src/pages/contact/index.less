.mainArea {
  background: var(--primary-background-color-17);
  border-radius: 8px;
  flex: 1;
  padding: 0;
  display: flex;
  flex-direction: column;
  align-items: center;

  .mainInfo {
    flex: 1;
    display: flex;
    flex-direction: column;
    max-width: 1533px;
    width: 100%;
    overflow-y: hidden;

    .header {
      padding: 17px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title {
        font-size: 18px;
        font-weight: 600;
        color: #2f3035;
        line-height: 24px;
        line-height: 26px;
      }
    }

    .searchUser {
      padding: 0 20px;
    }

    .breadCrumb {
      padding: 20px 28px 10px 20px;
      :global {
        .linkflow-breadcrumb li:last-child {
          color: #2f3035;
        }
      }
    }

    .teamArea {
      position: relative;
      margin-bottom: 20px;
      border-radius: 6px;
      border: 1px solid var(--primary-background-color-5);
      overflow: hidden;

      .teamList {
        display: flex;
        flex-wrap: wrap;
        padding: 7px 16px;
        overflow: hidden;
        margin: 0;
        height: max-content;
      }
      .teamListItem {
        margin: 4px;
        padding: 0 6px;
        color: var(--primary-text-color-1);
        border-radius: 28px;
        cursor: pointer;
        height: 28px;
        line-height: 28px;

        &:hover {
          background: var(--file-backgroud-color);
          border-radius: 4px;
        }
        &.selected {
          background: var(--primary-background-color-4);
          border-radius: 4px;
          color: var(--primary-text-color-pressed);
        }
      }
      .toggleBtn {
        position: absolute;
        right: 5px;
        bottom: 5px;
        font-size: 14px;
        display: flex;
        color: var(--primary-btn-backgroud-color);
        cursor: pointer;
        align-items: flex-end;
      }
    }

    .contactPanel {
      flex: 1;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 10px 0 16px 20px;
      // overflow-x: hidden;

      .employeeList {
        display: grid;
        transition: grid-template-columns 0.3s ease; /* 添加动画 */
        // justify-content: center;
        gap: 16px;
      }
    }
  }

  .footer {
    height: 72px;
    bottom: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    :global {
      .linkflow-pagination {
        li {
          line-height: 24px !important;
          min-width: 24px !important;
          height: 24px !important;
          background-color: transparent;
        }
        .linkflow-pagination-item {
          a {
            color: #2f3035;
          }
          &:hover {
            background: rgba(107, 107, 108, 8%);
            border-radius: 4px;
          }
        }
        .linkflow-pagination-item-active {
          background: #0074e2;
          border-radius: 4px;
          a {
            color: #ffffff;
          }
          &:hover {
            background: #0074e2;
          }
        }

        .linkflow-select-selector {
          height: 24px !important;
        }

        .linkflow-select-selection-item {
          line-height: 22px !important;
          font-size: 12px;
        }

        .linkflow-pagination-item-link {
          background-color: transparent;
        }

        .linkflow-pagination-options {
          margin-left: 8px;

          .linkflow-select:not(.linkflow-select-customize-input)
            .linkflow-select-selector {
            border-radius: 4px;
          }
        }
        .linkflow-select:not(.linkflow-select-disabled):hover
          .linkflow-select-selector {
          border-color: #0074e2;
          box-shadow: 0 0 0 1px rgba(0, 116, 226, 20%);
        }

        .linkflow-select-single.linkflow-select-open
          .linkflow-select-selection-item,
        .linkflow-select-arrow .spriteicon {
          color: #2f3035;
          font-size: 12px;
        }

        .linkflow-select-dropdown {
          border-radius: 8px;
          border: 1px solid var(--primary-background-color-5);
          padding: 4px 4px 3px;
          .linkflow-select-item {
            font-size: 12px;
            font-weight: 400;
            color: #1d1c1d;
            line-height: 18px;
            min-height: 28px;
            margin-bottom: 1px;
          }
          .linkflow-select-item-option-active:not(
              .linkflow-select-item-option-disabled
            ) {
            background: rgba(107, 107, 108, 8%);
            border-radius: 6px;
          }
          .linkflow-select-item-option-selected:not(
              .linkflow-select-item-option-disabled
            ) {
            color: #1d1c1d;
            font-weight: bold;
            background: var(--tab-actived-background-color);
            border-radius: 6px;
          }
        }
      }
    }
  }

  .loadingArea {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    @keyframes rotate {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }
    img {
      width: 80px;
      height: 80px;
      animation: rotate 2s linear infinite; /* 2秒完成一个周期，线性速度，无限次重复 */
    }
  }
}

.userDatailCard {
  background: var(--primary-background-color-6);
  width: 480px;
}
