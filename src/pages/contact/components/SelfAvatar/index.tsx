import { AvatarProps } from '@ht/sprite-ui';
import * as React from 'react';
import { memo } from 'react';

import classnames from 'classnames';
import { getAvatarUrl, getRandomDefaultAvatar } from '@/utils/avatar';
import useOnlineStatus from '@/hooks/useOnlineStatus';
import { OnlineState } from '@ht/openim-wasm-client-sdk';

import { useUserStore } from '@/store';
import styles from './index.less';

interface SelfAvatarProps extends AvatarProps {
  size?: number;
  userID?: string;
  borderRadius?: number;
  stateSize?: number;
  stateRight?: number;
  style?: React.CSSProperties;
}

// 先复制一份改一下本人，后面统一改头像逻辑，再基于修改后抽一个本人的即可
export const OnlineStatusComponent = ({
  userID,
  stateSize,
  stateRight,
  isPosition = true,
}: {
  userID: string;
  stateSize: number;
  stateRight: number;
  isPosition?: boolean;
}) => {
  const { onlineState } = useOnlineStatus(userID);

  return (
    <div
      className={classnames(
        styles.onLineState,
        onlineState?.status === OnlineState.Online
          ? styles.online
          : styles.offline
      )}
      style={{
        width: `${stateSize}px`,
        height: `${stateSize}px`,
        right: `${stateRight}px`,
        position: `${isPosition ? 'absolute' : 'relative'}`,
      }}
    ></div>
  );
};
const SelfAvatar: React.FC<SelfAvatarProps> = (props) => {
  const {
    size = 42,
    userID,
    borderRadius = 4,
    stateSize = 6,
    stateRight = 0,
    style,
  } = props;

  const { selfInfo } = useUserStore();
  const getDynamicClipPath = () => {
    const referenceSize = 20;

    const scale = size / referenceSize;

    const originalPath =
      'path("M16,0 C18.209139,0 20,1.790861 20,4 L20.000532,12.4162763 C19.38793,12.1485335 18.711311,12 18,12 C15.238576,12 13,14.2385763 13,17 C13,18.1261445 13.372301,19.1653335 14.000535,20.0011995 L4,20 C1.790861,20 0,18.209139 0,16 L0,4 C0,1.790861 1.790861,0 4,0 L16,0 Z")';

    const scaledPath = originalPath.replace(/([0-9.]+)/g, (match) => {
      return (parseFloat(match) * scale).toString();
    });

    return scaledPath;
  };

  return (
    <div style={{ position: 'relative', ...style }}>
      <div
        style={{
          width: `${size}px`,
          height: `${size}px`,
          borderRadius: `${borderRadius}px`,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          fontSize: '12px',
          color: '#fff',
          clipPath: userID != null ? getDynamicClipPath() : '',
        }}
      >
        <img
          src={selfInfo.faceURL || getAvatarUrl(selfInfo.userID)} // 这个要取这里，这样上传后可以更新
          onLoad={(e) => {
            if (!(e.target as any).inited) {
              (e.target as any).inited = true;
              (e.target as any).src =
                selfInfo.faceURL || getAvatarUrl(selfInfo.userID);
            }
          }}
          onError={(e) => {
            (e.target as any).src = getRandomDefaultAvatar(selfInfo.userID);
          }}
          style={{
            width: '100%',
            height: '100%',
            borderRadius: `${borderRadius}px`,
          }}
          alt=""
        />
      </div>
      {userID != null && userID !== '' && (
        <OnlineStatusComponent
          userID={userID}
          stateSize={stateSize}
          stateRight={stateRight}
        />
      )}
    </div>
  );
};

export default memo(SelfAvatar);
