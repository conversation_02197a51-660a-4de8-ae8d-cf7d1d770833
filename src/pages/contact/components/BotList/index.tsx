import { useConversationToggle } from '@/hooks/useConversationToggle';
import { EmployeeItem, SessionType } from '@ht/openim-wasm-client-sdk';
import OIMAvatar from '@/components/OIMAvatar';
import { useState, useCallback, useEffect } from 'react';
import { Virtuoso } from '@ht/react-virtuoso';
import { IMSDK } from '@/layouts/BasicLayout';
import styles from './index.less';

const PAGE_SIZE = 30;
const DEPARTMENT_ID = 'SZYG';

const fetchBots = async (page: number, pageSize: number) => {
  const offset = page * pageSize;

  const { data } = await IMSDK.getEmployeeListPageInDepartment(
    DEPARTMENT_ID,
    offset,
    pageSize
  );
  return {
    list: data?.list ?? [],
    total: data?.total ?? 0,
  };
};

export const BotList = () => {
  const [bots, setBots] = useState<EmployeeItem[]>([]);
  const [currentPage, setCurrentPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);
  const [totalCount, setTotalCount] = useState(0);

  const { toSpecifiedConversation } = useConversationToggle();

  const loadMore = useCallback(async () => {
    if (loading || !hasMore) {
      return;
    }

    setLoading(true);
    const { list: newBots, total } = await fetchBots(currentPage, PAGE_SIZE);

    setBots((prev) => [...prev, ...newBots]);
    setCurrentPage((prev) => prev + 1);
    setTotalCount(total);

    if (newBots.length < PAGE_SIZE || bots.length + newBots.length >= total) {
      setHasMore(false);
    }

    setLoading(false);
  }, [loading, hasMore, currentPage, bots.length]);

  useEffect(() => {
    loadMore(); // 首次加载第一页
  }, []);

  const handleBotClicked = (item: EmployeeItem) => {
    toSpecifiedConversation({
      sourceID: item.employeeID,
      sessionType: SessionType.Single,
    });
  };

  const getBorderRadisByIndex = (index: number) => {
    if (index === 0) {
      return '8px 8px 0 0';
    } else if (index === bots.length - 1) {
      return '0 0 8px 8px';
    }
    return '';
  };

  // useEffect(() => {
  //   if (bots.length < totalCount && !loading && hasMore) {
  //     // 等待一帧，等渲染后才能判断撑满没
  //     requestAnimationFrame(() => {
  //       const virtuosoContainer = document.querySelector(
  //         '[data-testid="virtuoso-scroller"]'
  //       ); // 类名根据你的版本微调
  //       if (
  //         virtuosoContainer &&
  //         virtuosoContainer.scrollHeight <= virtuosoContainer.clientHeight
  //       ) {
  //         // 没撑满，继续加载
  //         loadMore();
  //       }
  //     });
  //   }
  // }, [bots, hasMore, loadMore, loading, totalCount]);

  return (
    <div className={styles.botArea}>
      <div className={styles.botNum}>linkflow 中的 {totalCount} 个应用</div>

      <div className={styles.listArea}>
        {bots?.length > 0 && (
          <Virtuoso
            id="bots"
            style={{ height: '100%' }}
            data={bots}
            endReached={loadMore}
            computeItemKey={(_, item) => item.employeeID}
            itemContent={(index, item) => {
              return (
                <div onClick={() => handleBotClicked(item)}>
                  <div
                    className={styles.bot}
                    style={{
                      borderRadius: getBorderRadisByIndex(index),
                      borderBottom: index !== bots.length - 1 ? 'none' : '',
                    }}
                  >
                    <OIMAvatar
                      userID={item.employeeID}
                      size={56}
                      borderRadius={4}
                      hideOnlineStatus={true}
                    />
                    <div className={styles.info}>
                      <div className={styles.name}>{item.employeeName}</div>
                      <div
                        className={styles.desc}
                        title="该应用暂时还没有添加简介信息"
                      >
                        该应用暂时还没有添加简介信息
                      </div>
                    </div>
                  </div>
                </div>
              );
            }}
          />
        )}
      </div>
    </div>
  );
};
