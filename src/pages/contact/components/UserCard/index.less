.employeeCard {
  display: flex;
  position: relative;
  border-radius: 16px;
  cursor: pointer;
  width: 284px;
  height: 138px;
  background: #ffffff;
  box-shadow: 0 1px 8px 0 rgba(0, 0, 0, 8%);
  border: 2px solid transparent;

  &:hover {
    border: 2px solid #0074e2;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 12%);
  }

  .avatarWrapper {
    position: relative;
  }

  .descWrapper {
    display: flex;
    flex-direction: column;
    margin-top: 30px;
    overflow: hidden;
    padding-right: 28px;
  }

  .nameAndPostionArea {
    font-weight: 600;
    line-height: 22px;
    font-size: 16px;
    display: flex;
    align-items: center;

    .name {
      color: #1d1c1d;
    }

    .state {
      width: 15px;
      height: 15px;
      margin-left: 4px;
    }
  }

  .position {
    font-weight: 400;
    font-size: 12px;
    color: #999ba0;
    line-height: 14px;
    margin-top: 6px;
    height: 28px;
    margin-bottom: 0;
  }

  .otherDescWrapper {
    display: flex;
    align-items: center;
    margin-top: 4px;

    > div {
      margin-right: 16px;

      .icon {
        display: block;
      }

      .hoverIcon {
        display: none;
      }

      &:hover {
        .icon {
          display: none;
        }

        .hoverIcon {
          display: block;
        }
      }
    }
  }
}

.popStateContainer {
  :global {
    .linkflow-popover-content {
      border-radius: 5px;
      // background: black;
      color: #fff !important;
    }

    .linkflow-popover-inner-content {
      border-radius: 5px;
      background: black;
      padding: 6px 10px !important;
      color: #fff !important;
    }

    .linkflow-popover-arrow-content::before {
      background: black;
    }
  }
}
