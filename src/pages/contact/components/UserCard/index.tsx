import React, { memo, useState } from 'react';
import { Typo<PERSON>, Popover } from '@ht/sprite-ui';
import OIMAvatar from '@/components/OIMAvatar';
import _ from 'lodash';
import { EmployeeItem, SessionType } from '@ht/openim-wasm-client-sdk';
import { useConversationToggle } from '@/hooks/useConversationToggle';
import phoneIcon from '@/assets/contact/card/phone.svg';
import phoneHoverIcon from '@/assets/contact/card/phoneHover.svg';
import emailIcon from '@/assets/contact/card/email.svg';
import emailHoverIcon from '@/assets/contact/card/emailHover.svg';
import messageIcon from '@/assets/contact/card/message.svg';
import messageHoverICon from '@/assets/contact/card/messageHover.svg';
import { IMSDK } from '@/layouts/BasicLayout';
import styles from './index.less';
import OnlineOrTypingStatus from '../OnlineOrTypingStatus';

interface UserCardProp {
  user: EmployeeItem;
  handleUserClicked: (
    userID: string,
    e?: React.MouseEvent<HTMLDivElement, MouseEvent>
  ) => void;
}
const UserCard = ({ user, handleUserClicked }: UserCardProp) => {
  const { toSpecifiedConversation } = useConversationToggle();
  const [userDetailEx, setUserDetailEx] = useState<any>();

  const handleCreateConversation = () => {
    toSpecifiedConversation({
      sourceID: user.employeeID ?? '',
      sessionType: SessionType.Single,
    });
  };

  const [loading, setLoading] = useState(false);
  const [hasFetched, setHasFetched] = useState(false);

  const fetchData = async () => {
    if (hasFetched) {
      return;
    }
    setLoading(true);

    const userDetailDataResponse = await IMSDK.getUsersInfo([user.employeeID]);

    if (userDetailDataResponse?.data?.[0] != null) {
      setUserDetailEx(JSON.parse(userDetailDataResponse.data[0]?.ex || '{}'));
    }

    setHasFetched(true);
    setLoading(false);
  };

  const handleVisibleChange = (visible: boolean) => {
    if (visible && !hasFetched) {
      fetchData();
    }
  };

  const handleEmailClick = async (e: React.MouseEvent<HTMLAnchorElement>) => {
    if (!userDetailEx?.email) {
      e.preventDefault(); // 阻止默认跳转
      await fetchData();
      if (userDetailEx?.email) {
        // 手动打开邮箱客户端
        window.location.href = `mailto:${userDetailEx?.email}`;
      }
    }
  };

  return (
    <div
      className={styles.employeeCard}
      key={user.employeeID}
      onClick={(e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
        handleUserClicked(user.employeeID, e);
      }}
    >
      <div className={styles.avatarWrapper}>
        <OIMAvatar
          userID={user.employeeID}
          size={72}
          borderRadius={8}
          shape="square"
          stateSize={6}
          style={{
            marginTop: '30px',
            margin: '30px 26px 0 24px',
            height: '72px',
          }}
          hideOnlineStatus={true}
        />
        <OnlineOrTypingStatus
          userID={user?.employeeID}
          style={{
            height: 'auto',
            border: '3px solid #FFFFFF',
            position: 'absolute',
            top: '93px',
            left: '88px',
            borderRadius: '50%',
            background: '#fff',
          }}
        />
      </div>

      <div className={styles.descWrapper}>
        <div className={styles.nameAndPostionArea}>
          <Typography.Text
            className={styles.name}
            ellipsis={true}
            title={user.employeeName}
          >
            {user.employeeName}
          </Typography.Text>
        </div>
        <Typography.Paragraph
          className={styles.position}
          title={user.position}
          ellipsis={{
            rows: 2,
          }}
        >
          {user.position}
        </Typography.Paragraph>
        <div className={styles.otherDescWrapper}>
          <Popover
            overlayClassName={styles.popStateContainer}
            content={loading ? '' : userDetailEx?.mobile || '--'}
            placement="top"
            showArrow={true}
            autoAdjustOverflow={true}
            trigger={'hover'}
            arrowPointAtCenter={true}
            onOpenChange={handleVisibleChange}
          >
            <div>
              <img src={phoneIcon} className={styles.icon} />
              <img src={phoneHoverIcon} className={styles.hoverIcon} />
            </div>
          </Popover>

          <Popover
            overlayClassName={styles.popStateContainer}
            content={loading ? '' : userDetailEx?.email || '--'}
            placement="top"
            showArrow={true}
            autoAdjustOverflow={true}
            trigger={'hover'}
            arrowPointAtCenter={true}
            onOpenChange={handleVisibleChange}
          >
            <div>
              <img src={emailIcon} className={styles.icon} />
              <a
                href={
                  userDetailEx?.email
                    ? `mailto:${userDetailEx?.email}`
                    : undefined
                }
                onClick={handleEmailClick}
              >
                <img src={emailHoverIcon} className={styles.hoverIcon} />
              </a>
            </div>
          </Popover>

          <Popover
            overlayClassName={styles.popStateContainer}
            content={'发消息'}
            placement="top"
            showArrow={true}
            autoAdjustOverflow={true}
            trigger={'hover'}
            arrowPointAtCenter={true}
          >
            <div
              onClick={(e) => {
                e.stopPropagation();
                handleCreateConversation();
              }}
            >
              <img src={messageIcon} className={styles.icon} />
              <img src={messageHoverICon} className={styles.hoverIcon} />
            </div>
          </Popover>
        </div>
      </div>
    </div>
  );
};

export default memo(UserCard);
