import { CSSProperties, useState } from 'react';
import { Empty, Input, Spin, Tooltip } from '@ht/sprite-ui';
import { IMSDK } from '@/layouts/BasicLayout';
import InfiniteScroll from 'react-infinite-scroll-component';
import { useDebounceFn } from 'ahooks';
import searchIcon from '@/assets/contact/searchIcon.png';
import searchClearIcon from '@/assets/contact/searchClear.svg';
import { EmployeeItem } from '@ht/openim-wasm-client-sdk';
import classNames from 'classnames';
import OIMAvatar from '@/components/OIMAvatar';
import { isBotUser } from '@/utils/avatar';
import styles from './index.less';

interface SearchUserProps {
  handleUserClicked: (prop: EmployeeItem) => any;
  disabled?: boolean;
  autoClearInputAfterUserClicked?: boolean;
  autoCloseAfterUserClicked?: boolean;
  style?: CSSProperties;
  size?: 'small' | 'middle';
}

const containerHeight = '240px';
const pageSize = 8;
const SearchUser = ({
  handleUserClicked,
  disabled = false,
  autoClearInputAfterUserClicked = true,
  autoCloseAfterUserClicked = true,
  style,
  size = 'small',
}: SearchUserProps) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [inputValue, setInputValue] = useState('');
  const [searchValue, setSearchValue] = useState('');
  const [lock, setLock] = useState<boolean>(false);
  const [isShowPannel, setIsShowPannel] = useState(false);
  const [isFocus, setIsFocus] = useState(false);
  const [resultList, setResultList] = useState<EmployeeItem[]>([]);
  const [hasMore, setHasMore] = useState(true);
  const [pageNo, setPageNo] = useState(1);
  const getSearchData = async (pageNo: number, val = searchValue) => {
    try {
      if (!val) {
        return;
      }
      setLoading(true);
      const scrollElement = document.getElementById('containerId');
      if (pageNo === 1 && scrollElement) {
        scrollElement.scrollTop = 0;
      }
      const { data } = await IMSDK.searchEmployeeListPage(
        val,
        (pageNo - 1) * pageSize,
        pageSize
      );

      if (data?.list?.length < pageSize) {
        setHasMore(false);
      } else {
        setHasMore(true);
      }
      setLoading(false);
      let result = data.list || [];
      if (pageNo !== 1) {
        // 从第二页数据为累加
        result = resultList.concat(data.list || []);
      }
      setResultList(result);
      setPageNo(pageNo);
    } catch (e) {
      console.error('搜索人员失败', e);
      setLoading(false);
      setResultList([]);
    }
  };
  const { run: runSearch } = useDebounceFn(
    (val) => {
      getSearchData(1, val);
    },
    {
      wait: 300,
    }
  );

  const handleScroll = (e: any) => {
    // 处理滚动加载【处理InfiniteScroll在弹窗中不生效导致无法滚动加载问题】
    const content: any = e.target;

    if (
      hasMore &&
      content.scrollHeight - (content.scrollTop + content.clientHeight) < 1
    ) {
      getSearchData(pageNo + 1);
    }
  };
  const clearValue = () => {
    setInputValue('');
    setSearchValue('');
    setResultList([]);
  };

  const onInputChange = (e: any) => {
    if (e.type === 'compositionstart') {
      setLock(true);
      return;
    }
    setInputValue(e.target.value.trim());
    if (e.type === 'compositionend') {
      setSearchValue(e.target.value.trim());
      setLock(false);
    }
    if (!lock) {
      setSearchValue(e.target.value.trim());
    }
    runSearch(e.target.value.trim());
  };

  return (
    <div
      className={classNames(
        styles.searchArea,
        isFocus && styles.searchAreaActived
      )}
      style={style}
    >
      <div className={styles.searchIcon}>
        <img src={searchIcon} />
      </div>
      <Input
        disabled={disabled}
        className={styles.search}
        bordered={false}
        placeholder="搜索人员/搜索机器人"
        onChange={onInputChange}
        onFocus={() => {
          setIsShowPannel(true);
          setIsFocus(true);
        }}
        onBlur={() => {
          setIsFocus(false);
        }}
        value={inputValue}
        onCompositionStart={onInputChange}
        onCompositionEnd={onInputChange}
        allowClear={{
          clearIcon: <img src={searchClearIcon} />,
        }}
      />

      <Tooltip>
        <div
          className={styles.resultWrapper}
          style={{
            height: 'fit-content',
            display: isShowPannel && inputValue ? 'block' : 'none',
          }}
        >
          <Spin
            wrapperClassName={styles.searchLoading}
            spinning={loading}
            tip="Loading"
            size="small"
            style={{ height: containerHeight }}
          >
            <div
              id={'containerId'}
              className={styles.resultPannel}
              style={{
                maxHeight: containerHeight,
              }}
              onScroll={(e: any) => handleScroll(e)}
            >
              <InfiniteScroll
                dataLength={resultList.length}
                next={() => getSearchData(pageNo + 1)}
                hasMore={hasMore}
                loader={<span />}
                scrollableTarget={'containerId'}
                scrollThreshold={0.95}
                style={{
                  // overflowY: 'auto',
                  // overflowX: 'hidden',
                  // maxHeight: '240px',
                  padding: '8px',
                  overflow: 'unset',
                }}
              >
                {resultList?.length > 0 ? (
                  <div>
                    {resultList?.map((result: EmployeeItem) => {
                      return (
                        <div
                          key={result.employeeCode}
                          className={classNames(
                            styles.searchedItem,
                            styles[`size-${size}`]
                          )}
                          onClick={() => {
                            if (autoCloseAfterUserClicked) {
                              setIsShowPannel(false);
                            }

                            if (autoClearInputAfterUserClicked) {
                              clearValue();
                            }
                            handleUserClicked(result);
                          }}
                        >
                          <OIMAvatar
                            userID={result.employeeID}
                            size={22}
                            borderRadius={4}
                          />
                          <div className={styles.name}>
                            {result.employeeName}
                          </div>
                          {isBotUser(result.employeeID) ? (
                            <div
                              className={classNames(styles.tag, styles.tag_3)}
                            >
                              机器人
                            </div>
                          ) : (
                            <div className={styles.code}>
                              {result.employeeCode}
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div
                    style={{ height: containerHeight }}
                    className={styles.empty}
                  >
                    <Empty description="没有找到联系人" />
                  </div>
                )}
              </InfiniteScroll>
            </div>
          </Spin>
        </div>
      </Tooltip>

      {isShowPannel && inputValue && (
        <div
          className={styles.mask}
          onClick={() => {
            setIsShowPannel(false);
            clearValue();
          }}
        ></div>
      )}
    </div>
  );
};

export default SearchUser;
