.searchArea {
  height: 32px;
  position: relative;
  border-radius: 6px;
  border: 1px solid var(--primary-background-color-5);
  width: 100%;
  display: flex;
  .searchIcon {
    height: 100%;
    margin-left: 17px;
    display: flex;
    align-items: center;
    img {
      width: 14px;
      height: 14px;
    }
  }

  :global {
    .linkflow-input-affix-wrapper {
      z-index: 3 !important;
    }
  }
}

.searchAreaActived {
  border: 1px solid #0074e2 !important;
  box-shadow: 0 0 0 2px #c2e1f1;
}

.resultWrapper {
  position: absolute;
  top: 120%;
  left: 0;
  z-index: 3;
  width: 100%;
  background: var(--primary-background-color-6);
  // overflow: auto;
  max-height: 100px;
}

.resultPannel {
  position: absolute;
  top: 120%;
  left: 0;
  z-index: 4;
  width: 100%;
  overflow-x: hidden;
  overflow-y: scroll;
  border-radius: 6px;
  border: 1px solid var(--primary-background-color-5);
  background-color: #fff;

  .resultItem,
  .simpleresultItem {
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;

    .infoWrapper {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: space-between;
      .avatar {
        width: 48px;
        height: 48px;
        margin-right: 12px;
        font-size: 12px;
        border-radius: 100%;
        transform: scale(0.85);

        img {
          width: 100%;
          height: 100%;
        }
      }

      .info {
        flex: 1;
        color: #999999;
        font-weight: 500;
        font-size: 12px;

        div {
          // width: 90%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        :first-child {
          color: #000000;
          font-weight: 500;
          font-size: 14px;
        }

        .creator {
          height: 17px;
          color: #999999;
          font-weight: 400;
          font-size: 12px;
        }
      }
    }

    .botton {
      display: flex;
      align-items: center;
      box-sizing: border-box;
      width: 100px;
      padding: 2px 8px;
      border: 1px solid #dee8f5;
      border-radius: 2px;

      img {
        width: 16px;
        height: 16px;
        margin-right: 4px;
      }

      span {
        flex: 1;
        color: #25396f;
        font-weight: 400;
        font-size: 14px;
        text-align: center;
      }
    }
  }

  .resultItem {
    padding: 19px 8px;
  }
  .simpleresultItem {
    height: 61px;
  }

  .resultItem:hover,
  .simpleresultItem:hover {
    background: #f5f8fb;
  }

  .simpleresultItem {
    padding: 7px 0 0;
  }

  .empty {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.searchedItem {
  color: var(--primary-text-color-1);
  cursor: pointer;
  font-weight: 600;
  font-size: 14px;
  height: 40px;
  display: flex;
  align-items: center;

  &:hover {
    background: var(--msg-qute-backgroud-color);
    border-radius: 6px;
  }

  &.size-middle {
    padding-top: 20px;
    padding-bottom: 20px;

    > div:first-of-type {
      margin-left: 10px;
    }

    .avatar {
      width: 20px;
      height: 20px;
      margin-right: 12px;
    }
    .name {
      margin-left: 12px;
      font-size: 14px;
      font-weight: 600;
      color: #000;
    }

    .code {
      margin-left: 8px;
      font-size: 14px;
      font-weight: 400;
      color: var(--primary-text-color-7);
    }

    &:hover {
      background: var(--msg-qute-backgroud-color);
      border-radius: 6px;

      .code {
        font-weight: 600;
      }
    }
  }

  &.size-small {
    .avatar {
      width: 20px;
      height: 20px;
    }
    .code {
      margin-left: 8px;
      font-size: 14px;
      font-weight: 400;
      color: var(--primary-text-color-7);
    }

    &:hover {
      .code {
        font-weight: 600;
      }
    }
    > div {
      margin-left: 8px;
    }
    > div:first-of-type {
      margin-left: 10px;
    }
  }

  .tag {
    padding: 0 4px;
    height: 16px;
    border-radius: 4px;
    font-size: 11px;
    display: flex;
    align-items: center;
    margin-left: 12px;
    line-height: 16px;
  }

  .tag_3 {
    color: #cc8521;
    background: #fcf3e6;
    border: 1px solid #cc8521;
  }
}

.mask {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 2;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.resultItem,
.simpleresultItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;

  .infoWrapper {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: space-between;
    .avatar {
      width: 48px;
      height: 48px;
      margin-right: 12px;
      font-size: 12px;
      border-radius: 100%;
      transform: scale(0.85);

      img {
        width: 100%;
        height: 100%;
      }
    }

    .info {
      flex: 1;
      color: #999999;
      font-weight: 500;
      font-size: 12px;

      div {
        // width: 90%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      :first-child {
        color: #000000;
        font-weight: 500;
        font-size: 14px;
      }

      .creator {
        height: 17px;
        color: #999999;
        font-weight: 400;
        font-size: 12px;
      }
    }
  }

  .botton {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    width: 100px;
    padding: 2px 8px;
    border: 1px solid #dee8f5;
    border-radius: 2px;

    img {
      width: 16px;
      height: 16px;
      margin-right: 4px;
    }

    span {
      flex: 1;
      color: #25396f;
      font-weight: 400;
      font-size: 14px;
      text-align: center;
    }
  }
}

.resultItem {
  padding: 19px 8px;
}
.simpleresultItem {
  height: 61px;
}

.resultItem:hover,
.simpleresultItem:hover {
  background: #f5f8fb;
}

.simpleresultItem {
  padding: 7px 0 0;
}
