.leftTreeWrapper {
  background: var(--primary-background-color-17);
  border-radius: 8px;
  height: 100%;
  margin-right: 10px;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0 8px;
}
:global {
  .linkflow-tree {
    background: transparent;
    .linkflow-tree-node-content-wrapper {
      color: #1d1d1d;
      line-height: 32px;
      border-radius: 4px;

      &:hover {
        background: rgba(107, 107, 108, 8%);
        border-radius: 6px;
        color: #000000;
      }

      cursor: pointer;
      height: 32px;
      z-index: 1;
      span {
        line-height: 28px;
      }
    }
  }

  .linkflow-tree-title {
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .linkflow-tree-switcher {
    background: transparent !important;
    left: 5px;
    line-height: 28px;
    &:hover {
      background: rgba(0, 76, 140, 13%);
      border-radius: 4px;
    }
  }
  .linkflow-tree-switcher_close,
  .linkflow-tree-switcher_open {
    z-index: 2;
  }

  .linkflow-tree-node-content-wrapper {
    margin-left: -24px !important;
    padding-left: 32px !important;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .linkflow-tree-treenode-selected {
    .linkflow-tree-switcher {
      background-color: var(--primary-background-color-6);
    }
  }

  .linkflow-tree-treenode {
    padding: 4px 0 !important;
  }

  .linkflow-tree-node-selected {
    background: var(--link-color-theme-base-hgl-1) !important;
    border-radius: 6px !important;
    color: var(--primary-background-color-7) !important;
    font-weight: 600;
    // margin-left: 0 !important;
  }

  .linkflow-tree-indent {
    > ::before {
      border-right: 2px solid var(--list-text-color-soft-1) !important;
    }
  }

  .linkflow-tree-show-line .linkflow-tree-indent-unit::before {
    right: 7px !important;
  }
  .linkflow-tree-show-line .linkflow-tree-indent-unit-start {
    &::before {
      border-right: 2px solid var(--list-text-color-soft-1);
    }
  }

  .linkflow-tree-switcher-leaf-line {
    &::before {
      border-right: 2px solid var(--list-text-color-soft-1);
    }

    &::after {
      border-bottom: 2px solid var(--list-text-color-soft-1);
    }
  }

  .linkflow-tree-switcher-noop {
    z-index: 2;
    background-color: var(--primary-background-color-16) !important;

    &::after {
      content: "";
      padding-left: 30px;
      width: 10px;
      z-index: 3;
      clip-path: path("M0, 0 H12 A6,6 0 0 0 6,6 V26 A6,6 0 0 0 12,32 H0 Z");
      position: absolute;
      height: 100%;
      margin-left: -6px;
      background-color: var(--primary-background-color-16) !important;
    }
    &::before {
      content: "";
      padding-left: 0;
      width: 7px;
      z-index: 3;
      position: absolute;
      height: 100%;
      margin-left: -6px;
      background-color: var(--primary-background-color-16) !important;
    }
  }
}

.loadingBox {
  height: 100%;
}
.selected {
  color: var(--primary-background-color-7);
}
.unselected {
  color: #122a59cc;
}
.resizeBox {
  width: 24%;
  .resizeBoxLine {
    width: 1px;
    height: 100%;
    background-color: var(--primary-background-color);
  }
}

.line {
  height: 1px;
  border: 1px solid var(--primary-background-color-5);
  margin: 10px 8px;
}

.botTitle,
.title {
  display: flex;
  align-items: center;
  padding: 17px 10px 9px;

  .icon {
    width: 20px;
    height: 20px;
    margin-right: 8px;
  }

  font-size: 18px;
  font-weight: 600;
  color: #000000;
  line-height: 26px;
}

.botTitle {
  cursor: pointer;
  padding: 4px 10px;
  margin: 15px 0;
  &:hover {
    background: rgba(0, 76, 140, 13%);
    border-radius: 6px;
    .index {
      display: block;
    }
  }
  &.selected {
    background: var(--link-color-theme-base-hgl-1) !important;
    border-radius: 6px !important;
    color: var(--primary-background-color-7) !important;

    .index {
      display: block;
    }
  }

  .index {
    display: none;
    width: 14px;
    height: 14px;
  }
}
