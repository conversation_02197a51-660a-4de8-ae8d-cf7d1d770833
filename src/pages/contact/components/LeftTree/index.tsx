import { useEffect } from 'react';
import { Tree, TreeProps } from '@ht/sprite-ui';
import withScrollBar from '@/components/withScrollBar';
import { DataNode, EventDataNode } from '@ht/sprite-ui/lib/tree';
import useDeparmentInfo from '@/hooks/useDeparmentInfo';
import skeleton from '@/assets/contact/skeleton.svg';
import treeIcon from '@/assets/contact/treeIcon.svg';
import botIcon from '@/assets/contact/botIcon.svg';
import indexIcon from '@/assets/contact/indexIcon.png';
import classNames from 'classnames';
import styles from './index.less';
import { DepartmentTreeItem } from './util';
import { selectDepartmentProps } from '../..';

interface LeftTreeProps {
  selectedDepartmentID: selectDepartmentProps;
  handleDepartmentClicked: TreeProps['onSelect'];
  defaultSelectedDepartmentCode: string;
  clearSelectedDepartment: () => void;
  showBot: boolean;
  showSkeleton: boolean;
  setShowBot: (val: boolean) => void;
}

const LeftTree = ({
  handleDepartmentClicked,
  selectedDepartmentID,
  defaultSelectedDepartmentCode,
  clearSelectedDepartment,
  showBot,
  showSkeleton,
  setShowBot,
}: LeftTreeProps) => {
  const { departmentTreeData } = useDeparmentInfo();

  const treeData = departmentTreeData.filter((item) => item.key !== 'SZYG');

  useEffect(() => {
    if (
      selectedDepartmentID == null &&
      treeData != null &&
      defaultSelectedDepartmentCode
    ) {
      // 递归查找节点
      const findNodeByKey = (
        tree: DepartmentTreeItem[],
        key: string
      ): DepartmentTreeItem | undefined => {
        for (const node of tree) {
          if (node.key === key || node.data.departmentID === key) {
            return node; // 找到对应的节点
          }
          // 如果节点有子节点，递归查找子节点
          if (node.children && node.children.length > 0) {
            const childNode = findNodeByKey(node.children, key);
            if (childNode) {
              return childNode; // 在子节点中找到的节点
            }
          }
        }
        return undefined; // 如果没有找到，返回undefined
      };

      const node = findNodeByKey(treeData, defaultSelectedDepartmentCode);

      if (node != null && !showBot) {
        const info: {
          event: 'select'; // 强制指定event的类型为 "select"
          selected: boolean; // 选中状态
          node: EventDataNode<DataNode>; // 节点信息
          selectedNodes: DataNode[]; // 选中的节点数组
          nativeEvent: MouseEvent; // 原生鼠标事件
        } = {
          event: 'select', // 事件类型
          selected: true, // 选中状态
          node, // 当前节点
          selectedNodes: [node], // 选中的节点数组
          nativeEvent: new MouseEvent('click'), // 模拟的鼠标点击事件
        };
        if (handleDepartmentClicked) {
          handleDepartmentClicked([defaultSelectedDepartmentCode], info);
        }
      }
    }
  }, []);

  return (
    <div className={styles.leftTreeWrapper}>
      <div className={styles.title}>
        <img className={styles.icon} src={treeIcon}></img>
        <span>组织架构树</span>
      </div>

      {showSkeleton ? (
        <div
          className={styles.loadingBox}
          style={{
            backgroundImage: `url(${skeleton})`,
            backgroundRepeat: 'no-repeat',
            height: 'calc(100vh - 125px)',
            backgroundSize: '80% auto',
          }}
        ></div>
      ) : (
        <>
          <Tree
            blockNode={true}
            switcherIcon={(props: any) => (
              <svg
                width="8px"
                height="8px"
                viewBox="0 0 8 8"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  className={
                    props?.selected ? styles.selected : styles.unSelected
                  }
                  transform={
                    props?.expanded
                      ? 'rotate(0) translate(0,1.5)'
                      : 'rotate(-90,4,4) translate(0,1.5)'
                  }
                  d="M3.7316662,0 L0.26833377,0 C0.04447925,0 0.91948434,0.2452975 0.05811506,0.4127602 L3.78978129,4.903591 C3.89659512,5.0321363 4.1022686,5.0321363 4.2102187,4.903591 L7.9418849,0.4127602 C8.0805157,0.2452975 7.9555208,0 7.7316662,0 Z"
                  fill="currentColor"
                />
              </svg>
            )}
            showLine={{
              showLeafIcon: false,
            }}
            showIcon={false}
            treeData={treeData}
            onSelect={(selectedKeys, info) => {
              setShowBot(false);
              if (handleDepartmentClicked != null) {
                handleDepartmentClicked(selectedKeys, info);
              }
            }}
            selectedKeys={[selectedDepartmentID?.key]}
            icon={null}
            defaultExpandedKeys={[defaultSelectedDepartmentCode]}
          />

          <div className={styles.line}></div>
          <div
            className={classNames(
              styles.botTitle,
              showBot ? styles.selected : ''
            )}
            onClick={() => {
              clearSelectedDepartment();
              setShowBot(true);
            }}
          >
            <img src={botIcon} className={styles.icon}></img>
            <span style={{ flex: 1 }}>机器人</span>
            <img src={indexIcon} className={styles.index}></img>
          </div>
        </>
      )}
    </div>
  );
};

export default withScrollBar(LeftTree, {
  domId: 'contact-drag-scroll-bar-left',
  defaultWidth: '378px',
  minWidth: 294,
  maxWidth: 930,
  dragBarClassName: 'scrollbarLeft',
});
