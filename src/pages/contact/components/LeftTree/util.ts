import { DepartmentItem } from '@ht/openim-wasm-client-sdk';

export interface DepartmentTreeItem {
  key: string;
  title: string;
  data: DepartmentItem; // 包含原始的部门数据
  children: DepartmentTreeItem[]; // 子节点数组
  namePath: { departmentID: string; departmentName: string }[]; // 部门ID和名称的路径
}

// sortNum字段目前没生效，先按出现的顺序排列了，等田甲补充调整
export const buildTree = (
  departments: DepartmentItem[]
): DepartmentTreeItem[] => {
  const departmentMap = new Map<string, DepartmentTreeItem>();
  const tree: DepartmentTreeItem[] = [];

  // 第一步：创建所有节点
  departments.forEach((dept) => {
    departmentMap.set(dept.departmentID, {
      key: dept.departmentID,
      title: dept.departmentName,
      data: dept,
      children: [],
      namePath: [],
    });
  });

  // 第二步：构建父子关系
  departments.forEach((dept) => {
    const { parentID } = dept;
    const currentNode = departmentMap.get(dept.departmentID)!;

    if (parentID && departmentMap.has(parentID)) {
      const parentNode = departmentMap.get(parentID)!;
      currentNode.namePath = [
        ...parentNode.namePath,
        { departmentID: currentNode.key, departmentName: currentNode.title },
      ];
      parentNode.children.push(currentNode); // 先收集所有子节点
    } else if (!parentID) {
      currentNode.namePath = [
        { departmentID: currentNode.key, departmentName: currentNode.title },
      ];
      tree.push(currentNode);
    }
  });

  // 第三步：递归排序所有节点的子节点
  const sortChildren = (nodes: DepartmentTreeItem[]) => {
    nodes.forEach((node) => {
      node.children.sort((a, b) => a.data.sortNum - b.data.sortNum); // 升序排列
      sortChildren(node.children); // 递归排序子节点的子节点
    });
  };

  // 对根节点排序，然后递归排序所有子节点
  tree.sort((a, b) => a.data.sortNum - b.data.sortNum);
  sortChildren(tree);

  return tree;
};
