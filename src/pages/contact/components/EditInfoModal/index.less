.modalContainer {
  :global {
    .linkflow-modal-content {
      background: var(--primary-background-color-6);
      border-radius: 16px;
      height: 820px;
    }

    .linkflow-modal-body {
      padding: 0;
      margin: 0;
    }
  }
}
.container {
  background-color: var(--primary-background-color-6);
  padding: 22px 0 24px 28px;
  height: 840px;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .title {
      width: 176px;
      height: 30px;
      font-size: 22px;
      font-weight: 600;
      color: var(--link-color-content-pry);
      line-height: 30px;
    }
    img {
      width: 18px;
      cursor: pointer;
      margin-right: 32px;
    }

    margin-bottom: 19px;
  }
}

.content {
  display: flex;
  flex-direction: row;
  height: 100%;
  width: 100%;
  padding-right: 28px;
  overflow-y: auto;
  overflow-x: hidden;
}

.label {
  margin-bottom: 16px;
  width: 100%;
  height: 22px;
  font-size: 16px;
  font-weight: 600;
  color: var(--link-color-content-pry);
  line-height: 22px;
}
.info {
  margin-top: 12px;
  margin-bottom: 24px;
}

.left {
  position: relative;
  .nameArea {
    display: flex;
    flex-direction: column;

    > div {
      display: flex;
      align-items: center;
      margin-top: 12px;
      padding: 0 16px;
      width: 420px;
      height: 44px;
      background: var(--primary-background-color-6);
      border-radius: 8px;
      border: 1px solid var(--offline-border-color);
      > span {
        height: 22px;
        font-size: 16px;
        font-weight: 400;
        color: var(--link-color-content-pry);
        line-height: 22px;
      }
    }

    :global {
      .linkflow-radio {
        margin-top: -6px;
        margin-right: 8px;
      }

      .linkflow-radio-wrapper {
        margin-right: 48px;
      }
    }
  }
}

.right {
  display: flex;
  flex-direction: column;
  padding-left: 32px;

  .imgContainer {
    height: 192px;
    width: 192px;
  }
  .uploadBtn {
    margin-top: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    width: 192px;
    height: 36px;
    background: var(--primary-background-color-6);
    border-radius: 8px;
    border: 1px solid var(--offline-border-color);
    font-size: 16px;
    font-weight: 600;
    color: var(--link-color-content-pry);
    line-height: 22px;
  }
}

.footer {
  padding-right: 32px;
  display: flex;
  flex-direction: row-reverse;
  .footerBtn {
    cursor: pointer;
    width: 80px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: var(--link-color-content-pry);
    font-weight: 500;
    line-height: 22px;
    margin-left: 16px;
    border-radius: 6px;
    border: 1px solid var(--offline-border-color);
    background: var(--primary-background-color-6);

    // &:nth-child(1) {
    //   width: 160px;
    //   background: #2e629e;
    //   border-radius: 6px;
    //   color: #fff;
    // }
  }
}
