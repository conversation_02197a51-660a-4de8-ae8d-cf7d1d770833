import { useGlobalModalStore, useUserStore } from '@/store';
import messageIcon from '@/assets/contact/messageIcon.png';
import mailIcon from '@/assets/contact/mailIcon.png';
import phoneIcon from '@/assets/contact/phoneIcon.png';
import { useConversationToggle } from '@/hooks/useConversationToggle';
import { SessionType } from '@ht/openim-wasm-client-sdk';
import { getAvatarUrl, getRandomDefaultAvatar } from '@/utils/avatar';
import closeIcon from '@/assets/closeIcon.svg';
import clearStateIcon from '@/assets/contact/clearStateIcon.png';
import { Popover, Typography } from '@ht/sprite-ui';
import { IMSDK } from '@/layouts/BasicLayout';
import {
  getShowDescByStatus,
  getStatusImgSrcByStatus,
} from '@/components/UserState/SetStateModal/const';
import useUserInfo from '@/hooks/useUserInfo';
import RightAreaBackBtn from '@/components/RightAreaBackBtn';
import styles from './index.less';
import OnlineOrTypingStatus from '../OnlineOrTypingStatus';
import EditInfoModal from '../EditInfoModal';

interface UserDetailProps {
  userID: string;
  handleSelectedUserClear: () => void;
}

const UserDetail = ({ userID, handleSelectedUserClear }: UserDetailProps) => {
  const { selfInfo } = useUserStore();
  const { userDetail, userState, userIsBot } = useUserInfo(userID);
  const { selfStatus, updateSelfState, clearSelfState } = useUserStore();

  const { isEditModalOpen, setStatusModalOpen, setEditModalOpen } =
    useGlobalModalStore();

  const { toSpecifiedConversation } = useConversationToggle();
  const handleCreateConversation = () => {
    toSpecifiedConversation({
      sourceID: userID ?? '',
      sessionType: SessionType.Single,
    });
  };

  const handleClearStatus = async () => {
    const lastSelfStatus = selfStatus;
    clearSelfState();
    try {
      await IMSDK.setSelfState({});
    } catch (e) {
      console.error('清除状态失败', e);
      updateSelfState(JSON.stringify(lastSelfStatus));
    }
  };

  return (
    <div className={styles.personDetail}>
      <div className={styles.personDetailTitle}>
        <RightAreaBackBtn />
        <span>个人档案</span>
        <img
          src={closeIcon}
          className={styles.deleteIcon}
          onClick={() => handleSelectedUserClear()}
        ></img>
      </div>

      <div className={styles.personDetailContent}>
        <div className={styles.avatarContainer}>
          <div className={styles.imgConainer}>
            <img
              src={
                userID === selfInfo.userID
                  ? selfInfo.faceURL
                  : getAvatarUrl(userID)
              }
              onLoad={(e) => {
                const target = e.target as HTMLImageElement;
                setTimeout(() => (target.style.opacity = '1'), 0);
              }}
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = getRandomDefaultAvatar(userID);
              }}
              decoding="async" // 异步解码
              loading="lazy" // 延迟加载（非可视区图片）
              alt=""
            />
          </div>
        </div>

        <div className={styles.name}>
          <span>{`${userDetail?.nickname || '--'} ${
            !userIsBot ? userDetail?.employeeCode || '--' : ''
          }`}</span>
          {selfInfo.userID === userDetail?.userID && (
            <span
              onClick={() => {
                setEditModalOpen(true);
              }}
            >
              编辑
            </span>
          )}
        </div>

        {/* 类型报错先忽略，SDK没设置对 */}
        {userDetail?.positionInfos?.map((item) => {
          return (
            <div
              className={styles.position}
              key={item?.departmentName || `${item?.positionName}` || ''}
            >
              <div className={styles.left}>{item?.departmentName}</div>
              <div className={styles.right}>{item.positionName}</div>
            </div>
          );
        })}

        <OnlineOrTypingStatus
          userID={userID}
          showText={true}
          size={10}
          style={{
            height: 22,
            lineHeight: 22,
            marginTop: 16,
            marginLeft: 5,
          }}
          textStyle={{ marginLeft: '12px' }}
        />

        {(userState?.code || userState?.desc) && (
          <div className={styles.state}>
            <div className={styles.left}>
              <img src={getStatusImgSrcByStatus(userState)}></img>
            </div>
            <div className={styles.right}>{getShowDescByStatus(userState)}</div>
            {selfInfo?.userID === userID && (
              <Popover
                placement="left"
                content={<span style={{ color: '#fff' }}>清除状态</span>}
                overlayClassName={styles.clearBtnPopover}
                overlayInnerStyle={{ backgroundColor: 'black', padding: '0' }}
              >
                <img
                  src={clearStateIcon}
                  onClick={() => handleClearStatus()}
                  className={styles.clearStateIcon}
                ></img>
              </Popover>
            )}
          </div>
        )}

        <div className={styles.btns}>
          {selfInfo.userID === userID ? (
            <div
              className={styles.btn}
              onClick={() => {
                setStatusModalOpen(true);
              }}
            >
              <span>编辑状态</span>
            </div>
          ) : (
            <div
              className={styles.btn}
              onClick={() => handleCreateConversation()}
            >
              <img src={messageIcon} />
              <span>发送消息</span>
            </div>
          )}
        </div>
      </div>

      {!userIsBot && (
        <div className={styles.personDetailFooter}>
          {/* 联系信息 */}
          <div className={styles.footerTitle}>联系信息</div>
          <div className={styles.connectInfo}>
            <div className={styles.mailContainer}>
              <div className={styles.mailIcon}>
                <img src={mailIcon}></img>
              </div>
              <div className={styles.mailDesc}>
                <div style={{ marginRight: '8px' }}>电子邮箱地址</div>
                <Typography.Text
                  style={{
                    color: 'var(--primary-text-color-9)',
                    cursor: 'pointer',
                  }}
                  ellipsis={true}
                >
                  {`${JSON.parse(userDetail?.ex || '{}')?.mail || '无'}`}
                </Typography.Text>
              </div>
            </div>
            <div className={styles.mailContainer}>
              <div className={styles.mailIcon}>
                <img src={phoneIcon}></img>
              </div>
              <div className={styles.mailDesc}>
                <div style={{ marginRight: '8px' }}>手机号</div>
                <Typography.Text
                  style={{
                    color: 'var(--primary-text-color-9)',
                    cursor: 'pointer',
                  }}
                  ellipsis={true}
                >
                  {JSON.parse(userDetail?.ex || '{}')?.mobile || '无'}
                </Typography.Text>
              </div>
            </div>
          </div>
        </div>
      )}

      {isEditModalOpen && (
        <EditInfoModal
          onClose={() => {
            setEditModalOpen(false);
          }}
        />
      )}
    </div>
  );
};

export default UserDetail;
