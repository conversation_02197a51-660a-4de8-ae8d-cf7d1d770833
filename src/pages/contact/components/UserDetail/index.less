.personDetail {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: hidden;
  background: var(--primary-background-color-17);
  border-radius: 8px;

  .personDetailTitle {
    display: flex;
    align-items: center;
    padding: 16px 16px 0 20px;
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-text-color-1);
    line-height: 28px;

    .rightAreaBack {
      margin-right: 6px;
      cursor: pointer;
      &:hover {
        border-radius: 4px;
        background: var(--msg-qute-backgroud-color);
      }
    }

    span {
      flex: 1;
    }

    .deleteIcon {
      cursor: pointer;

      &:hover {
        border-radius: 4px;
        background: var(--msg-qute-backgroud-color);
      }
    }
  }

  .personDetailContent {
    padding: 24px 11px 0 20px;
    align-items: center;
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 400;
    color: var(--primary-text-color-1);

    .avatarContainer {
      display: flex;
      flex-direction: column;
      justify-content: center;
      width: 100%;
      max-width: 288px;
      margin-left: auto;
      margin-right: auto;
      padding: 0 16px;

      .imgConainer {
        // background-color: rgb(221, 221, 221);
        border-radius: 12px;
        width: 100%;
        height: 0;
        padding-top: 100%;
        position: relative;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          position: absolute;
          object-fit: "cover";
          top: 0;
          left: 0;
        }
      }
    }

    .name {
      display: flex;
      align-items: center;
      font-size: 22px;
      font-weight: 600;
      color: var(--primary-text-color-1);
      line-height: 30px;
      margin-top: 26px;

      span:nth-child(1) {
        flex: 1;
      }

      span:nth-child(2) {
        cursor: pointer;
        font-size: 15px;
        font-weight: 600;
        color: #0074e2;
        line-height: 22px;
        margin-right: 9px;
      }
    }

    .position {
      display: flex;
      align-items: center;
      line-height: 22px;
      margin-top: 8px;
      font-size: 15px;
      font-weight: 400;

      .left {
        color: var(--primary-text-color-1);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .right {
        margin-left: 12px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: var(--primary-text-color-3);
      }
    }

    .state {
      margin-top: 10px;
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 400;
      color: var(--primary-text-color-3);

      .left {
        display: flex;
        width: 18px;
        align-items: center;

        img {
          width: 100%;
        }
      }

      .right {
        flex: 1;
        margin-left: 9px;
        font-size: 16px;
        font-weight: 400;
        color: #1d1c1d;
        line-height: 22px;
      }

      .clearStateIcon {
        width: 32px;
        height: 32px;
        cursor: pointer;
        padding: 9px;

        &:hover {
          background: rgba(107, 107, 108, 8%);
          border-radius: 6px;
        }
      }
    }

    .clockIcon {
      width: 16px;
      height: 16px;
      margin-left: -3px;
      margin-right: 13px;
    }

    .btns {
      margin-top: 16px;
      display: flex;

      .btn {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 36px;
        min-width: 48px;
        max-width: 220px;
        // margin-right: 20px;
        border-radius: 8px;
        border: 1px solid var(--primary-border-color-2);
        color: var(--primary-text-color-1);
        cursor: pointer;
        width: 55%;
        background-color: var(--primary-background-color-6);
        font-weight: 500;

        span {
          // color: var(--primary-background-color-6);
          color: var(--primary-text-color-1);
          line-height: 32px;
        }

        > img {
          width: 20px;
          height: 20px;
          margin-right: 4px;
        }
      }
    }
  }

  .personDetailFooter {
    border-top: 1px solid var(--primary-background-color-5);
    padding: 16px 24px;

    .footerTitle {
      font-size: 16px;
      font-weight: 600;
      color: var(--primary-text-color-1);
      margin-bottom: 14px;
      line-height: 22px;
    }

    .connectInfo {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
    }

    .mailContainer {
      flex: 1;
      display: flex;
      font-size: 15px;
      font-weight: 500;
      color: var(--primary-text-color-1);
      line-height: 22px;
      max-width: 100%;

      .mailIcon {
        width: 40px;
        height: 40px;
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 16px;
        background: var(--msg-qute-backgroud-color);
      }

      .mailDesc {
        flex: 1;
        overflow: hidden;
      }
    }
  }
}

.clearBtnPopover {
  // background: black;
  color: #fff;

  :global {
    .linkflow-popover-arrow-content::before {
      background: black;
    }
  }
}
