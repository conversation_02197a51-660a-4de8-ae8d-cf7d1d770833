.browserWinWrapper {
  width: 100%;
  height: 100%;
  background-color: var(--primary-background-color-6);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid var(--primary-border-color-2);

  .browserWinHeader {
    display: flex;
    align-items: center;
    -webkit-app-region: drag;
    height: 44px;

    .title {
      flex: 1;
      text-align: center;
      -webkit-app-region: drag;
      cursor: default;
    }
  }

  .browserWinContent {
    flex: 1;
    overflow: hidden;
  }
}
