import { useEffect, useState, useRef } from 'react';
import { message as Message } from '@ht/sprite-ui';
import {
  DownloadOutlined,
  ReloadOutlined,
  VerticalAlignMiddleOutlined,
} from '@ht-icons/sprite-ui-react';
import resetIcon from '@/assets/channel/messageRender/reset.png';
import rotateIcon from '@/assets/channel/messageRender/rotate.png';
import styles from './index.less';

const ImagePreviewWin = ({ params }) => {
  const browserWindow = window?.htElectronSDK?.BrowserWindow;
  const { filepath } = params || {};
  const [file, setFile] = useState<string>('');

  const imgRef = useRef<HTMLDivElement>(null);
  const [scaleNumber, setScaleNumber] = useState<number>(1);
  const [rotateNum, setRotateNum] = useState<number>(0);
  const [isDrag, setIsDrag] = useState<boolean>(false);
  const [bounds, setBounds] = useState({
    left: 0,
    top: 0,
  });
  const [move, setMove] = useState({
    left: 0,
    top: 0,
  });
  const [size, setSize] = useState({
    width: 0,
    height: 0,
  });

  const getFileData = async (url: string) => {
    if (!url) {
      return;
    }
    const arrayBuffer = await browserWindow?.readFileLocal(url);
    const idx = url.lastIndexOf('.');
    const imageType = url.slice(idx + 1);
    if (arrayBuffer) {
      const blob = new Blob([arrayBuffer], { type: `image/${imageType}` });
      const blobUrl = URL.createObjectURL(blob);
      setFile(blobUrl);
    }
  };

  useEffect(() => {
    getFileData(filepath);
    // 监听主进程发送的图片更新事件
    browserWindow?.onUpdateImage((url: string) => {
      getFileData(url);
    });
  }, []);

  useEffect(() => {
    reset();
    document.addEventListener('wheel', wheelChange);
    return () => {
      document.removeEventListener('wheel', wheelChange);
    };
  }, [file]);

  const wheelChange = (event: any) => {
    let delta = 1;
    if (event.deltaY > 0) {
      delta = -1;
    }
    const zoomFactor = 0.1;
    setScaleNumber((pre) => {
      return Math.max(0.15, Math.min(pre + delta * (pre * zoomFactor), 10));
    });
  };

  // 鼠标按下
  const onMouseDown = (event: any) => {
    if (imgRef.current) {
      const startX = event.pageX - imgRef.current.offsetLeft;
      const startY = event.pageY - imgRef.current.offsetTop;
      setBounds({
        left: startX,
        top: startY,
      });
      setIsDrag(true);
    }
  };

  // 鼠标松开
  const onMouseUp = (e: any) => {
    setIsDrag(false);
  };
  // 鼠标移出
  const mouseOut = () => {
    setIsDrag(false);
  };

  // 鼠标移动
  const mouseMove = (event: any) => {
    if (isDrag) {
      const x = event.pageX - bounds.left;
      const y = event.pageY - bounds.top;
      setMove({
        left: x,
        top: y,
      });
    }
  };
  const reset = () => {
    setScaleNumber(1);
    setRotateNum(0);
    setBounds({
      left: 0,
      top: 0,
    });
    setMove({
      left: 0,
      top: 0,
    });
  };

  const rotateChange = () => {
    setRotateNum((pre) => {
      return pre + 90 > 360 ? 90 : pre + 90;
    });
  };

  const download = (url: string, name: string) => {
    if (file) {
      try {
        const link = document.createElement('a');
        link.href = file;
        link.download = `clipbord_${new Date().getTime()}.png`;
        document.body.appendChild(link);
        link.click();
      } catch (error) {
        Message.error('下载失败');
      }
    }
  };

  return (
    <div className={styles.imagePreviewWinWrapper}>
      {file && (
        <div className={styles.imgWrapper} onMouseMove={mouseMove}>
          <img
            src={file}
            ref={imgRef}
            onMouseDown={onMouseDown}
            onMouseUp={onMouseUp}
            onMouseLeave={mouseOut}
            onDoubleClick={reset}
            style={{
              transform: `scale(${scaleNumber}) rotate(${rotateNum}deg)`,
              left: move.left,
              top: move.top,
              right: move.left === 0 ? 0 : 'auto',
              bottom: move.top === 0 ? 0 : 'auto',
              margin: 'auto',
            }}
            draggable={false}
          />
        </div>
      )}
      <div className={styles.imgBottomWrapper}>
        <div onClick={download} className={styles.bottomBtn} title="下载">
          <DownloadOutlined
            style={{
              color: '#000',
              fontSize: '16px',
            }}
          />
        </div>
        <div className={styles.bottomBtn} onClick={reset} title="重制">
          <VerticalAlignMiddleOutlined
            style={{
              color: '#000',
              fontSize: '16px',
            }}
          />
        </div>
        <div className={styles.bottomBtn} onClick={rotateChange} title="旋转">
          <ReloadOutlined
            style={{
              color: '#000',
              fontSize: '16px',
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default ImagePreviewWin;
