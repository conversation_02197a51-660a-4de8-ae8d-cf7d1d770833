.imagePreviewWinWrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  justify-content: space-between;

  .imgWrapper {
    flex: 1;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-background-color-17);
    overflow: hidden;
    position: relative;

    > img {
      cursor: grab;
      user-select: none;
      position: absolute;
      max-width: 100%;
      max-height: 100%;
    }
  }

  .imgBottomWrapper {
    background: #fff;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;

    .bottomBtn {
      display: inline-flex;
      justify-content: center;
      align-items: center;
      user-select: none;
      -webkit-user-select: none;
      width: 28px;
      height: 28px;
      cursor: pointer;
      border-radius: 6px;
      transition: background-color 0.3s ease;
      margin-right: 12px;
      -webkit-app-region: no-drag;
    }

    .bottomBtn:hover {
      background: rgba(107, 107, 108, 8%);
    }
  }
}
