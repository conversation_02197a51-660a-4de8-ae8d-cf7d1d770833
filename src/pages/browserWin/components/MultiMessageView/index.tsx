import MultiMessageContent from '@/components/Channel/components/MultiMessageModal/MultiMessageContent';
import classNames from 'classnames';
import { ConfigProvider } from '@ht/sprite-ui';
import zhCN from '@ht/sprite-ui/lib/locale/zh_CN';
import BaseSettings from '../../../../../config/BaseSettings';

const MultiMessageView = ({ params }) => {
  const { contentParam } = params;
  const browserWindow = window?.htElectronSDK?.BrowserWindow;
  const currentWindowId = browserWindow?.getCurrentWindowId() || 1;
  return (
    <ConfigProvider prefixCls={BaseSettings.appName} locale={zhCN}>
      <div className={classNames('linkflow-theme--light')} id="BasicLayoutId">
        <MultiMessageContent
          oncancel={() => {
            browserWindow?.close(currentWindowId);
          }}
          hasForwardBtn={false}
          {...contentParam}
        />
      </div>
    </ConfigProvider>
  );
};

export default MultiMessageView;
