/*
 * @Author: 015313 <EMAIL>
 * @Date: 2025-02-13 15:31:28
 * @LastEditors: ypt
 * @LastEditTime: 2025-03-12 11:24:32
 * @FilePath: /linkflow/src/pages/mdDemo/index.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React from 'react';
import { EditMd, RenderMd } from '@/components/MdEditor';
import LcRender from '@/components/LcRender';
import { Button } from '@ht/sprite-ui';
import { ProsemirrorAdapterProvider } from '@prosemirror-adapter/react';
import { EditorProvider } from '@/components/MdEditor/editorContent';

const MdDemo = () => {
  const [mdtext, setMdtext] = React.useState('');

  const list = [
    {
      type: 'md',
      content: `## Md 自定义消息
  Main articles: [连接](http://pt.htsc/paas/index.html#/hostIndex)
  > 引用

  **加粗**
  - 列表
  - 列表`,
    },
    {
      type: 'lc',
      content: {
        name: '首页',
        target:
          'http://lowcode.fe.htsc/app/16a3f69b4d474e935620/editor?pageId=1e42cc566c9b759d6532',
        extendprops: {
          options: [
            {
              label: '菜单 1',
              value: '1',
            },
            {
              label: '菜单 2',
              value: '2',
            },
            {
              label: '菜单 3',
              value: '3',
            },
          ],
        },
      },
    },
  ];

  return (
    <div style={{ background: '#fff', padding: '20px' }}>
      <div>内容1</div>
      <EditorProvider>
        <ProsemirrorAdapterProvider>
          <EditMd
            onChange={(v) => {
              console.log(v);
              setMdtext(null);
              setTimeout(() => {
                setMdtext(v);
              }, 200);
            }}
            onBlur={(ctx) => {
              console.log(ctx);
            }}
            onFocus={(ctx) => {
              console.log(ctx);
            }}
            defaultValue={''}
            groupMemberList={[
              {
                groupID: '**********',
                userID: '021961',
                nickname: '刘雨晴',
                faceURL: 'https://example.com/avatar1.jpg',
                roleLevel: 100,
                joinTime: 1738926392940,
                joinSource: 2,
                inviterUserID: '021961',
                muteEndTime: 0,
                operatorUserID: '021961',
                ex: '',
                attachedInfo: '',
              },
              {
                groupID: '**********',
                userID: '011114',
                nickname: '曹啸',
                faceURL: 'http://eipsit.htsc.com.cn/object/011114/touxiang.jpg',
                roleLevel: 20,
                joinTime: 1738926392940,
                joinSource: 2,
                inviterUserID: '021961',
                muteEndTime: 0,
                operatorUserID: '021961',
                ex: '',
                attachedInfo: '',
              },
              {
                groupID: '**********',
                userID: '015269',
                nickname: '朱昂昂',
                faceURL: '/object/015269/touxiang2.jpeg',
                roleLevel: 20,
                joinTime: 1740120376154,
                joinSource: 2,
                inviterUserID: '021961',
                muteEndTime: 0,
                operatorUserID: '021961',
                ex: '',
                attachedInfo: '',
              },
              {
                groupID: '**********',
                userID: '015313',
                nickname: '杨鹏涛',
                faceURL: 'https://example.com/avatar1.jpg',
                roleLevel: 20,
                joinTime: 1738926392940,
                joinSource: 2,
                inviterUserID: '021961',
                muteEndTime: 0,
                operatorUserID: '021961',
                ex: '',
                attachedInfo: '',
              },
              {
                groupID: '**********',
                userID: '015754',
                nickname: '张辰',
                faceURL:
                  'http://eipsit.htsc.com.cn/object/015754/9227c348582c96a25ce3aa8b70b34ada.jpg',
                roleLevel: 20,
                joinTime: 1738926392940,
                joinSource: 2,
                inviterUserID: '021961',
                muteEndTime: 0,
                operatorUserID: '021961',
                ex: '',
                attachedInfo: '',
              },
              {
                groupID: '**********',
                userID: '016328',
                nickname: '刘田甲',
                faceURL: 'https://example.com/avatar1.jpg',
                roleLevel: 20,
                joinTime: 1740120034829,
                joinSource: 2,
                inviterUserID: '021961',
                muteEndTime: 0,
                operatorUserID: '021961',
                ex: '',
                attachedInfo: '',
              },
            ]}
            onPaste={(view, event) => {
              const { clipboardData } = event;
              if (!clipboardData) {
                return false;
              }
              // 获取剪贴板中的文本
              const text = clipboardData.getData('text/html');
              // 获取剪贴板中的文件
              const { files } = clipboardData;
              console.log('paste:', text, files);
              return false;
            }}
            onMentionRemove={(node) => console.log('mentionremove', node)}
          />
        </ProsemirrorAdapterProvider>
      </EditorProvider>
      <Button
        onClick={() => {
          setMdtext(`## Md 自定义消息
  Main articles: [连接](http://pt.htsc/paas/index.html#/hostIndex)
  > 引用

  **加粗**
  - 列表
  - 列表`);
        }}
      >
        更换富文本内容
      </Button>
      {mdtext ? <RenderMd id="tt" value={mdtext} /> : null}
      {/* <LcRender
        {...LcProps}
        callback={({ eventID, props }: { eventID: string; props: any }) => {
          console.log('触犯了 im 的函数', eventID, props);
        }}
        isDev={!location.hostname.startsWith('eip.htsc.com.cn')}
      /> */}
    </div>
  );
};

export default MdDemo;
