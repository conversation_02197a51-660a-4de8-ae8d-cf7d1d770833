import React, { useEffect, useState, useMemo } from 'react';
import { <PERSON><PERSON>, Card, Divider, Space, Typography } from '@ht/sprite-ui';
import { create } from 'zustand';
import { shallow } from 'zustand/shallow';

// 定义 store 的类型
interface TestStore {
  items: number[];
  userInfo: {
    profile: {
      name: string;
      age: number;
      hobbies: string[];
    };
    settings: {
      theme: string;
      notifications: boolean;
    };
  };
  conversations: Array<{
    id: number;
    title: string;
    unread: number;
  }>;
  updateArrayWithNewReference: () => void;
  updateArrayWithNewContent: () => void;
  updateObjectWithNewReference: () => void;
  updateObjectWithNewContent: () => void;
  updateConversationsWithNewReference: () => void;
  updateConversationsWithNewContent: () => void;
  conditionalUpdate: (newItems: number[]) => void;
}

// 创建一个复杂的 store 来验证引用变化的影响
const useTestStore = create<TestStore>((set, get) => ({
  // 简单数组
  items: [1, 2, 3],

  // 多层级对象
  userInfo: {
    profile: {
      name: '<PERSON>',
      age: 25,
      hobbies: ['reading', 'coding'],
    },
    settings: {
      theme: 'dark',
      notifications: true,
    },
  },

  // 对象数组
  conversations: [
    { id: 1, title: 'Chat 1', unread: 0 },
    { id: 2, title: 'Chat 2', unread: 2 },
  ],

  // 更新方法1：创建新引用但内容相同（数组）
  updateArrayWithNewReference: () =>
    set((state: TestStore) => ({
      items: [...state.items],
    })),

  // 更新方法2：创建新引用且内容不同（数组）
  updateArrayWithNewContent: () =>
    set((state: TestStore) => ({
      items: [...state.items, Math.floor(Math.random() * 100)],
    })),

  // 更新方法3：更新多层级对象，创建新引用但内容相同
  updateObjectWithNewReference: () =>
    set((state: TestStore) => ({
      userInfo: {
        ...state.userInfo,
        profile: {
          ...state.userInfo.profile,
          hobbies: [...state.userInfo.profile.hobbies],
        },
      },
    })),

  // 更新方法4：更新多层级对象，内容变化
  updateObjectWithNewContent: () =>
    set((state: TestStore) => ({
      userInfo: {
        ...state.userInfo,
        profile: {
          ...state.userInfo.profile,
          age: state.userInfo.profile.age + 1,
        },
      },
    })),

  // 更新方法5：更新对象数组，创建新引用但内容相同
  updateConversationsWithNewReference: () =>
    set((state: TestStore) => ({
      conversations: state.conversations.map((conv) => ({ ...conv })),
    })),

  // 更新方法6：更新对象数组，内容变化
  updateConversationsWithNewContent: () =>
    set((state: TestStore) => ({
      conversations: state.conversations.map((conv) =>
        conv.id === 1 ? { ...conv, unread: conv.unread + 1 } : conv
      ),
    })),

  // 条件更新：只在内容变化时创建新引用
  conditionalUpdate: (newItems: number[]) =>
    set((state: TestStore) => {
      const contentSame =
        newItems.length === state.items.length &&
        newItems.every(
          (item: number, index: number) => item === state.items[index]
        );

      if (contentSame) {
        return {};
      }

      return { items: newItems };
    }),
}));

// 1. 数组测试 - 普通 selector
const ArrayBasicSelector = () => {
  const items = useTestStore((state: TestStore) => state.items);
  const [renderCount, setRenderCount] = useState(0);

  useEffect(() => {
    setRenderCount((prev) => prev + 1);
  }, [items]);

  return (
    <Card title="数组 - 普通 Selector" style={{ marginBottom: 16 }}>
      <Typography.Text>渲染次数: {renderCount}</Typography.Text>
      <Divider />
      <Typography.Text>数据: {JSON.stringify(items)}</Typography.Text>
    </Card>
  );
};

// 2. 数组测试 - shallow 比较
const ArrayShallowSelector = () => {
  const items = useTestStore((state: TestStore) => state.items, shallow);
  const [renderCount, setRenderCount] = useState(0);

  useEffect(() => {
    setRenderCount((prev) => prev + 1);
  }, [items]);

  return (
    <Card title="数组 - Shallow Selector" style={{ marginBottom: 16 }}>
      <Typography.Text>渲染次数: {renderCount}</Typography.Text>
      <Divider />
      <Typography.Text>数据: {JSON.stringify(items)}</Typography.Text>
    </Card>
  );
};

// 3. 多层级对象测试 - 普通 selector
const ObjectBasicSelector = () => {
  const userInfo = useTestStore((state: TestStore) => state.userInfo);
  const [renderCount, setRenderCount] = useState(0);

  useEffect(() => {
    setRenderCount((prev) => prev + 1);
  }, [userInfo]);

  return (
    <Card title="多层级对象 - 普通 Selector" style={{ marginBottom: 16 }}>
      <Typography.Text>渲染次数: {renderCount}</Typography.Text>
      <Divider />
      <Typography.Text>
        数据: {JSON.stringify(userInfo, null, 2)}
      </Typography.Text>
    </Card>
  );
};

// 4. 多层级对象测试 - shallow 比较
const ObjectShallowSelector = () => {
  const userInfo = useTestStore((state: TestStore) => state.userInfo, shallow);
  const [renderCount, setRenderCount] = useState(0);

  useEffect(() => {
    setRenderCount((prev) => prev + 1);
  }, [userInfo]);

  return (
    <Card title="多层级对象 - Shallow Selector" style={{ marginBottom: 16 }}>
      <Typography.Text>渲染次数: {renderCount}</Typography.Text>
      <Divider />
      <Typography.Text>
        数据: {JSON.stringify(userInfo, null, 2)}
      </Typography.Text>
    </Card>
  );
};

// 5. 对象数组测试 - 普通 selector
const ConversationsBasicSelector = () => {
  const conversations = useTestStore((state: TestStore) => state.conversations);
  const [renderCount, setRenderCount] = useState(0);

  useEffect(() => {
    setRenderCount((prev) => prev + 1);
  }, [conversations]);

  return (
    <Card title="对象数组 - 普通 Selector" style={{ marginBottom: 16 }}>
      <Typography.Text>渲染次数: {renderCount}</Typography.Text>
      <Divider />
      <Typography.Text>数据: {JSON.stringify(conversations)}</Typography.Text>
    </Card>
  );
};

// 6. 对象数组测试 - shallow 比较
const ConversationsShallowSelector = () => {
  const conversations = useTestStore(
    (state: TestStore) => state.conversations,
    shallow
  );
  const [renderCount, setRenderCount] = useState(0);

  useEffect(() => {
    setRenderCount((prev) => prev + 1);
  }, [conversations]);

  return (
    <Card title="对象数组 - Shallow Selector" style={{ marginBottom: 16 }}>
      <Typography.Text>渲染次数: {renderCount}</Typography.Text>
      <Divider />
      <Typography.Text>数据: {JSON.stringify(conversations)}</Typography.Text>
    </Card>
  );
};

// 7. 深度比较组件
const DeepCompareSelector = () => {
  const userInfo = useTestStore((state: TestStore) => state.userInfo);
  const [renderCount, setRenderCount] = useState(0);

  // 使用 useMemo 进行深度比较
  const memoizedUserInfo = useMemo(() => userInfo, [JSON.stringify(userInfo)]);

  useEffect(() => {
    setRenderCount((prev) => prev + 1);
  }, [memoizedUserInfo]);

  return (
    <Card title="深度比较 - useMemo" style={{ marginBottom: 16 }}>
      <Typography.Text>渲染次数: {renderCount}</Typography.Text>
      <Divider />
      <Typography.Text>
        数据: {JSON.stringify(memoizedUserInfo, null, 2)}
      </Typography.Text>
    </Card>
  );
};

// 主页面
const ZustandUpdate = () => {
  const {
    updateArrayWithNewReference,
    updateArrayWithNewContent,
    updateObjectWithNewReference,
    updateObjectWithNewContent,
    updateConversationsWithNewReference,
    updateConversationsWithNewContent,
    conditionalUpdate,
  } = useTestStore(
    (state: TestStore) => ({
      updateArrayWithNewReference: state.updateArrayWithNewReference,
      updateArrayWithNewContent: state.updateArrayWithNewContent,
      updateObjectWithNewReference: state.updateObjectWithNewReference,
      updateObjectWithNewContent: state.updateObjectWithNewContent,
      updateConversationsWithNewReference:
        state.updateConversationsWithNewReference,
      updateConversationsWithNewContent:
        state.updateConversationsWithNewContent,
      conditionalUpdate: state.conditionalUpdate,
    }),
    shallow
  );

  return (
    <div
      style={{
        background: '#fff',
        padding: '20px',
        height: 'calc(100vh - 100px)',
        overflow: 'auto',
      }}
    >
      <Typography.Title level={2}>
        Zustand Selector 引用变化完整测试
      </Typography.Title>

      {/* 数组测试 */}
      <Card title="数组测试" style={{ marginBottom: 30 }}>
        <Space wrap={true} style={{ marginBottom: 16 }}>
          <Button type="primary" onClick={updateArrayWithNewReference}>
            数组：更新引用（内容不变）
          </Button>
          <Button type="primary" onClick={updateArrayWithNewContent}>
            数组：更新内容和引用
          </Button>
          <Button onClick={() => conditionalUpdate([1, 2, 3])}>
            数组：条件更新（相同内容）
          </Button>
          <Button onClick={() => conditionalUpdate([1, 2, 3, 4])}>
            数组：条件更新（不同内容）
          </Button>
        </Space>
        <div style={{ display: 'flex', gap: '16px' }}>
          <div style={{ flex: 1 }}>
            <ArrayBasicSelector />
          </div>
          <div style={{ flex: 1 }}>
            <ArrayShallowSelector />
          </div>
        </div>
      </Card>

      {/* 多层级对象测试 */}
      <Card title="多层级对象测试" style={{ marginBottom: 30 }}>
        <Space wrap={true} style={{ marginBottom: 16 }}>
          <Button type="primary" onClick={updateObjectWithNewReference}>
            对象：更新引用（内容不变）
          </Button>
          <Button type="primary" onClick={updateObjectWithNewContent}>
            对象：更新内容和引用
          </Button>
        </Space>
        <div style={{ display: 'flex', gap: '16px' }}>
          <div style={{ flex: 1 }}>
            <ObjectBasicSelector />
          </div>
          <div style={{ flex: 1 }}>
            <ObjectShallowSelector />
          </div>
        </div>
      </Card>

      {/* 对象数组测试 */}
      <Card title="对象数组测试" style={{ marginBottom: 30 }}>
        <Space wrap={true} style={{ marginBottom: 16 }}>
          <Button type="primary" onClick={updateConversationsWithNewReference}>
            对象数组：更新引用（内容不变）
          </Button>
          <Button type="primary" onClick={updateConversationsWithNewContent}>
            对象数组：更新内容和引用
          </Button>
        </Space>
        <div style={{ display: 'flex', gap: '16px' }}>
          <div style={{ flex: 1 }}>
            <ConversationsBasicSelector />
          </div>
          <div style={{ flex: 1 }}>
            <ConversationsShallowSelector />
          </div>
        </div>
      </Card>

      {/* 深度比较测试 */}
      <Card title="深度比较测试" style={{ marginBottom: 30 }}>
        <div style={{ marginBottom: 16 }}>
          <Typography.Text>
            此测试展示使用 useMemo 进行深度内容比较的效果
          </Typography.Text>
        </div>
        <DeepCompareSelector />
      </Card>

      <Divider />
      <Typography.Paragraph>
        <Typography.Text strong={true}>测试说明：</Typography.Text>
        <ul>
          <li>
            <strong>普通 Selector</strong>：使用{' '}
            <code>useStore(state =&gt; state.data)</code>
            ，当引用变化时会重新渲染，即使内容相同
          </li>
          <li>
            <strong>Shallow Selector</strong>：使用{' '}
            <code>useStore(state =&gt; state.data, shallow)</code>
            ，只有当数据的第一层属性变化时才会重新渲染
          </li>
          <li>
            <strong>深度比较</strong>：使用 <code>useMemo</code> 和{' '}
            <code>JSON.stringify</code>，只有当数据内容真正变化时才会重新渲染
          </li>
          <li>
            <strong>重要发现</strong>：
            <ul>
              <li>对于数组：shallow 比较会检查数组长度和每个元素的引用</li>
              <li>
                对于对象：shallow
                比较只检查第一层属性，嵌套对象的引用变化仍会触发更新
              </li>
              <li>对于复杂数据结构：需要根据具体场景选择合适的比较策略</li>
            </ul>
          </li>
        </ul>
      </Typography.Paragraph>
    </div>
  );
};

export default ZustandUpdate;
