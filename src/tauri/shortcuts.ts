import {
  register,
  isRegistered,
  unregister,
} from '@tauri-apps/plugin-global-shortcut';

const registerKeyBind = async (keyBind: string, handler: any) => {
  const isKeyBindRegistered = await isRegistered(keyBind);
  if (isKeyBindRegistered) {
    await unregister(keyBind);
  }
  await register(keyBind, handler);
};

export const initGlobalShortcut = () => {
  if (!window.__TAURI_INTERNALS__) {
  }
};
