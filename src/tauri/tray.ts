import { getCurrentWindow } from '@tauri-apps/api/window';
import { TrayIcon, TrayIconOptions, TrayIconEvent } from '@tauri-apps/api/tray';
import { Menu, MenuOptions } from '@tauri-apps/api/menu';
import { exit } from '@tauri-apps/plugin-process';
import BaseSettings from '../../config/BaseSettings';

const iconUrl = location.href.includes('localhost')
  ? 'icons/icon.png'
  : `${location.origin}/${BaseSettings.appName}/icons/icon.png`;

const options: TrayIconOptions = {
  icon: iconUrl,
  tooltip: 'LinkFlow',
  menuOnLeftClick: false,
  id: 'linkFlowTray',
  action: (event: TrayIconEvent) => {
    if (
      event.type === 'Click' &&
      event.button === 'Left' &&
      event.buttonState === 'Down'
    ) {
      // 显示窗口
      flashTray(false);
      winShowFocus();
    }
  },
};

/**
 * 窗口置顶显示
 */
export const winShowFocus = async () => {
  // 获取窗体实例
  const win = getCurrentWindow();
  // 检查窗口是否见，如果不可见则显示出来
  if (!(await win.isVisible())) {
    win.show();
  } else {
    // 检查是否处于最小化状态，如果处于最小化状态则解除最小化
    if (await win.isMinimized()) {
      await win.unminimize();
    }
    // 窗口置顶
    await win.setFocus();
  }
};

/**
 * 创建托盘菜单
 */
async function createMenu() {
  return Menu.new({
    // items 的显示顺序是倒过来的
    items: [
      {
        id: 'show',
        text: '显示窗口',
        action: () => {
          flashTray(false);
          winShowFocus();
        },
      },
      {
        // 菜单 id
        id: 'quit',
        // 菜单文本
        text: '退出',
        //  菜单项点击事件
        action: () => {
          // 退出应用
          exit(0);
        },
      },
    ],
  });
}

/**
 * 创建系统托盘
 */
export const initTray = async () => {
  // 获取 menu
  if (!window.__TAURI_INTERNALS__) {
    return;
  }
  options.menu = await createMenu();
  await TrayIcon.new(options);
};

let flashTimer: any = {};
export const flashTray = async (bool: boolean) => {
  if (!window.__TAURI_INTERNALS__) {
    return;
  }
  let flag = true;
  if (bool) {
    TrayIcon.getById('linkFlowTray').then(async (res) => {
      clearInterval(flashTimer);
      flashTimer = setInterval(() => {
        if (flag) {
          res?.setIcon(null);
        } else {
          // 支持把自定义图标放在默认icons文件夹，通过如下方式设置图标
          // res.setIcon('icons/msg.png')
          // 支持把自定义图标放在自定义文件夹tray，需要配置tauri.conf.json参数 "bundle": {"resources": ["tray"]}
          res?.setIcon(iconUrl);
        }
        flag = !flag;
      }, 500);
    });
  } else {
    clearInterval(flashTimer);
    const tray = await TrayIcon.getById('linkFlowTray');
    tray?.setIcon(iconUrl);
  }
};
