<?xml version="1.0" encoding="UTF-8"?>
<svg width="14px" height="14px" viewBox="0 0 14 14" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="消息-交互" transform="translate(-1703.000000, -2237.000000)">
            <g id="编组-7" transform="translate(1476.000000, 1736.000000)">
                <g id="1.通用/按钮/边框-展开" transform="translate(183.000000, 494.000000)">
                    <g id="编组-72" transform="translate(12.000000, 4.000000)">
                        <g id="编组" transform="translate(32.000000, 3.000000)">
                            <rect id="矩形" transform="translate(7.000000, 7.000000) scale(1, -1) translate(-7.000000, -7.000000) " x="0" y="0" width="14" height="14"></rect>
                            <path d="M11.646276,4.14507276 L11.7559209,4.23972087 C12.0814575,4.57624345 12.0814575,5.13530124 11.7553339,5.47240898 L7.58443351,9.76432962 L7.4809204,9.85476435 C7.33487345,9.96201874 7.16618651,10.010079 7.02083096,9.99824596 C6.79737835,10.0129967 6.57866023,9.93436529 6.41468685,9.7642836 L2.24524165,5.47236161 C1.91825278,5.13576406 1.91825278,4.57578066 2.24524165,4.23916282 C2.55501426,3.92028742 3.04504739,3.92028742 3.35477125,4.23911263 L7.0011985,7.98998989 L10.6443663,4.2391628 C10.9197209,3.95571684 11.3386493,3.92422284 11.646276,4.14507276 Z" id="路径" fill="#74757E" fill-rule="nonzero"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>