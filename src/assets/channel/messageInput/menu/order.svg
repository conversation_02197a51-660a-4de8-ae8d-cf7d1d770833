<?xml version="1.0" encoding="UTF-8"?>
<svg width="28px" height="28px" viewBox="0 0 28 28" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="消息主页-会话列表-私聊" transform="translate(-625.000000, -1292.000000)">
            <g id="编组-7" transform="translate(462.000000, 56.000000)">
                <g id="输入框" transform="translate(20.000000, 1230.000000)">
                    <g id="上部组建28" transform="translate(6.000000, 6.000000)">
                        <g id="编组" transform="translate(137.000000, 0.000000)">
                            <rect id="背景" x="0" y="0" width="28" height="28" rx="5.81818182"></rect>
                            <path d="M7.4363,17.500924 C7.6064,17.2795194 7.8701,17.1499168 8.15,17.1499168 C8.591,17.1499168 8.825,17.4919238 8.825,17.8249306 C8.825,17.8834318 8.7953,18.0004342 8.6693,18.1939382 C8.5109,18.4225429 8.3327,18.6376473 8.1347,18.8329514 C7.9043,19.0732563 7.6379,19.3261615 7.3517,19.596167 L7.2725,19.6717686 C7.0052,19.9210737 6.7415,20.175779 6.4814,20.4331842 C6.3059,20.6086878 6.3068,20.8939937 6.4823,21.0694973 C6.5669,21.153199 6.6812,21.2 6.8,21.2 L9.5,21.2 C9.7484,21.2 9.95,20.9992959 9.95,20.7499908 C9.95,20.5015856 9.7484,20.2999815 9.5,20.2999815 L7.9178,20.2999815 L7.97,20.2513805 C8.2544,19.983175 8.5361,19.7149695 8.7836,19.4575642 C9.0203,19.2199593 9.2345,18.960754 9.4235,18.6835483 C9.5855,18.4342432 9.725,18.1399371 9.725,17.8249306 C9.725,17.1166161 9.2012,16.2498983 8.15,16.2498983 C7.4534,16.2498983 6.818,16.6522065 6.521,17.2831195 C6.4148,17.5072241 6.5111,17.7763296 6.7361,17.8825318 C6.9611,17.987834 7.2293,17.892432 7.3355,17.6674274 C7.3625,17.6080262 7.3967,17.5531251 7.4363,17.500924 L7.4363,17.500924 Z M11.3,20.5249861 C11.3,20.1523785 11.6024,19.8499723 11.975,19.8499723 L20.975,19.8499723 C21.3476,19.8499723 21.65,20.1523785 21.65,20.5249861 C21.65,20.8975938 21.3476,21.2 20.975,21.2 L11.975,21.2 C11.6024,21.2 11.3,20.8975938 11.3,20.5249861 L11.3,20.5249861 Z M11.3,14.6748659 C11.3,14.3022582 11.6024,13.999852 11.975,13.999852 L20.975,13.999852 C21.3476,13.999852 21.65,14.3022582 21.65,14.6748659 C21.65,15.0474736 21.3476,15.3498798 20.975,15.3498798 L11.975,15.3498798 C11.6024,15.3498798 11.3,15.0474736 11.3,14.6748659 L11.3,14.6748659 Z M11.975,8.14973179 C11.6024,8.14973179 11.3,8.45213801 11.3,8.82474567 C11.3,9.19735332 11.6024,9.49975954 11.975,9.49975954 L20.975,9.49975954 C21.3476,9.49975954 21.65,9.19735332 21.65,8.82474567 C21.65,8.45213801 21.3476,8.14973179 20.975,8.14973179 L11.975,8.14973179 Z M8.4128,6.88520581 C8.5307,6.96890753 8.6,7.10481032 8.6,7.2497133 L8.6,10.399778 L9.5,10.399778 C9.7484,10.399778 9.95,10.6013822 9.95,10.8497873 C9.95,11.0990924 9.7484,11.2997965 9.5,11.2997965 L6.8,11.2997965 C6.5516,11.2997965 6.35,11.0990924 6.35,10.8497873 C6.35,10.6013822 6.5516,10.399778 6.8,10.399778 L7.7,10.399778 L7.7,7.87432613 L6.9422,8.12633131 C6.7037,8.19653276 6.4535,8.05882993 6.3842,7.82032502 C6.3185,7.59442038 6.4373,7.3568155 6.6578,7.27311378 L8.0078,6.82310453 C8.1446,6.77720359 8.2958,6.80060407 8.4128,6.88520581 L8.4128,6.88520581 Z" id="Fill-1" fill="#74757E" fill-rule="nonzero"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>