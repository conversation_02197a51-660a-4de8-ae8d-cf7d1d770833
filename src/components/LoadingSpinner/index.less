.loadingSpinner {
  display: inline-block;
  position: relative;
  width: 24px;
  height: 24px;

  div {
    transform-origin: 12px 12px;
    animation: loading-spinner 0.8s linear infinite;

    &::after {
      content: " ";
      display: block;
      position: absolute;
      top: 3px;
      left: 11px;
      width: 2px;
      height: 4px;
      border-radius: 50%;
      background: #000;
    }

    &:nth-child(1) {
      transform: rotate(0deg);
      animation-delay: -0.7s;
    }
    &:nth-child(2) {
      transform: rotate(45deg);
      animation-delay: -0.6s;
    }

    &:nth-child(3) {
      transform: rotate(90deg);
      animation-delay: -0.5s;
    }

    &:nth-child(4) {
      transform: rotate(135deg);
      animation-delay: -0.4s;
    }

    &:nth-child(5) {
      transform: rotate(180deg);
      animation-delay: -0.3s;
    }

    &:nth-child(6) {
      transform: rotate(225deg);
      animation-delay: -0.2s;
    }

    &:nth-child(7) {
      transform: rotate(270deg);
      animation-delay: -0.1s;
    }

    &:nth-child(8) {
      transform: rotate(315deg);
      animation-delay: 0s;
    }
  }
}

@keyframes loading-spinner {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}
