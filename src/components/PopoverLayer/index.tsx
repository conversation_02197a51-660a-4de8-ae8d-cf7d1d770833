import ReactDOM from 'react-dom';
import { Popover } from '@ht/sprite-ui';

let popoverContainer: HTMLElement | null = null;

export const showCommandLayer = (options: {
  content: React.ReactNode;
  cursorPosition: { x: number; y: number };
}) => {
  // 销毁旧容器
  if (popoverContainer) {
    ReactDOM.unmountComponentAtNode(popoverContainer);
    document.body.removeChild(popoverContainer);
  }

  // 创建新容器并定位
  popoverContainer = document.createElement('div');
  popoverContainer.style.position = 'absolute';
  popoverContainer.style.left = `${options.cursorPosition.x}px`;
  popoverContainer.style.top = `${options.cursorPosition.y + 8}px`;
  document.body.appendChild(popoverContainer);

  // 渲染 Popover 内容
  ReactDOM.createPortal(
    <Popover
      open={true}
      content={options.content}
      overlayStyle={{ position: 'static' }}
    />,
    popoverContainer
  );
};

// 关闭方法
export const hideCommandLayer = () => {
  if (popoverContainer) {
    ReactDOM.unmountComponentAtNode(popoverContainer);
    document.body.removeChild(popoverContainer);
    popoverContainer = null;
  }
};
