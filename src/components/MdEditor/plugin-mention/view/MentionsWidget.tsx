/*
 * @Author: ypt
 * @Description:
 * @LastEditors: ypt
 * @LastEditTime: 2025-03-12 10:12:01
 * @FilePath: /linkflow/src/components/MdEditor/plugin-mention/view/MentionsWidget.tsx
 */
import { Editor, EditorStatus, editorViewCtx } from '@milkdown/core';
import { useContext, useEffect, useState } from 'react';
import { useWidgetViewContext } from '@prosemirror-adapter/react';
import { GroupMemberItem } from '@ht/openim-wasm-client-sdk';
import { EditorContext } from '../../editorContent';
import { MentionsPluginOptions } from '../plugin';
import { MentionsPluginAttrs } from '../utils';
import { MentionsPluginDropdownView } from '../MentionDrop';

const defaultDropdownComponent = () => <div>暂无数据</div>;

const getDropdownListComponent = (editor: Editor | undefined) => {
  if (editor) {
    const { view: Comp = defaultDropdownComponent, list } = editor.ctx.get(
      MentionsPluginOptions.key
    );
    return Comp;
  }
  return MentionsPluginDropdownView;
};

export type MentionsListDropdownProps = {
  list: GroupMemberItem[];
  queryText: string;
  onMentionItemClick: (value: GroupMemberItem) => void;
};

export const MentionsWidget: React.FC = () => {
  const { spec } = useWidgetViewContext();
  const { editorInstance } = useContext(EditorContext);
  const { list, onMention } =
    editorInstance?.editor?.ctx?.get(MentionsPluginOptions.key) || {};
  const { queryText = '', range } = spec as NonNullable<MentionsPluginAttrs>;

  const onMentionItemClick = (value: GroupMemberItem) => {
    onMention?.(value);

    editorInstance.editor?.action((ctx) => {
      const view = ctx.get(editorViewCtx);
      const { state } = view;
      // const node = state.schema.text(`@${value.nickname} `);
      const node = state.schema.nodes.mention.create({
        id: value.userID,
        name: `${value.nickname} `,
      });
      // const node = state.schema.mention;
      const tr = state.tr.replaceWith(range.from, range.to, node);

      // // 计算新光标位置：原始位置 + 插入文本长度
      // const newPos = tr.mapping.map(range.from) + node.text!.length;
      // tr.setSelection(TextSelection.create(tr.doc, newPos));

      view.dispatch(tr);
      view.focus(); // 确保编辑器保持焦点
    });
  };

  const DropdownListComponent = getDropdownListComponent(
    editorInstance?.editor
  );

  return (
    <DropdownListComponent
      list={list}
      queryText={queryText}
      onMentionItemClick={onMentionItemClick}
    />
  );
};
