/*
 * @Author: ypt
 * @Description:
 * @LastEditors: ypt
 * @LastEditTime: 2025-03-12 12:17:10
 * @FilePath: /linkflow/src/components/MdEditor/plugin-mention/MentionShecma.ts
 */
import { expectDomTypeError } from '@milkdown/exception';
import { $nodeAttr, $nodeSchema, $remark } from '@milkdown/utils';
import type { Meta, MilkdownPlugin } from '@milkdown/ctx';

export const mentionRegex = /@{([^}]+)\s}/g; // 使用全局匹配

export function withMeta<T extends MilkdownPlugin>(
  plugin: T,
  meta: Partial<Meta> & Pick<Meta, 'displayName'>
): T {
  Object.assign(plugin, {
    meta: {
      package: '@milkdown/preset-commonmark',
      ...meta,
    },
  });

  return plugin;
}

export const mentionAttr = $nodeAttr('mention');

withMeta(mentionAttr, {
  displayName: 'Attr<mention>',
  group: 'Mention',
});

// 定义 Mention 属性接口
export interface MentionAttrs {
  id: string;
  name: string;
}

export const mentionSchema = $nodeSchema('mention', (ctx) => ({
  inline: true,
  group: 'inline',
  atom: true,
  marks: '',
  attrs: {
    id: { default: '' },
    name: { default: '' },
  },
  parseDOM: [
    {
      tag: 'span[data-type="mention"]',
      getAttrs: (dom) => {
        if (!(dom instanceof HTMLElement)) {
          // throw expectDomTypeError(dom);
          console.error('Mention 节点渲染失败');
        }
        return {
          id: dom.getAttribute('id') || '',
          name: dom.textContent?.replace(/^@/, '') || '',
        };
      },
    },
  ],
  toDOM: (node) => {
    return [
      'span',
      {
        'data-type': 'mention',
        // id: node.attrs.id,
        class: 'milkdown-mention',
      },
      `@${node.attrs.name}`,
    ];
  },
  parseMarkdown: {
    // 支持两种格式：原有的 mention 类型和新的 [[@name]]<id=id> 格式
    match: ({ type, value }) => {
      if (type === 'text' && typeof value === 'string') {
        // const mentionRegex = /@{([^}]+)\s}/g; // 使用全局匹配
        return mentionRegex.test(value);
      }
      return false;
    },
    runner: (state, node, type) => {
      if (typeof node.value === 'string') {
        const { value } = node;
        // const mentionRegex = /@{([^}]+)\s}/g;
        let match;
        let lastIndex = 0;

        // 循环匹配所有的 @提及
        // eslint-disable-next-line no-cond-assign
        while ((match = mentionRegex.exec(value)) !== null) {
          // 添加匹配之前的普通文本
          if (match.index > lastIndex) {
            state.addText(value.slice(lastIndex, match.index));
          }

          const [fullMatch, name] = match;
          // 添加 mention 节点
          state.addNode(type, {
            id: '',
            name: name.trim(),
          });

          lastIndex = match.index + fullMatch.length;
        }

        // 添加最后一个匹配后的剩余文本
        if (lastIndex < value.length) {
          state.addText(value.slice(lastIndex));
        }
      }
    },
  },
  toMarkdown: {
    match: (node) => {
      return node.type.name === 'mention';
    },
    runner: (state, node) => {
      const { name } = node.attrs;
      state.addNode('text', undefined, undefined, {
        value: `@{${name}}`,
      });
    },
  },
}));

withMeta(mentionSchema.node, {
  displayName: 'NodeSchema<mention>',
  group: 'Mention',
});

withMeta(mentionSchema.ctx, {
  displayName: 'NodeSchemaCtx<mention>',
  group: 'Mention',
});
