/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-statements */
import { editorViewCtx } from '@milkdown/core';
import { TextSelection, Plugin, PluginKey } from '@milkdown/prose/state';
import { DecorationSet } from '@milkdown/prose/view';
import { $ctx, $prose } from '@milkdown/utils';
import { FC, useMemo } from 'react';
import { isNil } from 'lodash';
import { WidgetDecorationFactory } from '@prosemirror-adapter/core';
import { ReactWidgetViewUserOptions } from '@prosemirror-adapter/react';
import { GroupMemberItem } from '@ht/openim-wasm-client-sdk';
import {
  MentionsPluginAttrs,
  computeStateFromSelection,
  getInitState,
} from './utils';
import {
  MentionsListDropdownProps,
  MentionsWidget,
} from './view/MentionsWidget';

export type MentionsOptions = {
  view?: React.FC<MentionsListDropdownProps>;
  list: GroupMemberItem[];
  onMention: (value: GroupMemberItem[]) => void;
};

export const MentionsPluginOptions = $ctx<MentionsOptions, 'mentionsOptions'>(
  {},
  'mentionsOptions'
);

// 计算下拉菜单位置的函数
export const calculateDropdownPosition = (
  start: { left: number; top: number; bottom: number },
  dropdownHeight = 200,
  dropdownWidth = 338
) => {
  const viewportHeight = window.innerHeight;
  const viewportWidth = window.innerWidth;
  const position = {
    left: `${start.left}px`,
    top: `${start.top - dropdownHeight}px`, // Always position above the cursor
  };

  // 检查是否会被左侧边界遮挡
  if (start.left + dropdownWidth > viewportWidth) {
    // 如果会被遮挡，显示在光标左侧
    position.left = `${start.left - dropdownWidth}px`;
  }

  return position;
};

export const MentionsPlugin = (widgetViewFactory: {
  (options: ReactWidgetViewUserOptions): WidgetDecorationFactory;
  (arg0: { as: HTMLDivElement; component: FC }): any;
}) => {
  const proseMentionsPlugin = useMemo(
    () =>
      $prose((ctx) => {
        const key = new PluginKey<MentionsPluginAttrs>('MENTIONS_PLUGIN');
        return new Plugin({
          key,
          state: {
            init() {
              return getInitState();
            },
            apply(tr, value) {
              const newState = getInitState();
              const { selection } = tr;
              if (selection.from !== selection.to) {
                return newState;
              }
              if (selection.to === 1 && selection.from === 1) {
                // 处理@出现后，用户并未选中@人员的情况
                return {
                  ...value,
                  flag: true,
                };
              }
              const stateFromSelection = computeStateFromSelection(
                ctx,
                selection
              );
              if (stateFromSelection) {
                return stateFromSelection;
              }

              return newState;
            },
          },
          props: {
            decorations(state) {
              const newState = key.getState(state);
              if (newState?.flag) {
                try {
                  // 将光标进行重新定位
                  const { range } = newState;
                  const editorView = ctx.get(editorViewCtx);

                  const newSelection = TextSelection.create(
                    state.doc,
                    range.to
                  ); // `pos` 是文档偏移量

                  // 创建事务并应用选区
                  const tr = state.tr.setSelection(newSelection);
                  editorView.dispatch(tr);
                } catch (error) {}
              } else if (!isNil(newState?.queryText)) {
                const { range } = newState;
                const editorView = ctx.get(editorViewCtx);

                const start = editorView.coordsAtPos(range.from);
                // const dropdownHeight = 200;
                // const position = calculateDropdownPosition(
                //   start,
                //   dropdownHeight
                // );
                // 在初始化时存储id和光标位置，方便在弹框高度变化时动态更新弹窗位置
                const portalContainer = document.createElement('div');
                portalContainer.id = 'mentionDropContainer';
                portalContainer.dataset.originalTop = start.top.toString();
                portalContainer.dataset.originalLeft = start.left.toString();
                portalContainer.style.zIndex = '1000';
                document.body.appendChild(portalContainer);
                const createWidget = widgetViewFactory({
                  as: portalContainer,
                  component: MentionsWidget,
                });

                return DecorationSet.create(state.tr.doc, [
                  createWidget(0, newState),
                ]);
              }

              return DecorationSet.empty;
            },
          },
        });
      }),
    []
  );

  const mentionsPlugin = useMemo(
    () => [MentionsPluginOptions, proseMentionsPlugin],
    []
  );

  return mentionsPlugin;
};
