import { useEffect, useRef, useState } from 'react';
import classNames from 'classnames';
import { Button, List } from '@ht/sprite-ui';
import { GroupMemberItem } from '@ht/openim-wasm-client-sdk';
import atAllIcon from '@/assets/images/messageInput/atAll.svg';
import downIcon from '@/assets/images/messageInput/downIcon.svg';
import enterIcon from '@/assets/images/messageInput/enterIcon.svg';
import upIcon from '@/assets/images/messageInput/upIcon.svg';
import OIMAvatar, { OnlineStatusComponent } from '@/components/OIMAvatar';
import type { MentionsListDropdownProps } from '../view/MentionsWidget';
import styles from './index.less';
import { calculateDropdownPosition } from '../plugin';

export const MentionsPluginDropdownView: React.FC<
  MentionsListDropdownProps
> = ({ list, queryText, onMentionItemClick }) => {
  const wrapperRef = useRef<HTMLDivElement>(null);
  const filteredList =
    list?.filter((member: GroupMemberItem) =>
      member.nickname.includes(queryText)
    ) || [];

  const [activedIndex, setActiveIndex] = useState<number>(0);

  const handleKeydown = (e: KeyboardEvent) => {
    if (e.key === 'ArrowUp') {
      e.preventDefault();
      setActiveIndex((prev) => {
        if (prev <= 0) {
          return prev;
        } else {
          handleScrollIntoView('ArrowUp', prev - 1);
          return prev - 1;
        }
      });
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      setActiveIndex((prev) => {
        if (prev >= filteredList.length - 1) {
          return prev;
        } else {
          handleScrollIntoView('ArrowDown', prev + 1);
          return prev + 1;
        }
      });
    } else if (e.key === 'Enter') {
      e.preventDefault();
      e.stopPropagation();
      if (activedIndex >= 0 && activedIndex < filteredList.length) {
        onMentionItemClick(filteredList[activedIndex]);
      }
    } else if (e.key === 'Escape' || e.key === 'Esc') {
      const mentionElement = document.getElementById('mentionDropId');
      if (mentionElement) {
        mentionElement.style.display = 'none';
        setActiveIndex(-1);
      }
    }
  };

  // 点击外部关闭悬浮
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const mentionElement = document.getElementById('mentionDropId');

      if (mentionElement && !mentionElement.contains(event.target as Node)) {
        if (mentionElement) {
          mentionElement.style.display = 'none';
          setActiveIndex(-1);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  const handlePreventDefault = (e: KeyboardEvent) => {
    if (e.key === 'Enter') {
      const mentionElement = document.getElementById('mentionDropId');
      if (mentionElement && mentionElement.style.display !== 'none') {
        e.preventDefault();
        e.stopPropagation();
        if (activedIndex >= 0 && activedIndex < filteredList.length) {
          onMentionItemClick(filteredList[activedIndex]);
        }
      }
    }
  };

  useEffect(() => {
    const pasteContainerElement = document.getElementById('pasteContainerId');
    pasteContainerElement?.addEventListener('keydown', handlePreventDefault);
    document.addEventListener('keydown', handleKeydown);
    return () => {
      pasteContainerElement?.removeEventListener(
        'keydown',
        handlePreventDefault
      );
      document.removeEventListener('keydown', handleKeydown);
    };
  }, [filteredList, activedIndex]);

  useEffect(() => {
    const mentionDropContainer = document.getElementById(
      'mentionDropContainer'
    );
    const mentionDropElement = wrapperRef.current;

    if (mentionDropContainer && mentionDropElement) {
      // 使用ResizeObserver监听下拉菜单大小变化
      const resizeObserver = new ResizeObserver(() => {
        const dropdownHeight = mentionDropElement.offsetHeight;
        // 从dataset获取原始光标位置
        const originalTop = parseFloat(
          mentionDropContainer.dataset.originalTop || '0'
        );
        const originalLeft = parseFloat(
          mentionDropContainer.dataset.originalLeft || '0'
        );

        // 使用原始光标位置信息
        const start = {
          left: originalLeft,
          top: originalTop,
          bottom: originalTop + 20, // 假设光标高度为20px
        };
        // 使用封装的函数重新计算位置
        const position = calculateDropdownPosition(start, dropdownHeight);
        // 更新容器位置
        mentionDropElement.style.position = 'fixed';
        mentionDropElement.style.left = position.left;
        mentionDropElement.style.top = position.top;
      });

      resizeObserver.observe(mentionDropElement);

      return () => {
        resizeObserver.disconnect();
      };
    }
  }, []);
  const isElementInContainer = (el: Element, container: HTMLElement) => {
    const elRect = el.getBoundingClientRect();
    const containerRect = container.getBoundingClientRect();

    return (
      elRect.top >= containerRect.top &&
      elRect.bottom <= containerRect.bottom &&
      elRect.left >= containerRect.left &&
      elRect.right <= containerRect.right
    );
  };

  const handleScrollIntoView = (
    type: 'ArrowUp' | 'ArrowDown',
    index: number
  ) => {
    const activedItem =
      document.getElementsByClassName('mentionItemView')[index];
    const mentionListEle = document.getElementById('mentionListId');
    if (activedItem && mentionListEle) {
      if (!isElementInContainer(activedItem, mentionListEle)) {
        const elRect = activedItem.getBoundingClientRect();
        const containerRect = mentionListEle.getBoundingClientRect();
        if (index === filteredList.length - 1) {
          mentionListEle.scrollTop = mentionListEle.scrollHeight;
        } else if (index === 0) {
          mentionListEle.scrollTop = 0;
        } else if (type === 'ArrowUp') {
          mentionListEle.scrollTop -= containerRect.top - elRect.top;
        } else {
          mentionListEle.scrollTop =
            mentionListEle.scrollTop + elRect.bottom - containerRect.bottom;
        }
      }
    }
  };

  if (filteredList.length === 0) {
    return <></>;
  }
  // const onScroll = (e: React.UIEvent<HTMLElement, UIEvent>) => {
  //   if (
  //     e.currentTarget.scrollHeight - e.currentTarget.scrollTop ===
  //     ContainerHeight
  //   ) {
  //     appendData();
  //   }
  // };

  return (
    <div>
      <div className={styles.mentionMaskBox}></div>
      <div
        className={styles.mentionListWrapper}
        id="mentionDropId"
        ref={wrapperRef}
      >
        <div className={styles.mentionList} id="mentionListId">
          {filteredList?.map((member, index) => (
            <Button
              key={member.userID}
              className={classNames(
                styles.mentionItem,
                index === activedIndex && styles.mentionItemActived,
                'mentionItemView'
              )}
              onClick={(e) => {
                onMentionItemClick(member);
              }}
              onMouseMove={() => {
                setActiveIndex(index);
              }}
            >
              {member.userID !== 'AtAllTag' ? (
                <OIMAvatar
                  userID={member.userID}
                  size={20}
                  hideOnlineStatus={true}
                />
              ) : (
                <img src={atAllIcon} />
              )}
              <div className={styles.name}>
                {member.nickname} {/* groupMember没有employeecode? */}
                {/* {member.userID !== 'AtAllTag' ? '' : ''} */}
              </div>
              {member.userID !== 'AtAllTag' && (
                <OnlineStatusComponent
                  userID={member.userID}
                  stateRight={0}
                  stateSize={8}
                  isPosition={false}
                />
              )}
            </Button>
          ))}
        </div>
        {filteredList.length > 0 && (
          <div className={styles.descWrapper}>
            <span>
              使用 <img src={upIcon} style={{ width: '9px' }} />
              &nbsp;
              <img src={downIcon} /> 键导航
            </span>
            <span>
              使用 <img src={enterIcon} style={{ width: '9px' }} /> 选择
            </span>
            <span>使用 Esc 关闭</span>
          </div>
        )}
      </div>
    </div>
  );
};
