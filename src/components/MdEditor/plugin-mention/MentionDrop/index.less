.mentionListWrapper {
  display: flex;
  flex-direction: column;
  max-height: 200px;
  width: 340px;
  border: 1px solid var(--primary-border-color);
  border-radius: 8px;
  overflow: hidden;
  background: #ffffff;
  box-shadow: 0 2px 4px 2px rgba(0, 0, 0, 4%);

  .mentionList {
    flex: 1;
    list-style: none;
    padding: 10px 6px;
    margin: 0;
    font-size: 14px;
    overflow: auto;
    color: var(--primary-text-color-1);
    background: #ffffff;

    /* 隐藏滚动条 */
    &::-webkit-scrollbar {
      display: none;
    }

    /* 针对 Firefox */
    scrollbar-width: none;
    .mentionItem {
      width: 100%;
      display: flex;
      align-items: center;
      height: 32px;
      padding: 0 10px;
      cursor: pointer;
      transition: background 0.2s;
      font-size: 14px;
      color: var(--link-color-content-pry);
      font-weight: bold;
      border: none;
      .name {
        margin: 0 8px;
      }
    }

    .mentionItemActived {
      background: var(--msg-qute-backgroud-color);
      border-radius: 4px;
      // color: var(--primary-text-color-pressed);
    }
  }

  .descWrapper {
    background: var(--msg-qute-backgroud-color);
    color: var(--primary-text-color-8);
    height: 32px;
    padding: 0 16px;
    font-size: 12px;
    line-height: 32px;
    display: flex;
    align-items: center;

    > span {
      margin-right: 16px;
      display: flex;
      align-items: center;
    }
  }
}
.mentionMaskBox {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
}
