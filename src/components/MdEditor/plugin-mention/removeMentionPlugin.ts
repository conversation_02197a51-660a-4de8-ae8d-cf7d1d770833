/*
 * @Author: ypt
 * @Description:
 * @LastEditors: ypt
 * @LastEditTime: 2025-03-11 16:33:58
 * @FilePath: /linkflow/src/components/MdEditor/plugin-mention/removeMentionPlugin.ts
 */
import { Plugin } from '@milkdown/prose/state';

export const mentionRemovePlugin = (
  callback: (node: { id: string; name: string }) => void
) => {
  return new Plugin({
    state: {
      init: () => null,
      apply: (tr, value, oldState, newState) => {
        if (tr.docChanged) {
          const oldMentions = new Set();
          const newMentions = new Set();

          // 收集旧文档中的所有 mention 节点内容
          oldState.doc.descendants((node) => {
            if (node.type.name === 'mention') {
              oldMentions.add(`${node.attrs.id}-${node.attrs.name}`);
            }
          });

          // 收集新文档中的所有 mention 节点内容
          newState.doc.descendants((node) => {
            if (node.type.name === 'mention') {
              newMentions.add(`${node.attrs.id}-${node.attrs.name}`);
            }
          });

          // 找出在旧文档中存在但在新文档中不存在的节点
          oldState.doc.descendants((node) => {
            if (node.type.name === 'mention') {
              const key = `${node.attrs.id}-${node.attrs.name}`;
              if (!newMentions.has(key)) {
                callback?.(node.attrs as any);
              }
            }
          });
        }
        return value;
      },
    },
  });
};
