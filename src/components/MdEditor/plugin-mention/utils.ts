import { Ctx } from '@milkdown/ctx';
import { linkSchema } from '@milkdown/preset-commonmark';
import { Selection } from '@milkdown/prose/state';

export type MentionsPluginAttrs = {
  range: {
    to: number;
    from: number;
  };
  queryText: string | undefined;
};

// const mentionsRegex = new RegExp('(^|\\s)@([\\w-\\+]*)$');
const mentionsRegex = new RegExp('(^|\\s*)@([\\w\\u4e00-\\u9fa5+-]*)$');
export const computeStateFromSelection = (
  ctx: Ctx,
  selection: Selection
): MentionsPluginAttrs | undefined => {
  try {
    const { $from } = selection;

    const parastart = $from.before();
    const text = $from.doc.textBetween(parastart, $from.pos, '\n', '\0');
    const match = mentionsRegex.exec(text);

    if (match) {
      const { index = 0 } = match;
      const [value, , queryText] = match;

      match.index = value.startsWith(' ') ? index + 1 : match.index;
      match[0] = value.trim();

      const from = $from.start() + match.index;
      const to = from + match[0].length;

      const isLink = $from.doc.rangeHasMark(from, to, linkSchema.type(ctx));

      if (isLink) {
        return undefined;
      }

      return {
        range: { from, to },
        queryText,
      };
    }

    return undefined;
  } catch (err) {
    return undefined;
  }
};

export const getInitState = (): MentionsPluginAttrs => ({
  range: {
    to: 0,
    from: 0,
  },
  queryText: undefined,
});
