/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-statements */
import React from 'react';
import ReactDOM from 'react-dom';
import '@ht/sprite-ui/dist/sprite-ui.less';
import {
  editorViewCtx,
  parserCtx,
  schemaCtx,
  serializerCtx,
} from '@milkdown/core';
import { getNodeFromSchema } from '@milkdown/prose';
import type { Node, Slice } from '@milkdown/prose/model';
import { DOMParser, DOMSerializer } from '@milkdown/prose/model';
import { Plugin, PluginKey, TextSelection } from '@milkdown/prose/state';
import { $ctx, $prose } from '@milkdown/utils';

const DEFAULT_TRIGGER_CHAR = '@';

// 创建一个PluginKey用于管理插件状态
const MENTIONS_PLUGIN_KEY = new PluginKey('MENTIONS_PLUGIN');

// 定义装饰类型
interface MentionDecoration {
  pos: number; // 装饰的位置
  widget: HTMLElement; // 下拉列表的DOM元素
}

export type MentionsOptions = {
  triggerChar?: string;
  listComponents?: React.FC<MentionsListDropdownProps>;
  items?: string[];
};

export const mentionsOptions = $ctx<MentionsOptions, 'mentionsOptions'>(
  {
    triggerChar: '/',
  },
  'mentionsOptions'
);

mentionsOptions.meta = {
  package: '@milkdown/plugin-mentions',
  displayName: 'Ctx<mentionsOptions>',
};

export const mentionsPlugin = $prose((ctx, options: MentionsOptions = {}) => {
  const {
    triggerChar,
    items = [],
    listComponents,
  } = ctx.get(mentionsOptions.key);
  const A = listComponents;

  const schema = ctx.get(schemaCtx);
  const parser = ctx.get(parserCtx);

  const key = MENTIONS_PLUGIN_KEY;
  const opts = {
    triggerChar: options.triggerChar || DEFAULT_TRIGGER_CHAR,
    items: options.items || ['User1', 'User2', 'User3'],
    onSelectItem: options.onSelectItem,
    dropdownClass: options.dropdownClass || '',
  };

  // 初始化装饰集合
  const initialDecorations: MentionDecoration[] = [];

  // 创建Plugin实例
  const plugin = new Plugin({
    key,
    state: {
      init(_, state) {
        return initialDecorations;
      },
      apply(tr, prevDecorations) {
        // 更新装饰逻辑
        return prevDecorations;
      },
    },
    props: {
      // 处理键盘事件
      handleKeyDown(view, event) {
        if (event.key !== opts.triggerChar) {
          return false;
        }

        // 阻止默认行为
        event.preventDefault();

        // 获取当前光标位置
        const pos = view.state.selection.anchor;
        const node = view.state.doc.nodeAt(pos);

        // 检查当前节点是否允许插入提及
        if (node?.type.spec.code) {
          return false;
        }

        // 创建下拉列表DOM元素
        const dropdown = document.createElement('div');
        dropdown.className = `mentions-dropdown ${opts.dropdownClass}`;
        dropdown.style.position = 'absolute';
        dropdown.style.left = `10px`;
        dropdown.style.top = `10px`;

        ReactDOM.createRoot(dropdown).render(
          React.createElement(listComponents)
        );

        // 添加样式
        dropdown.style.backgroundColor = '#fff';
        dropdown.style.border = '1px solid #ccc';
        dropdown.style.padding = '5px';
        dropdown.style.zIndex = '1000';

        // 添加提及项
        opts.items.forEach((item) => {
          const li = document.createElement('div');
          li.textContent = `@${item}`;
          li.addEventListener('mousedown', () => {
            // 触发选择回调
            if (opts.onSelectItem) {
              opts.onSelectItem(item);
            }

            // 插入提及内容
            const { tr } = view.state;
            tr.insertText(`@${item}`);
            view.dispatch(tr);
            // 移除下拉列表
            dropdown.remove();
          });
          dropdown.appendChild(li);
        });

        // 将下拉列表添加到编辑器容器
        view.dom.parentElement.appendChild(dropdown);

        // 更新装饰状态
        const newDecorations = [
          ...key.getState(view.state),
          {
            pos,
            widget: dropdown,
          },
        ];

        const newState = view.state.applyTransaction(
          view.state.tr.setMeta(key, newDecorations)
        );

        // view.updateState(newState);

        return true;
      },
    },
  });

  return plugin;
});

// 设置插件元数据
mentionsPlugin.meta = {
  displayName: 'Prose<mentionsPlugin>',
  package: '@milkdown/plugin-mentions',
};
