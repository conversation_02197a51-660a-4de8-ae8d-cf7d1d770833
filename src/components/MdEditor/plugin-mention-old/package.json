{"name": "@milkdown/plugin-mention", "type": "module", "version": "7.6.3", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/Milkdown/milkdown.git", "directory": "packages/plugins/plugin-clipboard"}, "keywords": ["milkdown", "milkdown plugin"], "sideEffects": false, "exports": {".": {"import": "./src/index.ts"}}, "main": "./src/index.ts", "publishConfig": {"main": "./lib/index.es.js", "types": "./lib/index.d.ts", "exports": {".": {"import": "./lib/index.es.js", "types": "./lib/index.d.ts"}}}, "files": ["lib", "src"], "scripts": {"start": "concurrently -n es,dts \"vite build --watch\"  \"tsc --emitDeclarationOnly --watch\"", "test": "vitest", "tsc": "tsc --noEmit && echo", "build": "tsc --emitDeclarationOnly && vite build"}, "peerDependencies": {"@milkdown/core": "^7.2.0", "@milkdown/prose": "^7.2.0"}, "dependencies": {"@milkdown/utils": "workspace:*", "tslib": "^2.8.1"}, "devDependencies": {"@milkdown/core": "workspace:*", "@milkdown/prose": "workspace:*"}, "nx": {"targets": {"build": {"outputs": ["{projectRoot}/lib"], "dependsOn": ["build"]}, "tsc": {"outputs": [], "dependsOn": ["build"]}}}}