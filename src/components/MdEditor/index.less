.wrap {
  // border: 1px solid rgb(103, 101, 101);
  // height: calc(100% - 48px);
  // padding: 8px 5px;
}

.inputWrap {
  padding: 8px 12px 0;
  // height: calc(100% - 48px);
  // overflow: auto;
  // overflow-y: auto;
  flex: 1;
}

// :global .milkdown {
//   height: 100%;
//   padding: 0;
//   color: var(--primary-text-color-1);
//   background-color: transparent;
//   font-weight: 400;
// }

:global {
  .cm-scroller {
    font-family: Consolas, Monaco, Menlo !important;
  }
  .milkdown .ProseMirror {
    pre,
    code {
      font-size: 13px;
      font-family: Consolas, Monaco, Menlo;
      color: var(--primary-text-color-1);
      background-color: var(--primary-background-color-11);
    }

    pre {
      border-radius: 8px;
      border: 1px solid var(--primary-background-color-2);
    }

    blockquote {
      padding-left: 15px;
      margin: 0;

      &::before {
        background-color: var(--primary-background-color-5);
      }
    }

    a {
      color: var(--primary-text-color-9) !important;
      word-break: break-all;
    }

    em {
      padding-right: 2px; //斜体展示不全
    }

    li {
      // height: 22px;
      line-height: 22px;
      // padding-left: 30px;
    }

    ol,
    ul {
      position: relative;
      top: -3px;
    }

    li .label-wrapper .label {
      color: var(--primary-text-color-1);

      svg {
        fill: var(--primary-text-color-1);
      }
    }
    li.ordered-item,
    li.unordered-item {
      position: relative;
      list-style: none;
      min-height: 22px;
    }
    li.ordered-item::before,
    li.unordered-item::before {
      position: absolute;
      left: 4px;
      top: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      width: auto;
      height: 22px;
      color: var(--primary-text-color-1);
    }
    li.ordered-item::before {
      content: attr(data-content);
      font-size: 16px;
    }
    li.unordered-item::before {
      content: attr(data-content);
      font-size: 18px;
    }

    table {
      width: 100%;
      overflow: scroll;
      border-collapse: separate;
      border-spacing: 0;
      border-radius: 8px;
      border: 1px solid var(--primary-background-color-12);
      background: var(--primary-background-color-6);
      tbody {
        td {
          min-width: 88px !important;
        }
        tr,
        td,
        th {
          border: 1px solid var(--primary-background-color-12);
          padding: 5px 12px;
        }

        th {
          background: rgba(206, 222, 251, 30%);
        }
      }
    }

    h1,
    h2,
    h3,
    h4,
    h5 {
      font-family: PingFangSC-Regular;
    }
  }
  .milkdown milkdown-code-block-custom .ͼ4 .cm-line {
    font-size: 13px;
    font-family: Consolas, Monaco, Menlo;
    white-space: pre-wrap;

    /* 支持换行 */
    word-wrap: break-word;

    /* 允许长单词断行 */
    overflow-wrap: break-word;
  }
  .milkdown milkdown-code-block-custom .cm-gutters {
    font-size: 13px;
    font-family: Consolas, Monaco, Menlo;
  }
  .milkdown milkdown-code-block-custom .tools .language-button {
    opacity: 1;
  }

  .milkdown li .label-wrapper .label {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .milkdown milkdown-list-item-block li {
    gap: 6px;

    .label-wrapper {
      height: 22px;
    }

    p {
      // height: 30px;
    }
  }
  .milkdown milkdown-code-block-custom .language-list-item {
    color: #abb2bf;
  }

  .milkdown .milkdown-mention {
    background: rgba(0, 88, 255, 10%);
    color: var(--primary-text-color-9);
    display: inline-block;
    height: 24px;
    border-radius: 12px;
    padding: 0 6px;
  }

  // .milkdown p:has(:global(.ProseMirror-trailingBreak)) {
  //   display: none;
  // }
}

.editorContainer {
  height: 100%;
  padding-bottom: 8px;
  display: flex;
  flex-direction: column;

  .inputFooterWrapper {
    background: var(--primary-background-color-14);
    border-radius: 8px 8px 0 0;
  }
}

.senWarp {
  :global {
    .milkdown .ProseMirror {
      overflow-x: hidden;
      blockquote {
        &::before {
          background-color: rgba(107, 152, 235, 30%);
        }
      }
      table {
        background: var(--primary-background-color-6);
      }
    }
  }
}

.mdWrap {
  :global {
    .milkdown p:has(> :global(.ProseMirror-trailingBreak):only-child) {
      display: none;
    }
  }
}
