import { createContext, ReactNode, useContext, useState } from 'react';
import { Crepe } from '@/components/Crepe';

// interface EditorContextType {
//   editor: Crepe | null;
//   setEditor: (editor: Crepe) => void;
// }

export const EditorContext = createContext<any>({});

export const useEditor = () => {
  const context = useContext(EditorContext);
  if (!context) {
    throw new Error('useEditor must be used within an EditorProvider');
  }
  return context;
};

// export const useEditorInstance = () => {
//   const { editor } = useEditor();
//   return editor;
// };
export const EditorProvider = ({ children }) => {
  const [editorInstance, setEditorInstance] = useState<Crepe | null>(null);
  return (
    <EditorContext.Provider value={{ editorInstance, setEditorInstance }}>
      {children}
    </EditorContext.Provider>
  );
};
