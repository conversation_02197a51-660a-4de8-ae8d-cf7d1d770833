import { FC } from 'react';
import { DownloadOutlined } from '@ht-icons/sprite-ui-react';
import { Tooltip } from '@ht/sprite-ui';
import { useConversationStore } from '@/store';
import useThreadState from '@/hooks/useThreadState';
import { MessageItem as MessageItemType } from '@ht/openim-wasm-client-sdk';
import threadIcon from '@/assets/channel/messageRender/thread.png';
import openViewIcon from '@/assets/channel/messageRender/openView.png';
import styles from './index.less';

interface FileFooterProps {
  message: MessageItemType;
  download: () => void;
  onClose: () => void;
  openView: () => void;
}

const FileFooter: FC<FileFooterProps> = ({
  message,
  download,
  onClose,
  openView,
}) => {
  const currentConversation = useConversationStore(
    (state) => state.currentConversation
  );
  const { openThread } = useThreadState();

  const openMessageColumn = async () => {
    await openThread({
      parentId: currentConversation?.groupID || '',
      startClientMsg: message,
    });
    onClose();
  };

  return (
    <div className={styles.fileFooterWarp}>
      <Tooltip title="下载">
        <div>
          <DownloadOutlined
            style={{
              color: '#fff',
              fontSize: '16px',
            }}
            onClick={download}
          />
        </div>
      </Tooltip>
      {/* <Tooltip title="显示子群">
        <div onClick={openMessageColumn}>
          <img src={threadIcon} />
        </div>
      </Tooltip> */}
      <Tooltip title="在新窗口打开">
        <div onClick={openView}>
          <img src={openViewIcon} />
        </div>
      </Tooltip>
      {/* <Tooltip title="更多操作">
        <div className={styles.more}>
          <div></div>
          <div></div>
          <div></div>
        </div>
      </Tooltip> */}
    </div>
  );
};
export default FileFooter;
