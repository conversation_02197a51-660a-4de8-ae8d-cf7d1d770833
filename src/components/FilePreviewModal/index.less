.filePreviewModal {
  width: 100%;
  height: 95vh;
  background: var(--primary-text-color-2);
  display: flex;
  flex-direction: column;
  .header {
    display: flex;
    width: 100%;
    height: 82px;
    justify-content: space-between;
    padding: 12px;
    .closeIcon {
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 99;
      border-radius: 8px;

      &:hover {
        background: #1d1c1dd9;
        cursor: pointer;
      }
    }
  }
  .warp {
    height: calc(100% - 134px);
    margin: 0 24px;
  }
  .footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 52px;
    padding: 12px;
  }
}

.fileHeaderWarp {
  display: flex;
  color: var(--primary-text-color-pressed);
  .info {
    font-size: 12px;
    margin-left: 10px;
    .senderNickname {
      font-size: 16px;
      line-height: 36px;
    }
  }
}

.fileFooterWarp {
  display: flex;
  align-items: center;
  > div {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-left: 12px;
    border-radius: 8px;
  }
  > div:hover {
    background-color: var(--primary-text-color-1);
  }
  .more {
    > div {
      width: 4px;
      height: 4px;
      border-radius: 50%;
      background-color: var(--primary-background-color-6);
      margin: 0 2px;
    }
  }
}

.deleteFileWarp {
  padding: 19px 24px 20px;
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 21px;
    font-size: 20px;
    font-weight: 600;
    color: var(--primary-text-color-1);
  }
  .title {
    color: var(--primary-text-color-1);
    font-weight: 400;
    font-size: 14px;
    margin-bottom: 15px;
  }
  .fileItem {
    display: flex;
    align-items: center;
    width: 100%;
    height: 65px;
    background: var(--primary-background-color-6);
    border-radius: 10px;
    border: 1px solid var(--offline-border-color);
    margin-bottom: 20px;
    .icon {
      margin: 0 12px;
      > img {
        width: 41px;
        height: 41px;
        border-radius: 8px;
      }
    }
    .fileInfo {
      width: calc(100% - 77px);
      .fileName {
        color: var(--primary-text-color-1);
        font-size: 16px;
        font-weight: 600;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .info {
        color: var(--primary-text-color);
        font-size: 12px;
        font-weight: 400;
      }
    }
  }
  .footer {
    display: flex;
    align-items: center;
    flex-direction: row-reverse;
    .deleteBtn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 136px;
      height: 36px;
      background: #d60e2f;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 600;
      color: var(--primary-background-color-6);
      cursor: pointer;
    }
    .cancelBtn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80px;
      height: 36px;
      background: #ffffff;
      border-radius: 8px;
      border: 1px solid #c6c8ca;
      font-weight: 600;
      font-size: 16px;
      color: var(--primary-text-color-1);
      cursor: pointer;
      margin-right: 12px;
    }
  }
}

.filePreviewWarp {
  background: var(--primary-background-color-17);
  border-radius: 8px;
  height: 100%;
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 16px 16px 20px;
    > div {
      display: inline-flex;
      align-items: center;
      font-size: 18px;
      font-weight: 600;
      color: var(--primary-text-color-1);
      line-height: 24px;
    }

    .deleteIcon {
      width: 28px;
      height: 28px;
      border-radius: 4px;
      // margin-left: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      cursor: pointer;

      &:hover {
        background: var(--msg-qute-backgroud-color);
      }
    }
  }
  .content {
    padding: 12px 20px 0;
    .info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .senderInfo {
        font-size: 14px;
        font-weight: 400;
        color: var(--primary-text-color-4);
        line-height: 20px;
        .senderName {
          font-size: 16px;
          font-weight: 600;
          color: var(--primary-text-color-1);
          line-height: 22px;
          margin-bottom: 4px;
        }
      }
      .btnBox {
        display: flex;
        align-items: center;
        > div {
          width: 24px;
          height: 24px;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-left: 4px;
          > img {
            width: 18px;
            height: 18px;
            cursor: pointer;
          }
        }
        > div:hover {
          background-color: var(--primary-background-color-5);
        }
      }
    }
    .messageItem {
      padding: 16px 0;
      .fileMessageItem {
        display: flex;
        width: 350px;
        height: 65px;
        padding: 0 12px;
        border-radius: 10px;
        border: 1px solid var(--offline-border-color);
        align-items: center;
        cursor: pointer;
        position: relative;
        background-color: var(--primary-background-color-6);
        .icon {
          flex-shrink: 0;
          width: 41px;
          height: 41px;
          border-radius: 8px;
          overflow: hidden;
          margin-right: 12px;
        }
        .fileInfo {
          flex: 1 1;
          display: flex;
          flex-direction: column;
          font-size: 12px;
          font-weight: 400;
          color: var(--primary-text-color);
          line-height: 17px;
          overflow: hidden;
          .fileName {
            width: 100%;
            font-size: 15px;
            font-weight: 600;
            font-family: PingFangSC-Regular;
            color: var(--primary-text-color-1);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-bottom: 2px;
          }
          .info {
            height: 17px;
          }
        }
      }
    }
  }
  .divider {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    font-weight: 400;
    color: var(--primary-text-color);
    line-height: 20px;
    padding: 0 20px;
    .line {
      width: calc(100% - 54px);
      height: 1px;
      background: var(--primary-background-color-5);
    }
  }
}

.groupInfoWarp {
  font-size: 12px;
  font-weight: 400;
  color: var(--primary-text-color);
  line-height: 17px;
  .info {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-text-color-1);
    margin-bottom: 6px;
    > img {
      width: 16px;
      height: 16px;
      margin-right: 4px;
    }
  }
}
