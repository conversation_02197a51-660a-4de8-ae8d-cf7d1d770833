import { FC } from 'react';
import { CloseOutlined } from '@ht-icons/sprite-ui-react';
import { Modal } from '@ht/sprite-ui';
import { getShowTime } from '@/utils/date';
import { MessageItem as MessageItemType } from '@ht/openim-wasm-client-sdk';
import { getFileIcon } from '../Channel/components/MessageInput/FileRender';
import styles from './index.less';

interface DeleteFileProps {
  open: boolean;
  onClose: () => void;
  deleteFile: () => void;
  message: MessageItemType;
}

const DeleteFile: FC<DeleteFileProps> = ({
  open,
  onClose,
  deleteFile,
  message,
}) => {
  const { fileElem, sendTime, senderNickname } = message;
  const { fileName = '' } = fileElem || {};
  return (
    <Modal
      title={null}
      footer={null}
      closable={false}
      centered={true}
      open={open}
      onCancel={onClose}
      width={520}
      bodyStyle={{
        padding: 0,
      }}
      style={{
        borderRadius: '8px',
        overflow: 'hidden',
      }}
      maskClosable={true}
      keyboard={true}
    >
      <div className={styles.deleteFileWarp}>
        <div className={styles.header}>
          <div>删除文件</div>
          <CloseOutlined
            style={{
              color: 'var(--primary-text-color)',
              fontSize: '16px',
              cursor: 'pointer',
            }}
            onClick={onClose}
          />
        </div>
        <div className={styles.title}>确定要永久删除此文件？</div>
        <div className={styles.fileItem}>
          <div className={styles.icon}>
            <img src={getFileIcon(fileName, 'icon')} />
          </div>
          <div className={styles.fileInfo}>
            <div className={styles.fileName} title={fileName}>
              {fileName}
            </div>
            <div className={styles.info}>
              <span>{senderNickname}</span>
              &nbsp;
              <span>{getShowTime(sendTime)}</span>
            </div>
          </div>
        </div>
        <div className={styles.footer}>
          <div className={styles.deleteBtn} onClick={deleteFile}>
            是，删除此文件
          </div>
          <div className={styles.cancelBtn} onClick={onClose}>
            取消
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default DeleteFile;
