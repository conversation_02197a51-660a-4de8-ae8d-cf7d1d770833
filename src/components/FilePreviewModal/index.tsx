import { FC, useState, useEffect } from 'react';
import dayjs from 'dayjs';
import { message as Message, Tooltip } from '@ht/sprite-ui';
import { MessageItem as MessageItemType } from '@ht/openim-wasm-client-sdk';
import deleteIcon from '@/assets/contact/deleteIcon.svg';
import downIcon from '@/assets/channel/messageRender/download.png';
import replyIcon from '@/assets/channel/reply.svg';
import { IMSDK } from '@/layouts/BasicLayout';
import ForwardModal from '@/components/ForwardModal';
import RightAreaBackBtn from '@/components/RightAreaBackBtn';
import { getFileIcon } from '../Channel/components/MessageInput/FileRender';
import { wpsFileType } from '../Channel/components/MessageItem/FileMessageRender';
import FilePreviewModal from './FileModal';
import SourceInfoItem from './SourceInfoItem';
import styles from './index.less';

interface FileDetailProps {
  onClose: () => void;
  message: MessageItemType;
}

const FileDetail: FC<FileDetailProps> = ({ onClose, message }) => {
  const { fileElem } = message;
  const { fileName = '', sourceUrl = '' } = fileElem || {};
  const [forwardModal, setForwardModal] = useState<boolean>(false);
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [open, setOpen] = useState<boolean>(false);
  const [dataInfo, setDataInfo] = useState<any>();
  const [showName, setShowName] = useState<string>('');
  const idx = fileName.lastIndexOf('.');
  const fileType = fileName.slice(idx + 1).toLowerCase();
  const isWps = wpsFileType.some((item: string) => item === fileType);

  useEffect(() => {
    setPreviewUrl('');
    if (message.groupID) {
      getGroupInfo(message.groupID);
    } else {
      getUserInfo(message.recvID);
    }
  }, [message]);

  const getGroupInfo = async (id: string) => {
    const { data } = await IMSDK.getSpecifiedGroupsInfo([id]);
    setDataInfo(data[0]);
    setShowName(data[0].groupName);
  };

  const getUserInfo = async (id: string) => {
    const { data } = await IMSDK.getUsersInfo([id]);
    setDataInfo(data[0]);
    setShowName(data[0].nickname);
  };

  const downloadFile = (url: string, name: string) => {
    if (!url) {
      Message.info('下载失败');
      return;
    }
    const link = document.createElement('a');
    link.href = url;
    link.download = name;
    link.click();
  };

  const getPreviewUrl = async () => {
    try {
      const { data } = await IMSDK.getPreviewUrl({
        fileName,
        relativeDownloadUrl: sourceUrl,
        clientMsgID: message.clientMsgID,
      });
      return data?.pcPreviewUrl || '';
    } catch (error) {
      console.error('getPreviewUrl', error);
      return '';
    }
  };

  const openModal = async () => {
    if (!isWps) {
      Message.info('该文件暂不支持预览！');
      return;
    }
    try {
      if (!previewUrl) {
        const url = await getPreviewUrl();
        if (url) {
          setPreviewUrl(url);
          setOpen(true);
        } else {
          Message.info('该文件暂不支持预览！');
        }
      } else {
        setOpen(true);
      }
    } catch (error) {
      console.error('openModal', error);
    }
  };

  const openView = async () => {
    try {
      if (!previewUrl) {
        const url = await getPreviewUrl();
        if (url) {
          setPreviewUrl(url);
          window.open(url, '_blank');
        } else {
          Message.info('该文件暂不支持预览！');
        }
      } else {
        window.open(previewUrl, '_blank');
      }
    } catch (error) {
      console.error('openView', error);
    }
  };

  return (
    <div className={styles.filePreviewWarp}>
      <div className={styles.header}>
        <div>
          <RightAreaBackBtn />
          <span>文件详情</span>
        </div>
        <div className={styles.deleteIcon} onClick={onClose}>
          <img src={deleteIcon} />
        </div>
      </div>
      <div className={styles.content}>
        <div className={styles.info}>
          <div className={styles.senderInfo}>
            <div className={styles.senderName}>{message.senderNickname}</div>
            <div>{dayjs(message.sendTime).format('YYYY-MM-DD HH:mm')}</div>
          </div>
          <div className={styles.btnBox}>
            <Tooltip title="下载">
              <div>
                <img
                  src={downIcon}
                  style={{ width: '14px', height: '14px' }}
                  onClick={() => downloadFile(sourceUrl, fileName)}
                />
              </div>
            </Tooltip>
            <Tooltip title="转发">
              <div>
                <img src={replyIcon} onClick={() => setForwardModal(true)} />
              </div>
            </Tooltip>
          </div>
        </div>
        <div className={styles.messageItem}>
          <div className={styles.fileMessageItem} onClick={openModal}>
            <div className={styles.icon}>
              <img src={getFileIcon(fileName, 'icon')} />
            </div>
            <div className={styles.fileInfo}>
              <div className={styles.fileName} title={fileName}>
                {fileName}
              </div>
              <div className={styles.info}>{getFileIcon(fileName, 'text')}</div>
            </div>
          </div>
        </div>
      </div>
      <div className={styles.divider}>
        <div>发送于</div>
        <div className={styles.line}></div>
      </div>
      <div style={{ padding: '16px 20px' }}>
        <SourceInfoItem
          isGroup={!!message.groupID}
          dataInfo={dataInfo}
          sendTime={message.sendTime}
        />
      </div>
      {forwardModal && (
        <ForwardModal
          open={forwardModal}
          onClose={() => setForwardModal(false)}
          message={message}
          isThread={false}
          isSender={false}
          inRightThread={true}
          conversationID={''}
          showName={showName}
        />
      )}
      {open && (
        <FilePreviewModal
          open={open}
          onClose={() => setOpen(false)}
          url={previewUrl}
          message={message}
          download={() => downloadFile(sourceUrl, fileName)}
          showName={showName}
          openView={openView}
        />
      )}
    </div>
  );
};
export default FileDetail;
