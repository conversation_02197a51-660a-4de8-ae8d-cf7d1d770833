import { FC, memo } from 'react';
import dayjs from 'dayjs';
import channelIcon from '@/assets/channel/channelIcon.svg';
import OIMAvatar from '@/components/OIMAvatar';
import styles from './index.less';

export const getDiffTime = (data: number) => {
  let str = '';
  const differenceDays = dayjs().diff(data, 'day');
  const differenceMonths = dayjs().diff(data, 'month');
  const differenceYears = dayjs().diff(data, 'year');
  const hour = dayjs().diff(data, 'hour');
  if (differenceYears >= 1) {
    str = `${differenceYears}年前`;
  } else if (differenceMonths >= 1) {
    str = `${differenceMonths}个月前`;
  } else if (differenceDays >= 1) {
    str = `${differenceDays}天前`;
  } else if (hour >= 1) {
    str = `${hour}小时前`;
  } else {
    const diffMinutes = dayjs().diff(data, 'minute');
    str = `${diffMinutes >= 1 ? diffMinutes : 1}分钟前`;
  }
  return str;
};

interface SourceInfoItemProps {
  dataInfo: any;
  sendTime: number;
  isGroup: boolean;
}

const SourceInfoItem: FC<SourceInfoItemProps> = ({
  dataInfo,
  sendTime,
  isGroup,
}) => {
  return (
    <div className={styles.groupInfoWarp}>
      {isGroup ? (
        <div className={styles.info}>
          <img src={channelIcon} />
          <span>{dataInfo?.groupName}</span>
        </div>
      ) : (
        <div className={styles.info}>
          <OIMAvatar
            userID={dataInfo?.userID}
            hideOnlineStatus={true}
            size={20}
          />
          <span style={{ marginLeft: '4px' }}>{dataInfo?.nickname}</span>
        </div>
      )}
      <div>{getDiffTime(sendTime)}</div>
    </div>
  );
};
export default memo(SourceInfoItem);
