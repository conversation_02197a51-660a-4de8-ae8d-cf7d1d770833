import { FC } from 'react';
import { CloseOutlined } from '@ht-icons/sprite-ui-react';
import { Modal } from '@ht/sprite-ui';
import { MessageItem as MessageItemType } from '@ht/openim-wasm-client-sdk';
import FileHeader from './FileHeader';
import FileFooter from './FileFooter';
import styles from './index.less';

interface FilePreviewModalProps {
  open: boolean;
  onClose: (e?: any) => void;
  download: () => void;
  message: MessageItemType;
  url: string;
  showName: string;
  openView: () => void;
}

const FilePreviewModal: FC<FilePreviewModalProps> = ({
  open = false,
  onClose,
  download,
  message,
  url = '',
  showName = '',
  openView,
}) => {
  return (
    <Modal
      title={null}
      footer={null}
      closable={false}
      centered={true}
      open={open}
      onCancel={onClose}
      width={window.innerWidth * 0.95}
      bodyStyle={{
        padding: 0,
      }}
      style={{
        borderRadius: '8px',
        overflow: 'hidden',
      }}
      maskClosable={true}
      keyboard={true}
    >
      <div className={styles.filePreviewModal}>
        <div className={styles.header}>
          <FileHeader message={message} showName={showName} />
          <div className={styles.closeIcon} onClick={onClose}>
            <CloseOutlined
              style={{
                color: 'var(--primary-background-color-6)',
                fontSize: '16px',
              }}
            />
          </div>
        </div>
        <div className={styles.warp}>
          <iframe src={url} style={{ width: '100%', height: '100%' }} />
        </div>
        <div className={styles.footer}>
          <div></div>
          <FileFooter
            message={message}
            download={download}
            onClose={onClose}
            openView={openView}
          />
        </div>
      </div>
    </Modal>
  );
};
export default FilePreviewModal;
