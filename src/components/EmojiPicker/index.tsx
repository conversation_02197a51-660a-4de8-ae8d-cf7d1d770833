import {
  ReactNode,
  useState,
  useImperativeHandle,
  ForwardRefRenderFunction,
  forwardRef,
  memo,
  useCallback,
} from 'react';
import _ from 'lodash';
import { Popover, message as Message } from '@ht/sprite-ui';
import { MessageItem, getSDK } from '@ht/openim-wasm-client-sdk';
import { useUserStore } from '@/store';
import Picker from '@emoji-mart/react';
import data from '@emoji-mart/data/sets/15/apple.json';
import i18n from '@emoji-mart/data/i18n/zh.json';
import { IMSDK } from '@/layouts/BasicLayout';
import styles from './index.less';

type EmojiPickerProps = {
  children: ReactNode;
  message: MessageItem;
  conversationID: string;
};

type ReactionInfo = {
  emoji: string;
  userID: string;
};

const EmojiPicker: ForwardRefRenderFunction<any, EmojiPickerProps> = (
  props: EmojiPickerProps,
  ref
) => {
  const { children, message, conversationID } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const { userID } = useUserStore.getState().selfInfo;

  useImperativeHandle(
    ref,
    () => ({
      visible,
    }),
    [visible]
  );

  const reactionClick = (item: any) => {
    const reactionList = message.attachedInfoElem?.reaction?.logs || [];
    const uniqList = _.uniqBy(reactionList, 'emoji');
    if (uniqList.length >= 36) {
      return Message.info('最多可添加36个表情');
    }
    const currentEmoji = _.filter(
      reactionList,
      (reactionItem: ReactionInfo) =>
        reactionItem.emoji === item.native && reactionItem.userID === userID
    );
    const sdk = IMSDK;
    if (currentEmoji && currentEmoji.length > 0) {
      sdk.reactMessage({
        conversationID,
        clientMsgID: message.clientMsgID,
        reaction: { emoji: item.native, action: 2 },
      });
    } else {
      sdk.reactMessage({
        conversationID,
        clientMsgID: message.clientMsgID,
        reaction: { emoji: item.native, action: 1 },
      });
    }
    setVisible(false);
  };

  const reactionChange = useCallback(
    _.throttle(reactionClick, 300, {
      leading: true,
      trailing: false,
    }),
    [message]
  );

  const renderPopoverContent = () => {
    return (
      <div className={styles.popoverContent}>
        {/* 表情列表 */}
        <Picker
          data={data}
          onEmojiSelect={(emoji: any) => {
            reactionChange(emoji);
          }}
          i18n={i18n}
          theme={'light'}
          categories={['frequent', 'people']}
          // set={'apple'}
        />
        {/* 添加表情 */}
        {/* <div className={styles.addEmojiWrapper}>
          <div className={styles.addEmojiBtn}>添加表情</div>
        </div> */}
      </div>
    );
  };

  return (
    <div>
      <Popover
        placement="topLeft"
        title={null}
        content={renderPopoverContent}
        trigger="click"
        open={visible}
        onOpenChange={(val) => {
          const reactionList = message.attachedInfoElem?.reaction?.logs || [];
          const uniqList = _.uniqBy(reactionList, 'emoji');
          if (uniqList.length >= 36) {
            Message.info('最多可添加36个表情');
          } else {
            setVisible(val);
          }
        }}
        overlayClassName={styles.emojiPickerWrapper}
        getPopupContainer={() =>
          document.getElementById('BasicLayoutId') || document.body
        }
        zIndex={1031}
      >
        {children}
      </Popover>
    </div>
  );
};

export default memo(forwardRef(EmojiPicker));
