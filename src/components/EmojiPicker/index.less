.emojiPickerWrapper {
  :global {
    .linkflow-popover-arrow {
      display: none;
    }
    .linkflow-popover-inner {
      width: 352px;
      background-color: var(--link-color-base-pry);
      box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 3%);
      border-radius: 8px;
      border: 1px solid var(--primary-background-color-5);
      padding: 0;
      overflow: hidden;
    }

    .linkflow-popover-inner-content {
      padding: 0;
    }
  }

  .popoverContent {
    .addEmojiWrapper {
      height: 60px;
      background: rgba(var(--link-color-plt-gray-0), 1);
      padding: 16px 20px;
      display: flex;
      justify-content: flex-end;

      .addEmojiBtn {
        width: 78px;
        height: 28px;
        background: var(--link-color-base-pry);
        border-radius: 6px;
        border: 1px solid var(--link-color-otl-sec);
        font-size: 13px;
        font-weight: 600;
        color: var(--link-color-content-pry);
        line-height: 28px;
        text-align: center;
        cursor: not-allowed;
      }
    }
    em-emoji-picker {
      border-radius: 0;
      box-shadow: none;
      height: 400px;
    }
  }
}
