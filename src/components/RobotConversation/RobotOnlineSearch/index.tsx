import { FC, memo, useEffect, useState } from 'react';
import onlineSearchIcon from '@/assets/channel/robotConversationList/onlineSearch.svg';
import closeIcon from '@/assets/channel/robotConversationList/close.svg';
import RightAreaBackBtn from '@/components/RightAreaBackBtn';
import classNames from 'classnames';
import styles from '../RobotAnswerSource/index.less';

interface sourceItem {
  index: number;
  title: string;
  url: string;
  content: string;
}

interface RobotOnlineSearchProps {
  onClose: () => void;
  data: sourceItem[];
  activeValue?: sourceItem;
}

const RobotOnlineSearch: FC<RobotOnlineSearchProps> = ({
  onClose,
  data = [],
  activeValue,
}) => {
  const [selectObj, setSelectObj] = useState<sourceItem | undefined>(undefined);

  useEffect(() => {
    setSelectObj(activeValue || undefined);
  }, [activeValue]);

  const openNewWindow = (url: string) => {
    window.open(url, '_blank');
  };

  return (
    <div className={styles.robotAnswerSource}>
      <div className={styles.answerSourceHeader}>
        <div className={styles.answerSourceTitle}>
          <RightAreaBackBtn />
          <img src={onlineSearchIcon} />
          <span>联网搜索</span>
        </div>
        <div className={styles.closeIcon}>
          <img src={closeIcon} onClick={onClose} />
        </div>
      </div>
      <div className={styles.warp}>
        {data.map((item: sourceItem) => {
          return (
            <div
              className={classNames(
                styles.item,
                selectObj && selectObj.index === item.index && styles.active
              )}
              key={item.index}
              onClick={() => openNewWindow(item.url)}
            >
              <div className={styles.header}>
                <div className={styles.sourceName}>
                  <span>{item.title}</span>
                </div>
                <div className={styles.serialNumber}>{item.index}</div>
              </div>
              <div className={styles.content}>{item.content}</div>
              <div className={styles.url}>{item.url}</div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
export default RobotOnlineSearch;
