import { FC, memo, useMemo } from 'react';
import dayjs from 'dayjs';
import isYesterday from 'dayjs/plugin/isYesterday';
import { Popover, Spin } from '@ht/sprite-ui';
import { shallow } from 'zustand/shallow';
import {
  ConversationItem,
  MessageItem,
  MessageType,
} from '@ht/openim-wasm-client-sdk';
import { isCurrentWeek } from '@/utils/date';
import { parserMdToText } from '@/utils/parserMdToHtml';
import { useConversationStore } from '@/store';
import closeIcon from '@/assets/channel/robotConversationList/close.svg';
import addIcon from '@/assets/channel/robotConversationList/add.svg';
import historyIcon from '@/assets/channel/robotConversationList/history.svg';
import moreIcon from '@/assets/channel/robotConversationList/more.svg';
import RightAreaBackBtn from '@/components/RightAreaBackBtn';
import classNames from 'classnames';
import styles from './index.less';

dayjs.extend(isYesterday);

const getTime = (date: any) => {
  if (!date) {
    return;
  }
  const currentDate = dayjs(date);
  const today = dayjs();
  if (currentDate.isSame(today, 'day')) {
    return '今天';
  } else if (currentDate.isYesterday()) {
    return '昨天';
  } else {
    return currentDate.format('YYYY年MM月DD日');
  }
};

const getShowText = (str: string) => {
  if (!str) {
    return '-';
  }
  const msg: MessageItem = JSON.parse(str);
  let text = msg.textElem?.content || '';
  let flag = true;
  if (msg.contentType === MessageType.CustomMessage) {
    let content: any = {};
    try {
      content = JSON.parse(msg?.customElem?.data || '{}');
    } catch (e) {
      content = msg?.customElem?.data || {};
    }
    if (content?.content?.error) {
      text =
        content?.content?.error.code === '999'
          ? '抱歉，系统开小差了，请稍后重试'
          : content?.content?.error.msg;
    } else if (content?.content?.answer || content?.content?.answer === '') {
      text = content?.content?.answer || '';
    } else {
      flag = false;
      text = content?.content || content?.answer || content || '';
    }
  }
  if (msg.contentType === MessageType.FileMessage) {
    return '[文件]';
  }
  if (msg.contentType === MessageType.PictureMessage) {
    return '[图片]';
  }
  return `${flag ? parserMdToText(text) : text}`;
};

interface RobotConversationListProps {
  onClose: () => void;
}

const RobotConversationList: FC<RobotConversationListProps> = ({ onClose }) => {
  const currentMultiSessionIniting = useConversationStore(
    (state) => state.currentMultiSessionIniting
  );
  const currentMultiSessionList = useConversationStore(
    (state) => state.currentMultiSessionList
  );
  const currentMultiSession = useConversationStore(
    (state) => state.currentMultiSession
  );
  const updateCurrentMultiSession = useConversationStore(
    (state) => state.updateCurrentMultiSession
  );
  const createCurrentMultiSession = useConversationStore(
    (state) => state.createCurrentMultiSession
  );

  const robotConversation = useMemo(() => {
    const weekData: ConversationItem[] = [];
    const oldData: ConversationItem[] = [];
    let flag = false;
    currentMultiSessionList?.map((item: ConversationItem) => {
      if (!flag && isCurrentWeek(item.latestMsgSendTime)) {
        weekData.push(item);
      } else {
        flag = true;
        oldData.push(item);
      }
      return item;
    });
    return {
      weekData,
      oldData,
    };
  }, [currentMultiSessionList]);

  const renderItem = (data: ConversationItem[], type: 'week' | 'old') => {
    return (
      <div style={type === 'week' ? {} : { padding: '8px 0' }}>
        <div className={styles.date}>{type === 'week' ? '本周' : '以前'}</div>
        {data.map((item: ConversationItem) => {
          return (
            <div
              className={classNames(
                styles.conversationItem,
                item.conversationID === currentMultiSession?.conversationID &&
                  styles.active
              )}
              key={item.conversationID}
              onClick={() => updateCurrentMultiSession(item)}
            >
              {/* <Popover
                content={
                  <div className={styles.menuContent}>
                    <div className={styles.menuItem}>删除会话记录</div>
                  </div>
                }
                overlayClassName={styles.menuWrap}
                placement="bottomRight"
              >
                <div className={styles.more}>
                  <img src={moreIcon} />
                </div>
              </Popover> */}
              <div className={styles.headerBox}>
                <div className={styles.title}>{getShowText(item.subName)}</div>
                <div className={styles.dateBox}>
                  {getTime(item.latestMsgSendTime)}
                </div>
              </div>
              <div className={styles.content}>
                {getShowText(item.latestMsg)}
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div className={styles.robotConversationListContainer}>
      <div className={styles.robotConversationListHeader}>
        <div className={styles.robotConversationListTitle}>
          <RightAreaBackBtn />
          <span>对话记录</span>
        </div>
        <div className={styles.closeIcon}>
          <img src={closeIcon} onClick={onClose} />
        </div>
      </div>
      <div
        className={styles.createConversation}
        onClick={() => createCurrentMultiSession()}
      >
        <img src={addIcon} />
        <div>新建会话</div>
      </div>
      <div className={styles.line}></div>
      <div className={styles.historyTitle}>
        <img src={historyIcon} />
        <div>历史会话</div>
      </div>
      {/* <Spin spinning={currentMultiSessionIniting}> */}
      {currentMultiSessionList && currentMultiSessionList.length > 0 ? (
        <div className={styles.historyList}>
          {robotConversation?.weekData && robotConversation.weekData.length > 0
            ? renderItem(robotConversation.weekData, 'week')
            : ''}
          {robotConversation?.oldData && robotConversation.oldData.length > 0
            ? renderItem(robotConversation.oldData, 'old')
            : ''}
        </div>
      ) : (
        <div className={styles.emptyWarp}>
          <div>暂无会话记录</div>
        </div>
      )}
      {/* </Spin> */}
    </div>
  );
};
export default memo(RobotConversationList);
