import { FC, memo, useEffect, useState } from 'react';
import sourceIcon from '@/assets/channel/robotConversationList/answerSource/source.svg';
import closeIcon from '@/assets/channel/robotConversationList/close.svg';
import pictureIcon from '@/assets/channel/robotConversationList/answerSource/picture.svg';
import xmlIcon from '@/assets/channel/robotConversationList/answerSource/xml.svg';
import htmlIcon from '@/assets/channel/robotConversationList/answerSource/html.svg';
import txtIcon from '@/assets/channel/robotConversationList/answerSource/txt.svg';
import otherIcon from '@/assets/channel/robotConversationList/answerSource/other.svg';
import { getDocIcon, docTypeEnum } from '@/utils/utils';
import { canSendImageTypeList } from '@/utils/common';
import classNames from 'classnames';
import { Modal } from '@ht/sprite-ui';
import { RenderMd } from '@/components/MdEditor';
import { processImageUrls } from '@/components/Channel/components/MessageItem/StreamMessage';
import RightAreaBackBtn from '@/components/RightAreaBackBtn';
import styles from './index.less';

const getFileIcon = (fileName: string) => {
  try {
    const idx = fileName.lastIndexOf('.');
    const fileType = fileName.slice(idx + 1).toLowerCase();
    const isImage = canSendImageTypeList.includes(fileType);
    if (isImage) {
      return pictureIcon;
    }
    switch (fileType) {
      case 'doc':
        return getDocIcon(docTypeEnum.WORD);
      case 'docx':
        return getDocIcon(docTypeEnum.WORD);
      case 'xls':
        return getDocIcon(docTypeEnum.EXCEL);
      case 'xlsx':
        return getDocIcon(docTypeEnum.EXCEL);
      case 'ppt':
        return getDocIcon(docTypeEnum.PPT);
      case 'pptx':
        return getDocIcon(docTypeEnum.PPT);
      case 'pdf':
        return getDocIcon(docTypeEnum.PDF);
      case 'xmind':
        return getDocIcon(docTypeEnum.MINDMAPNEW);
      case 'md':
        return getDocIcon(docTypeEnum.MARKDOWN);
      case 'txt':
        return txtIcon;
      case 'xml':
        return xmlIcon;
      case 'html':
        return htmlIcon;
      default:
        return otherIcon;
    }
  } catch (error) {
    return otherIcon;
  }
};

interface sourceItem {
  index: number;
  score: number;
  doc_name: string;
  doc_type: number;
  doc_url: string;
  content: string;
}

interface RobotAnswerSourceProps {
  onClose: () => void;
  data: sourceItem[];
  activeValue?: sourceItem;
}

const RobotAnswerSource: FC<RobotAnswerSourceProps> = ({
  onClose,
  data = [],
  activeValue,
}) => {
  const [open, setOpen] = useState<boolean>(false);
  const [selectObj, setSelectObj] = useState<sourceItem | undefined>(undefined);

  useEffect(() => {
    setSelectObj(activeValue || undefined);
  }, [activeValue]);

  const openNewWindow = (url: string) => {
    url && window.open(url, '_blank');
  };

  const openModal = (obj: sourceItem) => {
    setSelectObj(obj);
    setOpen(true);
  };

  const handleCancel = () => {
    setSelectObj(undefined);
    setOpen(false);
  };

  return (
    <div className={styles.robotAnswerSource}>
      <div className={styles.answerSourceHeader}>
        <div className={styles.answerSourceTitle}>
          <RightAreaBackBtn />
          <img src={sourceIcon} />
          <span>回答来源</span>
        </div>
        <div className={styles.closeIcon}>
          <img src={closeIcon} onClick={onClose} />
        </div>
      </div>
      <div className={styles.warp}>
        {data.map((item: sourceItem) => {
          return (
            <div
              className={classNames(
                styles.item,
                selectObj && selectObj.index === item.index && styles.active
              )}
              key={item.index}
            >
              <div className={styles.header}>
                <div className={styles.sourceName}>
                  <img src={getFileIcon(item.doc_name)} />
                  <span
                    className={styles.docName}
                    onClick={() => openNewWindow(item.doc_url)}
                  >
                    {item.doc_name}
                  </span>
                </div>
                <div className={styles.serialNumber}>{item.index}</div>
              </div>
              <div className={styles.content} onClick={() => openModal(item)}>
                {processImageUrls(item.content)}
              </div>
            </div>
          );
        })}
      </div>
      <Modal
        open={open}
        onCancel={() => handleCancel()} // 取消按钮回调
        footer={null}
        centered={true}
        closable={false}
        destroyOnClose={true}
        width={720}
        className={styles.answerSourceModal}
      >
        <div className={styles.answerSourceModalWarp}>
          <div className={styles.header}>
            <div>回答来源</div>
            <img src={closeIcon} onClick={handleCancel} />
          </div>
          <div className={styles.content}>
            <div className={styles.docName}>
              <img src={getFileIcon(selectObj?.doc_name || '')} />
              <span>{selectObj?.doc_name}</span>
            </div>
            <div className={styles.box}>
              <RenderMd
                id={`${selectObj?.score || ''}_RobotAnswerSourceModal`}
                value={processImageUrls(selectObj?.content || '')}
              />
            </div>
          </div>
        </div>
      </Modal>
    </div>
  );
};
export default RobotAnswerSource;
