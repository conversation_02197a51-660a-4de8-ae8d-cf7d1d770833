.robotAnswerSource {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--primary-background-color-17);
  .answerSourceHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 16px 24px;
    .answerSourceTitle {
      display: flex;
      align-items: center;
      font-size: 18px;
      font-weight: 600;
      line-height: 28px;
      color: var(--primary-text-color-1);
      > img {
        width: 28px;
        height: 28px;
        margin-right: 4px;
      }
    }
    .closeIcon {
      width: 28px;
      height: 28px;
      cursor: pointer;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .warp {
    flex: 1 1;
    padding: 0 20px 16px;
    overflow-y: auto;
    .item {
      padding: 12px;
      background: var(--primary-background-color-6);
      border-radius: 8px;
      margin-bottom: 8px;
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 6px;
        .sourceName {
          flex: 1 1;
          display: flex;
          align-items: center;
          font-size: 15px;
          font-weight: 600;
          color: var(--primary-text-color-1);
          line-height: 22px;
          overflow: hidden;
          > img {
            width: 24px;
            height: 24px;
            margin-right: 8px;
          }
          > span {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .docName:hover {
            // text-decoration: underline;
          }
        }
        .serialNumber {
          height: 17px;
          font-size: 12px;
          font-weight: 400;
          color: var(--primary-text-color-1);
          line-height: 17px;
          padding: 0 5px;
          border-radius: 12px;
          background-color: color-mix(
            in srgb,
            var(--tab-actived-background-color),
            transparent 70%
          );
          margin-left: 10px;
        }
      }
      .content {
        font-size: 13px;
        font-weight: 400;
        color: var(--primary-text-color-3);
        line-height: 20px;
        word-break: break-all;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3; /* 控制显示的行数 */
        overflow: hidden;
        text-overflow: ellipsis;
        margin-bottom: 4px;
        cursor: pointer;
      }
      .url {
        font-size: 13px;
        font-weight: 400;
        color: var(--primary-text-color-3);
        line-height: 18px;
        word-break: break-all;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      &:hover {
        background: var(--primary-background-color-15);
        .docName {
          // color: var(--primary-text-color-9);
        }
      }
    }
    .active {
      background: var(--tab-actived-background-color);
    }
  }
}

.answerSourceModal {
  .answerSourceModalWarp {
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 18px;
      font-weight: 600;
      line-height: 28px;
      color: var(--primary-text-color-1);
      margin-bottom: 20px;
      > img {
        width: 28px;
        height: 28px;
        cursor: pointer;
      }
    }
    .content {
      max-height: calc(90vh - 100px);
      overflow: auto;
      .docName {
        display: flex;
        align-items: center;
        font-size: 15px;
        font-weight: 600;
        color: var(--primary-text-color-1);
        line-height: 22px;
        overflow: hidden;
        margin-bottom: 16px;
        > img {
          width: 24px;
          height: 24px;
          margin-right: 8px;
        }
        > span {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .box {
        background: rgba(0, 116, 226, 6%);
        border-radius: 8px;
        font-size: 15px;
        font-weight: 400;
        color: var(--primary-text-color-1);
        line-height: 22px;
        padding: 12px;
      }
    }
  }
  :global {
    .linkflow-modal-content {
      border-radius: 8px;
    }
    .linkflow-modal-body {
      padding: 24px;
    }
  }
}
