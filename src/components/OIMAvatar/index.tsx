import { AvatarProps } from '@ht/sprite-ui';
import * as React from 'react';
import { memo } from 'react';
import classnames from 'classnames';
import {
  getAvatarUrl,
  getRandomDefaultAvatar,
  insertNewRandomUserIdList,
  insertNewValidUserIdList,
} from '@/utils/avatar';
import useOnlineStatus from '@/hooks/useOnlineStatus';
import { OnlineState } from '@ht/openim-wasm-client-sdk';
import { useUserStore } from '@/store';
import styles from './index.less';

interface IOIMAvatarProps extends AvatarProps {
  src?: string;
  size?: number;
  userID?: string;
  borderRadius?: number;
  stateSize?: number;
  stateRight?: number;
  stateBottom?: number;
  hideOnlineStatus?: boolean;
  style?: React.CSSProperties;
}

export const OnlineStatusComponent = ({
  userID,
  stateSize,
}: {
  userID: string;
  stateSize: number;
}) => {
  const { onlineState } = useOnlineStatus(userID);

  return (
    <div
      className={classnames(
        styles.onLineState,
        onlineState?.status === OnlineState.Online
          ? styles.online
          : styles.offline
      )}
      style={{
        width: `${stateSize}px`,
        height: `${stateSize}px`,
      }}
    ></div>
  );
};

const getBorderRadiusBySize = (size: number) => {
  if (size <= 36) {
    return 2;
  } else if (size > 36 && size < 72) {
    return 4;
  } else if (size >= 72) {
    return 8;
  }
  return 4;
};
const OIMAvatar: React.FC<IOIMAvatarProps> = (props) => {
  const {
    src,
    size = 42,
    userID,
    borderRadius = getBorderRadiusBySize(size),
    stateSize = 6,
    stateRight = -3,
    stateBottom = -3,
    hideOnlineStatus = false,
    style,
  } = props;

  const { selfInfo } = useUserStore();

  return (
    <div style={{ position: 'relative', ...style }}>
      <div
        style={{
          width: `${size ? size : ''}px`,
          height: `${size ? size : ''}px`,
          borderRadius: `${borderRadius}px`,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          fontSize: '12px',
          color: '#fff',
        }}
      >
        <img
          src={
            userID === selfInfo.userID ? selfInfo.faceURL : getAvatarUrl(userID)
          }
          onLoad={(e) => {
            const target = e.target as HTMLImageElement;
            setTimeout(() => (target.style.transition = ''), 0);
            insertNewValidUserIdList(userID);
          }}
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = getRandomDefaultAvatar(userID);
            insertNewRandomUserIdList(userID);
          }}
          style={{
            width: '100%',
            height: '100%',
            borderRadius: `${borderRadius}px`,
          }}
          decoding="async" // 异步解码
          loading="lazy" // 延迟加载（非可视区图片）
          alt=""
        />
      </div>
      {!hideOnlineStatus && userID != null && userID !== '' && (
        <div
          style={{
            borderRadius: '100%',
            right: `${stateRight}px`,
            bottom: `${stateBottom}px`,
            position: `absolute`,
            border: '2px solid #fff',
            background: '#fff',
          }}
        >
          <OnlineStatusComponent userID={userID} stateSize={stateSize} />
        </div>
      )}
    </div>
  );
};

export default memo(OIMAvatar);
