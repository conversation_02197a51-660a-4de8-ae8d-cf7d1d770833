// src/components/GlobalSearchModal/index.tsx
import { useEffect, useState } from 'react';
import { useHistory } from 'react-router-dom';
import {
  SessionType,
  EmployeeItem,
  ConversationItem,
} from '@ht/openim-wasm-client-sdk';
import { IMSDK } from '@/layouts/BasicLayout';
import { throttle, filter, isEmpty } from 'lodash';
import { useConversationToggle } from '@/hooks/useConversationToggle';
import { useConversationStore, useSearchInfoStore } from '@/store';
import { searchGroupConversationByInputValue } from '@/utils/utils';
import GlobalSearchModalUI, {
  ClickType,
} from './components/GlobalSearchModalUI';

interface GlobalSearchProps {
  open: boolean;
  onClose: () => void;
  width?: number;
}

export type GroupItemType = {
  groupID: string;
  showName: string;
  nickname?: string;
  keyword?: string;
};

const GlobalSearchModal = ({ open, onClose, width }: GlobalSearchProps) => {
  const [searchedFriends, setSearchedFriends] = useState<EmployeeItem[]>([]);
  const [searchedGroup, setSearchedGroup] = useState<GroupItemType[]>([]);
  const { toSpecifiedConversation } = useConversationToggle();
  const navigate = useHistory();
  const { searchValue } = useSearchInfoStore();

  const triggerSearch = (keyword: string) => {
    if (!keyword) {
      return;
    }
    setSearchedFriends([]);
    setSearchedGroup([]);
    searchFriend(keyword);
    searchGroup(keyword);
  };
  const throttledTriggerSearch = throttle(triggerSearch, 300);

  const searchFriend = async (val: string) => {
    const pageSize = 10;
    try {
      if (!val) {
        return;
      }
      const { data } = await IMSDK.searchEmployeeListPage(val, 0, pageSize);
      setSearchedFriends(data.list || []);
    } catch (e) {
      console.error('搜索人员失败', e);
      setSearchedFriends([]);
    }
  };

  const searchGroup = async (keyword: string) => {
    const groupData = await searchGroupConversationByInputValue(keyword);

    setSearchedGroup(groupData);
  };

  const handleClear = () => {
    setSearchedFriends([]);
    setSearchedGroup([]);
  };

  useEffect(() => {
    if (open) {
      throttledTriggerSearch(searchValue);
    }
  }, [open]);

  if (!open) {
    return null;
  }

  const handleClickItem = (type: ClickType, item: any) => {
    toSpecifiedConversation({
      sourceID: type === 'group' ? item.groupID : item.employeeID,
      sessionType: type === 'group' ? SessionType.Group : SessionType.Single,
    });
    onClose();
  };

  return (
    <GlobalSearchModalUI
      open={open}
      width={width}
      searchedList={{
        groupList: searchedGroup,
        memberList: searchedFriends,
      }}
      onClear={handleClear}
      onSearch={throttledTriggerSearch}
      onClose={onClose}
      onClickItem={handleClickItem}
    />
  );
};

export default GlobalSearchModal;
