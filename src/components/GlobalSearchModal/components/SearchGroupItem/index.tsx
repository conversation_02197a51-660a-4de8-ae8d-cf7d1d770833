/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable react/no-danger */
import React from 'react';
import { isEmpty } from 'lodash';
import groupIcon from '@/assets/coversation/group.svg';
import { GroupItemType } from '../..';
import styles from './index.less';

interface Props {
  group: GroupItemType;
}

export const handleHighLight = (keyword = '', nickname = '') => {
  return nickname.replace(
    // eslint-disable-next-line no-eval
    eval(`/${keyword}/gi`),
    `<font style='background-color:#FFF5DA'>${keyword}</font>`
  );
};

const SearchGroupItem: React.FC<Props> = ({ group }) => {
  return (
    <div className={styles.searchGroupItem}>
      <img src={groupIcon} style={{ width: '20px', borderRadius: '6px' }} />
      <span className={styles.groupName}>{group.showName}</span>
      {!isEmpty(group.nickname) && !isEmpty(group.nickname) && (
        <span className={styles.nicknameBox}>
          包含:
          <span
            dangerouslySetInnerHTML={{
              __html: handleHighLight(group.keyword, group.nickname),
            }}
            className={styles.nickname}
          ></span>
        </span>
      )}
    </div>
  );
};
export default SearchGroupItem;
