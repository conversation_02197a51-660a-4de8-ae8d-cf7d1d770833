.searchMemberItem {
  display: flex;
  flex-grow: 1;
  height: 40px;
  font-weight: bold;
  outline: none;
  align-items: center;
  margin: 0;
  padding: 4px 10px;
  line-height: 30px;
  font-size: 15px;
  cursor: pointer;
  .contentContainer {
    margin: 0 10px;
    display: flex;
    align-items: center;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--primary-text-color-7);

    .tag {
      padding: 0 4px;
      height: 16px;
      border-radius: 4px;
      font-size: 11px;
      display: flex;
      align-items: center;
      margin-left: 12px;
      line-height: 16px;
    }

    .tag_3 {
      color: #cc8521;
      background: #fcf3e6;
      border: 1px solid #cc8521;
    }
    .senderNickname {
      font-weight: 600;
      color: var(--link-color-content-pry);
    }
  }
  &:hover {
    background-color: var(--msg-qute-backgroud-color);
    border-radius: 6px;
  }
}
