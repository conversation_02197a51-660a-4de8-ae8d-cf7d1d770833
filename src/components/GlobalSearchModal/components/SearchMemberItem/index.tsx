import { EmployeeItem } from '@ht/openim-wasm-client-sdk';
import React from 'react';
import OIMAvatar, { OnlineStatusComponent } from '@/components/OIMAvatar';
import { isBotUser } from '@/utils/avatar';
import classNames from 'classnames';
import styles from './index.less';

interface Props {
  member: EmployeeItem;
}

const SearchMemberItem: React.FC<Props> = ({ member }) => {
  return (
    <div className={styles.searchMemberItem}>
      <OIMAvatar userID={member.employeeID} size={20} hideOnlineStatus={true} />
      <div className={styles.contentContainer}>
        <span className={styles.senderNickname}>{member.employeeName}</span>
        {isBotUser(member.employeeID) ? (
          <span className={classNames(styles.tag, styles.tag_3)}>机器人</span>
        ) : (
          <span>&nbsp;&nbsp;{member.employeeCode}</span>
        )}
      </div>
      <OnlineStatusComponent
        userID={member.employeeID}
        stateSize={8}
        stateRight={0}
        isPosition={false}
      />
    </div>
  );
};
export default SearchMemberItem;
