.modalContainer {
  position: absolute;
  top: 4px;
  // warning 注意同父元素层级的input的margin-left属性保持一致，如果左侧菜单可拖动，此处也要改
  // 搜索框弹窗比原搜索框宽16px
  left: 30vw;
  :global {
    .linkflow-modal-content {
      border-radius: 6px;
      box-shadow: 0 2px 4px 2px rgba(0, 0, 0, 4%);
    }

    .linkflow-modal-body {
      padding: 0;
      border-radius: 6px;
      border: 1px solid var(--primary-background-color-5);
    }

    .linkflow-modal-close-x {
      width: 28px;
      height: 28px;
      right: 18px;
      top: 13px;
    }

    .linkflow-modal-close-icon {
      margin: 0;
      height: 20px;
      font-size: 19px;
      align-items: flex-end;
    }
  }
  .searchInputAndClose {
    border-bottom: 1px solid var(--primary-background-color-5);
    display: flex;
    flex-direction: row;
    flex: none;
    justify-content: stretch;
    width: 100%;

    .searchInputContent {
      width: calc(100% - 46px);
      height: 54px;
      display: flex;
      justify-content: center;
      align-items: center;
      padding-right: 8px;
      :global {
        .linkflow-input {
          padding: 0;
          border: none;
          height: 100%;
        }
      }
      .iconContainer {
        color: rgba(97, 96, 97, 100%);
        align-self: center;
        margin-left: 20px;
        margin-right: 12px;
        position: relative;
        line-height: 54px;
      }

      .inputWrapper {
        flex: 1;
        position: relative;
        display: flex;
        align-items: center;
        overflow: hidden;

        .inputDiv {
          flex: 1;
          &:focus-visible {
            outline: none;
          }
        }

        .placeholderWrapper {
          position: absolute;
          left: 0;
          top: 0;
          font-size: 15px;
          text-overflow: ellipsis;
          white-space: nowrap;
          color: rgba(29, 28, 29, 100%);
          opacity: 0.5;
          pointer-events: none;
          font-style: normal;
        }
      }

      .clearBtn {
        border-right: 1px solid rgba(221, 221, 221, 100%);
        cursor: pointer;
        color: var(--primary-text-color-1);
        padding: 0 18px;
        word-break: keep-all;
        line-height: 16px;
      }
    }
  }

  .scrollBox {
    max-height: 60vh;
    overflow-y: auto;
    // margin: 8px 0;
  }

  .listwrapper {
    padding: 8px;

    .listItem {
      display: flex;
      align-items: center;
      min-height: 40px;
      padding: 4px 8px;
      background-color: var(--link-color-base-pry);
      color: var(--link-color-content-pry);
      cursor: pointer;

      .showName {
        margin-left: 12px;
        font-size: 16px;
        font-weight: bold;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .blueBox {
        margin-left: 5px;
        color: var(--link-color-content-pry);
        background-color: var(--primary-background-color-12);
        font-size: 15px;
      }

      &:hover {
        background-color: var(--msg-qute-backgroud-color);
        border-radius: 6px;
      }
    }
  }
  .netErrorWarning {
    display: flex;
    align-items: center;
    height: 39px;
    background: #f8f8f8;
    border-radius: 0 0 8px 8px;
    padding: 0 29px;
    img {
      width: 12px;
      height: 12px;
      margin-right: 4px;
    }

    span {
      font-size: 13px;
      font-weight: 400;
      color: #6c6f76;
      line-height: 18px;
    }
  }
  .groupsAndMembersBox {
    border-top: 1px solid rgba(221, 221, 221, 100%);
    padding: 8px;
  }

  .inputBeforeWrapper {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
  }

  .blueBox {
    word-break: keep-all;
    white-space: nowrap;
    color: var(--primary-text-color-9);
    border-radius: 4px;
    padding: 0 4px;
    background-color: #ebf0f8;
    line-height: 24px;
    overflow: hidden;
    flex-wrap: nowrap;
    text-overflow: ellipsis;
    display: block;
    max-width: 200px;
    font-weight: 600;
    font-size: 14px;
  }
}
