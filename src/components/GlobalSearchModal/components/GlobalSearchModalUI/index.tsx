import { useState, useRef, useEffect } from 'react';
import _, { isEmpty } from 'lodash';
import classNames from 'classnames';
import { useHistory } from 'react-router-dom';
import { Input, Empty, Modal } from '@ht/sprite-ui';
import { SessionType, EmployeeItem } from '@ht/openim-wasm-client-sdk';
import {
  useConversationStore,
  useSearchInfoStore,
  useUserStore,
} from '@/store';
import { ConversationItem } from '@ht/openim-wasm-client-sdk/lib/types/entity';
import closeIcon from '@/assets/closeIcon.svg';
import searchIcon from '@/assets/images/searchModal/searchIcon.svg';
import listSearchIcon from '@/assets/images/searchModal/listSearch.svg';
import inputSearchIcon from '@/assets/images/searchModal/inputSearch.svg';
import offlineIcon from '@/assets/images/searchModal/offlineIcon.png';
import SearchGroupItem from '../SearchGroupItem';
import SearchMemberItem from '../SearchMemberItem';
import { GroupItemType } from '../..';
import styles from './index.less';

export type ClickType = 'group' | 'member';
interface Props {
  open: boolean;
  searchedList: { groupList: GroupItemType[]; memberList: EmployeeItem[] };
  onClear: () => void;
  onSearch: (value: string) => void;
  onClose: () => void;
  onClickItem: (type: ClickType, item: GroupItemType | EmployeeItem) => void;
  width?: number;
}

const GlobalSearchModalUI: React.FC<Props> = ({
  searchedList,
  onClear,
  onSearch,
  onClose,
  onClickItem,
  open,
  width,
}) => {
  const inputRef = useRef<HTMLInputElement | null>(null);
  const { groupList, memberList } = searchedList;
  const [lock, setLock] = useState<boolean>(false);
  const navigate = useHistory();

  const currentConversation = useConversationStore(
    (state) => state.currentConversation
  );
  const { searchValue, updateSearchInfo, searchConversationInfo } =
    useSearchInfoStore();

  const [inputvalue, setInputValue] = useState<any>(searchValue);
  const [searchConversation, setSearchConversation] =
    useState<ConversationItem | null>(searchConversationInfo);

  const { syncState, connectState } = useUserStore();
  const showConnecting =
    syncState === 'success' &&
    (connectState === 'loading' || connectState === 'failed');

  useEffect(() => {
    if (open && inputRef.current) {
      inputRef.current.focus();
    }
  }, [open]);

  const handleInputChange = (e: any) => {
    if (e.type === 'compositionstart') {
      setLock(true);
      return;
    }
    setInputValue(e.target.value.trim());
    if (e.type === 'compositionend') {
      onSearch(e.target.value.trim());
      setLock(false);
    }
    if (!lock) {
      onSearch(e.target.value.trim());
    }
  };

  const handleInputKeyDown = (e: any) => {
    if (e.key === 'Backspace' || e.key === 'Delete') {
      const cursorPosition = e.target.selectionStart;
      if (cursorPosition === 0 && searchConversation?.showName) {
        setSearchConversation(null);
      }
    }
  };

  const handleClear = () => {
    setInputValue('');
    setSearchConversation(null);
    onClear();
  };

  const handleClickItem = (
    type: ClickType,
    item: GroupItemType | EmployeeItem
  ) => {
    onClickItem(type, item);
  };

  const renderInputBefore = () => {
    return (
      <div className={styles.inputBeforeWrapper}>
        <span>in:</span>
        <div className={styles.blueBox}>
          {searchConversation?.conversationType === SessionType.Group
            ? ``
            : `@`}
          {searchConversation?.showName}
        </div>
      </div>
    );
  };

  const onTargetSearchPage = (hasCurrentConversation: boolean) => {
    onClose();
    if (hasCurrentConversation) {
      updateSearchInfo({
        searchConversationInfo: currentConversation,
        searchValue: inputvalue,
      });
      navigate.push(`./search`);
    } else {
      updateSearchInfo({
        searchValue: inputvalue,
        searchConversationInfo: null,
      });
      navigate.push(`./search`);
    }
  };

  const handleUpdateNewSearchInfo = () => {
    onClose();
    updateSearchInfo({
      searchConversationInfo: searchConversation,
      searchValue: inputvalue,
    });
    navigate.push(`./search`);
  };

  const handleClearSearchInfo = () => {
    onClose();
    updateSearchInfo({
      searchValue: inputvalue,
      searchConversationInfo: searchConversation,
    });
  };

  return (
    <Modal
      open={open}
      footer={null}
      className={styles.modalContainer}
      onCancel={() => {
        handleClearSearchInfo();
      }}
      mask={false}
      maskClosable={true}
      width={'40vw'}
      getContainer={document.getElementById('BasicLayoutId') || document.body}
      closeIcon={<img src={closeIcon} />}
    >
      <div className={styles.searchInputAndClose}>
        <div className={styles.searchInputContent}>
          <div className={styles.iconContainer}>
            <img src={inputSearchIcon} />
          </div>
          <div className={styles.inputWrapper}>
            {searchConversation?.showName && renderInputBefore()}
            <Input
              ref={inputRef}
              value={inputvalue}
              onChange={handleInputChange}
              placeholder={searchConversation?.showName ? '' : '搜索'}
              style={{ width: '100%' }}
              onPressEnter={handleUpdateNewSearchInfo}
              onKeyDown={handleInputKeyDown}
            />
          </div>
          {(inputvalue || searchConversation?.showName) && (
            <div className={styles.clearBtn} onClick={handleClear}>
              清除
            </div>
          )}
        </div>
      </div>

      {isEmpty(inputvalue) ? (
        currentConversation?.showName ? (
          <>
            <div className={styles.listwrapper}>
              <div
                className={styles.listItem}
                onClick={() => {
                  setSearchConversation(currentConversation);
                  inputRef.current?.focus();
                }}
              >
                <img src={listSearchIcon} className={styles.listSearchIcon} />
                {currentConversation.conversationType === SessionType.Group ? (
                  <div className={styles.showName}>
                    在 {currentConversation.showName} 中查找
                  </div>
                ) : (
                  <div className={styles.showName}>
                    在与 {currentConversation.showName} 之间的私聊中查找
                  </div>
                )}
              </div>
            </div>
            {showConnecting && (
              <div className={styles.netErrorWarning}>
                <img src={offlineIcon} />
                <span>当前处于离线状态，搜索结果可能不完整。</span>
              </div>
            )}
          </>
        ) : (
          <Empty style={{ paddingBottom: '20px' }} />
        )
      ) : (
        <div className={styles.scrollBox}>
          <div className={styles.listwrapper}>
            <div
              className={styles.listItem}
              onClick={() => onTargetSearchPage(false)}
            >
              <img
                src={searchIcon}
                className={classNames(styles.searchIcon, styles.listSearchIcon)}
              />
              <div className={styles.showName}>{inputvalue}</div>
            </div>
            {(currentConversation?.userID || currentConversation?.groupID) && (
              <div
                className={styles.listItem}
                onClick={() => onTargetSearchPage(true)}
              >
                <img
                  src={searchIcon}
                  className={classNames(
                    styles.searchIcon,
                    styles.listSearchIcon
                  )}
                />
                <div className={styles.showName}>{inputvalue}</div>
                <div className={styles.blueBox}>
                  in:
                  {currentConversation?.conversationType === SessionType.Group
                    ? ``
                    : `@`}
                  {currentConversation.showName}
                </div>
              </div>
            )}
          </div>
          {showConnecting && (
            <div className={styles.netErrorWarning}>
              <img src={offlineIcon} />
              <span>当前处于离线状态，搜索结果可能不完整。</span>
            </div>
          )}
          {(groupList?.length > 0 || memberList.length > 0) && (
            <div className={styles.groupsAndMembersBox}>
              {memberList?.length > 0 && (
                <div className={styles.memberListWrapper}>
                  {memberList.map((result) => {
                    return (
                      <div
                        key={result.employeeID}
                        onClick={() => {
                          handleClickItem('member', result);
                        }}
                      >
                        <SearchMemberItem member={result} />
                      </div>
                    );
                  })}
                </div>
              )}
              {groupList?.length > 0 && (
                <div>
                  {groupList.map((result, index) => (
                    <div
                      key={result.groupID}
                      onClick={() => {
                        handleClickItem('group', result);
                      }}
                    >
                      <SearchGroupItem group={result} />
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </Modal>
  );
};
export default GlobalSearchModalUI;
