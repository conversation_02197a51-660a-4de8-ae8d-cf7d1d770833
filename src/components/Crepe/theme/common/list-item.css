.milkdown {
  milkdown-list-item-block {
    display: block;
    padding: 0;

    & > .list-item {
      display: flex;
      align-items: flex-start;
    }

    & > .list-item > .children {
      min-width: 0;
      flex: 1;
    }

    li {
      gap: 10px;
      .label-wrapper {
        color: var(--crepe-color-outline);
        svg {
          fill: var(--crepe-color-outline);
        }

        height: 22px;
        width: 24px;
        display: flex;
        justify-content: center;
        align-items: center;
        .label {
          height: 32px;
          padding: 4px 0;
          width: 24px;
          text-align: right;
        }
        .checkbox {
          cursor: pointer;
        }
        .readonly {
          cursor: not-allowed;
        }
      }
    }
  }
}
