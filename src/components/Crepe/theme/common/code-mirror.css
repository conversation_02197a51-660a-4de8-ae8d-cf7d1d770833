.milkdown {
  .cm-editor {
    outline: none !important;
    background: var(--crepe-color-surface);
  }
  milkdown-code-block-custom {
    display: block;
    position: relative;
    padding: 8px 20px 20px;
    background: var(--crepe-color-surface);
    margin: 4px 0;

    .code-header {
      width: 797px;
      height: 38px;
      background: #fafafa;
      border-radius: 0 0 8px 8px;
      border: 1px solid #dddee0;
      display: flex;
      align-items: center;
      padding: 0 12px;
    }

    .code-language-icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }

    .language-picker {
      padding-top: 10px;
      width: max-content;
      position: absolute;
      z-index: 1;
      display: none;

      &.show {
        display: block;
      }
    }

    .hidden {
      display: none !important;
    }

    &.selected {
      outline: 1px solid var(--crepe-color-primary);
    }

    .cm-editor {
      outline: none !important;
      background: var(--crepe-color-surface);
    }

    .cm-gutters {
      border-right: none;
      background: var(--crepe-color-surface);
    }

    .tools {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .preview-toggle-button {
        background: var(--crepe-color-secondary);
        color: var(--crepe-color-on-surface-variant);
        padding: 4px 10px;
        opacity: 0;
        cursor: pointer;
        border-radius: 100px;
        font-size: 12px;
        line-height: 16px;
        font-weight: 600;
        font-family: var(--crepe-font-default);
        transition: opacity 0.2s ease-in-out;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;

        svg {
          width: 14px;
          height: 14px;
          fill: var(--crepe-color-on-surface-variant);
        }
      }

      .language-button {
        display: flex;
        align-items: center;
        font-family: var(--crepe-font-default);
        gap: 6px;
        padding: 2px 4px 2px 8px;
        background: var(--crepe-color-surface-low);
        color: var(--crepe-color-on-surface-variant);
        border-radius: 4px;
        font-size: 12px;
        font-weight: 600;
        line-height: 16px;
        margin-bottom: 8px;
        opacity: 0;
        cursor: pointer;
        transition: opacity 0.2s ease-in-out;

        &:hover {
          background: var(--crepe-color-hover);
        }

        .expand-icon {
          transition: transform 0.2s ease-in-out;
          width: 18px;
          height: 18px;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .expand-icon svg {
          width: 14px;
          height: 14px;
          color: var(--crepe-color-outline);
        }

        &[data-expanded="true"] .expand-icon {
          transform: rotate(180deg);
        }

        .expand-icon svg:focus,
        .expand-icon:focus-visible {
          outline: none;
        }
      }
    }

    &:hover .language-button {
      opacity: 1;
    }

    &:hover .preview-toggle-button {
      opacity: 1;
    }

    .list-wrapper {
      background: var(--crepe-color-surface-low);
      border-radius: 12px;
      box-shadow: var(--crepe-shadow-1);
      width: 240px;
      padding-top: 12px;
    }

    .language-list {
      height: 410px;
      overflow-y: auto;
      overscroll-behavior: contain;
      margin: 0;
      padding: 0;
    }

    .language-list-item {
      cursor: pointer;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 4px 22px;
      font-size: 14px;
      font-weight: 600;
      line-height: 20px;

      &:hover {
        background: var(--crepe-color-hover);
      }

      &:focus-visible {
        outline: none;
        background: var(--crepe-color-hover);
      }

      .leading,
      .leading svg {
        width: 24px;
        height: 24px;
      }

      &.no-result {
        cursor: default;
        opacity: 0.6;
        &:hover {
          background: transparent;
        }
      }
    }

    .search-box {
      display: flex;
      align-items: center;
      margin: 0 12px 8px;
      background: transparent;
      border-radius: 4px;
      outline: 1px solid var(--crepe-color-primary);
      gap: 8px;
      padding: 6px 10px;
      &:has(input:focus) {
        outline: 2px solid var(--crepe-color-primary);
      }

      .search-input {
        width: 100%;
        color: var(--crepe-color-on-surface);
      }

      .search-icon {
        display: none;
      }

      .clear-icon {
        cursor: pointer;
        width: 20px;
        height: 20px;
        svg {
          width: 20px;
          height: 20px;
          color: var(--crepe-color-primary);
          fill: var(--crepe-color-primary);
        }
        &:hover {
          background: var(--crepe-color-hover);
        }
      }

      input {
        font-family: var(--crepe-font-default);
        font-size: 14px;
        line-height: 20px;
        background: transparent;
      }

      input:focus {
        outline: none;
      }
    }

    .preview-panel {
      .preview-divider {
        height: 1px;
        opacity: 0.2;
        background: var(--crepe-color-outline);
        margin: 6px 0;
      }

      .preview-label {
        margin: 6px 0;
        font-size: 12px;
        color: color-mix(
          in srgb,
          var(--crepe-color-on-surface),
          transparent 40%
        );
        font-weight: 600;
        text-transform: uppercase;
        font-family: var(--crepe-font-default);
      }

      .preview {
        text-align: center;
        overflow-x: auto;
      }
    }
  }
}
