.milkdown {
  milkdown-link-preview-custom {
    position: absolute;
    z-index: 10;
    &[data-show="false"] {
      display: none;
    }
    & > .link-preview {
      height: 32px;
      display: flex;
      justify-content: center;
      padding: 4px 10px;
      background: var(--crepe-color-surface);
      gap: 10px;
      border-radius: 8px;
      cursor: pointer;
      box-shadow: var(--crepe-shadow-1);

      & > .link-display {
        text-decoration: none;
        color: unset;
      }

      & > .link-display:hover::before {
        display: block;
      }

      & > .link-icon {
        & > svg {
          width: 18px;
          height: 18px;
          color: var(--crepe-color-outline);
          fill: var(--crepe-color-outline);
        }

        border-radius: 8px;
        padding: 3px;
        line-height: 24px;
        &:hover {
          background: var(--crepe-color-hover);
        }
      }

      & > .link-display {
        width: 240px;
        line-height: 24px;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 14px;
        white-space: nowrap;
        &:hover {
          text-decoration: underline;
        }
      }

      & > .button {
        & > svg {
          width: 18px;
          height: 18px;
          color: var(--crepe-color-outline);
          fill: var(--crepe-color-outline);
        }

        padding: 3px;
        border-radius: 8px;
        line-height: 24px;
        &:hover {
          background: var(--crepe-color-hover);
        }
      }
    }
  }

  milkdown-link-edit-custom {
    position: absolute;
    z-index: 10;
    &[data-show="false"] {
      display: none;
    }
    & > .link-edit {
      height: 32px;
      display: flex;
      justify-content: center;
      padding: 4px 10px 4px 20px;
      background: var(--crepe-color-surface);
      gap: 8px;
      border-radius: 8px;
      box-shadow: var(--crepe-shadow-1);

      & > .input-area {
        outline: none;
        background: transparent;
        width: 200px;
        font-size: 14px;
        color: var(--crepe-color-on-background);
      }

      & > .button {
        & > svg {
          width: 18px;
          height: 18px;
          color: var(--crepe-color-outline);
          fill: var(--crepe-color-outline);
        }

        padding: 3px;
        cursor: pointer;
        border-radius: 8px;
        font-size: 12px;
        line-height: 24px;
        &:hover {
          background: var(--crepe-color-hover);
        }
        &.hidden {
          visibility: hidden;
        }
      }
    }
  }
}
