.milkdown {
  milkdown-image-inline {
    outline: none;
    display: inline;

    & input {
      background: transparent;
      outline: none;
      border: 0;
    }

    &.empty {
      vertical-align: bottom;
    }

    & > .empty-image-inline {
      display: inline-flex;
    }

    & > .empty-image-inline .confirm {
      cursor: pointer;
    }

    & > .empty-image-inline .link-importer {
      position: relative;
      flex: 1;
    }

    & > .empty-image-inline .link-importer > .link-input-area {
      width: 208px;
      color: var(--crepe-color-on-background);
    }

    & > .empty-image-inline .link-importer .placeholder {
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      cursor: text;
    }

    & > .empty-image-inline .link-importer .placeholder .uploader {
      cursor: pointer;
      display: flex;
    }

    & .hidden {
      display: none !important;
    }

    &.empty.selected {
      background: none;
      outline: none;
      .empty-image-inline {
        box-shadow: var(--crepe-shadow-1);
      }
    }

    &.selected {
      background: none;
      outline: 1px solid var(--crepe-color-primary);
      :not(input)::selection {
        background: transparent;
      }
    }

    img.image-inline {
      vertical-align: text-bottom;
    }

    .empty-image-inline {
      align-items: center;
      padding: 4px 10px;
      gap: 10px;
      background: var(--crepe-color-surface);
      font-family: var(--crepe-font-default);
      border-radius: 8px;
    }

    .empty-image-inline .image-icon {
      svg {
        width: 18px;
        height: 18px;
        fill: var(--crepe-color-outline);
      }
      padding: 3px;
      width: 24px;
      height: 24px;
    }

    .empty-image-inline .link-importer {
      height: 24px;
    }

    .empty-image-inline .link-importer .placeholder {
      color: color-mix(
        in srgb,
        var(--crepe-color-on-background),
        transparent 60%
      );
      :not(input)::selection {
        background: transparent;
      }
    }

    .empty-image-inline .link-importer .link-input-area {
      line-height: 24px;
    }

    .empty-image-inline .link-importer .placeholder .uploader {
      gap: 8px;
      color: var(--crepe-color-primary);
      justify-content: center;
      transition: color 0.2s;
      font-family: var(--crepe-font-default);
    }

    .empty-image-inline .link-importer.focus .placeholder .uploader {
      color: unset;
    }

    .empty-image-inline .link-importer .placeholder .uploader:hover {
      color: var(--crepe-color-primary);
    }

    .empty-image-inline .link-importer .placeholder .text {
      margin-left: 8px;
    }

    .empty-image-inline .confirm {
      svg {
        width: 18px;
        height: 18px;
      }
      width: 24px;
      height: 24px;
      padding: 3px;
      border-radius: 8px;
      color: var(--crepe-color-primary);
      &:hover {
        background: var(--crepe-color-hover);
      }
    }
  }

  milkdown-image-block {
    outline: none;
    margin: 4px 0;
    display: block;

    & > .image-wrapper {
      position: relative;
      width: fit-content;
      margin: 0 auto;
      min-width: 100px;
    }

    & > .image-wrapper .operation {
      position: absolute;
      display: flex;
    }

    & > .image-wrapper .operation > .operation-item {
      cursor: pointer;
    }

    & > .image-wrapper img {
      max-width: 100%;
      min-height: 100px;
      display: block;
      object-fit: cover;
    }

    & > .image-wrapper > .image-resize-handle {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
    }

    & > .image-wrapper > .image-resize-handle:hover {
      cursor: row-resize;
    }

    input {
      background: transparent;
      outline: none;
      border: 0;
    }

    & > .caption-input {
      display: block;
      width: 100%;
      text-align: center;
      color: var(--crepe-color-on-background);
    }

    & > .image-edit {
      display: flex;
    }

    & > .image-edit .confirm {
      cursor: pointer;
    }

    & > .image-edit .link-importer {
      position: relative;
      flex: 1;
    }

    & > .image-edit .link-importer > .link-input-area {
      width: 100%;
    }

    & > .image-edit .link-importer .placeholder {
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      cursor: text;
    }

    & > .image-edit .link-importer .placeholder .uploader {
      cursor: pointer;
      display: flex;
    }

    .hidden {
      display: none !important;
    }

    &.selected > .image-edit:not(:has(input:focus)) {
      position: relative;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: color-mix(
          in srgb,
          var(--crepe-color-selected),
          transparent 60%
        );
        pointer-events: none;
      }
    }

    &.selected {
      & > .image-wrapper {
        position: relative;
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: color-mix(
            in srgb,
            var(--crepe-color-selected),
            transparent 60%
          );
        }
      }
      :not(input)::selection {
        background: transparent;
      }
    }

    .image-wrapper {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .image-wrapper .operation {
      gap: 12px;
      right: 12px;
      top: 12px;
      opacity: 0;
      transition: all 0.2s;
    }

    &:hover > .image-wrapper .operation {
      opacity: 1;
    }

    .image-wrapper .operation > .operation-item {
      color: var(--crepe-color-on-inverse);
      padding: 4px;
      background: var(--crepe-color-inverse);
      opacity: 0.6;
      border-radius: 50%;
      width: 32px;
      height: 32px;
      svg {
        width: 24px;
        height: 24px;
      }
    }

    .image-wrapper .image-resize-handle {
      height: 4px;
      bottom: -2px;
      max-width: 160px;
      width: 100%;
      background: var(--crepe-color-outline);
      opacity: 0;
      transition: all 0.2s;
      border-radius: 4px;
    }

    &:hover > .image-wrapper .image-resize-handle {
      opacity: 1;
    }

    .caption-input {
      margin: 4px auto;
      font-family: var(--crepe-font-default);
    }

    .image-edit {
      align-items: center;
      padding: 16px 24px;
      gap: 16px;
      background: var(--crepe-color-surface);
      height: 56px;
    }

    .image-edit .image-icon {
      color: var(--crepe-color-outline);
    }

    .image-edit .image-icon svg {
      width: 24px;
      height: 24px;
      display: flex;
      justify-content: center;
      align-items: center;
      fill: var(--crepe-color-outline);
    }

    .image-edit .link-importer .placeholder {
      color: color-mix(
        in srgb,
        var(--crepe-color-on-background),
        transparent 60%
      );
      :not(input)::selection {
        background: transparent;
      }
    }

    .image-edit .link-importer .link-input-area {
      line-height: 24px;
      color: var(--crepe-color-on-background);
    }

    .image-edit .link-importer .placeholder .uploader {
      gap: 8px;
      color: var(--crepe-color-primary);
      justify-content: center;
      transition: color 0.2s;
      font-weight: 600;
    }

    .image-edit .link-importer.focus .placeholder .uploader {
      color: unset;
    }

    .image-edit .link-importer .placeholder .uploader:hover {
      color: var(--crepe-color-primary);
    }

    .image-edit .link-importer .placeholder .text {
      margin-left: 8px;
    }

    .image-edit .confirm {
      background: var(--crepe-color-secondary);
      color: var(--crepe-color-on-secondary);
      line-height: 40px;
      padding: 0 24px;
      border-radius: 100px;
      font-size: 14px;
      font-weight: 600;
      &:hover {
        background:
          linear-gradient(
            0deg,
            rgba(29, 25, 43, 0.08) 0%,
            rgba(29, 25, 43, 0.08) 100%
          ),
          var(--crepe-color-secondary);
      }
    }
  }
}
