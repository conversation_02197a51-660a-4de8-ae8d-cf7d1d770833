/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable @typescript-eslint/no-parameter-properties */
import type { LanguageSupport } from '@codemirror/language';
import { javascript } from '@codemirror/lang-javascript';
import { python } from '@codemirror/lang-python';
import { java } from '@codemirror/lang-java';
import { cpp } from '@codemirror/lang-cpp';
import { sql } from '@codemirror/lang-sql';
import { go } from '@codemirror/lang-go';
import { css } from '@codemirror/lang-css';
import { html } from '@codemirror/lang-html';
import { rust } from '@codemirror/lang-rust';
import { xml } from '@codemirror/lang-xml';
import { json } from '@codemirror/lang-json';
import { markdown } from '@codemirror/lang-markdown';
import { yaml } from '@codemirror/lang-yaml';
import { php } from '@codemirror/lang-php';
import { wast } from '@codemirror/lang-wast';
import { sass } from '@codemirror/lang-sass';
import { less } from '@codemirror/lang-less';
import { vue } from '@codemirror/lang-vue';
import { angular } from '@codemirror/lang-angular';

export interface LanguageInfo {
  name: string;
  alias: readonly string[];
}

export const languageMap: Record<string, any> = {
  // 基础语言
  javascript: javascript(),
  typescript: javascript({ typescript: true }),
  python: python(),
  java: java(),
  cpp: cpp(),
  sql: sql(),
  go: go(),

  // Web开发
  html: html(),
  css: css(),
  jsx: javascript({ jsx: true }),
  tsx: javascript({ typescript: true, jsx: true }),
  typescriptreact: javascript({ typescript: true, jsx: true }),
  vue: vue(),
  angular: angular(),
  sass: sass(),
  scss: sass(),
  less: less(),

  // 数据格式
  json: json(),
  yaml: yaml(),
  toml: yaml(),
  xml: xml(),

  // 系统/工具
  rust: rust(),
  wasm: wast(),
  php: php(),

  // 文档和脚本
  markdown: markdown(),
  shell: markdown(),
  bash: markdown(),
  dockerfile: markdown(),
  ini: markdown(),
  properties: markdown(),
  plaintext: markdown(),
};

export class LanguageLoader {
  private readonly map: Record<string, LanguageSupport>;

  constructor() {
    this.map = {};
    Object.entries(languageMap).forEach(([alias, support]) => {
      this.map[alias.toLowerCase()] = support;
    });
  }

  getAll(): LanguageInfo[] {
    return Object.entries(this.map).map(
      ([alias, support]): LanguageInfo => ({
        name: alias,
        alias: [alias],
      })
    );
  }

  load(languageName: string): Promise<LanguageSupport | undefined> {
    const language = this.map[languageName.toLowerCase()];
    return Promise.resolve(language);
  }
}
