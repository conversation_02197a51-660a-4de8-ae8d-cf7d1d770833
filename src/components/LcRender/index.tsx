import React, { memo, useEffect, useRef, useState } from 'react';
import LCRender from '@lowcode/lc-render';
import { LCRenderProps } from '@lowcode/lc-render/dist/types';
import { LoadingSpinner } from '../LoadingSpinner';
import styles from './index.less';

const LcRender = (props: LCRenderProps) => {
  const [height, setHeight] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current) {
      return;
    }

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        entry.contentRect.height && setHeight(entry.contentRect.height);
      }
    });

    resizeObserver.observe(containerRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  return (
    <div
      style={{
        width: '980px',
        position: 'relative',
        // height: '200px',
      }}
    >
      <div ref={containerRef}>
        <LCRender
          {...props}
          renderLoading={() => {
            return (
              <div className={styles.lowcodeLoading}>
                <LoadingSpinner />
                卡片加载中...
              </div>
            );
          }}
        />
      </div>
    </div>
  );
};

export default memo(LcRender);
