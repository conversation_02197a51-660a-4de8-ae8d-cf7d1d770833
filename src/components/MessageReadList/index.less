.messageReadList {
  width: 212px;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  height: 280px;
  border-radius: 8px;
}

.tab {
  height: 49px;
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  line-height: 25px;

  .selected {
    color: #1e64aa;
  }

  > div {
    flex: 1;
    color: #999ba0;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
}

.line {
  width: 100%;
  height: 1px;
  background: var(--primary-background-color-5);
}

.content {
  margin: 8px 6px;
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;

  .user {
    height: 40px;
    padding: 8px 0 8px 16px;
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 600;
    color: #1d1c1d;
    // cursor: pointer;

    &:hover {
      background: rgba(107, 107, 108, 8%);
      border-radius: 8px;
    }
  }
  .name {
    margin-left: 8px;
  }
}
