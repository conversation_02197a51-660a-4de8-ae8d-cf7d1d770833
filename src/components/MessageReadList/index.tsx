import { useEffect, useState } from 'react';
import { Spin } from '@ht/sprite-ui';
import { IMSDK } from '@/layouts/BasicLayout';
import {
  GetGroupMessageReadResult,
  PublicUserItem,
} from '@ht/openim-wasm-client-sdk';
import styles from './index.less';
import OIMAvatar from '../OIMAvatar';

interface MessageReadListProps {
  conversationID: string;
  clientMsgID: string;
}

interface resultDataType {
  readListInfo?: PublicUserItem[];
  unreadListInfo?: PublicUserItem[];
}
const MessageReadList = ({
  conversationID,
  clientMsgID,
}: MessageReadListProps) => {
  const [showTab, setShowTab] = useState('read');

  const [groupMessageReadResponse, setGroupMessageReadResponse] =
    useState<GetGroupMessageReadResult>();
  const [result, setResult] = useState<resultDataType>();

  const [loading, setLoading] = useState(true);
  const [tabloading, setTabLoading] = useState(true);

  useEffect(() => {
    const getResultData = async () => {
      try {
        const { data } = await IMSDK.getGroupMessageRead({
          conversationID,
          clientMsgID,
        });

        setGroupMessageReadResponse(data);
        setLoading(false);
      } catch (e) {
        console.error('getGroupMessageRead查询失败', e);
        setLoading(false);
      }
    };

    setResult(undefined);
    getResultData();
  }, [clientMsgID, conversationID]);

  useEffect(() => {
    const initResultData = async () => {
      const resultData = {} as resultDataType;

      if (
        groupMessageReadResponse?.readMembers?.length != null &&
        groupMessageReadResponse?.readMembers?.length > 0
      ) {
        const { readMembers } = groupMessageReadResponse; // string[]
        const response = await IMSDK.getUsersInfo(readMembers);
        const userInfoList = response.data;

        // 建立一个 Map：userID => 排序索引
        const idIndexMap = new Map(readMembers.map((id, index) => [id, index]));

        resultData.readListInfo = userInfoList.sort((a, b) => {
          return (
            (idIndexMap.get(a.userID) ?? Infinity) -
            (idIndexMap.get(b.userID) ?? Infinity)
          );
        });
      }
      if (
        groupMessageReadResponse?.unreadMembers?.length != null &&
        groupMessageReadResponse?.unreadMembers?.length > 0
      ) {
        const { unreadMembers } = groupMessageReadResponse; // string[]
        const unreadResponse = await IMSDK.getUsersInfo(unreadMembers);
        const unreadUserInfoList = unreadResponse.data;

        // 根据原始 unreadMembers 顺序排序
        resultData.unreadListInfo = unreadUserInfoList.sort((a, b) => {
          return a.userID > b.userID ? 1 : -1;
        });
      }

      setResult(resultData);
      setTabLoading(false);
    };

    initResultData();
  }, [showTab, groupMessageReadResponse]);

  return (
    <div className={styles.messageReadList}>
      {loading ? (
        <Spin
          spinning={loading}
          style={{
            height: '100%',
            display: 'flex',
            alignContent: 'center',
            justifyContent: 'center',
          }}
        />
      ) : (
        <>
          <div className={styles.tab}>
            <div
              className={showTab === 'read' ? styles.selected : ''}
              onClick={() => setShowTab('read')}
            >
              <span style={{ fontSize: 18, lineHeight: 18 }}>
                {result?.readListInfo?.length || 0}
              </span>
              <span style={{ fontSize: 14, lineHeight: 16 }}> 人已读</span>
            </div>
            <div
              className={showTab === 'unread' ? styles.selected : ''}
              onClick={() => setShowTab('unread')}
            >
              <span style={{ fontSize: 18, lineHeight: 18 }}>
                {result?.unreadListInfo?.length || 0}
              </span>
              <span style={{ fontSize: 14, lineHeight: 16 }}> 人未读</span>
            </div>
          </div>
          <div className={styles.line}></div>
          <div className={styles.content}>
            {tabloading ? (
              <Spin
                spinning={tabloading}
                style={{
                  height: '100%',
                  display: 'flex',
                  alignContent: 'center',
                }}
              />
            ) : (
              <>
                {showTab === 'read' &&
                  result?.readListInfo?.map((item) => {
                    return (
                      <div key={item.userID} className={styles.user}>
                        <OIMAvatar
                          userID={item.userID}
                          size={24}
                          hideOnlineStatus={true}
                        />
                        <div className={styles.name}>{item.nickname}</div>
                      </div>
                    );
                  })}

                {showTab === 'unread' &&
                  result?.unreadListInfo?.map((item) => {
                    return (
                      <div key={item.userID} className={styles.user}>
                        <OIMAvatar
                          userID={item.userID}
                          size={24}
                          hideOnlineStatus={true}
                        />
                        <div className={styles.name}>{item.nickname}</div>
                      </div>
                    );
                  })}
              </>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default MessageReadList;
