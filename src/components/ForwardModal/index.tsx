/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-statements */
/* eslint-disable max-lines */
import { FC, useState, useEffect, useCallback } from 'react';
import { useDebounceFn } from 'ahooks';
import _ from 'lodash';
import {
  useConversationStore,
  useUserStore,
  useMultiSelectStore,
} from '@/store';
import { IMSDK } from '@/layouts/BasicLayout';
import { RenderMd } from '@/components/MdEditor';
import { Input, message as Message, Modal, Popover } from '@ht/sprite-ui';
import { IMessageItemProps } from '@/components/Channel/components/MessageItem';
import searchIcon from '@/assets/images/searchModal/searchIcon2.svg';
import deleteIcon from '@/assets/contact/deleteIcon.png';
import closeIcon from '@/assets/images/searchModal/closeIcon.svg';
import showMoreIcon from '@/assets/images/forwardModal/showMoreIcon.svg';
import {
  MessageItem as MessageItemType,
  MessageType,
  ConversationItem,
  EmployeeItem,
  SessionType,
} from '@ht/openim-wasm-client-sdk';
import { pushNewMessage } from '@/hooks/useHistoryMessageList';
import classNames from 'classnames';
import { getFileIcon } from '@/components/Channel/components/MessageInput/FileRender';
import ImgPreviewModal from '@/components/ImgPreviewModal';
import MultiMessageModal from '@/components/Channel/components/MultiMessageModal';
import {
  SearchedConversationType,
  searchGroupConversationByInputValue,
} from '@/utils/utils';
import { parserMdToText } from '@/utils/parserMdToHtml';
import { openMultiMessageWin } from '@/utils/message';
import { shallow } from 'zustand/shallow';
import CustomMessageRender from '../Channel/components/MessageItem/CustomMessageRender';
import styles from './index.less';

import { mentionRegex } from '../MdEditor/plugin-mention/MentionShecma';
import ChannelGroupItem from './components/ChanelItem/ChannelGroupItem';
import SearchPannel from './components/SearchPannel';
import ChannelUserItem from './components/ChanelItem/ChannelUserItem';

interface ForwardModalProps extends IMessageItemProps {
  open: boolean;
  onClose: () => void;
  message: MessageItemType;
  forwardType?: string; // single: 单条转发, multi: 多条转发
  multiForwardMode?: string; // separate: 逐条转发, combine: 合并转发
  onForwardSuccess?: () => void;
}

export interface SearchItemProp {
  userList: EmployeeItem[];
  conversationList: SearchedConversationType[];
}

export type SeachedItemType =
  | ConversationItem
  | SearchedConversationType
  | EmployeeItem;
const ForwardModal: FC<ForwardModalProps> = ({
  open,
  onClose,
  onForwardSuccess,
  message,
  forwardType = 'single',
  multiForwardMode = 'combine',
  ...props
}) => {
  const [searchList, setSearchList] = useState<SearchItemProp>();
  const [selectedList, setSelectedList] = useState<SeachedItemType[]>([]);
  const [imgPreviewModalOpen, setImgPreviewModalOpen] =
    useState<boolean>(false);
  const [multiMessageModalOpen, setMultiMessageModalOpen] =
    useState<boolean>(false);
  const { conversationList, currentConversation } = useConversationStore(
    (state) => ({
      conversationList: state.conversationList,
      currentConversation: state.currentConversation,
    }),
    shallow
  );
  const [forwardMessage, setForwardMessage] = useState<MessageItemType>(
    message || {}
  );
  const [inputvalue, setInputValue] = useState<any>();
  const [lock, setLock] = useState<boolean>(false);

  const [allChatItems, setAllChatItems] = useState<ConversationItem[]>([]);
  const { pictureElem } = message || {};
  const { url = '', uuid } = pictureElem?.sourcePicture || {};
  const fileName = uuid?.split('/')[1] || 'image.png';

  const [isSearching, setIsSearching] = useState<boolean>(true);

  const cancelMultiSelect = useMultiSelectStore(
    (state) => state.cancelMultiSelect
  );

  const fetchData = async (val: string) => {
    try {
      const { data: searchedUsersData } = await IMSDK.searchEmployeeListPage(
        val,
        0,
        50
      );

      let groupData = await searchGroupConversationByInputValue(val);

      const { data: groupDataList } = await IMSDK.getSpecifiedGroupsInfo(
        groupData.map((item) => item.groupID)
      );

      const validGroupList =
        groupDataList
          .filter((groupData) => groupData.memberCount !== 0)
          .map((groupData) => groupData.groupID) || [];

      groupData = groupData.filter((groupItem) =>
        validGroupList?.includes(groupItem.groupID)
      );
      setSearchList({
        userList: searchedUsersData?.list || [],
        conversationList: groupData || [],
      });

      setIsSearching(false);
    } catch (e) {
      setIsSearching(false);
    }
  };

  const { userID, nickname } = useUserStore.getState().selfInfo;

  const handleSelected = (
    selecteItem: EmployeeItem | SearchedConversationType | ConversationItem,
    isSelected: boolean
  ) => {
    let newSelectedList = selectedList || [];
    if (isSelected) {
      newSelectedList = [...(selectedList || []), selecteItem];
    } else if ('employeeID' in selecteItem) {
      newSelectedList = (selectedList || [])?.filter(
        (item) =>
          !('employeeID' in item && item.employeeID === selecteItem.employeeID)
      );
    } else if ('groupID' in selecteItem) {
      newSelectedList = (selectedList || [])?.filter(
        (item) => !('groupID' in item && item.groupID === selecteItem.groupID)
      );
    }

    setSelectedList(newSelectedList);
  };

  const { run: runSearch } = useDebounceFn(
    (val) => {
      fetchData(val);
    },
    {
      wait: 300,
    }
  );

  const onInputChange = (searchVal: any) => {
    if (searchVal != null && searchVal !== '') {
      setIsSearching(true);
      setSearchList({
        userList: [],
        conversationList: [],
      });
      runSearch(searchVal);
    }
  };

  const handleSendMessage = async (sendMessageProp: any, selectItem: any) => {
    let successMessage = undefined as any as MessageItemType;

    try {
      successMessage = (await IMSDK.sendMessage(sendMessageProp))?.data;
      // 如果是当前会话
    } catch (error) {
      console.error('转发消息失败', error);
    } finally {
      if (
        ('groupID' in selectItem &&
          currentConversation?.groupID === sendMessageProp.groupID) ||
        ('userID' in selectItem &&
          currentConversation?.userID === sendMessageProp.recvID &&
          currentConversation?.userID !== userID) // 自己跟自己的对话不用push了，因为会收到
      ) {
        if (successMessage != null) {
          pushNewMessage(successMessage);
        }
      }
    }
  };

  const onForwardMsg = async () => {
    if (_.isEmpty(selectedList)) {
      return;
    }

    const readlForwardMessage = { ...forwardMessage, isRead: false };

    selectedList?.map(async (item) => {
      const selectItem = item;
      const sendMessageProp = {
        recvID: '',
        message: undefined as any as MessageItemType,
        groupID: '',
      };

      // 设置recvID或groupID
      if ('groupID' in selectItem) {
        // 群聊
        sendMessageProp.groupID = selectItem.groupID;

        // 需要设置消息的recvID和groupID，否则会多发
        readlForwardMessage.groupID = selectItem.groupID;
        readlForwardMessage.recvID = '';
      } else {
        // 单聊
        sendMessageProp.recvID = selectItem.employeeID;

        // 需要设置消息的recvID和groupID，否则会多发
        readlForwardMessage.groupID = '';
        readlForwardMessage.recvID = selectItem.employeeID;
      }
      if (forwardType === 'multi' && multiForwardMode === 'separate') {
        // 逐条转发
        const messageList = message?.mergeElem?.multiMessage || [];
        for (const messageListItem of messageList) {
          try {
            const resMsg =
              (await IMSDK.createForwardMessage(messageListItem)).data || '';
            await handleSendMessage(
              { ...sendMessageProp, message: resMsg },
              selectItem
            );
          } catch (error) {
            console.error('处理消息失败', error);
          }
        }
      } else if (forwardType === 'multi' && multiForwardMode === 'combine') {
        handleSendMessage(
          { ...sendMessageProp, message: forwardMessage },
          selectItem
        );
      } else {
        // 设置消息
        const resMsg =
          (await IMSDK.createForwardMessage(readlForwardMessage)).data || '';
        handleSendMessage({ ...sendMessageProp, message: resMsg }, selectItem);
      }
    });

    cancelMultiSelect();
    onClose();
    // onForwardSuccess?.();
  };

  const renderFileForwardMessage = () => {
    const FileName = message.fileElem?.fileName || '';
    return (
      <div className={styles.fileMessageRenderContent}>
        <div className={styles.icon}>
          <img src={getFileIcon(FileName, 'icon')} />
        </div>
        <div className={styles.fileInfo}>
          <div className={styles.fileName} title={FileName}>
            {FileName}
          </div>
          <div className={styles.info}>{getFileIcon(FileName, 'text')}</div>
        </div>
      </div>
    );
  };

  const renderPictureForwardMessage = () => {
    return (
      <div className={styles.pictureMessageRenderContent}>
        <div className={styles.icon}>
          <img
            src={pictureElem?.sourcePicture.url}
            // onClick={() => setImgPreviewModalOpen(true)}
          />
        </div>
        <div className={styles.fileInfo}>
          <div className={styles.fileName} title={fileName}>
            {fileName}
          </div>
        </div>
      </div>
    );
  };

  const handleOpenMultiMessageView = useCallback(() => {
    openMultiMessageWin(
      {
        message,
        hasForwardBtn: false,
      },
      () => {
        setMultiMessageModalOpen(true);
      }
    );
  }, [message]);

  const handleOpenMultiMessageViewThrottle = _.throttle(
    handleOpenMultiMessageView,
    300,
    { leading: false, trailing: true }
  );

  const renderForwardMessage = () => {
    if (forwardMessage.contentType === MessageType.MergeMessage) {
      const multiForwardModeText =
        multiForwardMode === 'separate' ? '[逐条转发]' : '[合并转发]';
      const multiTitle = forwardMessage.mergeElem?.title || '';
      return (
        <div
          className={styles.textMessageRenderContent}
          onClick={() => {
            handleOpenMultiMessageViewThrottle();
          }}
        >
          <div className={styles.textArea}>
            {forwardType === 'multi' && multiForwardModeText}
            {multiTitle}
          </div>
          <img src={showMoreIcon}></img>
        </div>
      );
    } else if (
      forwardMessage.contentType === MessageType.TextMessage ||
      forwardMessage.contentType === MessageType.AtTextMessage ||
      forwardMessage.contentType === MessageType.QuoteMessage
    ) {
      const text =
        forwardMessage.textElem?.content ||
        forwardMessage.quoteElem?.text ||
        forwardMessage.atTextElem?.text?.replace(mentionRegex, '@$1') ||
        '';
      return (
        <Popover
          overlayClassName={styles.popStateContainer}
          content={
            <div className={styles.textContent}>
              <RenderMd id={message.clientMsgID} value={text} />
            </div>
          }
          placement="right"
          showArrow={true}
          autoAdjustOverflow={false}
          trigger={'click'}
          arrowPointAtCenter={true}
        >
          <div className={styles.textMessageRenderContent}>
            <div className={styles.textArea}>{parserMdToText(text)}</div>

            <img src={showMoreIcon}></img>
          </div>
        </Popover>
      );
    } else if (forwardMessage.contentType === MessageType.CustomMessage) {
      let content: any = {};
      try {
        content = JSON.parse(message?.customElem?.data || '{}');
      } catch (e) {
        content = message?.customElem?.data;
        return (
          <CustomMessageRender
            message={forwardMessage}
            isForwardMessage={true}
            {...props}
          />
        );
      }
      if (content?.type === 'stream') {
        return (
          <Popover
            overlayClassName={styles.popStateContainer}
            content={
              <div className={styles.textContent}>
                <RenderMd
                  id={message.clientMsgID}
                  value={content?.content?.answer}
                />
              </div>
            }
            placement="right"
            showArrow={true}
            autoAdjustOverflow={false}
            trigger={'click'}
            arrowPointAtCenter={true}
          >
            <div className={styles.textMessageRenderContent}>
              <div className={styles.textArea}>
                {parserMdToText(content?.content?.answer)}
              </div>

              <img src={showMoreIcon}></img>
            </div>
          </Popover>
        );
      } else {
        return (
          <CustomMessageRender
            message={forwardMessage}
            isForwardMessage={true}
            {...props}
          />
        );
      }
    } else if (forwardMessage.contentType === MessageType.FileMessage) {
      return renderFileForwardMessage();
    } else if (forwardMessage.contentType === MessageType.PictureMessage) {
      return renderPictureForwardMessage();
    } else {
      return <></>;
    }
  };

  const download = async () => {
    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('网络响应失败');
      }
      const blob = await response.blob();
      const blobUrl = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      URL.revokeObjectURL(blobUrl);
    } catch (error) {
      Message.error('下载失败');
    }
  };

  const openView = async () => {
    window.open(url, '_blank');
  };

  useEffect(() => {
    const initValueConversationList = async () => {
      const chatItemsRes = await IMSDK.getAllEffectiveConversationList<
        ConversationItem[]
      >();
      const chatItems =
        chatItemsRes?.data?.filter(
          (conversation) =>
            conversation?.parentId == null || conversation?.parentId === ''
        ) ?? [];

      setAllChatItems(chatItems);
    };
    initValueConversationList();
  }, [conversationList]);

  const handleInputChange = (e: any) => {
    if (e.type === 'compositionstart') {
      setLock(true);
      return;
    }
    setInputValue(e.target.value.trim());
    if (e.type === 'compositionend') {
      // setInputValue(e.target.value.trim());
      onInputChange(e.target.value.trim());
      setLock(false);
    }
    if (!lock) {
      onInputChange(e.target.value.trim());
      // setInputValue(e.target.value.trim());
    }
    // onInputChange(e.target.value.trim());
  };

  return (
    <Modal
      title={null}
      footer={null}
      closable={false}
      centered={true}
      open={open}
      onCancel={onClose}
      width={760}
      bodyStyle={{
        borderRadius: 8,
        border: '1px solid #DDDEE0',
        padding: 0,
        height: 634,
      }}
      maskClosable={true}
      keyboard={true}
      wrapClassName={styles.forwardModalWrapper}
      getContainer={document.getElementById('BasicLayoutId') || document.body}
    >
      <div className={styles.forwardModalWrap}>
        <div className={styles.left}>
          <div className={styles.search}>
            <div className={styles.searchInputContent}>
              <div className={styles.iconContainer}>
                <img src={searchIcon} />
              </div>
              <div className={styles.inputWrapper}>
                <Input
                  value={inputvalue}
                  onChange={handleInputChange}
                  placeholder={'搜索人员/搜索机器人'}
                  style={{ width: '100%' }}
                />
                <div
                  className={styles.deleteIcon}
                  onClick={() => {
                    setInputValue(undefined);
                  }}
                >
                  {inputvalue != null && inputvalue !== '' && (
                    <img src={deleteIcon}></img>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className={styles.result}>
            {inputvalue == null || inputvalue === '' ? (
              allChatItems?.map((item) => {
                if (item.groupID != null && item.groupID !== '') {
                  return (
                    <ChannelGroupItem
                      keyword=""
                      key={item.conversationID}
                      conversation={item}
                      selectedList={selectedList}
                      handleSelected={handleSelected}
                    />
                  );
                } else {
                  return (
                    <ChannelUserItem
                      keyword=""
                      key={item.conversationID}
                      employeeInfo={
                        {
                          employeeID: item.userID,
                        } as EmployeeItem
                      }
                      selectedList={selectedList}
                      handleSelected={handleSelected}
                    />
                  );
                }
              })
            ) : (
              <SearchPannel
                isSearching={isSearching}
                keyword={inputvalue}
                searchList={searchList}
                selectedList={selectedList}
                handleSelected={handleSelected}
              />
            )}
          </div>
        </div>
        <div className={styles.right}>
          <div className={styles.forwardToArea}>
            <div className={styles.toTitle}>
              <div className={styles.header}>分别转发给</div>
              <div className={styles.totalCount}>
                {selectedList?.length > 0 &&
                  `已选择${selectedList?.length || 0}个聊天`}
              </div>
              <div className={styles.closeIcon}>
                <img src={closeIcon} onClick={() => onClose()}></img>
              </div>
            </div>

            <div className={styles.content}>
              {selectedList?.map((item) => {
                if (
                  'groupID' in item &&
                  item.groupID != null &&
                  item.groupID !== ''
                ) {
                  return (
                    <ChannelGroupItem
                      keyword=""
                      key={item.groupID}
                      position="right"
                      conversation={item}
                      selectedList={selectedList}
                      handleSelected={handleSelected}
                    />
                  );
                } else if ('employeeID' in item) {
                  return (
                    <ChannelUserItem
                      keyword=""
                      position="right"
                      key={item.employeeID}
                      employeeInfo={item}
                      selectedList={selectedList}
                      handleSelected={handleSelected}
                    />
                  );
                } else {
                  return <></>;
                }
              })}
            </div>
            <div className={styles.line}></div>
            <div className={styles.msgWrapper}>{renderForwardMessage()}</div>
            <div className={styles.footer}>
              <div
                className={classNames(styles.footerBtn, styles.cancel)}
                onClick={() => onClose()}
              >
                取消
              </div>
              <div
                className={classNames(
                  styles.footerBtn,
                  styles.confirm,
                  _.isEmpty(selectedList) && styles.disable
                )}
                onClick={() => onForwardMsg()}
              >
                发送
              </div>
            </div>
          </div>
        </div>
      </div>
      {imgPreviewModalOpen && (
        <ImgPreviewModal
          open={open}
          onClose={() => setImgPreviewModalOpen(false)}
          download={download}
          imgMsg={message}
          fileName={fileName}
          showName={''}
          isInput={true}
          imgInfo={{
            url: message.pictureElem?.snapshotPicture.url,
            ...message.pictureElem?.sourcePicture,
          }}
          openView={openView}
        />
      )}
      {multiMessageModalOpen && (
        <MultiMessageModal
          modalOpen={multiMessageModalOpen}
          oncancel={() => setMultiMessageModalOpen(false)}
          message={forwardMessage}
          hasForwardBtn={false}
        />
      )}
    </Modal>
  );
};
export default ForwardModal;
