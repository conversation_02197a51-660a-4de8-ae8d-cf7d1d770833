import { useState } from 'react';
import styles from './index.less';
import ChannelItem from '../ChanelItem/ChannelGroupItem';
import { SeachedItemType, SearchItemProp } from '../..';
import ChannelUserItem from '../ChanelItem/ChannelUserItem';

interface Props {
  keyword: string;
  isSearching: boolean;
  searchList?: SearchItemProp;
  selectedList: SeachedItemType[];
  handleSelected: (conversation: SeachedItemType, isSelected: boolean) => void;
}

const SearchPannel = ({
  keyword,
  isSearching,
  searchList,
  selectedList,
  handleSelected,
}: Props) => {
  const [expand, setExpand] = useState<boolean>(false);

  const [groupExpand, setGroupExpand] = useState<boolean>(false);

  const changeExpand = (key: 'expand' | 'groupExpand') => {
    if (key === 'expand') {
      setExpand((prev) => !prev);
    } else if (key === 'groupExpand') {
      setGroupExpand((prev) => !prev);
    }
  };

  const hasValidInfo =
    (searchList?.conversationList != null &&
      searchList?.conversationList?.length > 0) ||
    (searchList?.userList != null && searchList?.userList?.length > 0);

  return (
    <>
      {isSearching && <></>}

      {!isSearching && hasValidInfo && (
        <>
          {searchList.userList?.length !== 0 && (
            <div className={styles.container}>
              <div className={styles.header}>
                <div className={styles.title}>联系人</div>
                <div
                  className={styles.moreBtn}
                  onClick={() => changeExpand('expand')}
                >
                  {searchList.userList?.length > 3 &&
                    (expand ? '收起' : '查看更多')}
                </div>
              </div>
              <div
                className={styles.list}
                style={{ maxHeight: expand ? '' : '204px' }}
              >
                {searchList.userList?.map((item) => {
                  return (
                    <ChannelUserItem
                      keyword={keyword}
                      key={item.employeeID}
                      employeeInfo={item}
                      selectedList={selectedList}
                      handleSelected={handleSelected}
                    />
                  );
                })}
              </div>
            </div>
          )}
          {searchList.conversationList?.length !== 0 && (
            <div className={styles.container}>
              <div className={styles.header}>
                <div className={styles.title}>群聊</div>
                <div
                  className={styles.moreBtn}
                  onClick={() => changeExpand('groupExpand')}
                >
                  {searchList.conversationList?.length > 3 &&
                    (groupExpand ? '收起' : '查看更多')}
                </div>
              </div>
              <div
                className={styles.list}
                style={{ maxHeight: groupExpand ? '' : '201px' }}
              >
                {searchList.conversationList?.map((item) => {
                  return (
                    <ChannelItem
                      keyword={keyword}
                      key={item.groupID}
                      conversation={item}
                      selectedList={selectedList}
                      handleSelected={handleSelected}
                    />
                  );
                })}
              </div>
            </div>
          )}
        </>
      )}

      {!isSearching && !hasValidInfo && (
        <div className={styles.emptyArea}>未匹配到相关结果</div>
      )}
    </>
  );
};

export default SearchPannel;
