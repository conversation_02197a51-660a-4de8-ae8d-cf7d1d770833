import React, { useEffect, useState } from 'react';
import { IMSDK } from '@/layouts/BasicLayout';
import selectedIcon from '@/assets/images/forwardModal/selected.png';
import unselectedIcon from '@/assets/images/forwardModal/unselected.png';
import excludeIcon from '@/assets/images/forwardModal/excludeIcon.svg';
import classNames from 'classnames';
import { SearchedConversationType } from '@/utils/utils';
import { ConversationItem } from '@ht/openim-wasm-client-sdk';
import GroupAvatar from '@/components/ChannelList/GroupAvatar';
import styles from './index.less';
import { SeachedItemType } from '../..';

interface Props {
  conversation: SearchedConversationType | ConversationItem;
  keyword: string;
  selectedList: SeachedItemType[];
  handleSelected: (conversation: SeachedItemType, isSelected: boolean) => void;
  position?: 'left' | 'right';
}

const ChannelGroupItem: React.FC<Props> = ({
  conversation,
  selectedList,
  keyword,
  handleSelected,
  position = 'left',
}) => {
  const [isSelected, setIsSelected] = useState(false);
  const [groupMemberCount, setGroupMemberCount] = useState<number>(0);

  // 判断这里
  useEffect(() => {
    setIsSelected(
      selectedList?.some(
        (item) => 'groupID' in item && item.groupID === conversation.groupID
      )
    );
  }, [conversation.groupID, selectedList]);

  useEffect(() => {
    const initGroupInfo = async () => {
      if (conversation.groupID) {
        const { data } = await IMSDK.getSpecifiedGroupsInfo([
          conversation.groupID,
        ]);
        const groupInfo = data[0];

        setGroupMemberCount(groupInfo?.memberCount || 0);
      }
    };

    initGroupInfo();
  }, [conversation.groupID]);

  const handleHighLight = (keyWord = '', nickname = '', groupCount = -1) => {
    if (!keyWord) {
      return `${nickname}${groupCount > 0 ? `(${groupCount})` : ''}`;
    }

    const regex = new RegExp(
      keyWord.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&'),
      'gi'
    );
    const highlighted = nickname.replace(
      regex,
      (match) => `<font style='color:#0074E2'>${match}</font>`
    );

    return `${highlighted}${groupCount > 0 ? `(${groupCount})` : ''}`;
  };

  const showExtraInfo =
    'keyword' in conversation &&
    ((keyword != null &&
      keyword !== '' &&
      conversation.nickname.includes(keyword)) ||
      (conversation.keyword != null && conversation.keyword !== ''));

  return (
    <div
      className={classNames(
        styles.channelContainer,
        isSelected ? styles.selected : ''
      )}
      onClick={() => {
        if (position === 'right') {
          return;
        }
        handleSelected(conversation, !isSelected);
      }}
    >
      <div className={classNames(styles[position], styles.content)}>
        {position === 'left' && (
          <img
            className={styles.selectedIcon}
            src={isSelected ? selectedIcon : unselectedIcon}
          ></img>
        )}

        <div>
          {<GroupAvatar faceMember={conversation?.faceMember} size="small" />}
        </div>
        <div className={styles.channelInfo}>
          <div className={styles.channelName}>
            <div
              // eslint-disable-next-line react/no-danger
              dangerouslySetInnerHTML={{
                __html: handleHighLight(
                  keyword,
                  conversation.showName,
                  groupMemberCount
                ),
              }}
              title={`${conversation.showName}(${groupMemberCount})`}
              className={styles.showName}
            ></div>
            {showExtraInfo && (
              <div className={styles.containerArea}>
                包含：
                <span
                  className={styles.nickname}
                  // eslint-disable-next-line react/no-danger
                  dangerouslySetInnerHTML={{
                    __html: handleHighLight(
                      conversation.keyword,
                      conversation.nickname
                    ),
                  }}
                ></span>
              </div>
            )}
          </div>
        </div>
        {position === 'right' && (
          <div
            className={styles.excludeArea}
            onClick={(e) => {
              e?.stopPropagation();
              handleSelected(conversation, false);
              return false;
            }}
          >
            <img src={excludeIcon} className={styles.excludeIcon} />
          </div>
        )}
      </div>

      {position === 'left' && <div className={styles.line}></div>}
    </div>
  );
};
export default ChannelGroupItem;
