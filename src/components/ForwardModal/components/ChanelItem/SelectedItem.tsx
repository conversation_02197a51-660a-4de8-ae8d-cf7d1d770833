import React, { useEffect, useState } from 'react';
import GroupAvatar from '@/components/ChannelList/GroupAvatar';
import { IMSDK } from '@/layouts/BasicLayout';
import selectedIcon from '@/assets/images/forwardModal/selected.png';
import unselectedIcon from '@/assets/images/forwardModal/unselected.png';
import classNames from 'classnames';
import { SearchedConversationType } from '@/utils/utils';
import { ConversationItem } from '@ht/openim-wasm-client-sdk';
import styles from './index.less';
import { SeachedItemType } from '../..';

interface Props {
  conversation: SearchedConversationType | ConversationItem;
  keyword: string;
  selectedList: SeachedItemType[];
  handleSelected: (conversation: SeachedItemType, isSelected: boolean) => void;
}

const SelectedItem: React.FC<Props> = ({
  conversation,
  selectedList,
  keyword,
  handleSelected,
}) => {
  const [isSelected, setIsSelected] = useState(false);
  const [groupMemberCount, setGroupMemberCount] = useState<number>(0);

  // 判断这里
  useEffect(() => {
    setIsSelected(
      selectedList?.some(
        (item) => 'groupID' in item && item.groupID === conversation.groupID
      )
    );
  }, [conversation.groupID, selectedList]);

  useEffect(() => {
    const initGroupInfo = async () => {
      if (conversation.groupID) {
        const { data } = await IMSDK.getSpecifiedGroupsInfo([
          conversation.groupID,
        ]);
        const groupInfo = data[0];

        setGroupMemberCount(groupInfo?.memberCount || 0);
      }
    };

    initGroupInfo();
  }, [conversation.groupID]);

  return (
    <div
      className={classNames(
        styles.channelContainer,
        isSelected ? styles.selected : ''
      )}
      onClick={() => handleSelected(conversation, !isSelected)}
    >
      <div className={styles.content}>
        <img
          className={styles.selectedIcon}
          src={isSelected ? selectedIcon : unselectedIcon}
        ></img>
        <div>
          {<GroupAvatar faceMember={conversation?.faceMember} size="small" />}
        </div>
        <div className={styles.channelInfo}>
          <div className={styles.channelName}>
            <div
              title={`${conversation.showName}(${groupMemberCount})`}
              className={styles.showName}
            >{`${conversation.showName}(${groupMemberCount})`}</div>
          </div>
        </div>
      </div>

      <div className={styles.line}></div>
    </div>
  );
};
export default SelectedItem;
