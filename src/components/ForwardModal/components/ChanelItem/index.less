.channelContainer {
  .content {
    &.left {
      width: 364px;
      margin: 0 8px;
      cursor: pointer;

      &:hover {
        background: rgba(107, 107, 108, 8%);
      }
    }

    &.right {
      width: 320px;
      cursor: default;
    }

    height: 66px;
    border-radius: 8px;
    display: flex;
    align-items: center;

    .selectedIcon {
      width: 20px;
      height: 20px;
      margin-left: 22px;
      margin-right: 16px;
    }

    .botSelectIcon {
      width: 20px;
      height: 20px;
      margin-left: 22px;
      margin-right: 16px;
      background: #e5e5e5;
      border-radius: 4px;
      border: 1px solid #c6c8ca;
    }

    .showName {
      font-size: 15px;
      font-weight: 600;
      color: var(--primary-text-color-1);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .channelInfo {
      display: flex;
      align-items: center;
      margin-left: 10px;
      flex: 1;
      overflow: hidden;
      .channelName {
        overflow: hidden;
      }
    }
  }

  .excludeArea {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    &:hover {
      background: rgba(107, 107, 108, 8%);
      border-radius: 6px;
    }
  }

  .excludeIcon {
    width: 14px;
    height: 14px;
  }

  .line {
    // width: 100%;
    height: 1px;
    background-color: rgba(0, 0, 0, 5%);
    margin: 0 10px;
  }
}
