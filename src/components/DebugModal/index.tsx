import React, { useState, useEffect } from 'react';
import { Modal, Table } from '@ht/sprite-ui';
// @ts-expect-error
import initSqlJs, { Database, SqlJsStatic } from '@jlongster/sql.js';
// @ts-expect-error
import { SQLiteFS } from 'absurd-sql-optimized';
// @ts-expect-error
import IndexedDBBackend from 'absurd-sql-optimized/dist/indexeddb-backend';
import { getSDK } from '@ht/openim-wasm-client-sdk';

import { getDBInstance } from '@/store/nouse';

import copy from 'copy-to-clipboard';

import './index.less';

(self as any).$RefreshReg$ = () => {};
(self as any).$RefreshSig$ = () => () => {};

// let getDBInstance: Promise<Database> | undefined;
let SQL: SqlJsStatic | undefined;

type DebugModalProps = {
  open: boolean;
  onClose: () => void;
};

const DebugModal: React.FC<DebugModalProps> = (props) => {
  const { open, onClose } = props;
  const [sqlString, setSqlString] = useState<any>('');
  const [resultString, setResultString] = useState<any>('');
  const [dataSource, setDataSource] = useState<any>([]);
  const [columns, setColumns] = useState<any>([]);

  const execSql = async (sql?: string) => {
    const DBinstance = await getDBInstance();
    const result = DBinstance.exec(sqlString);
    setResultString(JSON.stringify(result));
    const original = result[0].columns;
    const results = result[0].values;
    const resultDataSource: any = [];
    const resultColumns: any = [];
    original.forEach((item: any, index: number) => {
      resultColumns.push({
        title: item,
        dataIndex: item,
        key: item,
        // filters:[],
        // filterMode: "tree",
        // filterSearch: true,
        // onFilter: (value: string, record:any) => record.address.startsWith(value)
      });
    });
    results.forEach((result: any) => {
      const obj = {};
      result.forEach((item: any, index: number) => {
        obj[original[index]] = item;
      });
      resultDataSource.push(obj);
    });
    setColumns(resultColumns);
    setDataSource(resultDataSource);
  };

  return (
    <div>
      <Modal width={1500} open={open} footer={null} onCancel={onClose}>
        <textarea
          style={{ display: 'block', width: '100%', margin: '20px 0' }}
          value={sqlString}
          onChange={(e) => setSqlString(e.target.value)}
        />
        <button
          style={{ float: 'right' }}
          type="button"
          onClick={() => execSql()}
        >
          执行SQL
        </button>
        <button
          style={{ float: 'right' }}
          type="button"
          onClick={() => {
            copy(resultString);
          }}
        >
          复制输出结果
        </button>
        <button
          style={{ float: 'right' }}
          type="button"
          onClick={() => {
            setSqlString('');
            setResultString('');
          }}
        >
          清空SQL
        </button>
        <button
          style={{ float: 'right' }}
          type="button"
          onClick={() => {
            const sdk = getSDK();
            // sdk.createTextMessage('Hello world').then(({ data }) => {
            //   // 调用成功
            //   sdk.sendMessage({
            //     recvID: '',
            //     groupID: '464588223',
            //     message: data,
            //   });
            // });
            // sdk.testApi();
          }}
        >
          测试api
        </button>
        <Table dataSource={dataSource} columns={columns} />;
      </Modal>
    </div>
  );
};

export default DebugModal;
