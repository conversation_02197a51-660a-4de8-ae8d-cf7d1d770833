import React from 'react';
import classnames from 'classnames';
import styles from './index.less';

// eslint-disable-next-line @typescript-eslint/ban-types

interface WithScrollbarProps {
  domId: string;
  direction?: 'left' | 'right';
  minWidth?: number;
  maxWidth?: number;
  maxWidthCalc?: any;
  defaultWidth?: string | ((props: any) => string);
  dragBarClassName?: string;
  needLocalStorageWidth?: boolean;
}

interface State {
  pannelStyles: React.CSSProperties;
  scrollbarHeight: string;
}

// eslint-disable-next-line @typescript-eslint/ban-types
const withScrollbar = <P extends object>(
  WrappedComponent: React.ComponentType<P>, // 使 WrappedComponent 能被 JSX 使用
  withScrollProps: WithScrollbarProps
) => {
  const { needLocalStorageWidth = true } = withScrollProps;
  return class extends React.Component<P, State> {
    position = { x: 0, y: 0, width: 0 };

    pannelwidth = needLocalStorageWidth
      ? localStorage.getItem(`linkim.pannel.${withScrollProps.domId}.width`)
      : null;

    state: State = {
      pannelStyles: this.pannelwidth
        ? { width: this.pannelwidth, minWidth: this.pannelwidth }
        : {},
      scrollbarHeight: '100%',
    };

    initialWidth = window.innerWidth;

    childComponent = React.createRef<HTMLElement>(); // 确保这里引用的是 HTMLElement

    resizeObserver: ResizeObserver | null = null;

    componentDidMount() {
      // 确保子组件挂载后开始观察
      const child = this.childComponent.current;

      if (child) {
        this.resizeObserver = new ResizeObserver(this.handleResize);
        this.resizeObserver.observe(child);
      } else {
        console.warn('childComponent is not mounted yet.');
      }

      // 添加resize事件监听
      window.addEventListener('resize', this.handleWindowResize);
    }

    componentWillUnmount() {
      // 在卸载时断开监听
      if (this.resizeObserver) {
        this.resizeObserver.disconnect();
      }
      window.removeEventListener('resize', this.handleWindowResize);
    }

    handleWindowResize = () => {
      // 获取当前窗口宽度
      const currentWidth = window.innerWidth;

      const zoomLevel = currentWidth / this.initialWidth;
      if (zoomLevel !== 1) {
        const { domId, maxWidthCalc, minWidth = 210 } = withScrollProps;
        const dom = document.querySelector(`#${domId}`)?.parentElement;
        if (dom && maxWidthCalc) {
          let newWidth = dom.clientWidth * zoomLevel;
          const newMaxWidth = maxWidthCalc();

          // // 限制宽度在 minWidth 和 maxWidth 之间
          if (newWidth <= minWidth) {
            newWidth = minWidth; // 停留在最小宽度
          } else if (newWidth >= newMaxWidth) {
            newWidth = newMaxWidth; // 停留在最大宽度
          }
          dom.style.width = `${newWidth}px`;
          dom.style.minWidth = `${newWidth}px`;
          dom.style.maxWidth = `${newWidth}px`;
          localStorage.setItem(`linkim.pannel.${domId}.width`, `${newWidth}px`);
        }
      }
    };

    handleResize = () => {
      const { childComponent } = this;

      if (childComponent.current) {
        const scrollbarHeight = `${childComponent.current.scrollHeight}px`; // 使用 scrollHeight 来获取子组件的实际高度
        this.setState({ scrollbarHeight });
      }
    };

    handleScrollbarStart = (e: React.DragEvent) => {
      const { domId } = withScrollProps;

      // 设置拖拽效果
      if (e.nativeEvent.dataTransfer != null) {
        e.nativeEvent.dataTransfer.effectAllowed = 'move';
        e.nativeEvent.dataTransfer.dropEffect = 'move';
      }

      // 记录初始位置
      this.position.x = e.nativeEvent.pageX;
      this.position.y = e.nativeEvent.pageY;

      // 获取当前宽度
      const dom = document.querySelector(`#${domId}`)?.parentElement;
      if (dom) {
        const pannelStyle = window.getComputedStyle(dom);
        this.position.width = Number(pannelStyle.width.replace('px', ''));
      }
    };

    // 处理拖拽中
    handleDragOver = (e: React.DragEvent) => {
      e.preventDefault();
      if (e.nativeEvent.dataTransfer != null) {
        e.nativeEvent.dataTransfer.effectAllowed = 'move';
      }
    };

    // eslint-disable-next-line max-statements
    handleScrollbarDrag = (e: React.DragEvent) => {
      const {
        direction = 'left',
        minWidth = 210,
        maxWidth = direction === 'left' ? 800 : 1600,
        domId,
        maxWidthCalc,
      } = withScrollProps;
      let newMaxWidth = maxWidth;
      if (maxWidthCalc) {
        newMaxWidth = maxWidthCalc();
      }
      const nowPos = e.nativeEvent.pageX; // 当前鼠标位置
      if (nowPos <= 0) {
        // 鼠标移除窗口范围（如双屏跨屏），忽略拖拽
        return;
      }
      let newWidth; // 计算新的宽度
      if (direction === 'left') {
        // 组件在左侧
        // 往右拖动：宽度增加
        // 往左拖动：宽度减小
        newWidth =
          this.position.width + (nowPos - this.position.x) <= 0
            ? newWidth
            : this.position.width + (nowPos - this.position.x);
      } else {
        // 组件在右侧
        // 往左拖动：宽度增加
        // 往右拖动：宽度减小
        newWidth = this.position.width - (nowPos - this.position.x);

        if (newWidth <= 0) {
          newWidth = minWidth;
        }
      }
      if (!newWidth) {
        return;
      }

      // 限制宽度在 minWidth 和 maxWidth 之间
      if (newWidth <= minWidth) {
        newWidth = minWidth; // 停留在最小宽度
      } else if (newWidth >= newMaxWidth) {
        newWidth = newMaxWidth; // 停留在最大宽度
      }

      // 更新 DOM 宽度并保存到 localStorage
      const dom = document.querySelector(`#${domId}`)?.parentElement;
      if (dom) {
        dom.style.width = `${newWidth}px`;
        dom.style.minWidth = `${newWidth}px`;
        dom.style.maxWidth = `${newWidth}px`;
        localStorage.setItem(`linkim.pannel.${domId}.width`, `${newWidth}px`);
      }
    };

    render() {
      const {
        domId,
        direction = 'left',
        defaultWidth = '24%',
        dragBarClassName = '',
      } = withScrollProps;

      const dragBarPositon = direction === 'left' ? 'right' : 'left';

      return (
        <div
          className={styles.leftSiderBar}
          onDragOver={this.handleDragOver}
          style={{
            width: defaultWidth,
            minWidth: defaultWidth,
            ...this.state.pannelStyles,
          }}
        >
          <div
            draggable="true"
            onDragStart={this.handleScrollbarStart}
            onDrag={this.handleScrollbarDrag}
            className={classnames(
              styles.scrollbar,
              dragBarClassName && styles[dragBarClassName]
            )}
            style={{
              [dragBarPositon]: 0,
              height: this.state.scrollbarHeight, // 使用动态设置的高度
            }}
            id={domId}
          ></div>
          <div
            ref={this.childComponent}
            style={{
              height: '100%',
            }}
          >
            <WrappedComponent {...this.props} />
          </div>
        </div>
      );
    }
  };
};

export default withScrollbar;
