import { useState, useEffect, useCallback } from 'react';
import { Modal, Input, List, Button } from '@ht/sprite-ui';
import { ConversationItem, GroupMemberItem } from '@ht/openim-wasm-client-sdk';
import useGroupMembers from '@/hooks/useGroupMembers';

import { useUserStore } from '@/store';
import { IMSDK } from '@/layouts/BasicLayout';
import { feedbackToast } from '@/utils/common';
import styles from './index.less';
import OIMAvatar from '../OIMAvatar';

interface TransferOwnerModalProps {
  conversation: ConversationItem;
  onClose: () => void;
}
const TransferOwnerModal = ({
  conversation,
  onClose,
}: TransferOwnerModalProps) => {
  const [searchText, setSearchText] = useState('');
  const [users, setUsers] = useState<GroupMemberItem[]>([]);
  const [selectedUser, setSelectedUser] = useState<GroupMemberItem>();

  const { userID } = useUserStore.getState().selfInfo;
  const { fetchState, getMemberData, resetState } = useGroupMembers({
    groupID: conversation?.groupID,
  });

  const initOrRefreshData = useCallback(() => {
    if (conversation?.groupID) {
      getMemberData(true);
    }
  }, [conversation?.groupID, getMemberData]);

  useEffect(() => {
    initOrRefreshData();
    return () => resetState();
  }, [conversation.groupID]);

  const { groupMemberList } = fetchState;

  useEffect(() => {
    if (groupMemberList) {
      setUsers(groupMemberList.filter((item) => item.userID !== userID));
    }
  }, [groupMemberList]);
  const handleSearch = (e) => {
    setSearchText(e.target.value);
  };

  const handleSelectUser = (user: GroupMemberItem) => {
    setSelectedUser(user);
    setUsers(users.filter((u) => u.userID !== user.userID));
  };

  const handleRemoveUser = () => {
    if (selectedUser) {
      setUsers([...users, selectedUser]);
      setSelectedUser(undefined);
    }
  };

  const handleConfirm = async () => {
    try {
      if (selectedUser != null) {
        await IMSDK.transferGroupOwner({
          groupID: conversation.groupID,
          newOwnerUserID: selectedUser?.userID,
        });
      } else {
        feedbackToast({ msg: '请先选择一个用户' });
      }
    } catch (e) {}
    onClose();
  };

  return (
    <Modal
      title="转让群组"
      open={true}
      onCancel={onClose}
      destroyOnClose={true}
      onOk={handleConfirm}
    >
      <div style={{ display: 'flex', gap: '16px' }}>
        {/* 左侧选择框 */}
        <div
          style={{
            flex: 1,
            borderRight: '1px solid #ddd',
            paddingRight: '16px',
          }}
        >
          <Input
            placeholder="搜索用户"
            value={searchText}
            onChange={handleSearch}
          />
          <List
            dataSource={users.filter(
              (user) =>
                user.groupID.includes(searchText) ||
                user.nickname.includes(searchText)
            )}
            renderItem={(user) => (
              <List.Item
                onClick={() => handleSelectUser(user)}
                style={{ cursor: 'pointer' }}
              >
                <OIMAvatar
                  userID={user.userID}
                  className={styles.avatar}
                  size={20}
                />
                <div className={styles.name}>
                  {user.nickname} {user.employeeCode}
                </div>
              </List.Item>
            )}
          />
        </div>

        {/* 右侧已选择 */}
        <div style={{ flex: 1 }}>
          {selectedUser ? (
            <List.Item>
              <OIMAvatar
                userID={selectedUser.userID}
                className={styles.avatar}
                size={20}
              />
              <div className={styles.name}>
                {selectedUser.nickname} {selectedUser.employeeCode}
              </div>
              <Button type="link" danger={true} onClick={handleRemoveUser}>
                移除
              </Button>
            </List.Item>
          ) : (
            <p>未选择用户</p>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default TransferOwnerModal;
