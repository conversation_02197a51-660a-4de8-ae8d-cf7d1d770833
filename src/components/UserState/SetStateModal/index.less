.setStateModalWrapper {
  :global {
    .linkflow-modal-content {
      background: #ffffff;
      border-radius: 8px;
      // border: 1px solid var(--primary-background-color-5);
    }
  }
  .forwardModalWarp {
    display: flex;
    height: 100%;
    flex-direction: column;
    padding: 22px 0 24px;
  }
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 20px;
    font-weight: 600;
    color: var(--primary-text-color-1);
    line-height: 28px;
    margin: 0 24px 23px;

    .title {
      height: 28px;
      font-size: 20px;
      font-weight: 600;
      color: #1d1c1d;
      line-height: 28px;
    }
    > img {
      width: 18px;
      cursor: pointer;
    }
  }
  .searchArea {
    position: relative;
    margin: 0 24px;
    width: 352px;
    height: 40px;
    background: #ffffff;
    border-radius: 4px;
    // border: 1px solid #979797;
    display: flex;
  }
  .search {
    height: 40px;
    border-radius: 4px;
  }
  .content {
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;

    .stateList {
      flex: 1;
      margin-top: 8px;
      .stateItem {
        display: flex;
        align-items: center;
        height: 32px;
        line-height: 32px;
        margin-top: 8px;
        padding: 0 24px;
        img {
          width: 18px;
          height: 18px;
          margin-right: 5px;
        }

        &:hover {
          cursor: pointer;
          background-color: #1264a3;
          color: #fff;
        }
      }
    }

    .duration {
      padding: 0 24px;

      :global {
        .linkflow-select-selection-item {
          line-height: 40px;
        }

        .linkflow-select-selector {
          border-radius: 4px !important;
          // border: 1px solid #979797 !important;
        }
      }
    }

    .title {
      margin: 12px 0 8px;
      color: #1d1c1d;
      height: 20px;
      line-height: 20px;
      font-size: 14px;
      font-weight: 600;
    }

    .select {
      width: 352px;
      // height: 40px;
      background: #ffffff;
      border-radius: 4px;
      // border: 1px solid #979797;
    }

    // .optionItem {
    //   height: 32px;
    //   color: black;
    //   background-color: red;
    //   line-height: 32px;
    //   padding: 0 16px;
    //   &:hover {
    //     background-color: #1264a3;
    //     color: #fff;
    //   }
    // }
  }
  .footer {
    display: flex;
    flex-direction: row-reverse;
    margin: 16px 24px 0;
    .footerBtn {
      cursor: pointer;
      width: 80px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      color: #1d1c1d;
      font-weight: 500;
      line-height: 22px;
      margin-left: 16px;
      border-radius: 6px;
      border: 1px solid #c6c8ca;
      background: #ffffff;

      &.save {
        background: #007a5a;
        border-radius: 6px;
        color: #fff;
      }

      &.disable {
        cursor: not-allowed;
        background: var(--primary-background-color-5);
        color: #454447;
      }

      .cancel,
      .clear {
        background: #f4f5f7;
        border-radius: 6px;
        color: #1d1c1d;
      }
    }
  }
}

.fuzzyPopupClassName {
  max-height: 300px;
  overflow-y: scroll;
  padding: 0;
  margin: 16px 0;
  font-size: 14px;
  color: #1d1c1d;
  .optionItem {
    padding: 0 10px;
    width: 100%;
    display: flex;
    align-items: center;
    height: 32px;
    line-height: 32px;
    // padding: 12px;
    cursor: pointer;
    &:hover {
      background-color: #1264a3;
      color: #fff;
    }
  }
}
