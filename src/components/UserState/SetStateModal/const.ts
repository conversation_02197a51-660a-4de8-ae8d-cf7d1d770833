import defaultIcon from '@/assets/userDetail/defaultIcon.png';
import selfStateIcon from '@/assets/userDetail/selfStateIcon.png';
import inMeetingIcon from '@/assets/userDetail/inMeetingIcon.png';
import onBusinessTripIcon from '@/assets/userDetail/onBusinessTripIcon.png';
import sickLeaveIcon from '@/assets/userDetail/sickLeaveIcon.png';
import onVacationIcon from '@/assets/userDetail/onVacationIcon.png';
import temporarilyAwayIcon from '@/assets/userDetail/temporarilyAwayIcon.png';
import doNotDisturbIcon from '@/assets/userDetail/doNotDisturbIcon.png';
import offWorkIcon from '@/assets/userDetail/offWorkIcon.png';
import { DurationType, StatusType, UserStatusType } from '@/store/type';

type statuInfoType = {
  type: StatusType;
  desc: string;
  imgSrc: string;
};

export const statusInfoList: statuInfoType[] = [
  {
    type: 'InMeeting',
    desc: '会议中',
    imgSrc: inMeetingIcon,
  },

  {
    type: 'OnBusinessTrip',
    desc: '差旅中',
    imgSrc: onBusinessTripIcon,
  },
  {
    type: 'SickLeave',
    desc: '病假',
    imgSrc: sickLeaveIcon,
  },
  {
    type: 'OnVacation',
    desc: '休假',
    imgSrc: onVacationIcon,
  },
  {
    type: 'TemporarilyAway',
    desc: '短暂离开',
    imgSrc: temporarilyAwayIcon,
  },
  {
    type: 'DoNotDisturb',
    desc: '请勿打扰',
    imgSrc: doNotDisturbIcon,
  },
  {
    type: 'OffWork',
    desc: '下班了',
    imgSrc: offWorkIcon,
  },
];

type durationTypeOptionType = {
  type: DurationType;
  desc: string;
};

export const durationTypeOptionList: durationTypeOptionType[] = [
  {
    type: 'Permanent',
    desc: '不要清除',
  },

  {
    type: '30Minutes',
    desc: '30分钟',
  },
  {
    type: '1Hour',
    desc: '1小时',
  },
  {
    type: '4Hours',
    desc: '4小时',
  },
  {
    type: 'Today',
    desc: '今天',
  },
  {
    type: 'ThisWeek',
    desc: '本周',
  },
  {
    type: 'time',
    desc: '选择日期和时间',
  },
];

export const getShowDescByStatus = (status: UserStatusType) => {
  if (status == null) {
    return '';
  }
  const currentStatusInfo = statusInfoList.find(
    (item) => item.type === status?.code
  );

  if (currentStatusInfo) {
    return currentStatusInfo.desc;
  } else {
    return status?.desc;
  }
};
export const getStatusImgSrcByStatus = (status: UserStatusType) => {
  if (
    (status?.code == null || status.code === '') &&
    (status?.desc == null || status.desc === '')
  ) {
    return defaultIcon;
  }
  const currentStatusInfo = statusInfoList.find(
    (item) => item.type === status?.code
  );
  if (currentStatusInfo) {
    return currentStatusInfo?.imgSrc;
  } else {
    return selfStateIcon;
  }
};

export const getStatusImgSrcByDesc = (status?: string) => {
  if (status == null || status === '') {
    return defaultIcon;
  }
  const currentStatusInfo = statusInfoList.find((item) => item.desc === status);
  if (currentStatusInfo) {
    return currentStatusInfo?.imgSrc;
  } else {
    return selfStateIcon;
  }
};
