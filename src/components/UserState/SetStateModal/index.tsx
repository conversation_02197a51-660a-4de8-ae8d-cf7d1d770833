import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Input, InputRef, Modal } from '@ht/sprite-ui';
import closeIcon from '@/assets/channel/close.png';
import classNames from 'classnames';
import { useGlobalModalStore, useUserStore } from '@/store';
import { IMSDK } from '@/layouts/BasicLayout';

import styles from './index.less';
import {
  durationTypeOptionList,
  getShowDescByStatus,
  statusInfoList,
  getStatusImgSrcByDesc,
} from './const';

const SetStateModal = () => {
  const inputRef = useRef<InputRef>(null);

  const { isStatusModalOpen, setStatusModalOpen } = useGlobalModalStore();

  const { selfStatus, updateSelfState } = useUserStore();
  const [inputStatusValue, setInputStatusValue] = useState<string>();
  // const [searchValue, setSearchValue] = useState<string>(duration);
  const [btnStatus, setBtnStatus] = useState<string>('');
  const [searchValue, setSearchValue] = useState<
    | {
        value: string;
        label: string;
      }
    | undefined
  >({
    value: 'Permanent',
    label: '不要清除',
  });

  const [dropdownOpen, setDropdownOpen] = useState<boolean>(false);

  useEffect(() => {
    if (selfStatus != null) {
      setInputStatusValue(getShowDescByStatus(selfStatus));
    } else {
      setInputStatusValue(undefined);
    }
  }, [selfStatus, isStatusModalOpen]);

  // useEffect(() => {
  //   if (selfStatus != null) return 'clear';
  //   else if (selfStatus == null) return '保存,disable';
  // },[])
  const onInputChange = (e: any) => {
    setInputStatusValue(e.target.value.trim());
  };

  const prefixIcon = useMemo(() => {
    const imgSrc = getStatusImgSrcByDesc(inputStatusValue);

    return (
      <span style={{ marginLeft: 5, marginRight: 12 }}>
        <img
          src={imgSrc}
          style={{ width: 16, height: 16, marginTop: -3 }}
        ></img>
      </span>
    );
  }, [inputStatusValue]);

  const handleModalClose = () => {
    setInputStatusValue('');
    setSearchValue(undefined);
    setDropdownOpen(false);
    setStatusModalOpen(false);
  };

  const options = durationTypeOptionList.map((optionItem) => {
    return {
      label: optionItem.desc,
      value: optionItem.type,
    };
  });

  const dropdownRender = useCallback(
    (originNode: any) => {
      if (options?.length > 0) {
        return options?.map((option: any) => {
          return (
            <div
              key={option.value}
              className={styles.optionItem}
              onClick={() => {
                setDropdownOpen(false);
                setSearchValue(option);
              }}
            >
              {option.label}
            </div>
          );
        });
      } else {
        return originNode;
      }
    },
    [options]
  );

  const handleSave = async () => {
    const lastSelfStatus = selfStatus;
    try {
      const code = statusInfoList.find(
        (item) => item.desc === inputStatusValue
      );

      const param: { Code: any; Desc: any } = { Code: code?.type, Desc: '' };

      if (code == null) {
        param.Code = '';
        param.Desc = inputStatusValue;
      }

      setStatusModalOpen(false);
      updateSelfState(
        JSON.stringify({ code: param.Code, desc: param.Desc, duration: '' })
      );

      IMSDK.setSelfState(param);
    } catch (error) {
      updateSelfState(JSON.stringify(lastSelfStatus));
      console.error('更新状态失败', error);
    }
  };

  // const btnStatus2 = () => {
  //   if (selfStatus == null && inputStatusValue == null) {
  //     '保存'|'disabled'
  //    }
  //   else if (inputStatusValue != null) {
  //     '保存'
  //   } else if (selfStatus!=null) {
  //     '清除状态'
  //   }
  //   }
  // }

  const showSaveBtn =
    (selfStatus?.code == null && selfStatus?.desc == null) ||
    ((selfStatus?.code != null || selfStatus?.desc != null) &&
      inputStatusValue !== getShowDescByStatus(selfStatus));

  const saveBtnDisable =
    selfStatus?.code == null &&
    selfStatus?.desc == null &&
    (inputStatusValue === '' || inputStatusValue == null);

  return isStatusModalOpen ? (
    <Modal
      title={null}
      footer={null}
      closable={false}
      centered={true}
      wrapClassName={styles.setStateModalWrapper}
      open={isStatusModalOpen}
      destroyOnClose={true}
      onCancel={handleModalClose}
      width={400}
      bodyStyle={{
        padding: 0,
        height: 464,
      }}
      maskClosable={false}
      keyboard={true}
    >
      <div className={styles.forwardModalWarp}>
        <div className={styles.header}>
          <div className={styles.title}>设置状态</div>
          <img src={closeIcon} onClick={handleModalClose} />
        </div>
        <div className={styles.searchArea}>
          <Input
            className={styles.search}
            value={inputStatusValue}
            prefix={prefixIcon}
            placeholder="你的状态是什么？"
            onChange={onInputChange}
            ref={inputRef}
            onFocus={() => {}}
            onCompositionStart={onInputChange}
            onCompositionEnd={onInputChange}
            allowClear={true}
          />
        </div>
        {/* {inputStatusValue == null || inputStatusValue === '' ? ( */}
        <div className={styles.content}>
          <div className={styles.stateList}>
            {statusInfoList.map((statusItem) => (
              <div
                className={styles.stateItem}
                key={statusItem.type}
                onClick={() => {
                  setInputStatusValue(statusItem.desc);
                }}
              >
                <img src={statusItem.imgSrc}></img>
                <span>{statusItem.desc}</span>
              </div>
            ))}
          </div>
        </div>
        {/* ) : (
          <div className={styles.content}>
            <div className={styles.duration}>
              <div className={styles.title}>在......之后移除状态</div>
              <Select
                disabled={true}
                className={styles.select}
                popupClassName={styles.fuzzyPopupClassName}
                size={'large'}
                onDropdownVisibleChange={(visible) => setDropdownOpen(visible)}
                dropdownRender={dropdownRender}
                value={searchValue}
                options={options}
                open={dropdownOpen}
              />
            </div>
          </div>
        )} */}
        <div className={styles.footer}>
          {showSaveBtn ? (
            <>
              <div
                className={classNames(
                  styles.footerBtn,
                  saveBtnDisable ? styles.disable : styles.save
                )}
                onClick={saveBtnDisable ? () => {} : handleSave}
              >
                保存
              </div>
              <div
                className={classNames(styles.footerBtn, styles.cancel)}
                onClick={handleModalClose}
              >
                取消
              </div>
            </>
          ) : (
            <div
              className={classNames(styles.footerBtn, styles.clear)}
              onClick={handleModalClose}
            >
              取消
            </div>
          )}
        </div>
      </div>
    </Modal>
  ) : (
    <></>
  );
};

export default SetStateModal;
