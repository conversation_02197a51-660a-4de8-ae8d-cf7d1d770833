import { FC, memo, useEffect, useState } from 'react';
import { IMSDK } from '@/layouts/BasicLayout';
import ForwardModal from '@/components/ForwardModal';
import { MessageStatus } from '@ht/openim-wasm-client-sdk';
import styles from './index.less';

interface LinkwpsProps {
  conversationID: string | undefined;
}

const Linkwps: FC<LinkwpsProps> = ({ conversationID }) => {
  const [url, setUrl] = useState<string>('');
  const [forwardModal, setForwardModal] = useState<boolean>(false);
  const [message, setMessage] = useState<any>();

  const forwardDoc = async (docInfo: any) => {
    const { data } = await IMSDK.createCustomMessage({
      data: JSON.stringify({
        type: 'clouddocument',
        content: {
          ...docInfo,
          isShortCut: docInfo.isShortCuts,
        },
      }),
      extension: '',
      description: '',
    });
    setMessage({ ...data, status: MessageStatus.Succeed });
    setForwardModal(true);
  };

  useEffect(() => {
    if (conversationID) {
      getUrl(conversationID);
    } else {
      setUrl('');
    }
    window.forwardDoc = forwardDoc;
  }, [conversationID]);

  const getUrl = async (id: string) => {
    try {
      const { data } = await IMSDK.createFolder({
        conversationID: id,
      });
      const { fileCrdcId, folderId } = data || {};
      let { origin } = window.location;
      if (location.href.includes('http://localhost:8000')) {
        origin = 'http://eipsit.htsc.com.cn';
        // origin = 'http://localhost:8020';
      }
      const docUrl = `${origin}/htscPortalDocs/linkwps-im?folderId=${folderId}&fileCrdcId=${fileCrdcId}`;
      setUrl(docUrl);
    } catch (error) {
      console.error('linkWps', error);
    }
  };

  return (
    <div className={styles.linkwpsWarp}>
      {url && <iframe className={styles.iframeWarp} src={url}></iframe>}
      {forwardModal && (
        <ForwardModal
          open={forwardModal}
          onClose={() => setForwardModal(false)}
          message={message}
          isSender={true}
          isThread={false}
        />
      )}
    </div>
  );
};
export default memo(Linkwps);
