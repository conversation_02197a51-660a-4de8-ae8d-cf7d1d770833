import { FC, useEffect, useMemo, useState } from 'react';
import classNames from 'classnames';
import _ from 'lodash';
import { Modal, Tooltip, Dropdown, Menu } from '@ht/sprite-ui';
import { CheckOutlined } from '@ht-icons/sprite-ui-react';
import {
  ConversationItem,
  GroupMemberItem,
  GroupItem,
  MessageReceiveOptType,
  GroupMemberRole,
} from '@ht/openim-wasm-client-sdk';
import { useUserStore } from '@/store';
import { useConversationSettings } from '@/hooks/useConversationSettings';
import OIMAvatar from '@/components/OIMAvatar';
import deleteIcon from '@/assets/contact/deleteIcon.png';
import noNoticeIcon from '@/assets/images/chatSetting/noNotice.png';
import noticeIcon from '@/assets/images/chatSetting/notice.png';
import dropIcon from '@/assets/images/chatSetting/dropIcon.png';
import SettingDetails from './components/SettingDetails';
import SettingMembers from './components/SettingMembers';
import OtherSettings from './components/OtherSettings';
import styles from './index.less';

interface GroupSetModalProps {
  handleCancel: () => void;
  visible: boolean;
  isGroup: boolean;
  conversationInfo: ConversationItem | undefined;
  groupInfo: GroupItem | undefined;
  groupMemberList: GroupMemberItem[];
  afterConfirmBtnClick?: () => void;
  activedTab?: TabType;
}

export type TabType = 'details' | 'members' | 'settings';

interface TabItemProps {
  key: TabType;
  title: string;
  disabled?: boolean;
  renderItem?: any;
}

const ConversationSetModal: FC<GroupSetModalProps> = ({
  handleCancel,
  visible,
  isGroup,
  conversationInfo,
  groupInfo,
  groupMemberList = [],
  afterConfirmBtnClick,
  activedTab = 'details',
}) => {
  const { showName = '', userID = '', recvMsgOpt } = conversationInfo || {};
  const { ownerUserID } = groupInfo || {};
  const { userID: selfUserID } = useUserStore.getState().selfInfo;

  const [curTab, setCurTab] = useState<TabType>(activedTab);
  const [dropdownVisible, setDropdownVisible] = useState<boolean>(false);
  const { updateConversationMessageRemind } =
    useConversationSettings(conversationInfo);
  const selfUserInGroupInfo =
    _.filter(groupMemberList, (i) => i.userID === selfUserID)[0] || {};

  const ConversationSetTabs: TabItemProps[] = useMemo(() => {
    return [
      {
        key: 'details',
        title: '关于',
        disabled: false,
        renderItem: () => {
          return (
            <SettingDetails
              conversationInfo={conversationInfo}
              groupInfo={groupInfo}
              isGroup={isGroup}
              handleCancel={handleCancel}
            />
          );
        },
      },
      {
        key: 'members',
        title: `成员 ${
          groupMemberList.length > 0 ? groupMemberList.length : ''
        }`,
        disabled: !isGroup,
        renderItem: () => {
          return (
            <SettingMembers
              groupMemberList={groupMemberList}
              conversationInfo={conversationInfo}
              handleCancel={handleCancel}
              afterConfirmBtnClick={afterConfirmBtnClick}
            />
          );
        },
      },
      {
        key: 'settings',
        title: '设置',
        disabled: !(
          isGroup && selfUserInGroupInfo.roleLevel !== GroupMemberRole.Normal
        ),
        renderItem: () => {
          return (
            <OtherSettings
              conversationInfo={conversationInfo}
              groupInfo={groupInfo}
              selfUserInGroupInfo={selfUserInGroupInfo}
              handleCancel={handleCancel}
            />
          );
        },
      },
    ];
  }, [
    conversationInfo,
    groupInfo,
    groupMemberList,
    isGroup,
    selfUserInGroupInfo,
  ]);

  const currentTabInfo = useMemo(() => {
    return _.filter(ConversationSetTabs, (i) => i.key === curTab)[0] || {};
  }, [ConversationSetTabs, curTab]);

  const MessageNotification: any = useMemo(() => {
    return [
      {
        title: <div>接受所有消息的通知</div>,
        tip: '你将收到关于发送至此群聊的所有消息通知',
        key: MessageReceiveOptType.Normal,
        btnicon: noticeIcon,
        label: (
          <div
            className={classNames(
              styles.dropItem,
              recvMsgOpt === MessageReceiveOptType.Normal && styles.activedItem
            )}
            onClick={() => {
              updateConversationMessageRemind(false);
              setDropdownVisible(false);
            }}
          >
            <CheckOutlined className={styles.checkedIcon} />
            <div>
              <div className={styles.dropItemTitle}>所有消息</div>
              <div className={styles.dropItemDesc}>接收所有消息的通知</div>
            </div>
          </div>
        ),
      },
      // {
      //   title: (
      //     <div>
      //       <img src="" alt="" />
      //       接受@提及的通知
      //     </div>
      //   ),
      //   tip: '当你在此群聊中被提及时，你会收到通知',
      //   ifShow: recvMsgOpt === 1,
      // },
      {
        title: (
          <div>
            <img src="" alt="" />
            通知关闭
          </div>
        ),
        tip: '你不会收到关于此群聊的通知',
        key: MessageReceiveOptType.NotNotify,
        btnicon: noNoticeIcon,
        label: (
          <div
            className={classNames(
              styles.dropItem,
              recvMsgOpt === MessageReceiveOptType.NotNotify &&
                styles.activedItem
            )}
            onClick={() => {
              updateConversationMessageRemind(
                true,
                MessageReceiveOptType.NotNotify
              );
              setDropdownVisible(false);
            }}
          >
            <CheckOutlined className={styles.checkedIcon} />
            <div>
              <div className={styles.dropItemTitle}>关</div>
              <div className={styles.dropItemDesc}>你将不会收到通知</div>
            </div>
          </div>
        ),
      },
      // {
      //   title: (
      //     <div>
      //       <img src="" alt="" />
      //       已静音
      //     </div>
      //   ),
      //   tip: '如果你在此群聊中被提及，你讲只会看到一个消息红点',
      // },
    ];
  }, [recvMsgOpt]);

  const curNotification = useMemo(() => {
    return _.filter(MessageNotification, (i) => i.key === recvMsgOpt)[0] || {};
  }, [MessageNotification, recvMsgOpt]);

  useEffect(() => {}, []);

  return (
    <Modal
      onCancel={() => handleCancel()} // 取消按钮回调
      footer={null} // 隐藏默认的底部按钮
      width={580}
      open={visible}
      centered={true}
      closable={false}
      maskClosable={false}
      className={styles.conversationSetModalWrapper}
      getContainer={document.getElementById('BasicLayoutId') || document.body}
    >
      <div className={styles.conversationSetModalContent}>
        <img
          className={styles.closeIcon}
          onClick={() => handleCancel()}
          src={deleteIcon}
          alt="icon"
        />
        <div className={styles.contentHeader}>
          {isGroup ? (
            <div className={styles.showName}># {showName}</div>
          ) : (
            <div className={styles.singleHeader}>
              <OIMAvatar
                userID={userID}
                size={72}
                borderRadius={12}
                style={{ margin: '8px 16px 8px 0' }}
                hideOnlineStatus={true}
              />
              <div>
                <div className={styles.showName}>{showName}</div>
              </div>
            </div>
          )}
        </div>
        <div className={styles.setActionsWrapper}>
          <Dropdown
            overlay={<Menu items={MessageNotification} />}
            trigger={['click']}
            overlayClassName={styles.recvMsgOptDropMenu}
            getPopupContainer={() =>
              document.getElementById('BasicLayoutId') || document.body
            }
            onOpenChange={(val) => setDropdownVisible(val)}
            open={dropdownVisible}
          >
            <Tooltip title={curNotification.tip}>
              <div className={styles.setAction}>
                <img
                  src={curNotification.btnicon}
                  className={styles.noticeIcon}
                />
                <span className={styles.title}>{curNotification.title}</span>
                <img className={styles.dropIcon} src={dropIcon} />
              </div>
            </Tooltip>
          </Dropdown>
        </div>
        <div className={styles.searchTabs}>
          {ConversationSetTabs.map((tab: TabItemProps) => {
            return (
              !tab.disabled && (
                <div
                  className={classNames(
                    styles.searchTabItem,
                    curTab === tab.key && styles.activedTab
                  )}
                  key={tab.key}
                  onClick={() => {
                    setCurTab(tab.key);
                  }}
                >
                  <span>{tab.title}</span>
                </div>
              )
            );
          })}
        </div>
        <div className={styles.setTabContent}>
          {currentTabInfo.renderItem()}
        </div>
      </div>
    </Modal>
  );
};

export default ConversationSetModal;
