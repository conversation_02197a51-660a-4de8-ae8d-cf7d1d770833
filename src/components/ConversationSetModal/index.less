.conversationSetModalWrapper {
  :global {
    .linkflow-modal-body {
      padding: 0;
      height: 100%;
    }

    .linkflow-modal-content {
      border-radius: 8px;
      height: min(85vh, 820px);
    }
  }
}

.conversationSetModalContent {
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;

  .closeIcon {
    position: absolute;
    top: 28px;
    right: 28px;
    cursor: pointer;
    width: 20px;
    height: 20px;
  }

  .contentHeader {
    display: flex;
    align-items: center;
    padding: 20px 76px 0 28px;

    .showName {
      font-size: 22px;
      font-weight: bold;
      color: var(--link-color-content-pry);
    }

    .singleHeader {
      display: flex;
      align-items: center;
    }
  }

  .setActionsWrapper {
    display: flex;
    align-items: center;
    margin: 12px 0 8px 28px;

    .setAction {
      min-width: 56px;
      height: 28px;
      padding: 0 12px 1px;
      font-size: 13px;
      margin-right: 8px;
      background-color: var(--link-color-base-pry);
      border: 1px solid var(--link-color-otl-sec);
      color: var(--link-color-content-pry);
      font-weight: bold;
      border-radius: 6px;
      display: flex;
      align-items: center;
      cursor: pointer;

      .noticeIcon {
        width: 15px;
        margin-right: 8px;
      }

      .dropIcon {
        width: 15px;
        margin-left: 8px;
      }
    }
  }

  .searchTabs {
    padding-left: 20px;
    box-shadow: inset 0 -1px 0 0 var(--link-color-otl-ter);

    .searchTabItem {
      color: var(--link-color-content-sec);
      box-shadow: inset 0 -2px #0000;
      font-size: 13px;
      cursor: pointer;
      font-weight: bold;
      text-align: center;
      white-space: nowrap;
      justify-content: center;
      align-items: center;
      margin: 0 20px 0 8px;
      padding: 9px 0;
      display: inline-flex;
      transition: box-shadow 0.125s ease-out;
    }

    .activedTab {
      color: var(--link-color-content-pry);
      box-shadow: inset 0 -2px 0 0 var(--link-color-theme-base-pry, var(--link-color-otl-hgl-1));
    }
  }

  .setTabContent {
    flex: 1;
    background-color: rgba(29, 28, 29, 4%);
    overflow-y: auto;
  }
}

.recvMsgOptDropMenu {
  border-radius: 6px;

  :global {
    .linkflow-dropdown-menu {
      padding: 12px 0;
      border-radius: 6px;
      border: 1px solid rgba(29, 28, 29, 13%);
      background-color: rgba(248, 248, 248, 100%);
    }

    .linkflow-dropdown-menu-item {
      padding: 0;
    }
  }

  .dropItem {
    font-size: 15px;
    padding: 0 24px 0 4px;
    display: flex;
    cursor: pointer;

    .dropItemTitle {
      padding-top: 4px;
      line-height: 22px;
    }

    .dropItemDesc {
      color: rgba(29, 28, 29, 70%);
      text-overflow: ellipsis;
      padding-bottom: 8px;
      font-size: 13px;
      line-height: 18px;
      overflow-x: hidden;
    }

    .checkedIcon {
      font-size: 13px;
      margin-top: 5px;
      margin-right: 4px;
      visibility: hidden;
    }

    &:hover {
      background-color: var(--link-color-otl-hgl-1);
      color: var(--link-color-content-inv-pry);

      .checkedIcon {
        color: var(--link-color-content-inv-pry);
      }

      .dropItemDesc {
        color: var(--link-color-content-inv-pry);
      }
    }
  }

  .activedItem {
    color: var(--link-color-otl-hgl-1);
    .checkedIcon {
      visibility: visible;
      color: var(--link-color-otl-hgl-1);
    }
  }
}
