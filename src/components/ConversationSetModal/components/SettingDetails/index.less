.settingDetailsWrapper {
  .moduleWrapper {
    background: rgba(255, 255, 255, 100%);
    border: 1px solid rgba(29, 28, 29, 13%);
    border-radius: 12px;
    margin: 16px 28px;

    .paddingBox {
      padding: 16px 20px;
      border-top: 1px solid rgba(29, 28, 29, 13%);

      &:first-of-type {
        border-top-left-radius: 12px;
        border-top-right-radius: 12px;
        border-top: 0;
      }

      &:last-of-type {
        border-bottom-right-radius: 12px;
        border-bottom-left-radius: 12px;
      }

      .titleHeader {
        font-size: 15px;
        line-height: 22px;
        font-weight: bold;
        justify-content: space-between;
        align-items: baseline;
        display: flex;

        .editBtn {
          font-size: 13px;
          line-height: 18px;
          font-weight: bold;
          color: rgba(18, 100, 163, 100%);
        }
      }

      .redHeader {
        color: rgba(224, 30, 90, 100%);
      }

      .desc {
        font-size: 15px;
        color: var(--link-color-content-pry);
        .emptyDesc {
          color: var(--primary-text-color-3);
        }
        .mdBox {
          max-height: 244px;
          padding-top: 4px;
          overflow: hidden;
        }
      }

      &:hover {
        cursor: pointer;
        background-color: rgba(29, 28, 29, 4%);
      }
    }

    .paddingBoxDisabled {
      opacity: 0.6;
    }
  }

  .bottomWrapper {
    font-size: 13px;
    line-height: 1.18px;
    color: rgba(29, 28, 29, 70%);
    margin: 20px 28px 20px 48px;
    display: flex;
    align-items: center;
  }
}
