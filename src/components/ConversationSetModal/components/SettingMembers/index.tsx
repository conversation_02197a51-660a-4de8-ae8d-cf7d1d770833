/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-lines */
import { useCallback, useEffect, useMemo, useState, FC } from 'react';
import { useHistory } from 'react-router-dom';
import _ from 'lodash';
import { Menu, Dropdown, Button } from '@ht/sprite-ui';
import { useConversationStore, useUserStore } from '@/store';
import { Virtuoso } from '@ht/react-virtuoso';
import {
  GroupMemberRole,
  ConversationItem,
  GroupMemberItem,
} from '@ht/openim-wasm-client-sdk';
import { feedbackToast } from '@/utils/common';
import { isBotUser } from '@/utils/avatar';
import SearchUser from '@/pages/contact/components/SearchUser';
import OIMAvatar from '@/components/OIMAvatar';
import moreMenuIcon from '@/assets/contact/moreMenuIcon.png';
import { useDeepCompareEffect } from 'ahooks';
import { UserItemType } from '@/components/AddGroupModal/type';
import { IMSDK } from '@/layouts/BasicLayout';
import classNames from 'classnames';
import styles from './index.less';

interface SettingMembersProps {
  groupMemberList: GroupMemberItem[];
  conversationInfo: ConversationItem | undefined;
  handleCancel: () => void;
  afterConfirmBtnClick?: () => void;
}

const SettingMembers: FC<SettingMembersProps> = ({
  groupMemberList,
  conversationInfo,
  handleCancel,
  afterConfirmBtnClick,
}) => {
  const { userID, employeeCode, nickname, faceURL } =
    useUserStore.getState().selfInfo;

  const [users, setUsers] = useState<UserItemType[]>();
  const [groupID, setGroupID] = useState<string>('');
  const [changeRoleData, setChangeRoleData] = useState<UserItemType[]>([]);
  const updateCurrentConversation = useConversationStore(
    (state) => state.updateCurrentConversation
  );

  const conversationList = useConversationStore(
    (state) => state.conversationList
  );
  const currentMemberInGroup = useConversationStore(
    (state) => state.currentMemberInGroup
  );
  const navigate = useHistory();

  const isShowOperateBtn = useCallback(
    (member) => {
      if (
        member.roleLevel === GroupMemberRole.Admin &&
        currentMemberInGroup?.roleLevel === GroupMemberRole.Admin
      ) {
        return false;
      }
      if (
        member.roleLevel !== GroupMemberRole.Owner &&
        member.employeeID !== currentMemberInGroup?.userID &&
        (currentMemberInGroup?.roleLevel === GroupMemberRole.Owner ||
          currentMemberInGroup?.roleLevel === GroupMemberRole.Admin)
      ) {
        return true;
      } else {
        return false;
      }
    },
    [currentMemberInGroup]
  );

  useDeepCompareEffect(() => {
    const initUserList = async () => {
      if (groupMemberList?.length >= 0) {
        const userIDLlst = groupMemberList?.map((item) => item.userID);

        const { data: userInfoList } = await IMSDK.getUsersInfo(userIDLlst);
        const result =
          groupMemberList?.map((item) => {
            if (item.userID != null && item.userID !== '') {
              const userEmployeeCode =
                userInfoList?.filter(
                  (userInfo) => userInfo.userID === item.userID
                )?.[0] || {};
              return {
                ...item,
                employeeID: userEmployeeCode.userID,
                employeeCode: userEmployeeCode?.employeeCode,
                employeeName: userEmployeeCode?.nickname,
              };
            } else {
              return { ...item, employeeID: item.userID };
            }
          }) || [];

        setUsers(result);
      } else {
        // 当前用户默认为群主

        setUsers([
          {
            employeeCode,
            employeeName: nickname,
            employeeID: userID,
            faceURL,
            roleLevel: GroupMemberRole.Owner,
          },
        ]);
      }
    };

    initUserList();
  }, [groupMemberList, employeeCode, nickname, faceURL]);

  useEffect(() => {
    if (groupID) {
      const currentconversation = conversationList?.filter(
        (conversation) => conversation.groupID === groupID
      );
      if (currentconversation?.[0]) {
        setGroupID('');
        updateCurrentConversation(currentconversation[0]);
        navigate.push(`./chat`);
      }
    }
  }, [groupID, conversationList, updateCurrentConversation, navigate]);

  const getMenu = (user: UserItemType) => {
    if (
      currentMemberInGroup?.roleLevel === GroupMemberRole.Owner &&
      !isBotUser(user.employeeID)
    ) {
      return (
        <Menu>
          <Menu.Item
            key="1"
            onClick={() =>
              setUserAsAdmin(
                user,
                user.roleLevel === GroupMemberRole.Admin
                  ? GroupMemberRole.Normal
                  : GroupMemberRole.Admin
              )
            }
            className={styles.menuItem}
          >
            {user.roleLevel === GroupMemberRole.Admin
              ? '取消管理权限'
              : '设为群组管理者'}
          </Menu.Item>
          <Menu.Item
            key="2"
            onClick={() => handleUserRemove(user)}
            className={styles.menuItem}
            style={{
              color: 'red',
            }}
          >
            移除群成员
          </Menu.Item>
        </Menu>
      );
    } else {
      return (
        <Menu>
          <Menu.Item
            key="1"
            onClick={() => handleUserRemove(user)}
            className={styles.menuItem}
            style={{
              color: 'red',
            }}
          >
            移除群成员
          </Menu.Item>
        </Menu>
      );
    }
  };

  const setUserAsAdmin = (user: UserItemType, memberRole: GroupMemberRole) => {
    const updatedUserList = users?.map((u) =>
      u.employeeID === user.employeeID ? { ...u, roleLevel: memberRole } : u
    );
    const index = changeRoleData?.findIndex(
      (u) => u.employeeID === user.employeeID
    );
    if (index > -1) {
      const data = _.clone(changeRoleData);
      data[index] = { ...user, roleLevel: memberRole };
      setChangeRoleData(data);
    } else {
      setChangeRoleData([
        ...changeRoleData,
        { ...user, roleLevel: memberRole },
      ]);
    }
    setUsers(updatedUserList);
  };

  const handleUserRemove = (user: UserItemType) => {
    const newUsers = users?.filter((u) => u.employeeID !== user.employeeID);
    if (newUsers != null && newUsers?.length > 0) {
      setUsers([...newUsers]);
    } else {
      setUsers(undefined);
    }
  };

  const handleAddUser = (user: UserItemType) => {
    if (user.employeeID === userID) {
      feedbackToast({ error: '已有当前成员', msg: '已有当前成员' });
      return;
    }
    const tempData = users?.filter(
      (item) => item.employeeID === user.employeeID
    );

    if (tempData?.length != null && tempData?.length !== 0) {
      feedbackToast({ error: '已有当前成员', msg: '已有当前成员' });
    } else {
      setUsers((prevUser) => {
        if (prevUser) {
          return [...prevUser, user];
        } else {
          return [user];
        }
      });
    }
  };

  const isDisable = useMemo(() => {
    let flag = true;
    if (
      currentMemberInGroup?.roleLevel === GroupMemberRole.Owner ||
      currentMemberInGroup?.roleLevel === GroupMemberRole.Admin
    ) {
      flag = false;
    }
    return false;
  }, [currentMemberInGroup?.roleLevel]);

  const showRole = (roleLevel: number | undefined) => {
    if (roleLevel === GroupMemberRole.Owner) {
      return <div className={styles.role}>群主</div>;
    } else if (roleLevel === GroupMemberRole.Admin) {
      return <div className={styles.role}>管理员</div>;
    } else {
      return '';
    }
  };

  const changeRole = async (data: UserItemType[], groupId: string) => {
    for (const item of data) {
      await IMSDK.setGroupMemberInfo({
        groupID: groupId,
        roleLevel: item.roleLevel,
        userID: item.employeeID,
      });
    }
  };

  const handleChangeGroupInfo = async () => {
    try {
      if (!users || !conversationInfo?.groupID) {
        return;
      }
      const addUser = users.filter((itemA) =>
        groupMemberList.every((itemB) => itemB.userID !== itemA.employeeID)
      );
      const removeUser = groupMemberList.filter((itemA) =>
        users.every((itemB) => itemB.employeeID !== itemA.userID)
      );
      if (addUser && addUser.length > 0) {
        await IMSDK.inviteUserToGroup({
          groupID: conversationInfo?.groupID,
          reason: '',
          userIDList: addUser.map((user) => user.employeeID),
        });
      }
      if (removeUser && removeUser.length > 0) {
        await IMSDK.kickGroupMember({
          groupID: conversationInfo?.groupID,
          reason: '',
          userIDList: removeUser.map((user) => user.userID),
        });
      }
      if (changeRoleData && changeRoleData.length > 0) {
        const data = changeRoleData.filter((user) =>
          users.some((item) => item.employeeID === user.employeeID)
        );
        await changeRole(data, conversationInfo?.groupID);
      }
      if (afterConfirmBtnClick) {
        afterConfirmBtnClick();
      }
      handleCancel();
    } catch (error) {
      console.error('修改群信息失败，原因为：', error);
      feedbackToast({ error, msg: '修改群信息失败' });
    }
  };

  return (
    <div className={styles.settingMembersWrapper}>
      <SearchUser
        disabled={isDisable}
        handleUserClicked={(item) => handleAddUser(item)}
        style={{ margin: '16px 28px 16px ', width: 'calc(100% - 56px)' }}
      />
      {/* 添加人员区域 */}
      <div className={styles.addMember}>
        <span>添加人员</span>
      </div>
      {/* 成员列表区域 */}
      <div className={styles.memberList}>
        <Virtuoso
          className={styles.virtuosoListContainer}
          data={users || []}
          // computeItemKey={(index, user) => user.employeeCode}
          itemContent={(_index, member) => {
            return (
              <div key={member.employeeID} className={styles.memberItem}>
                <OIMAvatar
                  userID={member.employeeID}
                  className={styles.avatar}
                  size={20}
                  borderRadius={4}
                />
                <div className={styles.name}>
                  {member.employeeName}
                  {isBotUser(member.employeeID) ? (
                    <div className={classNames(styles.tag, styles.tag_3)}>
                      机器人
                    </div>
                  ) : (
                    <div className={styles.code}>{member.employeeCode}</div>
                  )}
                  {showRole(member?.roleLevel)}
                </div>
                {isShowOperateBtn(member) && (
                  <Dropdown
                    overlay={getMenu(member)}
                    trigger={['click']}
                    placement="topLeft"
                    overlayClassName={styles.dropdownArea}
                  >
                    <img
                      className={styles.moreOptions}
                      src={moreMenuIcon}
                    ></img>
                  </Dropdown>
                )}
              </div>
            );
          }}
        />
      </div>
      <div className={styles.newChannel}>
        <Button
          onClick={_.debounce(handleChangeGroupInfo, 500, {
            leading: false,
            trailing: true,
          })}
          type="primary"
          disabled={isDisable}
        >
          确认
        </Button>
      </div>
    </div>
  );
};

export default SettingMembers;
