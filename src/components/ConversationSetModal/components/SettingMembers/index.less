.settingMembersWrapper {
  background-color: var(--primary-background-color-6);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;

  .addMember {
    height: 40px;
    display: flex;
    align-items: center;
    font-weight: bold;
    padding: 12px 28px;
  }

  .memberList {
    flex: 1;
    overflow: auto;
    .memberItem {
      display: flex;
      align-items: center;
      height: 40px;
      padding: 0 28px;

      .name {
        height: 100%;
        display: flex;
        align-items: center;
        flex: 1;
        margin-left: 14px;
      }

      .tag {
        padding: 0 4px;
        height: 16px;
        border-radius: 4px;
        font-size: 11px;
        display: flex;
        align-items: center;
        margin-left: 12px;
        line-height: 16px;
      }

      .tag_3 {
        color: #cc8521;
        background: #fcf3e6;
        border: 1px solid #cc8521;
      }

      .code {
        margin-left: 8px;
        font-size: 14px;
        font-weight: 400;
        color: var(--primary-text-color-7);
      }
      img {
        width: 24px;
        height: 24px;
      }
      .role {
        display: inline-block;
        padding: 2px 4px;
        font-size: 12px;
        font-weight: 400;
        color: var(--mention-backgroup-color);
        border-radius: 4px;
        border: 1px solid var(--mention-backgroup-color);
        margin-left: 10px;
      }

      .moreOptions {
        cursor: pointer;
        width: 22px;
        height: 20px;
      }
    }
  }

  .newChannel {
    padding: 20px 24px 24px;
    text-align: right;
    cursor: pointer;
    font-size: 16px;
    font-weight: 400;
    color: #ffffff;
  }
}
