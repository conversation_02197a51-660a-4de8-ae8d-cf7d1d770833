import { Modal, Input } from '@ht/sprite-ui';
import { FC, useMemo, useState } from 'react';
import _ from 'lodash';
import classNames from 'classnames';
import { IMSDK } from '@/layouts/BasicLayout';
import deleteIcon from '@/assets/contact/deleteIcon.png';
import { GroupItem } from '@ht/openim-wasm-client-sdk';
import styles from './index.less';

export type ModalType = 'groupName' | 'introduction';

interface EditNameModalProps {
  handleCancel: () => void;
  visible: boolean;
  groupInfo: GroupItem | undefined;
  modalType?: ModalType;
}

const EditNameModal: FC<EditNameModalProps> = ({
  handleCancel,
  visible,
  groupInfo,
  modalType = 'groupName',
}) => {
  const { groupName, groupID, introduction } = groupInfo || {};
  const [channelName, setChannelName] = useState(groupName);
  const [channelIntroduction, setChannelIntroduction] = useState(introduction);

  const handleSave = async () => {
    if (renderSaveDisabled) {
      return;
    }
    if (!groupID) {
      handleCancel();
      return;
    }
    try {
      if (modalType === 'groupName') {
        await IMSDK.setGroupInfo({
          groupID,
          groupName: channelName,
        });
      } else if (modalType === 'introduction') {
        await IMSDK.setGroupInfo({
          groupID,
          introduction: channelIntroduction,
        });
      }
      handleCancel();
    } catch (e) {
      console.error(`修改群${groupID}名称失败，原因为：`, e);
    }
  };

  const renderModalTitle = useMemo(() => {
    if (modalType === 'groupName') {
      return '重命名此群聊';
    } else if (modalType === 'introduction') {
      return '编辑说明';
    } else {
      return '重命名此群聊';
    }
  }, [modalType]);

  const renderSaveDisabled = useMemo(() => {
    if (modalType === 'groupName') {
      return channelName === groupName;
    } else if (modalType === 'introduction') {
      return introduction === channelIntroduction;
    }
    return true;
  }, [channelIntroduction, channelName, groupName, introduction, modalType]);

  return (
    <Modal
      onCancel={() => handleCancel()} // 取消按钮回调
      footer={null} // 隐藏默认的底部按钮
      width={520}
      open={visible}
      centered={true}
      closable={false}
      className={styles.editNameModalWrapper}
      getContainer={document.getElementById('BasicLayoutId') || document.body}
    >
      <div className={styles.editNameModalContent}>
        <img
          className={styles.closeIcon}
          onClick={() => handleCancel()}
          src={deleteIcon}
          alt="icon"
        />
        <div className={styles.modalTitle}>{renderModalTitle}</div>
        <div className={styles.modalLabel}>群聊名称</div>
        <Input
          placeholder="#请输入群聊名称"
          value={channelName}
          onChange={(e) => setChannelName(e.target.value)}
          className={styles.channelInput}
          prefix={_.isEmpty(channelName) ? <></> : <span>#&nbsp;</span>}
          maxLength={50}
          showCount={true}
        />
        <div className={styles.desc}>长度不能超过 50 个字符。</div>
        <div className={styles.bottom}>
          <div className={styles.btn} onClick={handleCancel}>
            取消
          </div>
          <div
            className={classNames(
              styles.btn,
              renderSaveDisabled ? styles.noColor : styles.saveBtn
            )}
            onClick={handleSave}
          >
            保存更改
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default EditNameModal;
