.editNameModalWrapper {
  :global {
    .linkflow-modal-body {
      padding: 0;
    }

    .linkflow-modal-content {
      border-radius: 8px;
    }
  }
}

.editNameModalContent {
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .closeIcon {
    position: absolute;
    top: 28px;
    right: 28px;
    cursor: pointer;
    width: 20px;
    height: 20px;
  }

  .modalTitle {
    display: flex;
    align-items: center;
    width: 100%;
    min-height: 70px;
    padding: 20px 76px 20px 28px;
    font-size: 22px;
    line-height: 30px;
    font-weight: bold;
    color: var(--link-color-content-pry);
  }

  .modalLabel {
    font-size: 15px;
    color: var(--link-color-content-pry);
    line-height: 22px;
    font-weight: bold;
    padding: 0 28px;
    margin-bottom: 5px;
  }

  .channelInput {
    margin: 0 28px;
    width: calc(100% - 56px);
    border-radius: 12px;
    height: 44px;
    border: 1px solid var(--link-color-otl-sec);
    padding: 4px 12px;
  }

  :global {
    .linkflow-input-affix-wrapper-focused {
      border-color: #0000;
      box-shadow: 0 0 0 1px rgba(18, 100, 163, 100%),
        0 0 0 5px color-mix(in srgb, #1d9bd1 30%, transparent);
    }
  }

  .desc {
    color: var(--link-color-content-sec);
    text-wrap: pretty;
    margin: 4px 28px 20px;
    font-size: 13px;
    font-weight: 400;
    line-height: 18px;
  }

  .desc1 {
    margin: 4px 28px 5px;
  }

  .bottom {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 24px 28px;

    .btn {
      border-radius: 8px;
      min-width: 80px;
      height: 36px;
      padding: 0 12px 1px;
      font-size: 15px;
      background-color: var(--link-color-base-pry);
      border: 1px solid var(--link-color-otl-sec);
      color: var(--link-color-content-pry);
      font-weight: bold;
      transition: all 80ms linear;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      cursor: pointer;

      &:last-of-type {
        margin-right: 0;
      }

      &:hover {
        background-color: var(--link-color-base-sec);
        box-shadow: 0 1px 3px #00000014;
      }
    }

    .saveBtn {
      border: 1px solid var(--link-color-base-inv-hgl-2);
      background-color: var(--link-color-base-inv-hgl-2);
      color: var(--link-color-constants-white);

      &:hover {
        background-image: linear-gradient(
          var(--link-color-base-inv-hgl-2-hover),
          var(--link-color-base-inv-hgl-2-hover)
        );
        background-color: var(--link-color-base-inv-hgl-2);
        border: 1px solid var(--link-color-base-inv-hgl-2-hover);
        color: var(--link-color-constants-white);
        box-shadow: 0 1px 4px #0000004d;
      }
    }

    .noColor {
      border-color: var(--link-color-base-ter) !important;
      background-color: var(--link-color-base-ter) !important;
      color: var(--link-color-content-sec) !important;
      cursor: default;
      background-image: none;
      box-shadow: none;
    }
  }
}
