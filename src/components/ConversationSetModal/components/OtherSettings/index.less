.otherSettingsWrapper {
  .moduleWrapper {
    background: rgba(255, 255, 255, 100%);
    border: 1px solid rgba(29, 28, 29, 13%);
    border-radius: 12px;
    margin: 16px 28px;

    .paddingBox {
      padding: 16px 20px;
      border-top: 1px solid rgba(29, 28, 29, 13%);

      &:first-of-type {
        border-top-left-radius: 12px;
        border-top-right-radius: 12px;
        border-top: 0;
      }

      &:last-of-type {
        border-bottom-right-radius: 12px;
        border-bottom-left-radius: 12px;
      }

      .titleHeader {
        font-size: 15px;
        line-height: 22px;
        font-weight: bold;
        justify-content: space-between;
        align-items: baseline;
        display: flex;

        .editBtn {
          font-size: 13px;
          line-height: 18px;
          font-weight: bold;
          color: rgba(18, 100, 163, 100%);
        }
      }

      .redHeader {
        color: rgba(224, 30, 90, 100%);
      }

      .desc {
        font-size: 15px;
        color: var(--link-color-content-pry);
      }

      &:hover {
        cursor: pointer;
        background-color: rgba(29, 28, 29, 4%);
      }
    }
  }

  .confirmText {
    font-size: 15px;
    font-weight: 400;
    color: #1d1c1d;
    line-height: 22px;
  }
}
