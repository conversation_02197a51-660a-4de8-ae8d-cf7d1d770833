import { FC, useState } from 'react';
import classNames from 'classnames';
import { IMSDK } from '@/layouts/BasicLayout';
import {
  ConversationItem,
  GroupItem,
  GroupStatus,
  GroupMemberItem,
  GroupMemberRole,
} from '@ht/openim-wasm-client-sdk';
import ConfirmModal from '@/components/ConfirmModal';
import { useConversationStore } from '@/store';
import closeIcon from '@/assets/images/chatSetting/closeIcon.svg';
import dismissIcon from '@/assets/images/chatSetting/dismiss.png';
import EditNameModal from '../EditNameModal';
import styles from './index.less';

interface OtherSettingsProps {
  conversationInfo: ConversationItem | undefined;
  groupInfo: GroupItem | undefined;
  selfUserInGroupInfo: GroupMemberItem | undefined;
  handleCancel: () => void;
}
const OtherSettings: FC<OtherSettingsProps> = ({
  conversationInfo,
  groupInfo,
  selfUserInGroupInfo,
  handleCancel,
}) => {
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [confirmModalVisible, setConfirmModalVisible] =
    useState<boolean>(false);
  const { showName, groupID = '' } = conversationInfo || {};

  const updateConversation = useConversationStore(
    (state) => state.updateConversation
  );
  const setCurrentMessageInputValue = useConversationStore(
    (state) => state.setCurrentMessageInputValue
  );

  const dismissGroup = () => {
    if (groupID) {
      try {
        IMSDK.dismissGroup(groupID);

        // 解散群时，清理当前群的草稿
        if (conversationInfo != null) {
          updateConversation({
            ...conversationInfo,
            draftText: '',
            draftTextTime: 0,
          });

          setCurrentMessageInputValue(null);
        }
        setConfirmModalVisible(false);
        handleCancel();
      } catch (error) {
        console.warn('dismissGroup', error);
      }
    }
  };

  const forbiddenTalk = () => {
    try {
      if (groupInfo?.groupID != null) {
        let isMute = false;
        if (groupInfo?.status === GroupStatus.Normal) {
          isMute = true;
        } else if (groupInfo?.status === GroupStatus.Muted) {
          isMute = false;
        }
        IMSDK.changeGroupMute({
          groupID: groupInfo?.groupID,
          isMute,
        });
      }
    } catch (e) {
      console.error('群禁言切换失败', e);
    }
  };

  return (
    <div className={styles.otherSettingsWrapper}>
      {selfUserInGroupInfo?.roleLevel === GroupMemberRole.Owner && (
        <div className={styles.moduleWrapper}>
          <div
            className={styles.paddingBox}
            onClick={() => setModalVisible(true)}
          >
            <div className={styles.titleHeader}>
              <div>群聊名称</div>
              <div className={styles.editBtn}>编辑</div>
            </div>
            <div className={styles.desc}># {showName}</div>
          </div>
        </div>
      )}
      {selfUserInGroupInfo?.roleLevel !== GroupMemberRole.Normal && (
        <div className={styles.moduleWrapper}>
          <div className={styles.paddingBox} onClick={forbiddenTalk}>
            <div className={classNames(styles.titleHeader)}>
              <div>
                {groupInfo?.status === GroupStatus.Muted
                  ? '取消群禁言'
                  : '群禁言'}
              </div>
            </div>
          </div>
        </div>
      )}
      {selfUserInGroupInfo?.roleLevel === GroupMemberRole.Owner && (
        <div className={styles.moduleWrapper}>
          <div
            className={styles.paddingBox}
            onClick={() => setConfirmModalVisible(true)}
          >
            <div className={classNames(styles.titleHeader, styles.redHeader)}>
              <div>
                <img
                  src={dismissIcon}
                  style={{ width: '20px', marginRight: '12px' }}
                />
                解散群
              </div>
            </div>
          </div>
        </div>
      )}
      {modalVisible && (
        <EditNameModal
          visible={modalVisible}
          handleCancel={() => setModalVisible(false)}
          groupInfo={groupInfo}
        />
      )}
      {confirmModalVisible && (
        <ConfirmModal
          open={confirmModalVisible}
          title="提示"
          closeIcon={<img src={closeIcon} />}
          maskClosable={false}
          okText="确认解散"
          cancelText="取消"
          onCancel={() => setConfirmModalVisible(false)}
          onOk={dismissGroup}
          centered={true}
          width={420}
        >
          <div className={styles.confirmText}>解散后移除所有的群成员</div>
        </ConfirmModal>
      )}
    </div>
  );
};

export default OtherSettings;
