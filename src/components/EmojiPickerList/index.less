.emojiPickerListPopover {
  padding: 0;
  :global {
    .linkflow-popover-arrow {
      display: none;
    }
    .linkflow-popover-inner {
      width: 200px;
      background: var(--link-color-base-pry);
      box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 3%);
      border-radius: 8px;
      border: 1px solid var(--primary-background-color-5);
      overflow: hidden;
    }

    .linkflow-popover-inner-content {
      padding: 0;
    }
  }

  .popoverContainer {
    display: flex;
    flex-direction: column;

    .contentHeader {
      padding: 12px;
      font-size: 14px;
      font-weight: bold;
      color: var(--link-color-otl-hgl-1);
      line-height: 25px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-bottom: 1px solid var(--primary-background-color-5);

      span {
        font-size: 18px;
      }
    }

    .contentList {
      margin-top: 8px;
      max-height: 230px;
      overflow-y: auto;
      padding-bottom: 8px;

      .listItem {
        display: flex;
        align-items: center;
        padding: 8px 16px;
        cursor: pointer;

        .OIMName {
          font-size: 14px;
          font-weight: 600;
          color: var(--link-color-content-pry);
          line-height: 20px;
          margin-left: 8px;
        }

        &:hover {
          background-color: var(--link-color-base-inv-hgl-1);

          .OIMName {
            color: var(--link-color-ctr-pry);
          }
        }
      }
    }
  }
}
