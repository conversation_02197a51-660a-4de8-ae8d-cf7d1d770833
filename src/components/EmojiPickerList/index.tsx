import { ReactNode, useEffect, useState } from 'react';
import { Popover } from '@ht/sprite-ui';
import { IMSDK } from '@/layouts/BasicLayout';
import { ReactionInfo } from '@/components/Channel/components/MessageItem/ReactionSummary';
import OIMAvatar from '@/components/OIMAvatar';
import { CardInfo } from '@/components/UserCardModal';
import styles from './index.less';

type EmojiPickerListProps = {
  children: ReactNode;
  reactionItem: ReactionInfo;
};

const EmojiPickerList = (props: EmojiPickerListProps) => {
  const { children, reactionItem } = props;

  return (
    <div>
      <Popover
        placement="bottomLeft"
        title={null}
        content={<UserList reactionItem={reactionItem} />}
        trigger="hover"
        overlayClassName={styles.emojiPickerListPopover}
        getPopupContainer={() =>
          document.getElementById('BasicLayoutId') || document.body
        }
        destroyTooltipOnHide={false}
      >
        {children}
      </Popover>
    </div>
  );
};

export default EmojiPickerList;

interface UserListProps {
  reactionItem: ReactionInfo;
}

const UserList = ({ reactionItem }: UserListProps) => {
  const [userIDsInfo, setUserIDsInfo] = useState<CardInfo[]>([]);

  useEffect(() => {
    handleUserID();
  }, [reactionItem]);

  const handleUserID = async () => {
    const { data } = await IMSDK.getUsersInfo(reactionItem.userID);
    try {
      const userIDArr: string[] = reactionItem.userID;
      data.sort(
        (a, b) => userIDArr.indexOf(a.userID) - userIDArr.indexOf(b.userID)
      );
    } catch (error) {}
    setUserIDsInfo(data);
  };

  return (
    <div className={styles.popoverContainer}>
      <div className={styles.contentHeader}>
        <span>{reactionItem.userID?.length}</span>人已标记
      </div>
      <div className={styles.contentList}>
        {userIDsInfo.map((info: CardInfo) => {
          return (
            <div className={styles.listItem} key={info.userID}>
              <OIMAvatar size={24} userID={info.userID} borderRadius={4} />
              <div className={styles.OIMName}>{info.nickname}</div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
