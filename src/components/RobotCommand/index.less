.robotCommandListContainer {
  // :global .linkflow-modal-content {
  //   background-color: var(--primary-text-color-2);
  //   color: #fff;
  // }
  display: block;
  position: absolute;
  bottom: 100px;
  left: 0;
  top: -150px;
  background: #fff;
  padding: 17px;
  width: 400px;
  border-radius: 10px;
  border: 1px solid var(--primary-background-color-5);
  &:focus {
    outline: none;
  }
  .robotCommandList {
    padding: 4px 0;

    .commandItem {
      min-height: 40px;
      display: flex;
      justify-content: space-around;
      align-items: center;
      cursor: pointer;
      &:hover,
      &.active {
        background-color: var(--primary-background-color);
      }
      .commandItemContent {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .selectedForm {
        width: 100%;
        padding: 4px;
        background-color: #fff;
      }
    }
  }
}
