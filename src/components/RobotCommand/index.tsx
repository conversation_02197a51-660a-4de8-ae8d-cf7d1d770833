/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable default-case */
/* eslint-disable indent */
import React, { ReactNode, useState, useEffect, useRef } from 'react';
import { List, Form, Modal, Button, Input, Tag } from '@ht/sprite-ui';
import { RobotCommand, RobotCommandOptions, useRobot } from '@/hooks/useRobot';
import classNames from 'classnames';
import styles from './index.less';

interface RobotCommandModalProps {
  open: boolean;
  onClose: () => void;
  messageInputRef: ReactNode;
  groupId: string;
  onSelected: (v: RobotCommand) => void;
  inputValue: string;
}

const RobotCommandModal = ({
  open,
  onClose,
  messageInputRef,
  groupId,
  onSelected,
  inputValue,
}: RobotCommandModalProps) => {
  const { robotCommandList } = useRobot(groupId);
  const [activeIndex, setActiveIndex] = useState<number>(0);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const container = containerRef.current;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (!open || !robotCommandList) {
        return;
      }

      switch (e.key) {
        case 'ArrowUp':
          e.preventDefault();
          setActiveIndex((prev) =>
            prev <= 0 ? robotCommandList.length - 1 : prev - 1
          );
          break;
        case 'ArrowDown':
          e.preventDefault();
          setActiveIndex((prev) =>
            prev >= robotCommandList.length - 1 ? 0 : prev + 1
          );
          break;
        case 'Enter':
          e.preventDefault();
          e.stopPropagation();
          if (activeIndex >= 0 && activeIndex < robotCommandList.length) {
            onSelected(robotCommandList[activeIndex]);
          }
          break;
      }
    };

    container?.addEventListener('keydown', handleKeyDown);
    if (open) {
      container?.focus();
    }
    return () => container?.removeEventListener('keydown', handleKeyDown);
  }, [open, robotCommandList, activeIndex, onSelected]);

  if (!robotCommandList) {
    return null;
  }

  return (
    <div
      ref={containerRef}
      className={styles.robotCommandListContainer}
      style={{
        display: open && robotCommandList?.length > 0 ? 'block' : 'none',
      }}
      tabIndex={0}
      // onBlur={(e) => {
      //   // 防止点击内部元素导致失焦
      //   if (!e.currentTarget.contains(e.relatedTarget)) {
      //     onClose();
      //   }
      // }}
    >
      <RobotCommands
        commandList={robotCommandList}
        onSelected={onSelected}
        activeIndex={activeIndex}
        setActiveIndex={setActiveIndex}
      />
    </div>
  );
};

const RobotCommands = ({
  commandList,
  onSelected,
  activeIndex,
  setActiveIndex,
}) => {
  return (
    <div className={styles.robotCommandList}>
      {commandList.map((command, index) => (
        <div
          key={command.id}
          className={classNames(styles.commandItem, {
            [styles.active]: activeIndex === index,
          })}
          onClick={() => {
            onSelected(command);
          }}
          onMouseEnter={() => setActiveIndex(index)}
          onMouseLeave={() => setActiveIndex(-1)}
        >
          <div className={styles.commandName}>{command.name}</div>
          <div className={styles.commandOptions}>
            {command.options.map((option) => (
              <Tag key={option.id} className={styles.optionTag}>
                {option.name}
              </Tag>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};

export default RobotCommandModal;
