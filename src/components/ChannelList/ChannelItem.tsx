/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-statements */
/* eslint-disable max-lines */
import { Dropdown, Typography } from '@ht/sprite-ui';
import dayjs from 'dayjs';
import isYesterday from 'dayjs/plugin/isYesterday';
import {
  ReactNode,
  useRef,
  useState,
  useMemo,
  memo,
  useCallback,
  useEffect,
} from 'react';
import classNames from 'classnames';
import {
  ConversationItem,
  MessageReceiveOptType,
  MessageType,
  MessageItem as MessageItemType,
  GroupAtType,
  MessageStatus,
  SessionType,
} from '@ht/openim-wasm-client-sdk';
import { getUserRoles, isBotUser, userRolesType } from '@/utils/avatar';
import { isEmpty } from 'lodash';
import redIcon from '@/assets/coversation/red.svg';
import notNotifyIcon from '@/assets/coversation/notNotify.svg';
import sendFailedIcon from '@/assets/coversation/sendFailedIcon.svg';
import GroupAnnouncementModal from '@/components/ConversationSetModal/components/GroupAnnouncementModal';
import NotificationMessageRender from '@/components/Channel/components/MessageItem/NotificationMessageRender';
import { getMarkdownFromJson, parserMdToText } from '@/utils/parserMdToHtml';
import { useConversationStore } from '@/store';
import styles from './index.less';
import ChannelMenu from './ChannelMenu';
import { mentionRegex } from '../MdEditor/plugin-mention/MentionShecma';
import { startsWithSlashNoSpace } from '../Channel/components/MessageInput';
import BotIconComponent from './BotIconComponent';
import ChannelHoverItem from './ChannelHoverItem';
import { UserDetailProps } from './ChannelItemList';

dayjs.extend(isYesterday);

interface ChannelItemProp {
  isActive: boolean;
  iconBoxRender: ReactNode;
  contact: ConversationItem;
  // handleConversationClicked: (conversation: any) => void;
  today: any;
  isLastPinnedItem?: boolean; // 是否是最后一个置顶元素
  userDetail?: UserDetailProps;
  children: ReactNode;
  isNotification?: boolean;
}

const renderSenderNickname = (msg: MessageItemType) => {
  return msg.sessionType !== SessionType.Single
    ? `${msg.senderNickname}：`
    : '';
};

export const renderMsgMd = (msg: MessageItemType, isMultiSession: boolean) => {
  let text =
    msg.textElem?.content || msg.quoteElem?.text || msg.atTextElem?.text || '';
  if (msg.contentType === MessageType.AtTextMessage) {
    text = text.replace(mentionRegex, '@$1') || '';
  }
  if (msg.contentType === MessageType.GroupAnnouncementUpdated) {
    const groupAnnouncementDetails = JSON.parse(msg.notificationElem!.detail);
    const { group } = groupAnnouncementDetails || {};
    text = group?.notification || '';
  }
  const userRoles = getUserRoles(msg.sendID);
  if (
    startsWithSlashNoSpace(text) &&
    (userRoles === userRolesType.developers ||
      userRoles === userRolesType.employees)
  ) {
    const jsonRegex = /(\{.*\}|\[.*\])/s;
    const match = jsonRegex.exec(text);
    if (match) {
      try {
        text = text.replace(match[0], '').trim();
      } catch (e) {
        console.error('提取的 JSON 无效:', e);
      }
    }
  }
  if (isMultiSession) {
    return <span className={styles.mdContent}>[{parserMdToText(text)}]</span>;
  } else {
    return (
      <span className={styles.mdContent}>
        {renderSenderNickname(msg)}
        {parserMdToText(text)}
      </span>
    );
  }
};

export const renderMsgText = (
  msg: MessageItemType,
  isMultiSession: boolean
) => {
  let content: any = {};
  if (msg.status === MessageStatus.Failed) {
    return (
      <span style={{ display: 'flex', alignItems: 'center' }}>
        发送失败
        <img src={sendFailedIcon} style={{ marginLeft: '3px' }} />
      </span>
    );
  }
  switch (msg.contentType) {
    case MessageType.TextMessage:
      return renderMsgMd(msg, isMultiSession);
    case MessageType.QuoteMessage:
      return renderMsgMd(msg, isMultiSession);
    case MessageType.AtTextMessage:
      return renderMsgMd(msg, isMultiSession);
    case MessageType.PictureMessage:
      return <span>{renderSenderNickname(msg)}[图片]</span>;
    case MessageType.FileMessage:
      return <span>{renderSenderNickname(msg)}[文件]</span>;
    case MessageType.MergeMessage:
      return <span>{renderSenderNickname(msg)}[聊天记录]</span>;
    case MessageType.CustomMessage:
      try {
        content = JSON.parse(msg?.customElem?.data || '{}');
      } catch (e) {
        content = msg?.customElem?.data;
      }
      if (content?.type === 'clouddocument') {
        return `${renderSenderNickname(msg)}[云文档]`;
      } else if (content?.type === 'stream') {
        let renderValue = content?.answer || '';
        let flag = true;
        if (content?.content?.answer || content?.content?.answer === '') {
          renderValue = content?.content?.answer || '';
        } else {
          flag = false;
          renderValue = content?.content || content?.answer || content || '';
        }
        return (
          <span className={styles.mdContent}>{`${renderSenderNickname(msg)}${
            flag ? parserMdToText(renderValue) : renderValue
          }`}</span>
        );
      } else {
        return renderMsgMd(msg, isMultiSession);
      }
    case MessageType.GroupAnnouncementUpdated:
      return renderMsgMd(msg, isMultiSession);
    default:
      return (
        <NotificationMessageRender
          message={msg}
          mseInfoClassName="mseInfo-channelItem"
        />
      );
  }
};

// 修改总高度时记得修改ChannelList的fixedItemHeight字段，保持高度一致！
const ChannelItem: React.FC<ChannelItemProp> = ({
  isActive,
  iconBoxRender,
  contact,
  children,
  today,
  isLastPinnedItem,
  isNotification = false,
  userDetail,
}) => {
  const userInfoRef = useRef<HTMLDivElement>(null);
  const [dropDownOpen, setDropDownOpen] = useState(false);
  const [showGroupAnnouncement, setShowGroupAnnouncement] = useState(false);
  const [announcementObj, setAnnouncementObj] = useState<any>({});
  const handleOpenChange = (open: boolean) => {
    setDropDownOpen(open);
  };

  const conversationID = useConversationStore(
    (state) => state.currentConversation?.conversationID
  );

  const handleConversationClicked = useConversationStore(
    (state) => state.handleConversationClicked
  );

  const [isMultiSession, setIsMultiSession] = useState<boolean>(false);

  useEffect(() => {
    if (userDetail != null && isBotUser(contact.userID)) {
      setIsMultiSession(userDetail?.multiSession === 1);
    } else {
      setIsMultiSession(false);
    }
  }, [contact.userID, userDetail]);

  const latestMsgObj = JSON.parse(contact.latestMsg || '{}');

  const { draftText } = contact;

  const atMe =
    contact.groupAtType === GroupAtType.AtAllAtMe ||
    contact.groupAtType === GroupAtType.AtMe ||
    contact.groupAtType === GroupAtType.AtAll;

  const groupNotice = contact.groupTag === 1;
  const renderUnRead = (flag = true) => {
    if (
      contact?.conversationID != null &&
      contact?.conversationID === conversationID
    ) {
      // 当前会话的未读数不展示
      return null;
    } else if (contact?.unreadCount && contact?.unreadCount > 0) {
      if (contact.recvMsgOpt === MessageReceiveOptType.Normal) {
        return (
          <div
            className={classNames(
              styles.unreadCount,
              !flag && styles.notificationCount
            )}
            style={
              contact?.unreadCount >= 100
                ? { width: '23px', borderRadius: '9px', right: '-9px' }
                : {}
            }
          >
            {contact?.unreadCount < 100 ? contact?.unreadCount : '99+'}
          </div>
        );
      } else {
        return <img src={redIcon} className={styles.redIcon} />;
      }
    }
    return null;
  };

  const renderLatestMsg = useCallback(() => {
    return renderMsgText(latestMsgObj, isMultiSession);
  }, [latestMsgObj, isMultiSession]);

  const renderDraftMsg = useCallback(() => {
    let showContent = '';
    try {
      const draftTextJSON =
        draftText != null && draftText !== '' ? JSON.parse(draftText) : '';

      if (draftTextJSON == null || draftTextJSON === '') {
        return renderLatestMsg();
      }

      if (draftTextJSON?.selectedCommand != null) {
        const { selectedCommand, formInfo } = draftTextJSON;
        showContent = `/${selectedCommand?.name}`;
      } else {
        draftTextJSON?.fileList?.map(() => (showContent += '[云文档]'));

        if (draftTextJSON.value != null && draftTextJSON.value !== '') {
          let text = getMarkdownFromJson(draftTextJSON.value);
          text = text.replace(mentionRegex, '@$1') || '';
          showContent += parserMdToText(text);
        }
      }
    } catch (e) {
      // console.error(e);
      return renderLatestMsg();
    }

    return (
      <>
        <span className={styles.draftTag}>[草稿]</span>
        <span className={styles.mdContent}>{showContent}</span>
      </>
    );
  }, [draftText, renderLatestMsg]);

  const renderAt = useCallback(() => {
    return (
      <>
        <span className={styles.mentionedTag}>[提到了我]</span>
      </>
    );
  }, []);

  const renderGroupNotice = useCallback(() => {
    return (
      <>
        <span className={styles.mentionedTag}>[群公告]</span>
      </>
    );
  }, []);

  const renderUnReadNumber = useCallback(() => {
    if (
      contact.recvMsgOpt !== MessageReceiveOptType.Normal &&
      contact?.unreadCount &&
      contact?.unreadCount > 0
    ) {
      return <span>{`[${contact?.unreadCount}条]`}</span>;
    }
    return '';
  }, [contact.recvMsgOpt, contact?.unreadCount]);

  const renderContentDesc = useCallback(() => {
    const showDraftMsg =
      contact?.conversationID !== conversationID && !atMe && !groupNotice;
    return (
      <div className={styles.contentDesc}>
        {renderUnReadNumber()}
        {groupNotice && renderGroupNotice()}
        {atMe && renderAt()}
        {showDraftMsg ? renderDraftMsg() : renderLatestMsg()}
      </div>
    );
  }, [
    atMe,
    groupNotice,
    contact?.conversationID,
    conversationID,
    renderAt,
    renderGroupNotice,
    renderDraftMsg,
    renderLatestMsg,
    renderUnReadNumber,
  ]);

  const renderTime = useMemo(() => {
    let currentTime = '';
    if (contact?.latestMsgSendTime) {
      const currentDate = dayjs(contact?.latestMsgSendTime);

      if (currentDate.isSame(today, 'day')) {
        if (currentDate.isAfter(dayjs().subtract(1, 'hour'))) {
          const diffInMinutes = today.diff(currentDate, 'minute');
          currentTime = diffInMinutes < 1 ? '刚刚' : `${diffInMinutes}分钟前`;
        } else {
          currentTime = currentDate.format('HH:mm');
        }
      } else if (currentDate.isYesterday()) {
        currentTime = '昨天';
      } else if (currentDate.isAfter(dayjs().endOf('day').subtract(7, 'day'))) {
        const weekDays = [
          '星期日',
          '星期一',
          '星期二',
          '星期三',
          '星期四',
          '星期五',
          '星期六',
        ];
        currentTime = weekDays[Number(currentDate.format('d'))];
      } else if (currentDate.isSame(today, 'year')) {
        currentTime = currentDate.format('MM/DD');
      } else {
        currentTime = currentDate.format('YYYY/MM/DD');
      }
    }
    return currentTime;
  }, [contact, today]);

  return (
    <>
      <div
        className={classNames(
          styles.threadContainer,
          isActive ? styles.active : '',
          contact?.isPinned && !isNotification ? styles.isPinned : '',
          isLastPinnedItem ? styles.isLastPinned : ''
        )}
        ref={userInfoRef}
        key={contact.conversationID}
        onClick={() => {
          if (isNotification) {
            window.electronAPI?.ipcSend('notification-message-click', contact);
          } else {
            handleConversationClicked(contact);
          }
        }}
      >
        <>
          <Dropdown
            trigger={['contextMenu']}
            overlayClassName={styles.rightButtonMenu}
            open={dropDownOpen}
            onOpenChange={(open) => handleOpenChange(open)}
            destroyPopupOnHide={true}
            overlay={
              <ChannelMenu
                conversation={contact}
                handleClose={() => handleOpenChange(false)}
                showAnnouncementModal={(val: any) => {
                  setAnnouncementObj(val);
                  setShowGroupAnnouncement(true);
                }}
              />
            }
            placement="bottomRight"
          >
            <div className={classNames(styles.threadBox)}>
              <div className={styles.iocnBox}>
                {iconBoxRender}
                {isNotification ? '' : renderUnRead()}
              </div>
              <div className={styles.rightContent}>
                <div className={styles.headerContainer}>
                  <div
                    style={{
                      flex: '1',
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      display: 'flex',
                      alignItems: 'center',
                    }}
                  >
                    <Typography.Text
                      ellipsis={true}
                      title={contact.showName}
                      className={styles.showName}
                    >
                      {contact.showName}
                    </Typography.Text>
                    {isEmpty(contact?.groupID) && (
                      <BotIconComponent userID={contact.userID} />
                    )}

                    <ChannelHoverItem userDetail={userDetail} />
                  </div>
                  {contact.recvMsgOpt !== MessageReceiveOptType.Normal && (
                    <img src={notNotifyIcon} className={styles.notNotifyIcon} />
                  )}
                  {isNotification ? renderUnRead(false) : ''}
                </div>
                <div className={styles.descContent}>
                  {renderContentDesc()}
                  <div
                    className={styles.time}
                    style={isNotification ? { display: 'none' } : {}}
                  >
                    {renderTime}
                  </div>
                </div>
              </div>
            </div>
          </Dropdown>
          {children}
        </>
      </div>
      {showGroupAnnouncement && (
        <GroupAnnouncementModal
          visible={showGroupAnnouncement}
          isAdmin={announcementObj?.isAdmin || false}
          onCancel={() => setShowGroupAnnouncement(false)}
          groupInfo={announcementObj?.groupInfo || {}}
          type={announcementObj?.groupInfo?.notification ? 'preview' : 'edit'}
        />
      )}
    </>
  );
};

export default memo(ChannelItem);
