import { FC, useMemo } from 'react';
import { getUserRoles, isBotUser, userRolesType } from '@/utils/avatar';
import styles from './index.less';

interface BotIconComponentProps {
  userID: string;
}

const BotIconComponent: FC<BotIconComponentProps> = ({ userID }) => {
  const isRobot = useMemo(() => {
    return isBotUser(userID);
  }, [userID]);

  return isRobot ? <div className={styles.botIcon}>机器人</div> : <></>;
};
export default BotIconComponent;
