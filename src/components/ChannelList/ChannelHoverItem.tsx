import { Popover } from '@ht/sprite-ui';
import styles from './index.less';
import {
  getShowDescByStatus,
  getStatusImgSrcByStatus,
} from '../UserState/SetStateModal/const';
import { UserDetailProps } from './ChannelItemList';

const ChannelHoverItem = ({ userDetail }: { userDetail?: UserDetailProps }) => {
  const { status: userState } = userDetail || {};

  return (
    <Popover
      overlayClassName={styles.popStateContainer}
      content={
        userState?.code ||
        (userState?.desc && (
          <div className={styles.state}>
            <img src={getStatusImgSrcByStatus(userState)} />
            <span style={{ marginLeft: 2 }}>
              {getShowDescByStatus(userState)}
            </span>
          </div>
        ))
      }
      placement="top"
      showArrow={true}
      autoAdjustOverflow={true}
      trigger={'hover'}
      arrowPointAtCenter={true}
    >
      {(userState?.code != null || userState?.desc != null) && (
        <img
          style={{ marginLeft: 8, width: 15, height: 15 }}
          src={getStatusImgSrcByStatus(userState)}
        ></img>
      )}
    </Popover>
  );
};

export default ChannelHoverItem;
