import { useState } from 'react';
import { ConversationItem, GroupAtType } from '@ht/openim-wasm-client-sdk';
import { Dropdown, Typography } from '@ht/sprite-ui';
import styles from './index.less';
import ChannelMenu from './ChannelMenu';

interface ThreadItemProps {
  thread: ConversationItem;
  index: number;
  length: number;
  isActive: boolean;
  onClick: () => void;
}
const ThreadItem = ({
  thread,
  index,
  length,
  isActive,
  onClick,
}: ThreadItemProps) => {
  const [dropDownOpen, setDropDownOpen] = useState(false);
  const handleOpenChange = (open: boolean) => {
    setDropDownOpen(open);
  };
  const isLast = index === length - 1;
  const isSingle = length === 1;
  const mentioned =
    thread.groupAtType === GroupAtType.AtAllAtMe ||
    thread.groupAtType === GroupAtType.AtMe;

  let verticalLineHeight;
  if (isSingle) {
    verticalLineHeight = 'calc(50% - 10px)';
  } else if (index === 0) {
    verticalLineHeight = 'calc(100% + 6px)';
  } else if (isLast) {
    verticalLineHeight = 'calc(50% + 4px)';
  } else {
    verticalLineHeight = 'calc(100% + 8px)';
  }

  let lineTop;
  if (isSingle) {
    lineTop = '6px';
  } else if (index === 0) {
    lineTop = '8px';
  } else if (isLast) {
    lineTop = '-8px';
  } else {
    lineTop = '0';
  }

  let horizontalLineTop;
  if (isSingle) {
    horizontalLineTop = 'calc(50% - 1px)';
  } else {
    horizontalLineTop = 'calc(50% - 3px)';
  }

  return (
    <div
      key={thread.groupID}
      onClick={(e) => {
        e.stopPropagation();
        onClick();
      }}
      onContextMenu={(e) => {
        e.preventDefault();
        e.stopPropagation();
      }}
      className={`${styles.threadItem} ${isActive ? styles.highlighted : ''}`}
    >
      <>
        <div className={styles.lineContainer}>
          {/* 竖线 */}
          <span
            className={styles.verticalLine}
            style={{
              height: verticalLineHeight,
              top: lineTop,
            }}
          ></span>

          {/* 横线 */}
          <span
            className={styles.horizontalLine}
            style={{
              top: horizontalLineTop,
              borderBottomLeftRadius: isLast ? '6px' : '0',
            }}
          ></span>
        </div>

        {/* thread聊天名称 */}
        <div
          className={`${styles.threadContent} ${
            isActive ? styles.highlighted : ''
          }`}
          onContextMenu={(e) => {
            e.preventDefault();
            e.stopPropagation();
            setDropDownOpen(true);
          }}
        >
          <Dropdown
            trigger={['contextMenu']}
            overlayClassName={styles.rightButtonMenu}
            open={dropDownOpen}
            onOpenChange={(open) => handleOpenChange(open)}
            overlay={
              <ChannelMenu
                conversation={thread}
                handleClose={() => handleOpenChange(false)}
              />
            }
            placement="bottomRight"
          >
            <>
              <div
                className={`${styles.threadName} ${
                  mentioned ? styles.mentioned : ''
                }`}
              >
                <Typography.Text ellipsis={true} title={thread.showName}>
                  {thread.showName}
                </Typography.Text>
              </div>
              {mentioned && <div className={styles.mentionedTag}>提到了我</div>}
              {thread?.unreadCount && thread?.unreadCount > 0 ? (
                <div className={styles.right}>{thread?.unreadCount}</div>
              ) : (
                ''
              )}
            </>
          </Dropdown>
        </div>
      </>
    </div>
  );
};

export default ThreadItem;
