import { FC } from 'react';
import { Dropdown, Menu } from '@ht/sprite-ui';
import addIcon from '@/assets/coversation/add.svg';
import styles from './index.less';

interface ChannelHeaderProps {
  handleAddClick: () => void;
}

const ChannelHeader: FC<ChannelHeaderProps> = ({ handleAddClick }) => {
  const menu = (
    <Menu
      rootClassName={styles.menu}
      items={[
        {
          label: '创建群聊',
          key: 'createNew',
          onClick: () => handleAddClick(),
          popupOffset: [0, 0],
        },
      ]}
    />
  );

  return (
    <div className={styles.header}>
      <div className={styles.headerTitle}>全部会话</div>
      <Dropdown
        overlay={menu}
        trigger={['click']}
        overlayClassName={styles.dropDownMenu}
        placement="bottomLeft"
      >
        <div className={styles.headerAddIcon}>
          <img src={addIcon} />
        </div>
      </Dropdown>
    </div>
  );
};

export default ChannelHeader;
