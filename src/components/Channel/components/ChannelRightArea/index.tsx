import { memo, useCallback, FC, useMemo } from 'react';
import { useConversationStore } from '@/store';
import { isEmpty } from 'lodash';
import UserDetail from '@/pages/contact/components/UserDetail';
import FilePreviewModal from '@/components/FilePreviewModal';
import RobotConversationList from '@/components/RobotConversation/RobotConversationList';
import RobotAnswerSource from '@/components/RobotConversation/RobotAnswerSource';
import RobotOnlineSearch from '@/components/RobotConversation/RobotOnlineSearch';
import ChannelHistory from '@/components/ChannelHistory';
import ChannelMemberList from '../ChannelMemberList';
import RightArea from '../RightArea';
import { ChannelProps } from '../..';

const ChannelRightArea: FC<ChannelProps> = ({ hasDeleteIcon }) => {
  const rightAreaInGroupConversation = useConversationStore(
    (state) => state.rightAreaInGroupConversation
  );
  const currentConversation = useConversationStore(
    (state) => state.currentConversation
  );
  const changeRightArea = useConversationStore(
    (state) => state.changeRightArea
  );
  const rightAreaInGroupList = useConversationStore(
    (state) => state.rightAreaInGroupList
  );

  const isGroup = !!currentConversation?.groupID;

  const currentMemberInGroup = useConversationStore(
    (state) => state.currentMemberInGroup
  );

  // 是否退群或解散群聊
  const isExit =
    (currentConversation?.groupID &&
      (!currentMemberInGroup || !currentMemberInGroup.userID)) ||
    false;

  const ifShowMemberList = useMemo(() => {
    return isGroup && !isExit;
  }, [isExit, isGroup]);

  const renderRightList = useCallback(() => {
    if (isEmpty(rightAreaInGroupList)) {
      return ifShowMemberList ? <ChannelMemberList /> : null;
    } else {
      const newList = [...rightAreaInGroupList];
      let defaultWidth = '504px';
      let domId = 'drag-scroll-bar-right';
      let needLocalStorageWidth = true;
      if (
        rightAreaInGroupConversation &&
        ['personDetail', 'file', 'channelHistory'].includes(
          rightAreaInGroupConversation
        )
      ) {
        defaultWidth = '560px';
        domId = 'drag-scroll-bar-right-channelHistory';
        needLocalStorageWidth = false;
      }
      return (
        <RightArea
          defaultWidth={defaultWidth}
          domId={domId}
          needLocalStorageWidth={needLocalStorageWidth}
        >
          {newList.reverse().map((i: any) => {
            return (
              <div key={i.key} style={{ height: '100%' }}>
                {i.type === 'personDetail' && (
                  <UserDetail
                    userID={i.payload}
                    handleSelectedUserClear={() => {
                      changeRightArea('CLEAR_RIGHT_AREA');
                    }}
                  />
                )}
                {i.type === 'file' && (
                  <FilePreviewModal
                    message={i.payload}
                    onClose={() => {
                      changeRightArea('CLEAR_RIGHT_AREA');
                    }}
                  />
                )}
                {i.type === 'robotConversation' && (
                  <RobotConversationList
                    onClose={() => {
                      changeRightArea('CLEAR_RIGHT_AREA');
                    }}
                  />
                )}
                {i.type === 'robotAnswerSource' && (
                  <RobotAnswerSource
                    data={i.payload?.data || []}
                    activeValue={i.payload?.activeObj || undefined}
                    onClose={() => {
                      changeRightArea('CLEAR_RIGHT_AREA');
                    }}
                  />
                )}
                {i.type === 'robotOnlineSearch' && (
                  <RobotOnlineSearch
                    data={i.payload?.data || []}
                    activeValue={i.payload?.activeObj || undefined}
                    onClose={() => {
                      changeRightArea('CLEAR_RIGHT_AREA');
                    }}
                  />
                )}
                {i.type === 'channelHistory' && <ChannelHistory />}
              </div>
            );
          })}
        </RightArea>
      );
    }
  }, [
    changeRightArea,
    ifShowMemberList,
    rightAreaInGroupConversation,
    rightAreaInGroupList,
  ]);

  return hasDeleteIcon ? <></> : renderRightList();
};
export default memo(ChannelRightArea);
