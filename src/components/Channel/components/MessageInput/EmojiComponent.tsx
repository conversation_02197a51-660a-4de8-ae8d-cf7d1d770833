import { FC, useState } from 'react';
import { Popover } from '@ht/sprite-ui';
import Picker from '@emoji-mart/react';
import data from '@emoji-mart/data';
import i18n from '@emoji-mart/data/i18n/zh.json';
import styles from './index.less';

interface EmojiComponentProps {
  handleEmojiSelect: (val: any) => void;
  disabledBtn: boolean;
}

const EmojiComponent: FC<EmojiComponentProps> = ({
  handleEmojiSelect,
  children,
  disabledBtn = false,
}) => {
  const [open, setOpen] = useState<boolean>(false);

  return (
    <Popover
      content={() => (
        <Picker
          data={data}
          onEmojiSelect={(emoji: any) => {
            handleEmojiSelect(emoji);
            setOpen(false);
          }}
          i18n={i18n}
          theme={'light'}
          disabled={disabledBtn}
          categories={['frequent', 'people']}
        />
      )}
      placement="topLeft"
      mouseEnterDelay={0.3}
      trigger={'click'}
      open={open}
      onOpenChange={(val: boolean) => {
        if (disabledBtn) {
          setOpen(false);
        } else {
          setOpen(val);
        }
      }}
      overlayClassName={styles.emojiComponentWarp}
    >
      {children}
    </Popover>
  );
};

export default EmojiComponent;
