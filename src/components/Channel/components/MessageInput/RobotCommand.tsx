/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-lines */
/* eslint-disable default-case */
/* eslint-disable indent */
import React, {
  ReactNode,
  useState,
  useEffect,
  useRef,
  useMemo,
  forwardRef,
  useImperativeHandle,
} from 'react';
import { Form, Tooltip } from '@ht/sprite-ui';
import { CloseCircleFilled } from '@ht-icons/sprite-ui-react';
import returnIcon from '@/assets/channel/messageInput/robotCommand/return.png';
import checkMarkIcon from '@/assets/channel/messageInput/robotCommand/checkMark.png';
import botIcon from '@/assets/channel/messageInput/robotCommand/bot.svg';
import {
  BotCommandItem,
  BotCommandOptionItem,
} from '@ht/openim-wasm-client-sdk';
import { useRobot } from '@/hooks/useRobot';
import { filter, isEmpty } from 'lodash';
import { useDeepCompareEffect } from 'ahooks';
import classNames from 'classnames';
import { useKeyboardEvent } from '@/hooks/useKeyboardEvent';
import { KeyboardEventSource } from '@/utils/constants';
import { startsWithSlashNoSpace } from '.';
import AutoWidthInput from './AutoWidthInput';
import styles from './index.less';

interface RobotCommandProps {
  value: string;
  showCommandList: boolean;
  selectedCommand: BotCommandItem | null;
  messageInputRef: React.RefObject<any>;
  groupId: string;
  botId: string;
  onClose: () => void;
  onSelected: (command: BotCommandItem | null) => void;
  onSubmit: (values: any) => void;
  editMdRef: React.RefObject<any>;
  showFormater?: boolean;
  setFormInfo?: (info: any) => void;
  formInfo?: { values: any; showMore: boolean };
}

interface commandFormRefType {
  validateAndSubmitForm: () => void;
}

const RobotCommandRender = (
  {
    value,
    showCommandList,
    selectedCommand,
    messageInputRef,
    groupId,
    botId,
    onClose,
    onSelected,
    onSubmit,
    editMdRef,
    showFormater = false,
    setFormInfo,
    formInfo,
  }: RobotCommandProps,
  ref: any
) => {
  const [form] = Form.useForm();
  const commandFormRef = useRef<commandFormRefType>();

  // useImperativeHandle(
  //   ref,
  //   () => ({
  //     manuallySubmit,
  //   }),
  //   [selectedCommand]
  // );

  const manuallySubmit = () => {
    if (selectedCommand && !selectedCommand.options?.length) {
      onSubmit({ command: selectedCommand });
    } else {
      commandFormRef.current?.validateAndSubmitForm();
    }
  };

  // 添加键盘事件处理
  const handleKeyDown = useKeyboardEvent(
    KeyboardEventSource.ROBOT_COMMAND,
    {
      onEnter: () => {
        // 如果有选中的命令且没有参数，直接提交

        if (selectedCommand && !selectedCommand.options?.length) {
          onSubmit({ command: selectedCommand });
        }
      },
    },
    {
      // 只有在有选中命令且没有参数时才处理
      shouldHandle: !!selectedCommand && !selectedCommand.options?.length,
    }
  );

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown, true);
    return () => document.removeEventListener('keydown', handleKeyDown, true);
  }, [handleKeyDown]);

  return (
    <>
      {selectedCommand && (
        <div className={styles.selectedCommandContainer}>
          <div className={styles.selectedCommandBox}>
            {selectedCommand.options ? (
              <CommandForm
                ref={commandFormRef}
                command={selectedCommand}
                onSubmit={onSubmit}
                setFormInfo={setFormInfo}
                formInfo={formInfo}
              />
            ) : (
              <div className={styles.selectedCommandContent}>
                <Form layout="inline" className={styles.commandForm}>
                  <Form.Item>
                    <div className={styles.robotCommandBox}>
                      <img
                        src={
                          selectedCommand?.botIcon
                            ? selectedCommand?.botIcon
                            : botIcon
                        }
                      />
                      <span>/{selectedCommand.name}</span>
                    </div>
                  </Form.Item>
                </Form>
              </div>
            )}
          </div>
          <div className={styles.selectedCommandFooter}>
            <div
              className={styles.clearBtn}
              onClick={() => {
                onClose();
              }}
            >
              <Tooltip
                title={'返回聊天'}
                overlayInnerStyle={{
                  borderRadius: '8px',
                  padding: '6px 12px',
                }}
              >
                <img src={returnIcon} />
              </Tooltip>
            </div>
            <div className={styles.submitBtn} onClick={() => manuallySubmit()}>
              <img src={checkMarkIcon} />
            </div>
          </div>
        </div>
      )}
      <RobotCommandList
        showCommandList={showCommandList}
        groupId={groupId}
        botId={botId}
        onSelected={(v) => {
          onSelected(v);
          editMdRef.current?.clearValue();
        }}
        inputValue={value}
        messageInputRef={messageInputRef}
        changeFormater={showFormater}
      />
    </>
  );
};
export const RobotCommand = forwardRef(RobotCommandRender);

const validateAndSubmitForm = async (
  form: any,
  command: BotCommandItem,
  onSubmit: (values: any) => void
) => {
  try {
    const values = await form.validateFields();
    onSubmit({
      ...values,
      command,
    });
  } catch (err) {}
};

interface CommandFormProps {
  command: BotCommandItem;
  onSubmit: (values: any) => void;
  setFormInfo?: RobotCommandProps['setFormInfo'];
  formInfo: RobotCommandProps['formInfo'];
}

const CommandFormRender = (
  { command, onSubmit, setFormInfo, formInfo }: CommandFormProps,
  ref: any
) => {
  const [form] = Form.useForm();
  const [showMore, setShowMore] = useState<boolean>(
    !(command?.numbers && command?.numbers > 0)
  );
  const [showOptionDes, setShowOptionDes] = useState<any>({});
  const formBox = useRef(null);

  useEffect(() => {
    const options = command.options || [];
    setShowOptionDes(options[0] || {});
  }, [command]);

  useEffect(() => {
    if (formInfo != null && form != null) {
      form.setFieldsValue(formInfo?.values);
      setShowMore(formInfo?.showMore);
    }
  }, [form, formInfo]);

  useImperativeHandle(
    ref,
    () => ({
      validateAndSubmitForm: submit,
    }),
    [form]
  );

  const submit = () => {
    validateAndSubmitForm(form, command, onSubmit);
  };

  const handleKeyDown = useKeyboardEvent(KeyboardEventSource.COMMAND_FORM, {
    onEnter: () => {
      validateAndSubmitForm(form, command, onSubmit);
    },
  });

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown, true);
    return () => document.removeEventListener('keydown', handleKeyDown, true);
  }, [handleKeyDown]);

  return (
    <div ref={formBox}>
      <div className={styles.describe}>
        <div className={styles.name}>{showOptionDes.name}：</div>
        <div className={styles.desc}>
          {showOptionDes.description || showOptionDes.name}
        </div>
      </div>
      <div className={styles.selectedCommandContent}>
        <Form
          layout="inline"
          form={form}
          onFinish={(values) => {}}
          onChange={() => {
            if (setFormInfo != null) {
              setFormInfo((prev) => {
                return { ...prev, values: form.getFieldsValue() };
              });
            }
          }}
          className={styles.commandForm}
        >
          <Form.Item>
            <div className={styles.robotCommandBox}>
              <img src={command?.botIcon ? command?.botIcon : botIcon} />
              <span>/{command.name}</span>
            </div>
          </Form.Item>
          {(showMore ? command.options : command?.requiredOptions)?.map(
            (item: BotCommandOptionItem) => {
              return (
                <Form.Item
                  key={item.key}
                  // label={item.name}
                  name={item.key}
                  required={Boolean(item.required)}
                  className={styles.commandFormItem}
                  rules={
                    item.required
                      ? [
                          {
                            required: Boolean(item.required),
                            message: ``,
                          },
                        ]
                      : []
                  }
                >
                  <AutoWidthInput
                    label={item.name}
                    onKeyDown={handleKeyDown}
                    autoComplete="off"
                    maxWidth={
                      formBox.current?.clientWidth
                        ? formBox.current?.clientWidth - 40
                        : 400
                    }
                    required={Boolean(item.required)}
                    onFocus={() => setShowOptionDes(item)}
                  />
                </Form.Item>
              );
            }
          )}
          {!showMore && (
            <Form.Item>
              <div
                className={styles.btn}
                onClick={() => {
                  if (setFormInfo != null) {
                    setFormInfo((prev) => {
                      return { ...prev, showMore: true };
                    });
                  }
                  setShowMore(true);
                }}
              >
                +{command?.numbers}可选项
              </div>
            </Form.Item>
          )}
        </Form>
      </div>
    </div>
  );
};
export const CommandForm = forwardRef(CommandFormRender);

const RobotCommandList = ({
  showCommandList,
  groupId,
  botId,
  onSelected,
  inputValue,
  messageInputRef,
  changeFormater,
}: {
  showCommandList: boolean;
  groupId: string;
  botId: string;
  onSelected: (command: BotCommandItem) => void;
  inputValue: string;
  messageInputRef: React.RefObject<any>;
  changeFormater: boolean;
}) => {
  const { robotCommandList, groupedCommands, fetchRobotCommands } =
    useRobot(groupId);
  const [activeIndex, setActiveIndex] = useState<number>(0);
  const [positionBottom, setPositionBottom] = useState<number>(120);
  const containerRef = useRef<HTMLDivElement>(null);
  const filteredGroupedCommands = useMemo(() => {
    if (!groupedCommands) {
      return new Map();
    }
    const filtered = new Map();
    for (const [botId, data] of groupedCommands.entries()) {
      const value = inputValue.trim().replace('/', '').toLowerCase();
      const filteredCommands = data.commands.filter((cmd) => {
        if (
          inputValue.trim() === '/' ||
          cmd.name.toLowerCase().startsWith(value)
        ) {
          if (cmd?.options && cmd?.options.length > 3) {
            const requiredOptions: any[] = [];
            cmd.options.map((item: any) => {
              if (item.required) {
                requiredOptions.push(item);
              }
              return item;
            });
            cmd.requiredOptions = requiredOptions;
            cmd.numbers = cmd?.options.length - requiredOptions.length || 0;
          }
          return cmd;
        }
      });
      if (filteredCommands.length > 0) {
        filtered.set(botId, {
          botName: data.botName,
          commands: filteredCommands,
        });
      }
    }
    return filtered;
  }, [groupedCommands, inputValue]);

  // 扁平化的命令列表，用于键盘导航
  const flattenedCommands = useMemo(() => {
    return Array.from(filteredGroupedCommands.values()).reduce<
      BotCommandItem[]
    >((acc, { commands }) => [...acc, ...commands], []);
  }, [filteredGroupedCommands]);

  useEffect(() => {
    if (messageInputRef.current?.clientHeight) {
      const timer = setTimeout(() => {
        clearTimeout(timer);
        setPositionBottom(messageInputRef.current?.clientHeight || 120);
      }, 100);
    }
  }, [changeFormater, messageInputRef.current]);

  const handleKeyDown = useKeyboardEvent(
    KeyboardEventSource.COMMAND_LIST,
    {
      onEnter: () => {
        if (
          flattenedCommands.length > 0 &&
          activeIndex >= 0 &&
          activeIndex < flattenedCommands.length
        ) {
          onSelected(flattenedCommands[activeIndex]);
        }
      },
      onArrowUp: () => {
        setActiveIndex((prev) =>
          prev <= 0 ? flattenedCommands.length - 1 : prev - 1
        );
      },
      onArrowDown: () => {
        setActiveIndex((prev) =>
          prev >= flattenedCommands.length - 1 ? 0 : prev + 1
        );
      },
    },
    {
      shouldHandle: showCommandList && flattenedCommands.length > 0,
    }
  );

  useEffect(() => {
    (groupId || botId) &&
      showCommandList &&
      fetchRobotCommands({ groupID: groupId, botID: botId });
  }, [showCommandList, groupId, botId]);

  useDeepCompareEffect(() => {
    document.addEventListener('keydown', handleKeyDown, true);

    return () => document.removeEventListener('keydown', handleKeyDown, true);
  }, [showCommandList, groupedCommands, activeIndex, onSelected]);

  if (!startsWithSlashNoSpace(inputValue.trim()) || isEmpty(groupedCommands)) {
    return null;
  }

  return (
    <div
      ref={containerRef}
      className={styles.robotCommandListContainer}
      style={{
        display: showCommandList ? 'block' : 'none',
        bottom: positionBottom || 120,
      }}
      tabIndex={0}
    >
      <div className={styles.robotCommandList}>
        {Array.from(filteredGroupedCommands.entries())?.length > 0 ? (
          Array.from(filteredGroupedCommands.entries()).map(
            ([botID, { botName, commands }]) => (
              <div key={botID} className={styles.botGroup}>
                {commands.map((command: BotCommandItem, index) => (
                  <div
                    key={command.key}
                    className={classNames(styles.commandItem, {
                      [styles.active]: activeIndex === index,
                    })}
                    onClick={() => {
                      onSelected(command);
                    }}
                    onMouseEnter={() => setActiveIndex(index)}
                    onMouseLeave={() => setActiveIndex(-1)}
                  >
                    <div className={styles.commandContent}>
                      <div className={styles.commandHeader}>
                        <div className={styles.commandName}>
                          /{command.name}
                        </div>
                        {command?.options && activeIndex === index && (
                          <div className={styles.commandOptions}>
                            {(command?.numbers && command?.numbers > 0
                              ? command?.requiredOptions
                              : command.options
                            ).map((option) => (
                              <div
                                key={option.key}
                                className={styles.optionTag}
                              >
                                {option.name}
                              </div>
                            ))}
                            {command?.numbers && command?.numbers > 0 ? (
                              <div className={styles.optionalNumbers}>
                                | +{command?.numbers}可选项
                              </div>
                            ) : (
                              ''
                            )}
                          </div>
                        )}
                      </div>
                      <div className={styles.commandDescription}>
                        {command.description}
                      </div>
                    </div>
                    <div className={styles.botName}>
                      <Tooltip
                        title={botName || botID}
                        overlayInnerStyle={{
                          borderRadius: '8px',
                          padding: '6px 12px',
                        }}
                      >
                        <div>{botName || botID}</div>
                      </Tooltip>
                    </div>
                  </div>
                ))}
              </div>
            )
          )
        ) : (
          <div className={styles.botGroup}>
            <div className={styles.promptTitle}>无{inputValue}相关命令</div>
            <div className={styles.prompt}>
              输入提示：您可在&quot;/&quot;后添加空格发出内容
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
