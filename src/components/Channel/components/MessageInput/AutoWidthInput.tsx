/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-statements */
import React, { useState, useRef, useEffect } from 'react';
import { Input } from '@ht/sprite-ui';
import classNames from 'classnames';
import styles from './index.less';

const { TextArea } = Input;

const AutoResizeInput = ({
  maxWidth = 400,
  maxRows = 4,
  required = false,
  label = '',
  ...props
}) => {
  const ariaInvalid = props['aria-invalid'] || false;

  const [isFocus, setIsFocus] = useState<boolean>(false);
  const [dimensions, setDimensions] = useState({
    width: 60,
    height: 30,
    isWarp: false,
  });
  const textareaRef = useRef(null);
  const mirrorRef = useRef(null);
  const labelRef = useRef(null);

  const calculateDimensions = (value: string, flag = false) => {
    try {
      if (!textareaRef.current || !mirrorRef.current) {
        return;
      }

      // const textarea = textareaRef.current.resizableTextArea.textArea;
      const mirror = mirrorRef.current;
      const labelWidth = labelRef.current?.offsetWidth || 95;
      // 更新镜像内容
      mirror.textContent = value || props.placeholder || '';

      // 先计算单行宽度
      mirror.style.whiteSpace = 'nowrap';
      mirror.style.width = 'auto';
      const singleLineWidth = mirror.offsetWidth + 14; // 预留8px的滚动条宽度和一个字符开宽度

      const maxNum = maxWidth - labelWidth;
      if (singleLineWidth < maxNum) {
        // 单行模式
        setDimensions({
          width: Math.max(singleLineWidth, 60),
          height: 30,
          isWarp: false,
        });
      } else {
        // 多行模式
        mirror.style.whiteSpace = 'pre-wrap';
        mirror.style.wordWrap = 'break-word';
        mirror.style.width = `${maxNum}px`;

        let height = mirror.offsetHeight + 6; // 加上上下padding
        if (height < 30) {
          height = 30;
        }
        const lineHeight = parseFloat(
          window.getComputedStyle(mirror).lineHeight
        );
        const maxHeight = lineHeight * maxRows + 6;
        setDimensions({
          width: maxNum,
          height: Math.min(height, maxHeight),
          isWarp: true,
        });
      }
    } catch (error) {}
    if (flag && props.onChange) {
      props.onChange(value);
    }
  };

  useEffect(() => {
    calculateDimensions(props.value || '');
  }, [props.value]);

  const handleChange = (e) => {
    calculateDimensions(e.target.value, true);
  };

  return (
    <>
      <div
        ref={mirrorRef}
        style={{
          fontFamily: 'PingFangSC-Regular',
          fontSize: '15px',
          fontWeight: '400',
          fontStyle: 'normal',
          letterSpacing: 'normal',
          wordSpacing: '0px',
          textIndent: '0px',
          textTransform: 'none',
          padding: '0 11px',
          borderLeftWidth: '0px',
          borderRightWidth: '0px',
          boxSizing: 'border-box',
          position: 'absolute',
          visibility: 'hidden',
          whiteSpace: 'nowrap',
          overflowWrap: 'break-word',
          top: '-9999px',
          left: '-9999px',
          width: 'auto',
          lineHeight: '22px',
        }}
      />
      <div
        className={classNames(
          styles.autoResizeInput,
          isFocus && styles.autoResizeInputFocus,
          ariaInvalid && styles.autoResizeInputRrror
        )}
      >
        <div
          className={classNames(styles.label, required && styles.required)}
          ref={labelRef}
        >
          {label || props.id}
        </div>
        <TextArea
          ref={textareaRef}
          {...props}
          autoSize={false}
          bordered={false}
          style={{
            width: `${dimensions.width}px`,
            height: `${dimensions.height}px`,
            whiteSpace: dimensions.isWarp ? 'pre-wrap' : 'nowrap',
            overflow: dimensions.isWarp ? 'auto' : 'hidden',
            // minWidth: '100px',
            minHeight: '30px',
            resize: 'none',
            lineHeight: '22px',
            transition: 'all 0.2s',
            ...props.style,
          }}
          onChange={handleChange}
          onBlur={() => setIsFocus(false)}
          onFocus={() => {
            setIsFocus(true);
            props.onFocus?.();
          }}
        />
      </div>
    </>
  );
};

export default AutoResizeInput;
