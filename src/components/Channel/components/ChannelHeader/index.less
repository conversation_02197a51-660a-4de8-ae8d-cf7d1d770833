.ChannelHeadeWarp {
  // height: 118px;
  // border-bottom: 1px solid rgba(0, 0, 0, 5%);
  // box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 10%);
  border-radius: 8px 8px 0 0;
  z-index: 2;

  .basicInfoWarp {
    padding: 16px 16px 0 20px;
    font-size: 18px;
    font-weight: 600;
    font-family: PingFangSC-Regular;
    color: #2f3035;
    line-height: 28px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .nameWarp {
      display: flex;
      align-items: center;
      padding-bottom: 3px;
    }

    .rightAreaBack {
      margin-right: 6px;
      cursor: pointer;
      &:hover {
        border-radius: 4px;
        background: var(--msg-qute-backgroud-color);
      }
    }

    // > div {
    //   display: flex;
    //   align-items: center;
    // }
    .nameWarp {
      flex: 1 1;
      overflow: hidden;
      padding-bottom: 3px;
    }

    .headerTextWrap {
      // flex: 1 1;
      max-width: 100%;
      width: max-content;
      display: flex;
      align-items: center;
      color: var(--primary-text-color-1);
      // overflow: hidden;
      cursor: pointer;

      &.withState {
        cursor: pointer;

        &:hover {
          background-color: var(--primary-background-color-2);
        }

        .state {
          margin-left: 4px;
          margin-right: 6px;
          // margin-bottom: 6px;
          width: 16px;
          height: 16px;
        }
      }

      > span {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .personWarp {
      flex-shrink: 0;
      display: flex;
      align-items: center;
      padding-left: 20px;

      .toolItem {
        display: flex;
        align-items: center;
        height: 30px;
        margin-right: 20px;
        cursor: pointer;
        > img {
          width: 24px;
          height: 24px;
        }
        .threadIocn {
          display: inline-block;
        }
        .threadActiveIcon {
          display: none;
        }
      }
      .toolItem:hover {
        .threadIocn {
          display: none;
        }
        .threadActiveIcon {
          display: inline-block;
        }
      }

      .more,
      .deleteIcon {
        width: 28px;
        height: 28px;
        border-radius: 4px;
        // margin-left: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        cursor: pointer;

        > div {
          width: 4px;
          height: 4px;
          background-color: var(--primary-text-color);
          border-radius: 50%;
          margin-bottom: 3px;
        }

        > div:last-child {
          margin-bottom: 0;
        }
      }
      .more:hover,
      .deleteIcon:hover {
        background: var(--msg-qute-backgroud-color);
      }

      .activedBtn {
        background: var(--msg-qute-backgroud-color);
      }

      .deleteIcon {
        margin-left: 8px;
        > img {
          width: 14px;
        }
      }

      > div {
        margin-left: 12px;

        &:first-of-type {
          margin-left: 0;
        }
      }
    }
  }

  .tabwarp {
    width: 100%;
    overflow-x: auto;
    display: flex;
    align-items: center;
    padding: 9px 20px 6px;
    // box-shadow: 0 5px 4px -3px rgba(0, 0, 0, 13%);
    position: relative;
    padding-bottom: 22px;
    margin-bottom: -16px;
    // height: 38px;
    // line-height: 38px;
    .tabItem {
      flex-shrink: 0;
      font-size: 14px;
      font-weight: 400;
      font-family: PingFangSC-Regular;
      color: var(--primary-text-color-7);
      cursor: pointer;
      position: relative;
      padding: 0 12px;
      border-radius: 6px;
      line-height: 20px;
      margin-right: 2px;
      height: 36px;
      display: flex;
      align-items: center;

      > img {
        width: 20px;
        height: 20px;
        margin-right: 4px;
      }
    }

    .tabItem:hover {
      // background: rgba(107, 107, 108, 8%);
      color: var(--primary-text-color-7);
    }

    .active {
      color: #0074e2 !important;
      font-weight: 600;
      // background: var(--tab-actived-background-color) !important;
      &::after {
        content: "";
        width: calc(100% - 24px);
        height: 2px;
        background: var(--primary-text-color-9);
        border-radius: 6px;
        position: absolute;
        left: 12px;
        bottom: -4px;
      }
    }

    &::after {
      content: "";
      position: absolute;
      bottom: 16px;
      left: 0;
      right: 0;
      height: 50px;
      box-shadow: rgba(0, 0, 0, 8%) 0 2px 16px 0;
      clip-path: inset(0 0 -16px 0);
      z-index: -1;
    }
  }

  // .boxShadowBox {
  //   position: relative;
  //   width: 100%;
  //   height: 100%;
  // }

  .confirmText {
    font-size: 15px;
    font-weight: 400;
    color: #1d1c1d;
    line-height: 22px;
  }
}

.ChannelHeadeWarp_CloudDoc {
  border-bottom: 1px solid transparent;
  box-shadow: none;
  .tabwarp {
    padding-bottom: 6px;
    margin-bottom: 0;
    &::after {
      display: none;
    }
  }
}

.dropDownMenu {
  :global {
    .linkflow-dropdown-menu {
      width: 240px;
      background: #ffffff;
      border-radius: 8px;
      border: 1px solid var(--primary-background-color-5);
      overflow: hidden;
      padding: 6px;
      box-shadow: 0 2px 4px 2px rgba(0, 0, 0, 4%);
    }
    .linkflow-dropdown-menu-item {
      padding: 8px 18px;
      font-size: 14px;
      font-weight: 500;
      color: var(--primary-text-color-1);
      line-height: 20px;

      &:hover {
        background: var(--msg-qute-backgroud-color);
        border-radius: 6px;
      }
    }
    .linkflow-dropdown-menu-item-divider {
      margin: 3px 0;
      background-color: var(--primary-background-color-5);
    }
  }
}

.popStateContainer {
  :global {
    .linkflow-popover-content {
      border-radius: 5px;
      // background: black;
      color: #fff !important;
    }
    .linkflow-popover-inner-content {
      border-radius: 5px;
      background: black;
      padding: 6px 10px !important;
      color: #fff !important;
    }
    .linkflow-popover-arrow-content::before {
      background: black;
    }
  }

  .hoverArea {
    height: 20px;
    line-height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;

    .state {
      margin-left: 4px;
      margin-right: 6px;

      > span {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

.titleTooltip {
  :global {
    .linkflow-tooltip-inner {
      padding: 7px 16px;
      border-radius: 8px;
      box-shadow: 0 6px 16px 0 rgba(29, 34, 44, 8%),
        0 3px 6px -4px rgba(29, 34, 44, 12%);
      font-size: 14px;
      font-weight: 400;
      color: #ffffff;
      line-height: 20px;
    }
    .linkflow-tooltip-arrow {
      bottom: 1px;
    }
  }
}
