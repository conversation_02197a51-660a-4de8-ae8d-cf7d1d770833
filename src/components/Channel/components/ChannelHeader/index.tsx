/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable complexity */
/* eslint-disable max-lines */
/* eslint-disable indent */
import React, { useState, useCallback, useMemo, useEffect } from 'react';
import {
  GroupMemberItem,
  GroupMemberRole,
  GroupStatus,
  MessageReceiveOptType,
} from '@ht/openim-wasm-client-sdk';
import {
  useConversationStore,
  useUserStore,
  useSearchInfoStore,
} from '@/store';
import OIMAvatar from '@/components/OIMAvatar';
import {
  Dropdown,
  Menu,
  message,
  Modal,
  Popover,
  Tooltip,
} from '@ht/sprite-ui';
import type { MenuProps } from '@ht/sprite-ui';
// import messageNewIcon from '@/assets/channel/tab/messageNew.svg';
// import cloudDocNewIcon from '@/assets/channel/tab/cloudDocNew.svg';
import messageIcon from '@/assets/channel/tab/message.svg';
import messageActiveIcon from '@/assets/channel/tab/messageActive.svg';
import cloudDocIcon from '@/assets/channel/tab/cloudDoc.svg';
import cloudDocActiveIcon from '@/assets/channel/tab/cloudDocActive.svg';
import moreIcon from '@/assets/channel/more.svg';
import channelHistoryIcon from '@/assets/channel/channelHistory.svg';
import { isBotUser } from '@/utils/avatar';
import dialogueHistoryIcon from '@/assets/channel/robotConversationList/dialogueHistory.png';
import ConversationSetModal, {
  TabType,
} from '@/components/ConversationSetModal';
import AddGroupModal from '@/components/AddGroupModal';
import classNames from 'classnames';
import { IMSDK } from '@/layouts/BasicLayout';
import ThreadsModal from '@/components/ThreadsModal';
import {
  getShowDescByStatus,
  getStatusImgSrcByStatus,
} from '@/components/UserState/SetStateModal/const';
import { t } from 'i18next';
import BotIconComponent from '@/components/ChannelList/BotIconComponent';
import { useConversationSettings } from '@/hooks/useConversationSettings';
import { feedbackToast } from '@/utils/common';
import TransferOwnerModal from '@/components/TransferOwnerModal';
import useUserInfo from '@/hooks/useUserInfo';
import deleteIcon from '@/assets/contact/deleteIcon.svg';
import GroupAnnouncementModal from '@/components/ConversationSetModal/components/GroupAnnouncementModal';
import ConfirmModal from '@/components/ConfirmModal';
import closeIcon from '@/assets/images/chatSetting/closeIcon.svg';
import RightAreaBackBtn from '@/components/RightAreaBackBtn';
import useGroupMembers from '@/hooks/useGroupMembers';
import { isSingleChat } from '@/utils/utils';
import { ChannelHeaderTabType } from '../..';
import styles from './index.less';

interface tabItemProps {
  key: ChannelHeaderTabType;
  icon: any;
  title: string;
  active: any;
}

const groupTabArr: tabItemProps[] = [
  {
    key: 'message',
    icon: messageIcon,
    active: messageActiveIcon,
    title: '消息',
  },
  // {
  //   key: 'pins',
  //   icon: pinningIcon,
  //   title: '群公告',
  // },
  {
    key: 'docs',
    icon: cloudDocIcon,
    active: cloudDocActiveIcon,
    title: '云文档',
  },
  // {
  //   key: 'bookmarks',
  //   icon: bookmarkIcon,
  //   title: '群收藏',
  // },
  // {
  //   key: 'files',
  //   icon: fileIcon,
  //   title: '聊天文件',
  // },
];

const userTabArr: tabItemProps[] = [
  {
    key: 'message',
    icon: messageIcon,
    active: messageActiveIcon,
    title: '消息',
  },
];

const userDownMenuList: MenuProps['items'] = [
  {
    label: '向对话添加成员',
    key: 'createGroup',
  },
];

interface Props {
  hasDeleteIcon?: boolean;
}

const ChannelHeader: React.FC<Props> = (props) => {
  const { hasDeleteIcon = false } = props;
  const { userID, employeeCode, nickname, faceURL } =
    useUserStore.getState().selfInfo;

  const currentConversation = useConversationStore(
    (state) => state.currentConversation
  );
  const currentGroupInfo = useConversationStore(
    (state) => state.currentGroupInfo
  );
  const currentMemberInGroup = useConversationStore(
    (state) => state.currentMemberInGroup
  );
  const changeRightArea = useConversationStore(
    (state) => state.changeRightArea
  );
  const changeRightAreaBack = useConversationStore(
    (state) => state.changeRightAreaBack
  );
  const rightAreaInGroupList = useConversationStore(
    (state) => state.rightAreaInGroupList
  );

  const [showGroupCardModal, setShowGroupCardModal] = useState(false);
  const [showThreadModal, setShowThreadModal] = useState(false);
  const [showGroupAnnouncement, setShowGroupAnnouncement] = useState(false);
  const [targetUserInfo, setTargetUserInfo] = useState<GroupMemberItem[]>();
  const isGroup = !!currentConversation?.groupID;
  const [activedTab, setActivedTab] = useState<TabType>('details');
  const [confirmModalVisible, setConfirmModalVisible] =
    useState<boolean>(false);

  const { userState, multiSession } = useUserInfo(currentConversation?.userID);
  const mulitConversation = !!(multiSession && multiSession === 1);
  const { updateConversationMessageRemind } =
    useConversationSettings(currentConversation);

  const changeSearchRightArea = useSearchInfoStore(
    (state) => state.changeRightArea
  );

  const updateChannelHeaderCurTab = useConversationStore(
    (state) => state.updateChannelHeaderCurTab
  );
  const channelHeaderCurTab = useConversationStore(
    (state) => state.channelHeaderCurTab
  );

  const { fetchState, getMemberData, resetState } = useGroupMembers({
    groupID: currentConversation?.groupID,
  });
  const { groupMemberList } = fetchState;

  const initOrRefreshData = useCallback(() => {
    if (currentConversation?.groupID) {
      getMemberData(true);
    }
  }, [currentConversation?.groupID, getMemberData]);

  useEffect(() => {
    initOrRefreshData();
    return () => resetState();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentConversation?.groupID]);

  const isRobot = useMemo(() => {
    return isBotUser(currentConversation?.userID || '');
  }, [currentConversation?.userID]);

  // 是否退群或解散群聊
  const isExit =
    (currentConversation?.groupID &&
      (!currentMemberInGroup || !currentMemberInGroup.userID)) ||
    false;

  // const isThread =
  //   currentConversation?.groupID && currentConversation?.parentId != null;

  const tabArr = isGroup ? groupTabArr : userTabArr;

  const [showTransferModal, setShowTransferModal] = useState(false);

  const tabClikc = (type: ChannelHeaderTabType) => {
    updateChannelHeaderCurTab?.(type);
  };

  const groupDownMenuList: MenuProps['items'] = useMemo(() => {
    const isOwnerList: MenuProps['items'] = [
      {
        label: '群管理',
        key: 'groupSeting',
      },
      {
        label: '群公告',
        key: 'groupNotification',
      },
      {
        type: 'divider',
      },
      {
        label:
          currentGroupInfo?.status === GroupStatus.Muted
            ? '取消群禁言'
            : '群禁言',
        key: 'forbiddenTalk',
      },
      {
        type: 'divider',
      },
      {
        label: currentConversation?.recvMsgOpt
          ? t('placeholder.messageToast')
          : t('placeholder.notNotify'),
        key: 'changeNotify',
      },
      {
        type: 'divider',
      },
      // {
      //   label: '转让群组',
      //   key: 'transferOwner',
      // },
      // {
      //   type: 'divider',
      // },
      // {
      //   label: t('placeholder.exitGroup'),
      //   key: 'memberQuit',
      // },
      {
        label: '解散群聊',
        key: 'dismissGroup',
      },
    ];
    const isAdminList: MenuProps['items'] = [
      {
        label: '群管理',
        key: 'groupSeting',
      },
      {
        label: '群公告',
        key: 'groupNotification',
      },
      {
        type: 'divider',
      },
      {
        label:
          currentGroupInfo?.status === GroupStatus.Muted
            ? '取消群禁言'
            : '群禁言',
        key: 'forbiddenTalk',
      },
      {
        type: 'divider',
      },
      {
        label: currentConversation?.recvMsgOpt
          ? t('placeholder.messageToast')
          : t('placeholder.notNotify'),
        key: 'changeNotify',
      },
      {
        type: 'divider',
      },
      {
        label: t('placeholder.exitGroup'),
        key: 'memberQuit',
      },
    ];
    const isNotOwnerList: MenuProps['items'] = [
      {
        label: '群管理',
        key: 'groupSeting',
      },
      {
        label: '群公告',
        key: 'groupNotification',
      },
      {
        type: 'divider',
      },
      {
        label: currentConversation?.recvMsgOpt
          ? t('placeholder.messageToast')
          : t('placeholder.notNotify'),
        key: 'changeNotify',
      },
      {
        type: 'divider',
      },
      {
        label: t('placeholder.exitGroup'),
        key: 'memberQuit',
      },
    ];
    return currentMemberInGroup?.roleLevel === GroupMemberRole.Owner
      ? isOwnerList
      : currentMemberInGroup?.roleLevel === GroupMemberRole.Admin
      ? isAdminList
      : isNotOwnerList;
  }, [
    currentConversation?.recvMsgOpt,
    currentGroupInfo?.status,
    currentMemberInGroup,
  ]);

  const toggleRecvMsgOpt = () => {
    if (currentConversation?.recvMsgOpt === MessageReceiveOptType.Normal) {
      updateConversationMessageRemind(true, MessageReceiveOptType.NotNotify);
    } else {
      updateConversationMessageRemind(false);
    }
  };

  const forbiddenTalk = () => {
    try {
      if (currentGroupInfo?.groupID != null) {
        let isMute = false;
        if (currentGroupInfo?.status === GroupStatus.Normal) {
          isMute = true;
        } else if (currentGroupInfo?.status === GroupStatus.Muted) {
          isMute = false;
        }
        IMSDK.changeGroupMute({
          groupID: currentGroupInfo?.groupID,
          isMute,
        });
      }
    } catch (e) {
      console.error('群禁言切换失败', e);
    }
  };

  const transferOwner = () => {
    setShowTransferModal(true);
  };

  const handleClick = ({ key }) => {
    switch (key) {
      case 'groupSeting':
        setShowGroupCardModal(true);
        setActivedTab('details');
        break;
      case 'groupNotification':
        groupAnnouncementCLick();
        break;
      case 'createGroup':
        createGroup();
        break;
      case 'memberQuit':
        memberQuit();
        break;
      case 'dismissGroup':
        setConfirmModalVisible(true);
        break;
      case 'changeNotify':
        toggleRecvMsgOpt();
        break;
      case 'forbiddenTalk':
        forbiddenTalk();
        break;
      case 'transferOwner':
        transferOwner();
        break;
      default:
        break;
    }
  };

  const groupAnnouncementCLick = () => {
    if (
      currentMemberInGroup?.roleLevel === GroupMemberRole.Owner ||
      currentMemberInGroup?.roleLevel === GroupMemberRole.Admin
    ) {
      setShowGroupAnnouncement(true);
    } else if (currentGroupInfo?.notification) {
      setShowGroupAnnouncement(true);
    } else {
      message.info('仅群主和管理员可发布群公告');
    }
  };

  const createGroup = async () => {
    if (!isGroup && currentConversation != null) {
      try {
        const userDetailDataResponse = await IMSDK.getUsersInfo([
          currentConversation?.userID,
        ]);
        setTargetUserInfo([
          {
            employeeCode,
            nickname,
            userID,
            faceURL,
            roleLevel: GroupMemberRole.Owner,
          },
          {
            employeeCode: userDetailDataResponse.data[0].employeeCode,
            userID: currentConversation?.userID,
            nickname: userDetailDataResponse.data[0].nickname,
            faceURL: userDetailDataResponse.data[0].faceURL,
            roleLevel: GroupMemberRole.Normal,
          },
        ]);
      } catch (e) {
        console.error('私聊建群查询对方信息失败，原因为：', e);
      }
    }
    setShowGroupCardModal(true);
  };

  // const setCurrentMessageInputValue = useConversationStore(
  //   (state) => state.setCurrentMessageInputValue
  // );
  const memberQuit = async () => {
    if (currentConversation?.groupID) {
      Modal.confirm({
        content: t('placeholder.exitGroupToast'),
        onOk: async () => {
          try {
            // if (
            //   currentConversation?.conversationID != null &&
            //   currentConversation?.draftText != null &&
            //   currentConversation?.draftText !== ''
            // ) {
            //   setCurrentMessageInputValue(null);
            //   IMSDK.setConversationDraft({
            //     conversationID: currentConversation.conversationID,
            //     draftText: '',
            //   });
            // }
            await IMSDK.quitGroup(currentConversation?.groupID);
          } catch (error) {
            feedbackToast({ error, msg: '退出群会话失败，请确认你不是群主' });
          }
        },
      });
    } else {
    }
  };

  const dismissGroup = () => {
    if (currentConversation?.groupID) {
      try {
        IMSDK.dismissGroup(currentConversation?.groupID);
        setConfirmModalVisible(false);
      } catch (error) {
        console.warn('dismissGroup', error);
      }
    }
  };

  const renderHover = () => {
    return (
      <div className={styles.hoverArea}>
        {(userState?.code || userState?.desc) && (
          <>
            <img src={getStatusImgSrcByStatus(userState)} />
            <div className={styles.state}>
              <span>{getShowDescByStatus(userState)}</span>
            </div>
          </>
        )}
      </div>
    );
  };

  const renderHeadText = useCallback(
    (str?: string) => {
      return (
        <div
          className={classNames(
            styles.headerTextWrap,
            str === 'withState' ? styles.withState : null
          )}
          onClick={() => {
            if (isSingleChat()) {
              return;
            }
            if (isGroup && currentMemberInGroup?.groupID) {
              handleClick({ key: 'groupSeting' });
            } else if (!isGroup) {
              if (location.pathname === '/linkflow/search') {
                changeSearchRightArea(
                  'OPEN_PERSON_DETAIL',
                  currentConversation?.userID
                );
              } else {
                changeRightArea(
                  'OPEN_PERSON_DETAIL',
                  currentConversation?.userID
                );
              }
            }
          }}
        >
          {!isGroup && currentConversation?.userID ? (
            <OIMAvatar
              userID={currentConversation?.userID}
              size={28}
              shape="square"
              stateSize={8}
              borderRadius={5}
              style={{ marginRight: 8 }}
            />
          ) : (
            <></>
          )}
          <span title={currentConversation?.showName}>
            {currentConversation?.showName}
          </span>
          <BotIconComponent userID={currentConversation?.userID || ''} />
          {!isGroup && (userState?.code != null || userState?.desc != null) && (
            <Popover
              overlayClassName={styles.popStateContainer}
              content={renderHover()}
              placement="top"
              showArrow={true}
              autoAdjustOverflow={true}
              trigger={'hover'}
              arrowPointAtCenter={true}
            >
              <img
                className={styles.state}
                src={getStatusImgSrcByStatus(userState)}
              ></img>
            </Popover>
          )}
        </div>
      );
    },
    [
      currentConversation?.showName,
      currentConversation?.userID,
      isGroup,
      userState,
      currentMemberInGroup,
    ]
  );
  return (
    <div
      className={classNames(
        styles.ChannelHeadeWarp,
        channelHeaderCurTab === 'docs' && styles.ChannelHeadeWarp_CloudDoc
      )}
    >
      <div className={styles.basicInfoWarp}>
        <div className={styles.nameWarp}>
          {hasDeleteIcon && <RightAreaBackBtn />}
          {!isGroup && (userState?.code != null || userState?.desc != null)
            ? renderHeadText('withState')
            : renderHeadText()}
        </div>
        <div className={styles.personWarp}>
          {/* <div className={styles.toolItem}>
            {isGroup && !isThread && (
              <img src={threadIcon} className={styles.threadIocn} />
            )}
            <img
              src={threadActiveIcon}
              className={styles.threadActiveIcon}
              onClick={() => setShowThreadModal(true)}
            />
          </div> */}
          {!isRobot && !hasDeleteIcon && (
            <Tooltip
              title="聊天记录"
              overlayClassName={styles.titleTooltip}
              color="#000000"
            >
              <div
                className={classNames(
                  styles.more,
                  rightAreaInGroupList[rightAreaInGroupList.length - 1]
                    ?.type === 'channelHistory' && styles.activedBtn
                )}
                onClick={() => {
                  if (
                    rightAreaInGroupList[rightAreaInGroupList.length - 1]
                      ?.type === 'channelHistory'
                  ) {
                    changeRightAreaBack();
                  } else {
                    changeRightArea('OPEN_CHANNEL_HISTORY');
                  }
                }}
              >
                <img src={channelHistoryIcon} />
              </div>
            </Tooltip>
          )}

          {!hasDeleteIcon && mulitConversation && (
            <div
              className={classNames(
                styles.more,
                rightAreaInGroupList[rightAreaInGroupList.length - 1]?.type ===
                  'robotConversation' && styles.activedBtn
              )}
              onClick={() => {
                if (
                  rightAreaInGroupList[rightAreaInGroupList.length - 1]
                    ?.type === 'robotConversation'
                ) {
                  changeRightAreaBack();
                } else {
                  changeRightArea(
                    'OPEN_ROBOT_CONVERSATION_AREA',
                    currentConversation?.conversationID
                  );
                }
              }}
            >
              <img src={dialogueHistoryIcon} />
            </div>
          )}
          {isRobot ||
          (isGroup && !currentMemberInGroup?.groupID) ||
          (!isGroup && userID === currentConversation?.userID) ? (
            <></>
          ) : (
            <Dropdown
              overlay={
                <Menu
                  items={isGroup ? groupDownMenuList : userDownMenuList}
                  onClick={handleClick}
                />
              }
              overlayClassName={styles.dropDownMenu}
            >
              <div className={styles.more}>
                <img src={moreIcon} />
              </div>
            </Dropdown>
          )}
          {hasDeleteIcon && (
            <div className={styles.deleteIcon}>
              <img
                src={deleteIcon}
                onClick={() => {
                  // 这里要清除掉currentConversation吧
                  changeSearchRightArea('CLEAR_RIGHT_AREA');
                }}
              ></img>
            </div>
          )}
        </div>
      </div>
      <div className={styles.tabwarp}>
        {tabArr.map((item) => {
          return isExit && item.key !== 'message' ? (
            <></>
          ) : (
            <div
              key={item.key}
              className={classNames(
                styles.tabItem,
                channelHeaderCurTab === item.key && styles.active
              )}
              onClick={() => tabClikc(item.key)}
            >
              <img
                src={channelHeaderCurTab === item.key ? item.active : item.icon}
              />
              <span>{item.title}</span>
            </div>
          );
        })}
      </div>
      {showGroupCardModal && (
        <>
          {isGroup ? (
            <ConversationSetModal
              visible={showGroupCardModal}
              handleCancel={() => {
                setShowGroupCardModal(false);
              }}
              groupInfo={isGroup ? currentGroupInfo : undefined}
              conversationInfo={currentConversation}
              isGroup={isGroup}
              groupMemberList={isGroup ? groupMemberList : targetUserInfo}
              afterConfirmBtnClick={initOrRefreshData}
              activedTab={activedTab}
            />
          ) : (
            // eslint-disable-next-line react/jsx-no-undef
            <AddGroupModal
              visible={showGroupCardModal}
              handleCancel={() => {
                setShowGroupCardModal(false);
              }}
              groupMemberList={isGroup ? groupMemberList : targetUserInfo}
              currentConversation={isGroup ? currentConversation : undefined}
              type={isGroup ? 'update' : 'create'}
              afterConfirmBtnClick={initOrRefreshData}
            />
          )}
        </>
      )}
      {showThreadModal && isGroup && (
        <ThreadsModal
          handleCancel={() => setShowThreadModal(false)}
          groupId={currentConversation?.groupID}
        />
      )}
      {showTransferModal && (
        <TransferOwnerModal
          conversation={currentConversation}
          onClose={() => setShowGroupCardModal(false)}
        />
      )}
      {confirmModalVisible && (
        <ConfirmModal
          open={confirmModalVisible}
          title="提示"
          closeIcon={<img src={closeIcon} />}
          maskClosable={false}
          okText="确认解散"
          cancelText="取消"
          onCancel={() => setConfirmModalVisible(false)}
          onOk={dismissGroup}
          centered={true}
          width={420}
        >
          <div className={styles.confirmText}>解散后移除所有的群成员</div>
        </ConfirmModal>
      )}
      {showGroupAnnouncement && (
        <GroupAnnouncementModal
          visible={showGroupAnnouncement}
          isAdmin={
            currentMemberInGroup?.roleLevel === GroupMemberRole.Owner ||
            currentMemberInGroup?.roleLevel === GroupMemberRole.Admin
          }
          onCancel={() => setShowGroupAnnouncement(false)}
          groupInfo={currentGroupInfo}
          type={currentGroupInfo?.notification ? 'preview' : 'edit'}
        />
      )}
    </div>
  );
};
export default ChannelHeader;
