import { useEffect, useState } from 'react';
import upArrow from '@/assets/images/upArrow.svg';
import { useConversationStore } from '@/store';
import { v4 as uuidv4 } from 'uuid';
import { ConversationItem } from '@ht/openim-wasm-client-sdk';
import styles from './index.less';

const GotoFirstUnReadBtn = () => {
  const [showBtn, setShowBtn] = useState(false);

  const {
    currentConversation,
    updateCurrentConversation,
    updateTargetMsg,
    clearReadSeqInfo,
  } = useConversationStore((state) => ({
    currentConversation: state.currentConversation,
    updateCurrentConversation: state.updateCurrentConversation,
    updateTargetMsg: state.updateTargetMsg,
    clearReadSeqInfo: state.clearReadSeqInfo,
  }));

  const { currentReadSeqInfo } = useConversationStore((state) => ({
    currentReadSeqInfo: state.currentReadSeqInfo,
  }));

  useEffect(() => {
    if (
      currentConversation?.conversationID !==
        currentReadSeqInfo?.conversationID ||
      currentReadSeqInfo?.maxSeq == null ||
      currentReadSeqInfo?.hasReadSeq == null
    ) {
      return;
    }
    const ifShowGotoFirstUnReadBtn =
      currentReadSeqInfo?.maxSeq - currentReadSeqInfo?.hasReadSeq > 10;

    setShowBtn(ifShowGotoFirstUnReadBtn);
  }, [currentConversation?.conversationID, currentReadSeqInfo]);

  const handleGotoFirstUnReadBtn = () => {
    if (
      currentConversation?.conversationID !==
        currentReadSeqInfo?.conversationID ||
      currentReadSeqInfo?.maxSeq == null ||
      currentReadSeqInfo?.hasReadSeq == null
    ) {
      return;
    }

    const firstUnReadSeq = currentReadSeqInfo?.hasReadSeq + 1;
    updateTargetMsg({
      clientMsgID: undefined,
      seq: firstUnReadSeq,
    });

    clearReadSeqInfo();
  };

  return showBtn && currentReadSeqInfo != null ? (
    <div
      className={styles.gotoFirstUnReadBtn}
      onClick={handleGotoFirstUnReadBtn}
    >
      <span>
        {currentReadSeqInfo?.maxSeq - currentReadSeqInfo?.hasReadSeq}
        条新消息
      </span>
      <img src={upArrow}></img>
    </div>
  ) : (
    <></>
  );
};

export default GotoFirstUnReadBtn;
