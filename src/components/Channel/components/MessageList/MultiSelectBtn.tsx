import { useEffect, useMemo, useState } from 'react';
import classNames from 'classnames';
import { isEmpty } from 'lodash';
import {
  ConversationItem,
  MessageItem,
  SessionType,
} from '@ht/openim-wasm-client-sdk';
import { IMSDK } from '@/layouts/BasicLayout';
import separateForwardIcon from '@/assets/channel/multiSelectBtn/singleForward.svg';
import separateForwardHoverIcon from '@/assets/channel/multiSelectBtn/singleForwardHover.svg';
import combineForwardIcon from '@/assets/channel/multiSelectBtn/mergeForward.svg';
import combineForwardHoverIcon from '@/assets/channel/multiSelectBtn/mergeForwardHover.svg';
import cancelIcon from '@/assets/channel/multiSelectBtn/cancel.svg';
import ForwardModal from '@/components/ForwardModal';
import { useMultiSelectStore, useUserStore } from '@/store';
import styles from './index.less';

const BtnList = [
  {
    key: 'separate',
    label: '逐条转发',
    icon: separateForwardIcon,
    hoverIcon: separateForwardHoverIcon,
  },
  {
    key: 'combine',
    label: '合并转发',
    icon: combineForwardIcon,
    hoverIcon: combineForwardHoverIcon,
  },
];
const MultiSelectBottom = ({
  currentConversation,
}: {
  currentConversation: ConversationItem | undefined;
}) => {
  const { userID, nickname } = useUserStore.getState().selfInfo;
  const [forwardModal, setForwardModal] = useState<boolean>(false);
  const [multiForwardMode, setMultiForwardMode] = useState<string>(''); // separate: 逐条转发, combine: 合并转发

  const [curMessage, setCurMessage] = useState<MessageItem>();
  const multiSelectList = useMultiSelectStore((state) => state.multiSelectList);
  const cancelMultiSelect = useMultiSelectStore(
    (state) => state.cancelMultiSelect
  );

  const ifDisabledForwardBtn = useMemo(() => {
    return multiSelectList.length <= 0;
  }, [multiSelectList]);

  const renderMutiTitle = useMemo(() => {
    const isSelf = currentConversation?.userID === userID;
    const showNameText = isSelf
      ? nickname
      : `${currentConversation?.showName || ''}和${nickname}`;
    return currentConversation?.conversationType === SessionType.Group
      ? `群聊的聊天记录`
      : `${showNameText}的聊天记录`;
  }, [currentConversation, userID, nickname]);

  const handleMergeMeaasge = async () => {
    const resMsg =
      (
        await IMSDK.createMergerMessage({
          title: renderMutiTitle,
          messageList: multiSelectList,
          summaryList: [],
        })
      ).data || '';
    setCurMessage(resMsg);
  };

  useEffect(() => {
    if (!isEmpty(curMessage)) {
      setForwardModal(true);
    }
  }, [curMessage]);

  return (
    <div className={styles.multiSelectBottomWrapper}>
      <div className={styles.btnGroup}>
        {BtnList.map((btnItem) => {
          return (
            <div key={btnItem.key} className={styles.btnItemWrapper}>
              <div
                className={classNames(
                  styles.btnIconWrapper,
                  ifDisabledForwardBtn && styles.btnIconWrapper_disabled
                )}
                onClick={() => {
                  if (ifDisabledForwardBtn) {
                    return;
                  }
                  setMultiForwardMode(btnItem.key);
                  handleMergeMeaasge();
                }}
              >
                <img src={btnItem.icon} className={styles.btnIcon} />
                <img src={btnItem.hoverIcon} className={styles.btnHoverIcon} />
              </div>
              <div className={styles.btnLabel}>{btnItem.label}</div>
            </div>
          );
        })}
      </div>
      <div className={styles.cancelBtn} onClick={() => cancelMultiSelect()}>
        <img src={cancelIcon} />
      </div>

      {forwardModal && curMessage && (
        <ForwardModal
          open={forwardModal}
          onClose={() => setForwardModal(false)}
          message={curMessage}
          forwardType={'multi'}
          multiForwardMode={multiForwardMode}
          isThread={false}
          isSender={false}
          conversationID={currentConversation?.conversationID}
          showName={currentConversation?.showName}
        />
      )}
    </div>
  );
};

export default MultiSelectBottom;
