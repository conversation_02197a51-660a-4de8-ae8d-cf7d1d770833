import { memo, useEffect, useRef, useState } from 'react';
import upArrow from '@/assets/images/upArrow.svg';
import { debounce, isEmpty } from 'lodash';
import { v4 as uuidv4 } from 'uuid';
import { useConversationStore } from '@/store';
import styles from './index.less';

const GotoFirstUnReadBtn = () => {
  const [showBtn, setShowBtn] = useState(false);
  const timeRef = useRef<any>();

  const currentConversation = useConversationStore(
    (state) => state.currentConversation
  );

  const updateTargetMsg = useConversationStore(
    (state) => state.updateTargetMsg
  );

  const updateCurrentConversation = useConversationStore(
    (state) => state.updateCurrentConversation
  );

  const hideReadSeqInfo = useConversationStore(
    (state) => state.hideReadSeqInfo
  );

  const currentReadSeqInfo = useConversationStore(
    (state) => state.currentReadSeqInfo
  );

  useEffect(() => {
    if (
      currentConversation?.conversationID !==
        currentReadSeqInfo?.conversationID ||
      currentReadSeqInfo?.maxSeq == null ||
      currentReadSeqInfo?.hasReadSeq == null
    ) {
      return;
    }
    // 至少两条，最后一条点过去肯定能直接看到的
    const ifShowGotoFirstUnReadBtn =
      currentReadSeqInfo != null &&
      !isEmpty(currentReadSeqInfo.unReadSeqs) &&
      currentReadSeqInfo?.maxSeq - currentReadSeqInfo?.hasReadSeq > 1 &&
      !currentReadSeqInfo.clicked;

    // 这里是要等待初始化加载后，可见范围内的消息标是否已出现，因为不好定一个时间点，如果能精确的知道初始化加载完后可见范围内会有哪些消息就好了。先用1000ms的等待。by zaa 20250703
    timeRef.current = setTimeout(() => {
      console.debug('进入了设置');
      setShowBtn(ifShowGotoFirstUnReadBtn);
      if (timeRef.current) {
        clearTimeout(timeRef.current);
      }
    }, 500);

    return () => {
      if (timeRef.current) {
        clearTimeout(timeRef.current);
      }
    };
  }, [currentConversation?.conversationID, currentReadSeqInfo]);

  const handleGotoFirstUnReadBtn = () => {
    if (
      currentConversation?.conversationID !==
        currentReadSeqInfo?.conversationID ||
      currentReadSeqInfo?.maxSeq == null ||
      currentReadSeqInfo?.hasReadSeq == null
    ) {
      return;
    }

    const firstUnReadSeq = currentReadSeqInfo?.hasReadSeq + 1;

    setShowBtn(false);
    hideReadSeqInfo();

    updateTargetMsg({
      clientMsgID: undefined,
      seq: firstUnReadSeq,
    });

    const ref = setTimeout(() => {
      updateCurrentConversation({
        ...currentConversation,
        ex: `${uuidv4()}`,
      });
      if (ref) {
        clearTimeout(ref);
      }
    }, 100);
  };

  const debouceHandleGotoFirstUnReadBtn = debounce(
    handleGotoFirstUnReadBtn,
    100
  );
  console.debug({ currentReadSeqInfo }, showBtn);

  return showBtn && currentReadSeqInfo != null ? (
    <div
      className={styles.gotoFirstUnReadBtn}
      onClick={debouceHandleGotoFirstUnReadBtn}
    >
      <span>
        {currentReadSeqInfo?.maxSeq - currentReadSeqInfo?.hasReadSeq > 99
          ? '99+'
          : currentReadSeqInfo?.maxSeq - currentReadSeqInfo?.hasReadSeq}
        条新消息
      </span>
      <img src={upArrow}></img>
    </div>
  ) : (
    <></>
  );
};

export default memo(GotoFirstUnReadBtn);
