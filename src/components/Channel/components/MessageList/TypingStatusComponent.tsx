import useTypingStatus from '@/hooks/useTypingStatus';
import { t } from 'i18next';
import { ConversationItem } from '@ht/openim-wasm-client-sdk';
import styles from './index.less';

const TypingStatusComponent = ({
  conversation,
}: {
  conversation?: ConversationItem;
}) => {
  const { typing } = useTypingStatus(conversation);

  return typing ? (
    <span className={styles.typingStatus}>
      <span>{t('placeholder.typing')}</span>
      <span className={styles.dot}></span>
    </span>
  ) : (
    <></>
  );
};

export default TypingStatusComponent;
