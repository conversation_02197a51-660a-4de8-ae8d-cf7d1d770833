import React, { useMemo } from 'react';
import {
  ConversationItem,
  GroupMemberRole,
  GroupStatus,
} from '@ht/openim-wasm-client-sdk';
import { useConversationStore } from '@/store';
import shallow from 'zustand/shallow';
import exitIcon from '@/assets/images/exit.svg';
import disableContactIcon from '@/assets/images/disableContact.svg';
import MessageInput from '../MessageInput';
import styles from './index.less';
import TypingStatus from './typingStatus';

interface DisableAreaProps {
  conversation?: ConversationItem;
  inRightThread: boolean;
}

const DisableArea: React.FC<DisableAreaProps> = ({
  conversation,
  inRightThread,
}) => {
  const {
    currentMemberInGroup,
    currentGroupInfo,
    currentMemberInGroupIniting,
  } = useConversationStore(
    (state) => ({
      currentMemberInGroup: state.currentMemberInGroup,
      currentGroupInfo: state.currentGroupInfo,
      currentMemberInGroupIniting: state.currentMemberInGroupIniting,
    }),
    shallow
  );

  // 群禁言 禁用输入框相关操作
  const disabledBtn = useMemo(() => {
    return (
      (conversation?.groupID &&
        currentGroupInfo?.status === GroupStatus.Muted &&
        currentMemberInGroup?.roleLevel === GroupMemberRole.Normal) ||
      false
    );
  }, [
    conversation?.groupID,
    currentGroupInfo?.status,
    currentMemberInGroup?.roleLevel,
  ]);

  // 是否退群或解散群聊
  const isExit = useMemo(() => {
    return (
      (conversation?.groupID &&
        (!currentMemberInGroup || !currentMemberInGroup.userID) &&
        !currentMemberInGroupIniting) ||
      false
    );
  }, [
    conversation?.groupID,
    currentMemberInGroup,
    currentMemberInGroupIniting,
  ]);

  return isExit || disabledBtn ? (
    <div className={styles.exitBox}>
      <img src={isExit ? exitIcon : disableContactIcon} />
      <span>{isExit ? '你已退出该群聊，无法发送消息' : '已禁言'}</span>
    </div>
  ) : (
    <div className={styles.messageInputWrap}>
      <TypingStatus conversation={conversation} />
      <MessageInput conversation={conversation} inRightThread={inRightThread} />
    </div>
  );
};

export default DisableArea;
