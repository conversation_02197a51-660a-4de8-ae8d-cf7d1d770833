import { LoadingSpinner } from '@/components/LoadingSpinner';
import React, { useState, useEffect, useMemo, useLayoutEffect } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';

interface Props {
  messages: any[];
  hasMoreOld: boolean;
  onLoadMore: () => void;
  itemContent: (index: number, item: any) => React.ReactNode;
}

const MyList: React.FC<Props> = ({
  messages,
  hasMoreOld,
  onLoadMore,
  itemContent,
}) => {
  // 使用 useMemo 缓存反转后的消息数组，避免不必要的重复计算
  const reversedMessages = useMemo(() => [...messages].reverse(), [messages]);

  const handleLoadMore = () => {
    if (!hasMoreOld) {
      return;
    }
    onLoadMore();
  };

  return (
    <div
      id="scrollableDiv"
      style={{
        height: '100%',
        overflow: 'auto',
        display: 'flex',
        flexDirection: 'column-reverse',
      }}
    >
      {/* Put the scroll bar always on the bottom */}
      <InfiniteScroll
        dataLength={reversedMessages.length}
        next={handleLoadMore}
        style={{
          display: 'flex',
          flexDirection: 'column-reverse',
          overflow: 'hidden',
        }} // To put endMessage and loader to the top.
        inverse={true}
        hasMore={hasMoreOld}
        loader={
          <div
            style={{
              width: '100%',
              height: '40px',
              display: 'flex',
              justifyContent: 'center',
              alignContent: 'center',
            }}
          >
            <LoadingSpinner />
          </div>
        }
        scrollableTarget="scrollableDiv"
      >
        {reversedMessages.map((_, index) => {
          const originalIndex = messages.length - 1 - index;
          return itemContent(originalIndex, reversedMessages[index]);
        })}
      </InfiniteScroll>
    </div>
  );
};
export default MyList;
