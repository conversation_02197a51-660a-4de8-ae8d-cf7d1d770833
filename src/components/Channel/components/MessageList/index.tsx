/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable max-statements */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable max-lines */
import {
  forwardRef,
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { IMSDK } from '@/layouts/BasicLayout';
import { useHistoryMessageList } from '@/hooks/useHistoryMessageList';
import { useConversationStore, useUserStore, useMultiSelectStore } from '@/store';
import {
  MessageItem as MessageItemType,
  ConversationItem,
  GroupStatus,
  GroupMemberRole,
  MessageType,
  MessageStatus,
} from '@ht/openim-wasm-client-sdk';
import {
  LogLevel,
  Virtuoso,
  VirtuosoHandle,
} from '@ht/react-virtuoso';
import useConversationState from '@/hooks/useConversationState';
import emitter from '@/utils/events';
import _, { throttle, isEmpty } from 'lodash';
import { useDeepCompareEffect, useLatest } from 'ahooks';
import offlineIcon from '@/assets/images/offlineIcon.png';
import exitIcon from '@/assets/images/exit.svg';
import disableContactIcon from '@/assets/images/disableContact.svg';
import doubleDownArrowIcon from '@/assets/images/doubleDownArrow.svg';
import { useMessageScroll } from '@/hooks/useMessageScroll';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { useConversationSettings } from '@/hooks/useConversationSettings';
import {  useResizeObserverWithThreshold } from '@/hooks/useMessageListWidthChange';
import { NotificationMessageList } from '@/components/Channel/components/MessageItem/index';
import MessageItem from '../MessageItem';
import MessageInput from '../MessageInput';
import MessageListForeword from '../MessageListForeword';
import styles from './index.less';
import MessageUp from '../MessageUp';
import TypingStatusComponent from './TypingStatusComponent';
import GuideQuestions from '../MessageItem/StreamMessage/GuideQuestions';
import GotoFirstUnReadBtn from './GotoFirstUnReadBtn';
import MultiSelectBottom from './MultiSelectBtn';

const MessageList = ({
  conversation,
  inRightThread = false,
}: {
  conversation?: ConversationItem;
    inRightThread?: boolean;
  }) => {
  
  const virtuoso = useRef<VirtuosoHandle>(null);

  const [showHeader, setShowHeader] = useState(false);

  const currentConversation = conversation;

  const { throttleCheckConversationState } =
    useConversationState(currentConversation);

  const latestUnreadCount = useLatest(currentConversation?.unreadCount ?? 0);
  const groupAtType = useLatest(currentConversation?.groupAtType ?? 0);

  const { markConversationMessageAsRead } =
    useConversationSettings(currentConversation);

  // const updateConversation = useConversationStore(
  //   (state) => state.updateConversation
  // );
  const [atBottomState, setAtBottomState] = useState(false);
  const atBottomStateChangeStamp = useRef<number>(0);
  
  const {
    conversationID,
    loadState,
    moreOldLoading,
    moreOldLoadingReverse,
    getMoreOldMessages,
    getMoreOldMessagesReverse,
  } = useHistoryMessageList(currentConversation);

  const [showVirtuosoLisAfterDelay, setShowVirtuosoLisAfterDelay] =
    useState(false);

  const { messageList } = loadState;

  const flistUseConversation = useConversationStore(
    (state) => state.currentConversation
  );

  const currentMemberInGroup = useConversationStore(
    (state) => state.currentMemberInGroup
  );

  const currentGroupInfo = useConversationStore(
    (state) => state.currentGroupInfo
  );
  // const targetMsg = useConversationStore((state) => state.targetMsg);
  // const updateTargetMsg = useConversationStore(
  //   (state) => state.updateTargetMsg
  // );
  const currentMemberInGroupIniting = useConversationStore(
    (state) => state.currentMemberInGroupIniting
  );
  const { questions, clientMsgId }  = useConversationStore(
    (state) => state.llmQuestions
  );
  const isMultiSession = useConversationStore((state) => state.isMultiSession);
  const currentBotConfig = useConversationStore(
    (state) => state.currentBotConfig
  );

  const currentMessageUpInfo = useConversationStore(
    (state) => state.currentMessageUpInfo
  );

  const getCurrentMessageUpInfo = useConversationStore(
    (state) => state.getCurrentMessageUpInfo
  );

  const multiSelectState = useMultiSelectStore(
    (state) => state.multiSelectState
  );

  const isThread = useMemo(() => {
    return (
      currentConversation?.groupID && currentConversation?.parentId != null
    );
  }, [currentConversation]);

  // 是否退群或解散群聊
  const isExit = useMemo(() => {
    return (
      (currentConversation?.groupID &&
        (!currentMemberInGroup || !currentMemberInGroup.userID) &&
        !currentMemberInGroupIniting) ||
      false
    );
  }, [currentConversation, currentMemberInGroup, currentMemberInGroupIniting]);

  // 群禁言 禁用输入框相关操作
  const disabledBtn = useMemo(() => {
    return (
      (currentConversation?.groupID &&
        currentGroupInfo?.status === GroupStatus.Muted &&
        currentMemberInGroup?.roleLevel === GroupMemberRole.Normal) ||
      false
    );
  }, [currentGroupInfo, currentMemberInGroup]);

  const markConversationAsRead = useCallback(() => {
    setTimeout(() => {
      console.debug('markConversationAsRead','进去这里');
      if (latestUnreadCount.current !=null && latestUnreadCount.current > 0 || groupAtType.current !== 0) {
        throttleCheckConversationState();
      }
    }, 0);
  }, [throttleCheckConversationState, groupAtType]);

  const {
    hasScrolled,
    scrollToBottomSmooth,
    handleMessagesChange,
    handleNearBottom,
  } = useMessageScroll({
    virtuosoRef: virtuoso,
    conversation: currentConversation,
    onMarkAsRead: markConversationAsRead,
  });
  const isFirstLoadRef = useRef(true);

  // 更新消息列表的 effect
  const processedMessages = useMemo(() => {
    try {
      const baseMessages = messageList;

      if (baseMessages != null) {
        return baseMessages;
      }
      return null;
    } catch (error) {
      console.error(error);
      return null;
    }
  }, [currentConversation?.conversationID, messageList]);

  const targetMsg = useConversationStore((state) => state.targetMsg);
  const updateTargetMsg = useConversationStore((state) => state.updateTargetMsg);
  

  // 感觉没啥意义？
  useDeepCompareEffect(() => {
    if (isFirstLoadRef.current) {
      if (!targetMsg.seq) {
        // scrollToBottomSmooth();
      } else {
        updateTargetMsg({});
      }
      isFirstLoadRef.current = false;
      // updateTargetMsg({});
    }
  }, [loadState.messageList?.length, conversation?.conversationID, targetMsg]);

  useDeepCompareEffect(() => {
    const handleScrollToBottom = ({
      conversation: targetConv = currentConversation,
    }) => {
      if (targetConv?.conversationID === currentConversation?.conversationID) {
        scrollToBottomSmooth();
      }
    };

    emitter.on('CHAT_LIST_SCROLL_TO_BOTTOM', handleScrollToBottom);
    return () => {
      emitter.off('CHAT_LIST_SCROLL_TO_BOTTOM', handleScrollToBottom);
    };
  }, [currentConversation]);

  useDeepCompareEffect(() => {
    if (processedMessages == null || processedMessages?.length === 0) {
      return;
    }
    // 系统消息不算是自己发送的消息
    const isSelfMessage =
      processedMessages[processedMessages.length - 1]?.sendID ===
      useUserStore.getState().selfInfo.userID && !NotificationMessageList.includes(processedMessages[processedMessages.length - 1].contentType);
    handleMessagesChange(processedMessages, isSelfMessage);
  }, [processedMessages?.length, handleMessagesChange]);

  const syncState = useUserStore((state) => state.syncState);
  const connectState = useUserStore((state) => state.connectState);

  const loadMoreMessage = useCallback(
    throttle(
      () => {
        if (!loadState.hasMoreOld || moreOldLoading) {
          return;
        }
        console.debug('查询更多');
        getMoreOldMessages();
      },
      200,
      { leading: true, trailing: false }
    ),
    [loadState.hasMoreOld, moreOldLoading, getMoreOldMessages]
  );

  const loadMoreMessageReverse = useCallback(
    throttle(
      () => {
        if (!loadState.hasMoreOldReverse || moreOldLoadingReverse) {
          return;
        }
        getMoreOldMessagesReverse();
      },
      200,
      { leading: true, trailing: false }
    ),
    [
      loadState.hasMoreOldReverse,
      moreOldLoadingReverse,
      getMoreOldMessagesReverse,
    ]
  );

  const ref = useResizeObserverWithThreshold<HTMLDivElement>(
    () => {
      if(atBottomState || (!atBottomState && ((Date.now() - atBottomStateChangeStamp.current)<300))){
        virtuoso.current?.scrollToIndex({
          index: 'LAST',
          align: 'end',
          behavior: 'smooth',
        })
      }
    },
    {
      debounceDelay: 50,
      widthThreshold: 5, // 至少变化 5px 才响应
    }
  )

  const itemContentWrap = useCallback(
    (index: number, msg: MessageItemType) => {
      try {
        const isSender = useUserStore.getState().selfInfo.userID === msg.sendID
        const messageItemProps = {
          isThread,
          inRightThread, // 原样透传，重新发送消息的时候会用到
          conversationID,
          message: msg,
          messageUpdateFlag: msg.senderNickname + msg.senderFaceUrl,
          isSender,
          showName: currentConversation?.showName,
          // scrollToBottomSmooth: () => scrollToBottomSmooth(),
          currentConversation,
          // hasScrolled,
          virtuoso: virtuoso.current,
          messageList,
          hasForwardBtn: true,
        };
        const messageIndex = processedMessages?.findIndex(
          (item) => item.clientMsgID === msg.clientMsgID
        ) || 0

        return (
          <>
            <MessageItem
              hasMoreMessageBefore={loadState.hasMoreOld}
              messageIndex={messageIndex}
              isLastMsg={processedMessages ? messageIndex === processedMessages.length - 1 : false}
              prevMsg={processedMessages && messageIndex > 0 ? processedMessages?.[messageIndex - 1] : undefined}
              clientMsgId={msg.clientMsgID}
              sendID={msg.sendID}
              key={msg.clientMsgID}
              {...messageItemProps}
              isExit={isExit}
              markConversationMessageAsRead={markConversationMessageAsRead}
            />
            {msg.clientMsgID === clientMsgId && !!questions?.length && <GuideQuestions questions={questions} currentConversation={currentConversation} />}
            {isMultiSession && !isEmpty(currentBotConfig) &&<div className={styles.lastMultiSessionItem}></div>}
          </>
        );
      } catch (e) {
        console.error('渲染消息报错', e);
        return <></>;
      }
    },
    [
      messageList,
      isThread,
      conversationID,
      currentConversation,
      // scrollToBottomSmooth,
      loadState.hasMoreOld,
    ]
  );

  const isConnecting = useMemo(() => {
    return (
      syncState === 'success' &&
      (connectState === 'loading' || connectState === 'failed')
    );
  }, [syncState, connectState]);

  const showNewWorkErrorInfo = useMemo(() => {
    return (
      loadState.initLoading &&
      (processedMessages == null || processedMessages?.length === 0) &&
      isConnecting
    );
  }, [loadState, processedMessages, isConnecting]);


  const showVirtuoso = !loadState.initLoading && processedMessages != null;

  // Header组件的显示时机要注意下，如果是无会话记录的新用户，直接展示；否则，在第一页历史消息查询完、rangeChanged后才应该触发，否则Header会先于虚拟列表中的消息item先出现、导致闪烁效果
  useEffect(() => {
    if (processedMessages?.length === 0 && showVirtuoso) {
      setShowHeader(true);
    }
  }, [processedMessages, showVirtuoso]);

  useEffect(() => {
    if (showVirtuoso) {
      // virtuoso.current?.autoscrollToBottom()
      setTimeout(() => setShowVirtuosoLisAfterDelay(true), 80);
    }
  }, [showVirtuoso]);

  // 缓存下
  const renderMessageUpInfo = useMemo(() => {
    if (
      currentMessageUpInfo.filter(
        (i) => i.msgs?.groupID === currentConversation?.groupID
      )?.length > 0 && !isExit
    ) {
      return (
        <div className={styles.messageUpWrap}>
          <MessageUp
            currentMessageUpInfo={currentMessageUpInfo}
            messageList={messageList}
            virtuoso={virtuoso.current}
          />
        </div>
      );
    }
    return <></>;
  }, [currentMessageUpInfo, messageList, isExit]);

  useEffect(() => {
    if (!isExit && currentConversation?.groupID) {
      getCurrentMessageUpInfo(currentConversation?.groupID)
    }
  }, [isExit, currentConversation?.groupID])

  const emptyPlaceholder = useCallback(() => {
    return <MessageListForeword
      key={flistUseConversation?.conversationID}
      isGroup={!isEmpty(flistUseConversation?.groupID)}
      currentConversation={flistUseConversation}
      sourceFormEmpty={true}
    />
  },[flistUseConversation])

  
  const setCurrentMessageInputValue = useConversationStore(
    (state) => state.setCurrentMessageInputValue
  );
  
  useEffect(() => {
    if (isExit) {
      try {
        if (showVirtuoso && currentConversation?.conversationID != null && currentConversation?.draftText != null && currentConversation?.draftText !== "") {

          setCurrentMessageInputValue(null);
          IMSDK.setConversationDraft({
            conversationID,
            draftText: '',
          });
          
        }
      } catch (e) {
        console.error(currentConversation?.conversationID,'已退群或解散群清除草稿失败')
      }
    }
  }, [isExit,showVirtuoso,currentConversation])

  // // 获取当前在可见范围的消息内容
  // const getActualVisibleRange = () => {
  //   // const viewPortDom = document.getElementById('messageList');
  //   const viewPortDom = virtuoso.current;
  //   if (processedMessages==null) {
  //     return [];
  //   }

  //   if (
  //     viewPortDom != null &&
  //     virtuoso?.current != null
  //   ) {

  //     const viewRect = viewPortDom?.getBoundingClientRect();

  //     const readClientMsgIDList = [];
  //     if (viewRect == null) {return [];}

  //     console.debug("getActualVisibleRange",{ viewRect })
      
  //     const allSelect = viewPortDom.querySelectorAll('[data-index]');


  //     console.debug("getActualVisibleRange",{ allSelect })
      

  //     for (let i = 0; i <= allSelect.length - 1; i++) {
  //       const index = allSelect[i];
  //       // 获取该项的实际高度（这里需要根据你的实际DOM结构来获取）
  //       const itemRect = index?.getBoundingClientRect();
  //       console.debug("getActualVisibleRange",itemRect,viewRect)
      

  //       if (itemRect.bottom<viewRect.top || itemRect.top>viewRect.bottom) {
  //         continue;
  //       }
  //       else {
  //         const number = Number(index.getAttribute('data-index'));
  //         readClientMsgIDList.push(processedMessages[number]);
  //       }
  //     }

  //     console.debug('可见范围', readClientMsgIDList);
  //     return readClientMsgIDList;
  //     // debounceMarkGroupMessageAsRead(readClientMsgIDList);
  //   } else {
  //     return [];
  //   }
  // };

  // // 消息是否需标已读，过滤掉自己发送的消息、已标为已读的等消息
  // const isMsgNeedMarkAsRead = (msgItem:MessageItemType) => {
  //   if (msgItem.sendID === selfID) { return false; }// 自己发的消息不用标
  //   if (msgItem.isRead) { return false; }// 已标为已读的不用标
  //   if (isBotUser(msgItem.sendID)) { return false }// 机器人消息不用标已读
    
  //   if (msgItem.status === MessageStatus.Failed || msgItem.status === MessageStatus.Sending) {return false;} // 发送中和失败的不用标记，这个按理说只有自己发的消息会存在，也过滤下吧

  //   if (msgItem.contentType === MessageType.GroupCreated ||
  //     msgItem.contentType === MessageType.MemberInvited ||
  //     msgItem.contentType === MessageType.MemberEnter ||
  //     msgItem.contentType === MessageType.MemberKicked ||
  //     msgItem.contentType === MessageType.GroupDismissed ||
  //     msgItem.contentType === MessageType.MemberQuit ||
  //     msgItem.contentType === MessageType.GroupNameUpdated) {
  //     return false;
  //   };// 一些通知类消息，不需要展示已读未读的，不用标记

  //   return true;
  // }

  // // 监听滚动事件，更新实际可见范围
  // const handleScroll = () => {
  //   if (isExit) {return;} // 已退群的，不需要markAsRead
  //   const viewPortMessageItems = getActualVisibleRange();
  //   console.debug('实际可见范围', viewPortMessageItems);
  //   if (viewPortMessageItems.length > 0) {
  //     const needMarkAsReadMsgClientIDs = new Set(viewPortMessageItems?.filter((item) => isMsgNeedMarkAsRead(item))?.map(item => item.clientMsgID));

  //     markConversationMessagesAsRead([...needMarkAsReadMsgClientIDs]);
  //   }
  // };

  // console.debug('messageList', processedMessages);

  return (
    <div className={styles.messageWarp} key={conversationID} ref={ref}>
      {renderMessageUpInfo}
      <div className={styles.messageContent}>
        <div className={styles.box}>
          {/* {showSkeleton && <CustomSkeleton />} */}
          <>
            {showNewWorkErrorInfo && (
              <div className={styles.netErrorArea}>
                <img src={offlineIcon}></img>
                <span>网络已断开</span>
              </div>
            )}
            {showVirtuoso && (
              <Virtuoso
                style={{
                  position: 'absolute',
                  width: '100%',
                  visibility: showVirtuosoLisAfterDelay ? 'visible' : 'hidden',
                }}  
                // logLevel={LogLevel.DEBUG}
                id={'messageList'}
                className={styles.virtuosoListContainer}
                followOutput={(isAtBottom) => {
                  if (document.hidden || !isAtBottom) {
                    return false;
                  }
                  return 'smooth';
                }}
                skipAnimationFrameInResizeObserver={true}
                firstItemIndex={loadState.firstItemIndex}
                initialTopMostItemIndex={{
                  index: loadState.initialTopMostItemIndex,
                  align: 'end',
                }}
                startReached={() => {
                  // if (targetMsg?.seq != null) {
                  //   return;
                  // }
                  loadMoreMessage();
                }}
                // onScroll={handleScroll}
                endReached={() => {
                  // if (targetMsg?.seq != null) {
                  //   return;
                  // }
                  loadMoreMessageReverse();
                }}
                ref={virtuoso}
                data={processedMessages}
                computeItemKey={(_, item) => item.clientMsgID}
                increaseViewportBy={{ top: 1500, bottom: 1200 }}
                defaultItemHeight={200}
                itemContent={itemContentWrap}
                components={{
                  Header: () => {
                    if (!showHeader) {
                      return <></>;
                    }
                    return (
                      <div
                        style={{
                          position: 'fixed',
                          left: '50%',
                          display: 'flex',
                          justifyContent: 'center',
                          padding: '5px 0',
                          visibility: loadState.hasMoreOld
                            ? 'visible'
                            : 'hidden',
                        }}
                      >
                        <LoadingSpinner />
                      </div>
                    );
                  },
                  EmptyPlaceholder: emptyPlaceholder, 

                }}
                atBottomStateChange={(atBottomState: boolean) => {
                  setAtBottomState(atBottomState);
                  handleNearBottom(atBottomState);
                  if (atBottomState) {atBottomStateChangeStamp.current = Date.now();}
                }}
                // onScroll={() => {
                //   console.debug('hasReachedBottomInitially','滚走')
                //   hasReachedBottomInitially.current=false
                // }}
                rangeChanged={(range) => {
                  console.info({range});
                  if (showHeader) {
                    return;
                  }

                  if (range.startIndex !== range.endIndex) {
                    setShowHeader(true);
                  } else if (
                    range.startIndex === range.endIndex &&
                    range.endIndex === 9999 &&
                    processedMessages.length === 1
                  ) {
                    setShowHeader(true);
                  }
                }}
              />
            )}
            {hasScrolled &&  latestUnreadCount?.current!=null && latestUnreadCount?.current > 0 && (
              <div
                className={styles.unReadCount}
                onClick={scrollToBottomSmooth}
              >
                最新消息
                <img src={doubleDownArrowIcon} />
              </div>
            )}
            <GotoFirstUnReadBtn />
          </>
        </div>
      </div>
      
      {multiSelectState ? <MultiSelectBottom currentConversation={currentConversation} /> : <>
        {isExit || disabledBtn ? (
          <div className={styles.exitBox}>
            <img src={isExit ? exitIcon : disableContactIcon} />
            <span>{isExit ? '你已退出该群聊，无法发送消息' : '已禁言'}</span>
          </div>
        ) : (
          <div className={styles.messageInputWrap}>
            <TypingStatusComponent conversation={currentConversation} />
            <MessageInput
              conversation={conversation}
              inRightThread={inRightThread}
              multiSessionDataIsEmpty={processedMessages?.length === 0}
            />
          </div>
        )}
      </>}
    </div>
  );
};

// const CustomSkeleton = () => {
//   return (
//     <div
//       className={styles.loadingBox}
//       style={{
//         paddingTop: '10px',
//         backgroundImage: `url(${skeleton})`,
//         backgroundRepeat: 'no-repeat',
//         margin: '10px 20px',
//       }}
//     />
//   );
// };
export default memo(MessageList);
