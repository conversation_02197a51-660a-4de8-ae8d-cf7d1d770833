import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import classNames from 'classnames';
import { Typography } from '@ht/sprite-ui';
import { GroupMemberRole } from '@ht/openim-wasm-client-sdk';
import { Virtuoso } from '@ht/react-virtuoso';
import { useConversationStore } from '@/store';
import { RenderMd } from '@/components/MdEditor';
import OIMAvatar from '@/components/OIMAvatar';
import withScrollBar from '@/components/withScrollBar';
import { isBotUser } from '@/utils/avatar';
import GroupAnnouncementModal from '@/components/ConversationSetModal/components/GroupAnnouncementModal';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import useGroupMembers from '@/hooks/useGroupMembers';
import { PopPersonCardContainer } from '../MessageItem/PopPersonCardContainer';
import styles from './index.less';

const ChannelMemberList = () => {
  const renderMdRef = useRef();
  const announcementContentRef = useRef();
  const currentConversation = useConversationStore(
    (state) => state.currentConversation
  );
  const currentMemberInGroup = useConversationStore(
    (state) => state.currentMemberInGroup
  );

  const isGroup = !!currentConversation?.groupID;

  const { fetchState, getMemberData, resetState } = useGroupMembers({
    groupID: currentConversation?.groupID,
  });
  const { groupMemberList } = fetchState;

  const initOrRefreshData = useCallback(() => {
    if (currentConversation?.groupID) {
      getMemberData(true);
    }
  }, [currentConversation?.groupID, getMemberData]);

  // 是否退群或解散群聊
  const isExit =
    (currentConversation?.groupID &&
      (!currentMemberInGroup || !currentMemberInGroup.userID)) ||
    false;

  const ifShowMemberList = useMemo(() => {
    return isGroup && !isExit;
  }, [isExit, isGroup]);

  useEffect(() => {
    initOrRefreshData();
    return () => resetState();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentConversation?.groupID]);

  const currentGroupInfo = useConversationStore(
    (state) => state.currentGroupInfo
  );

  const { notification = '', groupID = '' } = currentGroupInfo || {};
  const [showGroupAnnouncement, setShowGroupAnnouncement] = useState(false);

  const renderMdRefHeight = renderMdRef.current?.offsetHeight || 0;
  const announcementContentRefHeight =
    announcementContentRef.current?.offsetHeight || 0;

  const showShadow = renderMdRefHeight > announcementContentRefHeight;

  return ifShowMemberList ? (
    <div className={styles.channelMemberListWrapper}>
      {notification && (
        <div
          className={styles.announcementWarp}
          onClick={() => setShowGroupAnnouncement(true)}
        >
          <div className={styles.title}>群公告</div>
          <div
            className={styles.announcementContent}
            ref={announcementContentRef}
          >
            <div ref={renderMdRef}>
              <RenderMd
                value={notification}
                id={`notificationForNumber_${groupID}`}
              />
            </div>
          </div>
          {showShadow && <div className={styles.shadow}></div>}
        </div>
      )}
      {notification && (
        <div className={styles.line}>
          <div></div>
        </div>
      )}
      <div className={styles.headerTitle}>
        群成员（{groupMemberList.length}）
      </div>
      {/* 成员列表区域 */}
      <div className={styles.memberList}>
        <Virtuoso
          data={groupMemberList || []}
          className={styles.virtuosoListContainer}
          itemContent={(_index, member) => {
            return (
              <PopPersonCardContainer
                sendID={member.userID}
                mouseEnterDelay={0.5}
              >
                <div className={styles.memberItemWrapper}>
                  <div className={styles.memberItem}>
                    <OIMAvatar
                      userID={member.userID}
                      size={32}
                      shape="square"
                      borderRadius={8}
                      stateSize={8}
                    />
                    <Typography.Text
                      ellipsis={true}
                      className={styles.nickname}
                    >
                      {member.nickname}
                    </Typography.Text>
                    {member.roleLevel === GroupMemberRole.Owner && (
                      <div className={classNames(styles.tag, styles.tag_1)}>
                        群主
                      </div>
                    )}
                    {member.roleLevel === GroupMemberRole.Admin && (
                      <div className={classNames(styles.tag, styles.tag_2)}>
                        管理员
                      </div>
                    )}
                    {isBotUser(member.userID) && (
                      <div className={classNames(styles.tag, styles.tag_3)}>
                        机器人
                      </div>
                    )}
                  </div>
                </div>
              </PopPersonCardContainer>
            );
          }}
          components={{
            EmptyPlaceholder: () => {
              // 群聊一定有群成员 所以空数据的时候就是还在加载中
              return (
                <div style={{ textAlign: 'center', marginTop: '20px' }}>
                  <LoadingSpinner />
                </div>
              );
            },
          }}
        />
      </div>
      {showGroupAnnouncement && (
        <GroupAnnouncementModal
          visible={showGroupAnnouncement}
          isAdmin={
            currentMemberInGroup?.roleLevel === GroupMemberRole.Owner ||
            currentMemberInGroup?.roleLevel === GroupMemberRole.Admin
          }
          onCancel={() => setShowGroupAnnouncement(false)}
          groupInfo={currentGroupInfo}
          type={'preview'}
        />
      )}
    </div>
  ) : (
    <></>
  );
};

export default withScrollBar(ChannelMemberList, {
  domId: 'drag-scroll-bar-right-channelMemberList',
  direction: 'right',
  defaultWidth: '280px',
  maxWidth: 350,
  minWidth: 250,
  dragBarClassName: 'scrollbarRight',
  needLocalStorageWidth: false,
});
