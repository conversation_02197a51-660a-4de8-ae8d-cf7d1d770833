.channelEmptyWrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;

  .headerTitle {
    font-size: 33px;
    font-weight: 600;
    color: var(--primary-text-color-1);
    line-height: 46px;
    margin-bottom: 10px;
    margin-top: 20px;
  }

  .headerTips {
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-text-color-3);
    line-height: 18px;
    margin-bottom: 28px;
  }

  .btnGroup {
    max-width: 850px;
    overflow-y: auto;
    overflow-x: hidden;
    margin-bottom: 20px;

    .btnItem {
      display: flex;
      align-items: center;
      width: 270px;
      height: 92px;
      padding: 0 20px;
      border-radius: 8px;
      border: 1px solid #bbbabb;

      .btnImg {
        width: 50px;
        margin-right: 14px;
      }

      .btnDesc {
        font-size: 20px;
        color: var(--primary-text-color-1);
      }

      &:hover {
        cursor: pointer;
        background: #f6f6f6;
        box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 4%);
        border: 1px solid #b2b2b3;
      }
    }
  }
}
