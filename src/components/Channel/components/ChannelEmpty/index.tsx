import { useState, FC } from 'react';
import { useHistory } from 'react-router-dom';
import { Row, Col } from '@ht/sprite-ui';
import chatIcon from '@/assets/channel/channelEmpty/chat.png';
import channelIcon from '@/assets/channel/channelEmpty/channel.png';
import contactIcon from '@/assets/channel/channelEmpty/contact.png';
import userInfoIcon from '@/assets/channel/channelEmpty/userInfo.png';
import statusIcon from '@/assets/channel/channelEmpty/status.png';
import moreIcon from '@/assets/channel/channelEmpty/more.png';
import {
  useSearchInfoStore,
  useUserStore,
  useConversationStore,
} from '@/store';
import AddGroupModal from '@/components/AddGroupModal';
import { useGlobalModalStore } from '@/store/globalModal';
import styles from './index.less';

const ChannelEmpty = () => {
  const { updateSearchInfo } = useSearchInfoStore();
  const navigate = useHistory();
  const changeRightArea = useConversationStore(
    (state) => state.changeRightArea
  );
  const [showGroupAddModal, setShowGroupAddModal] = useState<boolean>(false);
  const { userID } = useUserStore.getState().selfInfo;

  const { setStatusModalOpen } = useGlobalModalStore();

  return (
    <div className={styles.channelEmptyWrapper}>
      <div className={styles.headerTitle}>欢迎加入Linkflow</div>
      <div className={styles.headerTips}>在这里你可以和企业成员进行对话</div>
      <div className={styles.btnGroup}>
        <Row gutter={[20, 20]} justify="center">
          <Col>
            <div
              className={styles.btnItem}
              onClick={() => {
                updateSearchInfo({ globalSearchModalVisible: true });
              }}
            >
              <img src={chatIcon} className={styles.btnImg} />
              <div className={styles.btnDesc}>发起私聊对话</div>
            </div>
          </Col>
          <Col>
            <div
              className={styles.btnItem}
              onClick={() => {
                setShowGroupAddModal(true);
              }}
            >
              <img src={channelIcon} className={styles.btnImg} />
              <div className={styles.btnDesc}>创建群聊对话</div>
            </div>
          </Col>
          <Col>
            <div
              className={styles.btnItem}
              onClick={() => {
                navigate.push(`./contact`);
              }}
            >
              <img src={contactIcon} className={styles.btnImg} />
              <div className={styles.btnDesc}>查看企业通讯录</div>
            </div>
          </Col>
          <Col>
            <div
              className={styles.btnItem}
              onClick={() => {
                changeRightArea('OPEN_PERSON_DETAIL', userID);
              }}
            >
              <img src={userInfoIcon} className={styles.btnImg} />
              <div className={styles.btnDesc}>查看我的个人信息</div>
            </div>
          </Col>
          <Col>
            <div
              className={styles.btnItem}
              onClick={() => {
                setStatusModalOpen(true);
              }}
            >
              <img src={statusIcon} className={styles.btnImg} />
              <div className={styles.btnDesc}>添加我的工作状态</div>
            </div>
          </Col>
          <Col>
            <div
              className={styles.btnItem}
              onClick={() => {
                window.open('https://linkapp.htsc.com.cn/S/005B3W', '_blank');
              }}
            >
              <img src={moreIcon} className={styles.btnImg} />
              <div className={styles.btnDesc}>更多产品说明</div>
            </div>
          </Col>
        </Row>
      </div>
      {showGroupAddModal && (
        <AddGroupModal
          visible={showGroupAddModal}
          handleCancel={() => {
            setShowGroupAddModal(false);
          }}
        />
      )}
    </div>
  );
};

export default ChannelEmpty;
