.multiMessageModalWrapper {
  :global {
    .linkflow-modal-body {
      padding: 0;
    }
  }
}
.multiMessageContentWrapper {
  overflow: hidden;
  background: #ffffff;
  border-radius: 8px;
  padding: 24px 0;
  display: flex;
  flex-direction: column;

  .header {
    height: 28px;
    font-size: 18px;
    color: #1d1c1d;
    line-height: 28px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;

    .btnGroup {
      display: flex;
      align-items: center;
      > img {
        margin-left: 8px;

        &:hover {
          cursor: pointer;
          background: rgba(107, 107, 108, 8%);
          border-radius: 6px;
        }
      }
    }
  }

  .contentBox {
    flex: 1;
    overflow-y: auto;
    max-height: calc(90vh - 100px);
    margin-top: 20px;
    padding: 0 24px;

    .messageItem {
      display: flex;
      padding: 12px 24px 0;

      .msgContent {
        flex: 1;
        margin-left: 8px;
        padding-bottom: 12px;
        border-bottom: 1px solid rgba(0, 0, 0, 5%);
        overflow: hidden;
      }
    }
  }
}
