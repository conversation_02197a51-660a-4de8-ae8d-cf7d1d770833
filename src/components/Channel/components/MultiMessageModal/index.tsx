import { FC } from 'react';
import { MessageItem } from '@ht/openim-wasm-client-sdk';
import { Modal } from '@ht/sprite-ui';
import MultiMessageContent from './MultiMessageContent';
import styles from './index.less';

export type MultiMessageModalProps = {
  modalOpen: boolean;
  oncancel?: () => void;
  hasForwardBtn?: boolean;
  message?: MessageItem;
  conversationID?: string;
  showName?: string;
};
const MultiMessageModal: FC<MultiMessageModalProps> = (props) => {
  const { modalOpen, oncancel } = props;
  return (
    <Modal
      open={modalOpen}
      onCancel={() => oncancel} // 取消按钮回调
      footer={null}
      centered={true}
      closable={false}
      width={720}
      maskClosable={false}
      wrapClassName={styles.multiMessageModalWrapper}
      getContainer={document.getElementById('BasicLayoutId') || document.body}
    >
      <MultiMessageContent {...props} />
    </Modal>
  );
};

export default MultiMessageModal;
