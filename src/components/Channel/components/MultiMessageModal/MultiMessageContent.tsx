import { FC, useState } from 'react';
import { MessageItem as MessageItemType } from '@ht/openim-wasm-client-sdk';
import OIMAvatar from '@/components/OIMAvatar';
import ForwardModal from '@/components/ForwardModal';
import closeIcon from '@/assets/channel/multiSelectBtn/close.svg';
import forwardIcon from '@/assets/channel/multiSelectBtn/forward.svg';
import { MultiMessageModalProps } from '.';
import MultiMessageItem from './MultiMessageItem';
import styles from './index.less';

const MultiMessageContent: FC<MultiMessageModalProps> = ({
  oncancel,
  hasForwardBtn = false,
  message,
  conversationID,
  showName,
}) => {
  const [forwardModal, setForwardModal] = useState<boolean>(false);
  const { title: renderMutiTitle = '', multiMessage: messageList = [] } =
    message?.mergeElem || {};
  const renderMsg = (msg: MessageItemType) => {
    return (
      <MultiMessageItem
        hasMoreMessageBefore={false}
        messageIndex={messageList?.findIndex(
          (item) => item.clientMsgID === msg.clientMsgID
        )}
        clientMsgId={msg.clientMsgID}
        sendID={msg.sendID}
        key={msg.clientMsgID}
        message={msg}
        inMultiMessageModal={true}
        conversationID={''}
        hasForwardBtn={hasForwardBtn}
      />
    );
  };
  return (
    <div className={styles.multiMessageContentWrapper}>
      <div className={styles.header}>
        <div>{renderMutiTitle}</div>
        <div className={styles.btnGroup}>
          {hasForwardBtn && (
            <img
              src={forwardIcon}
              onClick={(e) => {
                e.stopPropagation();
                setForwardModal(true);
              }}
            />
          )}
          <img
            src={closeIcon}
            onClick={(e) => {
              e.stopPropagation();
              oncancel?.();
            }}
          />
        </div>
      </div>
      <div
        className={styles.contentBox}
        style={{
          minHeight: 'min(calc(90vh - 100px), 704px)',
        }}
      >
        {messageList.map((msg: MessageItemType, index: number) => {
          return (
            <div key={msg.clientMsgID} className={styles.messageItem}>
              <div
                style={{
                  visibility:
                    msg.sendID === messageList[index - 1]?.sendID
                      ? 'hidden'
                      : 'visible',
                }}
              >
                <OIMAvatar
                  userID={msg.sendID}
                  size={40}
                  shape="square"
                  borderRadius={4}
                  hideOnlineStatus={true}
                />
              </div>
              <div className={styles.msgContent}>
                <div>{renderMsg(msg)}</div>
              </div>
            </div>
          );
        })}
      </div>
      {forwardModal && message && (
        <ForwardModal
          open={forwardModal}
          onClose={() => setForwardModal(false)}
          onForwardSuccess={() => {
            oncancel?.();
          }}
          message={message}
          isThread={false}
          isSender={false}
          conversationID={conversationID}
          showName={showName}
        />
      )}
    </div>
  );
};

export default MultiMessageContent;
