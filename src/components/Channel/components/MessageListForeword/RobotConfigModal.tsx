import { FC, memo, useRef } from 'react';
import { Modal } from '@ht/sprite-ui';
import { FormInstance } from '@ht/sprite-ui/es/form/Form';
import { BotConfigItem } from '@/store/type';
import { useConversationStore } from '@/store';
import closeIcon from '@/assets/channel/robotConversationList/close.svg';
import { ConfigFormComponent } from './RobotConfig';
import styles from './index.less';

interface configFormRefType {
  configForm: FormInstance;
}

interface RobotConfigModalProps {
  modalOpen: boolean;
  oncancel: () => void;
  botConfig: BotConfigItem[];
  defaultValue: any;
  showName: string;
}

const RobotConfigModal: FC<RobotConfigModalProps> = ({
  modalOpen = false,
  oncancel,
  botConfig,
  defaultValue,
  showName = '',
}) => {
  const configFormRef = useRef<configFormRefType>();
  const updateRobotConfig = useConversationStore(
    (state) => state.updateRobotConfig
  );

  const submit = async () => {
    try {
      const values = await configFormRef.current?.configForm.validateFields();
      updateRobotConfig({
        data: values || {},
        config: botConfig,
      });
      oncancel();
    } catch (err) {}
  };

  return (
    <Modal
      open={modalOpen}
      onCancel={() => oncancel} // 取消按钮回调
      footer={null}
      centered={true}
      closable={false}
      width={720}
      className={styles.robotConfigModal}
    >
      <div className={styles.robotConfigModaWarp}>
        <div className={styles.header}>
          <div>{showName}-对话设置</div>
          <img src={closeIcon} onClick={oncancel} />
        </div>
        <div
          className={styles.formBox}
          style={{ maxHeight: 'calc(90vh - 100px)' }}
        >
          <ConfigFormComponent
            ref={configFormRef}
            botConfig={botConfig}
            defaultValue={defaultValue || {}}
          />
        </div>
        <div className={styles.footer}>
          <div className={styles.onOk} onClick={() => submit()}>
            确定
          </div>
          <div onClick={() => oncancel()}>取消</div>
        </div>
      </div>
    </Modal>
  );
};
export default memo(RobotConfigModal);

const { confirm } = Modal;

interface PromptModalProps {
  promptText: string;
  type?: 'fill' | 'reset' | 'prompt';
  onOk?: () => void;
  onCancel?: () => void;
  onOkText?: string;
  onCancelText?: string;
  showFooterCancel?: boolean;
}

export const PromptModal = ({
  promptText,
  showFooterCancel = false,
  onOkText = '确定',
  onCancelText = '取消',
  onOk,
  onCancel,
}: PromptModalProps) => {
  const modal = confirm({
    title: null,
    icon: null,
    content: (
      <div className={styles.promptModalWarp}>
        <div className={styles.header}>
          <div>提示</div>
          <img
            src={closeIcon}
            onClick={() => {
              onCancel?.();
              modal.destroy();
            }}
          />
        </div>
        <div className={styles.promptText}>{promptText}</div>
        <div className={styles.footer}>
          <div
            className={styles.onOk}
            onClick={() => {
              onOk?.();
              modal.destroy();
            }}
          >
            {onOkText}
          </div>
          {showFooterCancel ? (
            <div
              onClick={() => {
                onCancel?.();
                modal.destroy();
              }}
            >
              {onCancelText}
            </div>
          ) : (
            ''
          )}
        </div>
      </div>
    ),
    centered: true,
    className: styles.promptModal,
    width: 420,
  });
  return modal;
};
