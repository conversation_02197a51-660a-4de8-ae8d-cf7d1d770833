.listForewordWrapper {
  padding: 48px 20px 4px;
  border-bottom: 1px solid var(--primary-border-color);

  .singleWrapper {
    display: flex;
    flex-direction: column;
    .avatarsWrapper {
      flex-wrap: wrap;
      padding-bottom: 16px;
      display: flex;
      align-items: center;
      color: var(--link-color-content-pry);

      .showName {
        font-size: 18px;
        font-weight: bold;
        margin-right: 8px;
        cursor: pointer;
      }
    }
  }

  .groupWrapper {
    display: flex;
    flex-direction: column;
    .showName {
      font-size: 28px;
      font-weight: bold;
      color: var(--link-color-content-pry);
      word-break: break-all;
    }

    .highLightDesc {
      color: var(--link-color-content-hgl-1);
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .descriptionWrapper {
    padding-bottom: 16px;
    font-size: 17px;
    line-height: 27px;
    display: inline-block;

    .highLightWrapper {
      // background: rgba(29, 155, 209, 10%);
      color: var(--link-color-content-hgl-1);
      border-radius: 3px;
      padding: 0 2px 1px;
      cursor: pointer;

      // &:hover {
      //   background: rgba(var(--link-highlight_accent, 29, 155, 209), 0.2);
      //   color: rgba(var(--link-highlight_hover, 11, 76, 140), 1);
      //   text-decoration: none;
      // }
    }
  }

  .profileBtn {
    border-radius: 8px;
    min-width: 80px;
    height: 36px;
    padding: 0 12px 1px;
    font-size: 15px;
    background-color: var(--link-color-base-pry);
    border: 1px solid var(--link-color-otl-sec);
    color: var(--link-color-content-pry);
    font-weight: bold;
    transition: all 80ms linear;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    width: fit-content;
    // margin-bottom: 16px;
    display: inline-flex;

    .editIcon {
      width: 14px;
      margin-right: 5px;
    }

    &:hover {
      background-color: var(--link-color-base-sec);
      box-shadow: 0 1px 3px #00000014;
    }
  }
}

.listForewordGroupWrapper {
  padding-top: 88px;
}
.robotConfig {
  width: 540px;
  border-radius: 8px;
  border: 1px solid rgba(0, 116, 226, 10%);
  .headerBox {
    display: flex;
    align-items: center;
    height: 60px;
    background: rgba(0, 116, 226, 10%);
    border-radius: 8px 8px 0 0;
    padding: 0 20px;
    font-weight: 600;
    > img {
      width: 20px;
      height: 20px;
      margin-right: 10px;
    }
  }
  .content {
    padding: 20px 0;
  }
  .submitBtn {
    margin: 0 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 88px;
    height: 36px;
    font-size: 14px;
    font-weight: 500;
    color: var(--primary-text-color-pressed);
    line-height: 20px;
    background: var(--primary-text-color-9);
    border-radius: 8px;
    cursor: pointer;
  }
}

.formBox {
  padding: 0 20px;
  max-height: 424px;
  overflow: auto;
  .formLabel {
    font-size: 15px;
    font-weight: 600;
    color: var(--primary-text-color-1);
    line-height: 22px;
  }
  .itemRequired::after {
    display: inline-block;
    margin-left: 2px;
    color: var(--primary-text-color-11);
    font-size: 14px;
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: "*";
  }
  :global {
    .linkflow-form-item-label {
      padding: 0 0 4px;
    }
    .linkflow-input:focus,
    .linkflow-input-focused {
      border-color: var(--primary-text-color-9);
    }
    .linkflow-form-item-required::before {
      display: none !important;
    }
    .linkflow-input-status-error:not(
        .linkflow-input-disabled,
        .linkflow-input-borderless
      ).linkflow-input {
      border-color: var(--primary-text-color-11);
    }
    .linkflow-select-status-error.linkflow-select:not(
        .linkflow-select-disabled,
        .linkflow-select-customize-input,
        .linkflow-pagination-size-changer
      )
      .linkflow-select-selector {
      border-color: var(--primary-text-color-11) !important;
    }
    .linkflow-form-item-explain-error {
      color: var(--primary-text-color-11);
    }
    .linkflow-form-item-control-input-content {
      > input {
        border-radius: 6px;
      }
      > textarea {
        border-radius: 6px;
      }
    }
    .linkflow-select-selector {
      border-radius: 6px;
    }
  }
}

.openQuestionWarp {
  padding: 4px 6px;
  .title {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-text-color-1);
    line-height: 28px;
    padding-bottom: 8px;
  }
  .desc {
    font-size: 15px;
    font-weight: 400;
    color: var(--primary-text-color-1);
    line-height: 22px;
    padding-bottom: 12px;
  }
  .optionsBox {
    .item {
      padding-bottom: 12px;
      .box {
        display: flex;
        width: 420px;
        font-size: 15px;
        font-weight: 400;
        color: var(--primary-text-color-1);
        line-height: 22px;
        padding: 11px 16px;
        background: var(--primary-background-color-6);
        border-radius: 8px;
        border: 1px solid var(--primary-border-color);
        cursor: pointer;
        word-break: break-all;
        .icon {
          display: flex;
          align-items: center;
          width: 18px;
          height: 18px;
          font-family: EmojiMart;
          margin-right: 10px;
          margin-top: 2px;
        }
        &:hover {
          background: var(--msg-qute-backgroud-color);
        }
      }
    }
  }
}

.robotConfigModal {
  .robotConfigModaWarp {
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 18px;
      font-weight: 600;
      line-height: 28px;
      color: var(--primary-text-color-1);
      margin-bottom: 20px;
      > img {
        width: 28px;
        height: 28px;
        cursor: pointer;
      }
    }
    .footer {
      display: flex;
      flex-direction: row-reverse;
      padding-top: 20px;

      > div {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 88px;
        height: 36px;
        font-size: 14px;
        font-weight: 600;
        color: var(--primary-text-color-1);
        line-height: 20px;
        background: var(--primary-border-color);
        border-radius: 8px;
        cursor: pointer;
      }
      .onOk {
        color: var(--primary-text-color-pressed);
        background: var(--primary-text-color-9);
        margin-left: 16px;
      }
    }
  }

  :global {
    .linkflow-modal-content {
      border-radius: 8px;
    }
    .linkflow-modal-body {
      padding: 24px;
    }
  }
}

.promptModal {
  .promptModalWarp {
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 18px;
      font-weight: 600;
      line-height: 28px;
      color: var(--primary-text-color-1);
      margin-bottom: 20px;
      > img {
        width: 28px;
        height: 28px;
        cursor: pointer;
      }
    }
    .promptText {
      font-size: 15px;
      font-weight: 400;
      color: var(--primary-text-color-1);
      line-height: 22px;
    }
    .footer {
      display: flex;
      flex-direction: row-reverse;
      padding-top: 20px;

      > div {
        display: flex;
        align-items: center;
        justify-content: center;
        // width: 88px;
        height: 36px;
        padding: 0 30px;
        font-size: 14px;
        font-weight: 600;
        color: var(--primary-text-color-1);
        line-height: 20px;
        background: var(--primary-border-color);
        border-radius: 8px;
        cursor: pointer;
      }
      .onOk {
        color: var(--primary-text-color-pressed);
        background: var(--primary-text-color-9);
        margin-left: 16px;
      }
    }
  }
  :global {
    .linkflow-modal-content {
      border-radius: 8px;
    }
    .linkflow-modal-body {
      padding: 24px;
    }
    .linkflow-modal-confirm-content {
      margin-top: 0;
    }
    .linkflow-modal-confirm-btns {
      display: none;
    }
  }
}
