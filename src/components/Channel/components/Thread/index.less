.threadContainer {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: rgba(var(--sk_primary_background, 255, 255, 255), 1);
  box-shadow: outset -10px 10px 15px rgba(226, 226, 226, 50%);
  .threadHeader {
    height: 64px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    background-color: var(--primary-background-color-6);
    .threadTitle {
      width: 80%;
      color: var(--primary-text-color-1);
      font-family: PingFangSC-Regular;
      > span {
        color: var(--primary-text-color-1);
      }
    }
    .closeIcon {
      // position: absolute;
      // right: 24px;
      // top: 20px;
      width: 16px;
      cursor: pointer;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
}
