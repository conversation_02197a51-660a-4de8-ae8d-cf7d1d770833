import { FC, memo } from 'react';
import { useConversationStore } from '@/store';
import closeIcon from '@/assets/channel/close.png';
import withScrollBar from '@/components/withScrollBar';
import { Typography } from '@ht/sprite-ui';
import { shallow } from 'zustand/shallow';
import MessageList from '../MessageList';
import ThreadFirstMessage from '../ThreadFirstMessage';
import styles from './index.less';

interface ThreadProps {
  onClose: () => void;
}

const Thread: FC<ThreadProps> = ({ onClose }) => {
  const { currentThreadConversation, currentThreadFirstMessage } =
    useConversationStore(
      (state) => ({
        currentThreadConversation: state.currentThreadConversation,
        currentThreadFirstMessage: state.currentThreadFirstMessage,
      }),
      shallow
    );

  return (
    <div className={styles.threadContainer}>
      <div className={styles.threadHeader}>
        <div className={styles.threadTitle}>
          <Typography.Text
            ellipsis={true}
            strong={true}
            title={currentThreadConversation?.showName || '创建子群'}
          >
            {currentThreadConversation?.showName || '创建子群'}
          </Typography.Text>
        </div>
        <div className={styles.closeIcon}>
          <img src={closeIcon} onClick={onClose} />
        </div>
      </div>
      <ThreadFirstMessage
        currentThreadFirstMessage={currentThreadFirstMessage}
      />
      <MessageList
        key={currentThreadConversation?.conversationID}
        conversation={currentThreadConversation}
        inRightThread={true}
      />
    </div>
  );
};
export default memo(
  withScrollBar(Thread, {
    domId: 'drag-scroll-bar-right',
    direction: 'right',
    defaultWidth: '504px',
  })
);
