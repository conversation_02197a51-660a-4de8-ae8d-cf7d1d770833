import { useConversationStore } from '@/store';
import { memo } from 'react';
import Linkwps from '@/components/Linkwps';
import styles from './index.less';
import MessageList from '../MessageList';

const ChannelArea = () => {
  const channelHeaderCurTab = useConversationStore(
    (state) => state.channelHeaderCurTab
  );

  const currentConversation = useConversationStore(
    (state) => state.currentConversation
  );

  const isMultiSession = useConversationStore((state) => state.isMultiSession);
  const currentMultiSession = useConversationStore(
    (state) => state.currentMultiSession
  );

  const useConversation = isMultiSession
    ? currentMultiSession
    : currentConversation;
  const { ex } = useConversation || {};

  return (
    <div className={styles.content}>
      {channelHeaderCurTab === 'message' ? (
        <MessageList
          conversation={useConversation}
          key={`${useConversation?.conversationID || ''}${ex || ''}`}
        />
      ) : (
        <></>
      )}

      {channelHeaderCurTab === 'docs' ? (
        <Linkwps conversationID={useConversation?.conversationID} />
      ) : (
        <></>
      )}
    </div>
  );
};

export default memo(ChannelArea);
