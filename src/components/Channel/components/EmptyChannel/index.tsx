import { throttle } from 'lodash';
import { memo, useCallback, useEffect, useState } from 'react';
import { useConversationStore } from '@/store';
import { isSingleChat } from '@/utils/utils';
import ChannelEmpty from '../ChannelEmpty';

const EmptyChannel = () => {
  const conversationIniting = useConversationStore(
    (state) => state.conversationIniting
  );
  const conversationListLength = useConversationStore(
    (state) => state.conversationList.length
  );

  const [shouldShowChannelEmpty, setShouldShowChannelEmpty] =
    useState<boolean>(false);

  const changeShouldShowChannelEmpty = useCallback((val) => {
    throttle(
      () => {
        setShouldShowChannelEmpty(val);
      },
      500,
      { leading: false, trailing: true }
    )();
  }, []);

  useEffect(() => {
    if (!conversationIniting && conversationListLength === 0) {
      changeShouldShowChannelEmpty(true);
    } else {
      changeShouldShowChannelEmpty(false);
    }
  }, [
    changeShouldShowChannelEmpty,
    conversationIniting,
    conversationListLength,
  ]);

  return (
    <div style={{ width: '100%', height: '100%' }}>
      {shouldShowChannelEmpty && !isSingleChat() && <ChannelEmpty />}
    </div>
  );
};

export default memo(EmptyChannel);
