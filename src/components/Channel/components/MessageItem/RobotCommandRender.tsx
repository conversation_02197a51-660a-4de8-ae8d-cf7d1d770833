import React, { FC, memo, useMemo } from 'react';
import { Popover } from '@ht/sprite-ui';
import { isEmpty } from 'lodash';
import slashIcon from '@/assets/channel/messageRender/slash.svg';
import styles from './index.less';

interface RobotCommandRenderProps {
  content: string;
}

const RobotCommandRender: FC<RobotCommandRenderProps> = ({ content }) => {
  const robotCommand = useMemo(() => {
    const str = content.trim().replace('/', '');
    let commandName = str;
    let data = {};
    const jsonRegex = /(\{.*\}|\[.*\])/s;
    const match = jsonRegex.exec(str);
    if (match) {
      try {
        data = JSON.parse(match[0]);
        commandName = str.replace(match[0], '').trim();
      } catch (e) {
        console.error('提取的 JSON 无效:', e);
      }
    }
    return {
      commandName,
      data,
    };
  }, [content]);

  return (
    <div className={styles.robotCommandRenderWarp}>
      <div>使用了命令</div>

      {isEmpty(robotCommand.data) ? (
        <div className={styles.robotCommandBox}>
          <div className={styles.addonBefore}>
            <img src={slashIcon} />
          </div>
          <div className={styles.command}>
            {robotCommand.commandName || content}
          </div>
        </div>
      ) : (
        <Popover
          content={
            <div className={styles.commandInfo}>
              <div className={styles.title}>/{robotCommand.commandName}</div>
              <div>
                {Object.keys(robotCommand.data).map((key) => {
                  return (
                    <div key={key} className={styles.item}>
                      <div className={styles.lable}>
                        <div className={styles.dot}></div>
                        <div>{key}：</div>
                      </div>
                      <div>{robotCommand?.data[key]}</div>
                    </div>
                  );
                })}
              </div>
            </div>
          }
          placement="bottomLeft"
          overlayClassName={styles.robotCommandPopoverWarp}
          title={null}
          mouseEnterDelay={0.3}
        >
          <div className={styles.robotCommandBox}>
            <div className={styles.addonBefore}>
              <img src={slashIcon} />
            </div>
            <div className={styles.command}>
              {robotCommand.commandName || content}
            </div>
          </div>
        </Popover>
      )}
    </div>
  );
};

export default memo(RobotCommandRender);
