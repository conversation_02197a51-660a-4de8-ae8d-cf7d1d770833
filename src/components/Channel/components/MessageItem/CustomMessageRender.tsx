import { FC, memo } from 'react';
import { RenderMd } from '@/components/MdEditor';
import LcRender from '@/components/LcRender';
import { isEmpty } from 'lodash';
import { useConversationStore, useUserStore } from '@/store';
import { IMSDK } from '@/layouts/BasicLayout';
import { IMessageItemProps } from '.';
import UserInfoRender from './UserInfoRender';
import ClouddocumentRender from './ClouddocumentRender';
import StreamMessageRender from './StreamMessage';

const CustomMessageRender: FC<IMessageItemProps> = ({ ...msgProps }) => {
  const { message, isForwardMessage } = msgProps;
  const { nickname } = useUserStore.getState().selfInfo;
  const currentConversation = useConversationStore(
    (state) => state.currentConversation
  );

  let content: any = {};
  try {
    content = JSON.parse(message?.customElem?.data || '{}');
  } catch (e) {
    content = message?.customElem?.data;

    return null;
  }

  const renderItem = () => {
    if (content?.type === 'clouddocument') {
      return <ClouddocumentRender {...msgProps} />;
    } else if (content?.type === 'stream') {
      return <StreamMessageRender {...msgProps} content={content} />;
    } else {
      const renderContent = content.content || {};
      return (
        <LcRender
          id={message.clientMsgID}
          {...renderContent}
          callback={async ({
            eventID,
            actionId,
            actionName,
            props,
            type,
          }: {
            eventID: string;
            actionId: string;
            actionName: string;
            props: any;
            type: number;
          }) => {
            // 这里可以根据 eventID 来判断是哪个函数，props为传递的参数

            const params = {
              // 应用ID
              appID: '',
              // 机器人ID
              botID: currentConversation?.userID
                ? `${currentConversation?.userID}`
                : '',
              // 交互类型（指令、可交互消息、modal submit）
              type: 2,
              // 会话类型 1 单聊 2 群聊
              sessionType: currentConversation?.conversationType,
              // 群聊ID
              groupID: currentConversation?.groupID,
              // 交互ID，根据不同的交互类型，可以是指令ID、交互组件ID、表单提交按钮ID等
              actionID: actionId,
              actionName: actionName ? actionName : '',
              sessionID: currentConversation?.conversationID,
              // 交互数据
              data: props,
              // 消息信息
              msgInfo: message,
              userName: nickname,
            };

            await IMSDK.invokeBotInteraction(params);
          }}
          isDev={!location.hostname.startsWith('eip.htsc.com.cn')}
        />
      );
    }
  };

  const renderMessageContent = () => {
    return isEmpty(content) ? (
      <div>暂无内容</div>
    ) : message?.customElem?.extension === 'md' ? (
      <RenderMd id={message.clientMsgID} value={content as string} />
    ) : (
      renderItem()
    );
  };

  return !isForwardMessage ? (
    <UserInfoRender {...msgProps}>{renderMessageContent()}</UserInfoRender>
  ) : (
    <>{renderMessageContent()}</>
  );
};

export default memo(CustomMessageRender);
