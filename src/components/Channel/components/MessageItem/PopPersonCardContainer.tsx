import React, { useState } from 'react';
import { Popover, PopoverProps } from '@ht/sprite-ui';
import PersonalCard from '@/components/PersonalCard';
import styles from './index.less';

interface Props extends PopoverProps {
  sendID: string;
  isDisabled?: boolean;
}
export const PopPersonCardContainer: React.FC<Props> = (props) => {
  const { sendID, children, isDisabled = false } = props;
  const [openPopover, setOpenPopover] = useState(false);
  return (
    <Popover
      {...props}
      overlayClassName={styles.popPersonCardContainer}
      overlayInnerStyle={{
        padding: 0,
        borderRadius: '10px',
      }}
      destroyTooltipOnHide={true}
      placement="rightTop"
      content={
        <div
          onContextMenu={(e) => {
            e.stopPropagation();
          }}
        >
          <PersonalCard userID={sendID} />
        </div>
      }
      open={isDisabled ? false : openPopover}
      onOpenChange={(val) => setOpenPopover(val)}
    >
      {children}
    </Popover>
  );
};
