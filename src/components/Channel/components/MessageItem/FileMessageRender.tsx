/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-lines */
import React, { FC, useEffect, useRef, useState } from 'react';
import Mark from 'mark.js';
import { MessageStatus } from '@ht/openim-wasm-client-sdk';
import { isEmpty } from 'lodash';
import { message as Message, Popover } from '@ht/sprite-ui';
import downloadIcon from '@/assets/channel/messageRender/download.png';
import replyIcon from '@/assets/channel/messageRender/reply.png';
import moreIcon from '@/assets/channel/messageRender/more.png';
import hideIcon from '@/assets/images/messageItem/hideIcon.png';
import { deleteOneMessage } from '@/hooks/useHistoryMessageList';
import { feedbackToast } from '@/utils/common';
import { IMSDK } from '@/layouts/BasicLayout';
import classNames from 'classnames';
import FilePreviewModal from '@/components/FilePreviewModal/FileModal';
import DeleteFile from '@/components/FilePreviewModal/DeleteFile';
import ForwardModal from '@/components/ForwardModal';
import {
  useUserStore,
  useConversationStore,
  useSearchInfoStore,
  useMultiSelectStore,
} from '@/store';
import { IMessageItemProps } from '.';
import { getFileIcon } from '../MessageInput/FileRender';
import UserInfoRender from './UserInfoRender';

import styles from './index.less';

export const wpsFileType = [
  'doc',
  'docx',
  'xls',
  'xlsx',
  'csv',
  'ppt',
  'pptx',
  'pdf',
  // 'zip',
  // 'rar',
  // 'html',
  'xmind',
  'md',
];

const FileMessageRender: FC<IMessageItemProps> = ({ ...props }) => {
  const {
    message,
    conversationID = '',
    showName = '',
    isThread,
    isSender,
    isSearch = false,
    searchValue = '',
    inMultiMessageModal = false,
  } = props;
  const changeRightArea = useConversationStore(
    (state) => state.changeRightArea
  );
  const getCurrentMessageUpInfo = useConversationStore(
    (state) => state.getCurrentMessageUpInfo
  );

  const changeSearchRightArea = useSearchInfoStore(
    (state) => state.changeRightArea
  );

  const deleteMultiSelect = useMultiSelectStore(
    (state) => state.deleteMultiSelect
  );

  const { fileElem } = message;
  const { fileName = '', sourceUrl = '' } = fileElem || {};
  const moreMenuRef = useRef<any>();
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [open, setOpen] = useState<boolean>(false);
  const [deleteOpen, setDeleteOpen] = useState<boolean>(false);
  const [forwardModal, setForwardModal] = useState<boolean>(false);
  const selfID = useUserStore.getState().selfInfo.userID;
  const showDelete = (selfID === message.sendID || false) && !isSearch;
  const idx = fileName.lastIndexOf('.');
  const fileType = fileName.slice(idx + 1).toLowerCase();
  const isWps = wpsFileType.some((item: string) => item === fileType);

  const highLightContentRef = useRef(null);
  const markInstanceRef = useRef<any>(null);

  const downloadFile = (url: string, name: string) => {
    const link = document.createElement('a');
    link.href = url;
    link.download = name;
    link.click();
  };

  const tryRemove = async () => {
    try {
      await IMSDK.deleteMessage({
        clientMsgID: message.clientMsgID,
        conversationID,
      });
      deleteOneMessage(message.clientMsgID);
      deleteMultiSelect(message);
      if (message.groupID) {
        getCurrentMessageUpInfo(message.groupID);
      }
      setDeleteOpen(false);
    } catch (error) {
      feedbackToast({ error, msg: '删除失败' });
    }
  };

  const getPreviewUrl = async () => {
    try {
      const { data } = await IMSDK.getPreviewUrl({
        fileName,
        relativeDownloadUrl: sourceUrl,
        clientMsgID: message.clientMsgID,
      });
      return data?.pcPreviewUrl || '';
    } catch (error) {
      console.error('getPreviewUrl', error);
      return '';
    }
  };

  const openView = async () => {
    try {
      if (!previewUrl) {
        const url = await getPreviewUrl();
        if (url) {
          setPreviewUrl(url);
          window.open(url, '_blank');
        } else {
          Message.info('该文件暂不支持预览！');
        }
      } else {
        window.open(previewUrl, '_blank');
      }
    } catch (error) {
      console.error('openView', error);
    }
  };

  const openModal = async () => {
    if (!isWps) {
      Message.info('该文件暂不支持预览！');
      return;
    }
    try {
      if (!previewUrl) {
        const url = await getPreviewUrl();
        if (url) {
          setPreviewUrl(url);
          setOpen(true);
        } else {
          Message.info('该文件暂不支持预览！');
        }
      } else {
        setOpen(true);
      }
    } catch (error) {
      console.error('openModal', error);
    }
  };

  useEffect(() => {
    if (highLightContentRef.current) {
      markInstanceRef.current = new Mark(highLightContentRef.current);
    }
  }, []);

  useEffect(() => {
    if (markInstanceRef.current) {
      markInstanceRef.current.unmark();
      if (searchValue.trim() && isSearch) {
        markInstanceRef.current.mark(searchValue.trim(), {
          className: styles.search_Highlight,
        });
      }
    }
  }, [isSearch, searchValue]);

  return (
    <div className={styles.fileMessageRenderWarp}>
      <UserInfoRender {...props}>
        <div>
          <div className={styles.fileMessageRenderContent} onClick={openModal}>
            <div className={styles.icon}>
              <img src={getFileIcon(fileName, 'icon')} />
            </div>
            <div className={styles.fileInfo}>
              <div
                className={styles.fileName}
                title={fileName}
                ref={highLightContentRef}
              >
                {fileName}
              </div>
              <div className={styles.info}>{getFileIcon(fileName, 'text')}</div>
            </div>
            {!inMultiMessageModal && (
              <div
                className={styles.operationMenu}
                onClick={(e) => {
                  e.stopPropagation();
                }}
              >
                <div
                  className={styles.operationItem}
                  onClick={() => downloadFile(sourceUrl, fileName)}
                >
                  <img src={downloadIcon} />
                </div>
                {message.status !== MessageStatus.Failed && (
                  <div
                    className={styles.operationItem}
                    onClick={() => setForwardModal(true)}
                  >
                    <img src={replyIcon} />
                  </div>
                )}
                <Popover
                  placement="rightTop"
                  title={null}
                  content={
                    <div className={styles.moreMenuWarp}>
                      <div
                        className={styles.menuBox}
                        style={showDelete ? {} : { borderBottom: 'none' }}
                      >
                        {isWps ? (
                          <div className={styles.menuItem} onClick={openView}>
                            在新选项卡中打开
                          </div>
                        ) : (
                          ''
                        )}
                        <div
                          className={styles.menuItem}
                          onClick={() => {
                            if (location.pathname === '/linkflow/search') {
                              changeSearchRightArea('OPEN_FILE_AREA', message);
                            } else {
                              changeRightArea('OPEN_FILE_AREA', message);
                            }
                          }}
                        >
                          查看文件详情
                        </div>
                      </div>
                      <div
                        className={styles.menuBox}
                        style={showDelete ? {} : { display: 'none' }}
                      >
                        <div
                          className={classNames(
                            styles.menuItem,
                            styles.deleteBtn
                          )}
                          onClick={() => setDeleteOpen(true)}
                        >
                          删除文件
                        </div>
                      </div>
                    </div>
                  }
                  overlayClassName={styles.operationMoreMenuWarp}
                  getPopupContainer={() => moreMenuRef.current || document.body}
                >
                  <div className={styles.operationItem} ref={moreMenuRef}>
                    <img src={moreIcon} />
                  </div>
                </Popover>
              </div>
            )}
          </div>
        </div>
      </UserInfoRender>
      {open && (
        <FilePreviewModal
          open={open}
          onClose={() => setOpen(false)}
          url={previewUrl}
          message={message}
          download={() => downloadFile(sourceUrl, fileName)}
          showName={showName}
          openView={openView}
        />
      )}
      {deleteOpen && (
        <DeleteFile
          open={deleteOpen}
          onClose={() => setDeleteOpen(false)}
          deleteFile={tryRemove}
          message={message}
        />
      )}
      {forwardModal && (
        <ForwardModal
          open={forwardModal}
          onClose={() => setForwardModal(false)}
          message={message}
          isThread={isThread}
          isSender={isSender}
          conversationID={conversationID}
          showName={showName}
        />
      )}
    </div>
  );
};

export default FileMessageRender;
