import { FC, useCallback, useState } from 'react';
import { throttle } from 'lodash';
import { message as Message } from '@ht/sprite-ui';
import ImgPreviewModal from '@/components/ImgPreviewModal';
import { openImgPreviewWin } from '@/utils/message';
import { IMessageItemProps } from '.';
import UserInfoRender from './UserInfoRender';
import styles from './index.less';

const PictureMessageRender: FC<IMessageItemProps> = ({ ...props }) => {
  const { message, showName, inMultiMessageModal } = props;
  const { pictureElem } = message;
  const { sourcePath = '' } = pictureElem;
  const { url = '', uuid } = pictureElem?.sourcePicture || {};
  let fileName = uuid?.split(/[\\/]/).pop() || 'image.png';
  if (sourcePath) {
    fileName = sourcePath.split(/[\\/]/).pop() || 'image.png';
  }
  const [open, setOpen] = useState<boolean>(false);

  // const [Filepath, setFilepath] = useState<string | undefined>(undefined);
  // const browserWindow = window?.htElectronSDK?.BrowserWindow;

  const download = async () => {
    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('网络响应失败');
      }
      const blob = await response.blob();
      const blobUrl = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      URL.revokeObjectURL(blobUrl);
    } catch (error) {
      Message.error('下载失败');
    }
  };

  const openView = async () => {
    window.open(url, '_blank');
    // try {
    //   const response = await fetch(url);
    //   if (!response.ok) {
    //     throw new Error('网络响应失败');
    //   }
    //   const blob = await response.blob();
    //   const blobUrl = URL.createObjectURL(blob);
    //   window.open(blobUrl, '_blank');
    // } catch (error) {
    //   console.error('openView', error);
    // }
  };
  const calcBestWidthHeight = (
    width: number | undefined,
    height: number | undefined
  ) => {
    const size = {
      width: 420,
      height: 300,
    };

    if (!width || !height) {
      return size;
    }
    try {
      const aspectRatio = width / height;
      const aspectWidth = 300 * aspectRatio;
      // 气泡最小宽度458，减去padding 20
      if (width <= 438 && height <= 300) {
        return { width, height };
      } else if (aspectRatio >= 2 || aspectWidth > 438) {
        return {
          width: 438,
          height: 438 / aspectRatio,
        };
      } else {
        return {
          width: aspectWidth,
          height: 300,
        };
      }
    } catch (e) {}

    return size;
  };

  // const handleImageCache = async () => {
  //   const filepath = await browserWindow?.imageLocalUrl(url);
  //   if (filepath && !browserWindow?.existsFileLocal(filepath)) {
  //     const response = await fetch(url);
  //     if (!response.ok) {
  //       throw new Error('网络响应失败');
  //     }
  //     const blob = await response.blob();

  //     const arrayBuffer = await blob.arrayBuffer(); // 转为 ArrayBuffer
  //     const buffer = Buffer.from(arrayBuffer); // 转换为 Buffer

  //     browserWindow?.writeImageLocal(filepath, buffer);
  //   }
  //   setFilepath(filepath);
  // };

  // useEffect(() => {
  //   if (window.electronAPI) {
  //     handleImageCache();
  //   }
  // }, []);

  const previewImg = useCallback(async () => {
    openImgPreviewWin(
      {
        ...pictureElem?.sourcePicture,
      },
      () => {
        setOpen(true);
      }
    );
  }, [pictureElem?.sourcePicture]);

  const previewImgThrottle = throttle(previewImg, 300, {
    leading: false,
    trailing: true,
  });

  return (
    <div className={styles.pictureWarp}>
      <UserInfoRender {...props}>
        <div>
          <div
            style={{
              ...calcBestWidthHeight(
                pictureElem?.bigPicture?.width,
                pictureElem?.bigPicture?.height
              ),
            }}
          >
            <img
              className={styles.showPicture}
              src={pictureElem?.sourcePicture.url}
              onClick={async () => {
                previewImgThrottle();
              }}
            />
          </div>
        </div>
      </UserInfoRender>
      {open && (
        <ImgPreviewModal
          open={open}
          onClose={() => setOpen(false)}
          download={download}
          imgMsg={message}
          fileName={fileName}
          showName={showName}
          openView={openView}
          inMultiMessageModal={inMultiMessageModal}
          // imgInfo={{
          //   ...pictureElem?.sourcePicture,
          //   fileName,
          // }}
        />
      )}
    </div>
  );
};

export default PictureMessageRender;
