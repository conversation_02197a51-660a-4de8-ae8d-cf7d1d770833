import React from 'react';
import styles from './index.less';

interface LoadingProps {
  size?: 'sm' | 'default' | '2x' | '3x';
  dark?: boolean;
  className?: string;
}

const Loading: React.FC<LoadingProps> = ({
  size = 'default',
  dark = false,
  className = '',
}) => {
  const sizeClass = size !== 'default' ? styles[`la-${size}`] : '';
  const darkClass = dark ? styles['la-dark'] : '';
  const classes =
    `${styles.loading} ${sizeClass} ${darkClass} ${className}`.trim();

  return (
    <div className={classes}>
      <div></div>
      <div></div>
      <div></div>
    </div>
  );
};

export default Loading;
