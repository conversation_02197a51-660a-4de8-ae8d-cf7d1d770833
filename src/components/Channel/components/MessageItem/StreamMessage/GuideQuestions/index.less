.guideQuestions {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-left: 72px;
  width: 100%;
  font-family: <PERSON> YaHei, EmojiMart;
  .itemCard {
    width: fit-content;
    padding: 8px 10px;
    padding-right: 32px;
    border: 1px solid var(--primary-background-color-5);
    border-radius: 8px;
    color: var(--primary-text-color-1);
    font-size: 14px;
    background-color: #fff;
    cursor: pointer;
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.5s ease forwards;
    animation-fill-mode: both;
    position: relative;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: var(--primary-background-color-15);
    }

    .arrowIcon {
      position: absolute;
      right: 12px;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
