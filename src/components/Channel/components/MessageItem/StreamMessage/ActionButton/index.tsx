import React, { useState, ReactNode } from 'react';
import { Space, Tooltip } from '@ht/sprite-ui';
import { CopyOutlined, ReloadOutlined } from '@ant-design/icons';
import { MessageItem as MessageItemType } from '@ht/openim-wasm-client-sdk';
import { handleCopy } from '@/utils/message';
import styles from './index.less';

interface ActionButtonProps {
  message: MessageItemType;
  onRefresh?: () => void;
  onFeedback?: (type: 'like' | 'dislike') => void;
  buttons: Array<{
    key: string;
    title: string;
    icon: ReactNode;
    onClick: () => void;
    loading?: boolean;
    active?: boolean;
  }>;
}

const ActionButton: React.FC<ActionButtonProps> = ({
  message,
  onRefresh,
  onFeedback,
  buttons: customButtons,
}) => {
  const buttons = customButtons;

  return (
    <Space size={2}>
      {buttons.map(({ key, title, icon, onClick, loading }) => (
        <Tooltip title={title} key={key}>
          <div onClick={onClick} className={styles.actionButton}>
            {icon}
          </div>
        </Tooltip>
      ))}
    </Space>
  );
};

export default ActionButton;
