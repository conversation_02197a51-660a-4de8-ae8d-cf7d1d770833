import React, { FC, useState } from 'react';
import thinkIcon from '@/assets/stream/think.png';
import arrowIcon from '@/assets/stream/arrow.png';
import styles from './index.less';

interface ThinkContentProps {
  content: {
    think?: {
      answer: string;
    };
    plugins?: {
      name: string;
      status: 0 | 1;
    }[];
  };
}

const ThinkContent: FC<ThinkContentProps> = ({ content }) => {
  console.log('渲染ThinkContent', content);
  const thinkText = content?.think?.answer;
  const plugins = content?.plugins;
  const [isExpanded, setIsExpanded] = useState(true);

  if (!thinkText && !plugins?.length) {
    return;
  }

  return (
    <div className={styles.thinkContainer}>
      <div
        className={styles.thinkTitle}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <img src={thinkIcon}></img>
        思考和规划过程
        <span
          className={
            isExpanded
              ? styles.thinkArrow
              : `${styles.thinkArrow} ${styles.collapsed}`
          }
        >
          <img src={arrowIcon} alt="arrow" />
        </span>
      </div>
      <div
        className={`${styles.thinkContentContainer} ${
          isExpanded ? styles.expanded : styles.collapsed
        }`}
      >
        {thinkText}
      </div>
    </div>
  );
};

export default ThinkContent;
