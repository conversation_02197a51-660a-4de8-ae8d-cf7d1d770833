import React, { <PERSON> } from 'react';
import { Card, List, Typography, Space, Tag, Divider } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';

interface QAItem {
  index: number;
  score: number;
  question: string;
  answer: string;
}

interface QAContentProps {
  content: {
    results: QAItem[];
    query?: string;
    latency?: number;
  };
}

const QAContent: FC<QAContentProps> = ({ content }) => {
  const { results = [], query } = content;

  if (!results || results.length === 0) {
    return <Typography.Text type="secondary">无相关问答内容</Typography.Text>;
  }

  return (
    <Card
      title={
        <Space>
          <QuestionCircleOutlined /> 问答库检索结果
        </Space>
      }
      size="small"
      style={{ width: '100%' }}
      extra={query && <Tag color="blue">{query}</Tag>}
    >
      <List
        itemLayout="vertical"
        dataSource={results}
        renderItem={(item) => (
          <List.Item key={item.index}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Space>
                <Typography.Text strong={true}>
                  Q: {item.question}
                </Typography.Text>
                <Tag color="green">
                  相关度: {(item.score * 100).toFixed(0)}%
                </Tag>
              </Space>
              <Typography.Text>A: {item.answer}</Typography.Text>
            </Space>
          </List.Item>
        )}
        split={true}
      />
    </Card>
  );
};

export default QAContent;
