import React, { FC } from 'react';
import networkIcon from '@/assets/channel/messageRender/network.png';
import { useConversationStore } from '@/store';
import KnowledgeButton from '../KnowledgeContent/KnowledgeButton';

interface NetworkItem {
  index: number;
  title: string;
  url: string;
  content: string;
}

interface NetworkContentProps {
  content: {
    text?: string;
    url?: string;
    results?: NetworkItem[];
    query?: string;
    latency?: number;
  };
}

const NetworkContent: FC<NetworkContentProps> = ({ content }) => {
  const { results = [] } = content;
  const changeRightArea = useConversationStore(
    (state) => state.changeRightArea
  );

  if (!results || results.length === 0) {
    return null;
  }

  return (
    <div>
      <KnowledgeButton
        icon={<img src={networkIcon} alt="" />}
        text={`${results.length}个网络结果`}
        onClick={() => {
          changeRightArea('OPEN_ROBOT_ONLINE_SEARCE', {
            data: results,
          });
        }}
      />
    </div>
  );
};

export default NetworkContent;
