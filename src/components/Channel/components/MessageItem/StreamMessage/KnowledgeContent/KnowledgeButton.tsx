import React, { FC, ReactNode } from 'react';
import styles from './index.less';

interface KnowledgeButtonProps {
  icon?: ReactNode;
  text: string;
  onClick?: () => void;
  style?: React.CSSProperties;
}

const KnowledgeButton: FC<KnowledgeButtonProps> = ({
  icon,
  text,
  onClick,
  style,
}) => {
  return (
    <div className={styles.container} onClick={onClick} style={style}>
      {icon}
      <span className={styles.text}>{text}</span>
    </div>
  );
};

export default KnowledgeButton;
