import React, { FC } from 'react';
import knowledgeIcon from '@/assets/channel/messageRender/knowledge.png';
import { useConversationStore } from '@/store';
import KnowledgeButton from './KnowledgeButton';

interface KnowledgeItem {
  index: number;
  score: number;
  doc_name: string;
  doc_type: number;
  doc_url?: string;
  content: string;
}

interface KnowledgeContentProps {
  content: {
    results: KnowledgeItem[];
    query?: string;
    latency?: number;
  };
}

const KnowledgeContent: FC<KnowledgeContentProps> = ({ content }) => {
  const changeRightArea = useConversationStore(
    (state) => state.changeRightArea
  );
  const { results = [], query } = content;

  if (!results || results.length === 0) {
    return null;
  }

  return (
    <div>
      <KnowledgeButton
        icon={<img src={knowledgeIcon} alt="" />}
        text={`${results.length}个回答来源`}
        onClick={() => {
          changeRightArea('OPEN_ROBOT_ANSWER_SOURCE', {
            data: results,
          });
        }}
      />
    </div>
  );
};

export default KnowledgeContent;
