.messageContainer {
  display: flex;
}
.streamMessage {
  .errorContent {
    overflow-wrap: anywhere;
  }
  .errorFooter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;

    .actionButtonWrapper {
      display: flex;
      align-items: center;
      flex: 1;
    }

    .errorStatus {
      font-size: 13px;
      color: var(--primary-text-color-3);
      display: flex;
      align-items: center;
      height: 20px;
    }
  }
}

/* 图标样式 */
.refreshIcon {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

/* 操作区域样式 */
.actionFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search_Highlight {
  padding: 0 !important;
  background: #fff5da !important;
  border-radius: 2px;
}
