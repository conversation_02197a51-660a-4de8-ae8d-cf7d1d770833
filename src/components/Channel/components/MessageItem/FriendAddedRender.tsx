/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable react/no-danger */
import React, { FC } from 'react';
import dayjs from 'dayjs';
import { IMessageItemProps } from '.';
import styles from './index.less';

const getTime = (data: number) => {
  const nowTiem = dayjs().format('YYYY/M/D');
  const senderTime = dayjs(data).format('YYYY/M/D');
  if (nowTiem === senderTime) {
    const time = dayjs(data).format('HH:mm');
    return time;
  } else {
    return dayjs(data).format('YYYY-MM-DD HH:mm');
  }
};

const FriendAddedRender: FC<IMessageItemProps> = ({ message }) => {
  return (
    <div className={styles.friendAdded}>
      <div className={styles.friendAddedTime}>{getTime(message.sendTime)}</div>
      <div>你们已经成为好友，可以开始聊天了！</div>
    </div>
  );
};

export default FriendAddedRender;
