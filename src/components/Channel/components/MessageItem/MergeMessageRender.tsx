import { FC, memo, useCallback, useState } from 'react';
import { throttle } from 'lodash';
import { MessageItem, SessionType } from '@ht/openim-wasm-client-sdk';
import { openMultiMessageWin } from '@/utils/message';
import { renderMsgText } from '@/components/ChannelList/ChannelItem';
import MultiMessageModal from '@/components/Channel/components/MultiMessageModal';
import { IMessageItemProps } from '.';
import UserInfoRender from './UserInfoRender';
import styles from './index.less';

const MergeMessageRender: FC<IMessageItemProps> = ({ ...props }) => {
  const {
    message,
    conversationID = '',
    showName = '',
    hasForwardBtn = false,
  } = props;
  const { title, multiMessage } = message.mergeElem || {};

  const [multiMessageModalOpen, setMultiMessageModalOpen] =
    useState<boolean>(false);

  const handleOpenMultiMessageView = useCallback(async () => {
    openMultiMessageWin(
      {
        message,
        conversationID,
        showName,
        hasForwardBtn,
      },
      () => {
        setMultiMessageModalOpen(true);
      }
    );
  }, [conversationID, message, showName, hasForwardBtn]);

  const handleOpenMultiMessageViewThrottle = throttle(
    handleOpenMultiMessageView,
    300,
    { leading: false, trailing: true }
  );

  const renderContent = useCallback(() => {
    return (
      <div
        className={styles.mergeMessageRenderWrapper}
        onClick={(e) => {
          e.stopPropagation();
          handleOpenMultiMessageViewThrottle();
        }}
      >
        <div className={styles.mergeMessageTitle}>
          <div className={styles.line}></div>
          <span className={styles.title}>{title}</span>
        </div>
        <div className={styles.mergeMessageContent}>
          {multiMessage?.map((msgItem: MessageItem, index: number) => {
            return (
              index <= 2 && (
                <div className={styles.msgItemWrapper}>
                  <div>{msgItem.senderNickname}：</div>
                  <div className={styles.msgText}>
                    {renderMsgText(
                      {
                        ...msgItem,
                        sessionType: SessionType.Single, // 以此控制不展示名字
                      },
                      false
                    )}
                  </div>
                </div>
              )
            );
          })}
        </div>
      </div>
    );
  }, [multiMessage, title]);

  return (
    <div className={styles.mergeMessageWrapper}>
      <UserInfoRender {...props}>{renderContent()}</UserInfoRender>
      {multiMessageModalOpen && (
        <div
          onContextMenu={(e) => {
            e.stopPropagation();
            e.preventDefault();
          }}
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          <MultiMessageModal
            modalOpen={multiMessageModalOpen}
            oncancel={() => setMultiMessageModalOpen(false)}
            hasForwardBtn={hasForwardBtn}
            message={message}
            conversationID={conversationID}
            showName={showName}
          />
        </div>
      )}
    </div>
  );
};

export default memo(MergeMessageRender);
