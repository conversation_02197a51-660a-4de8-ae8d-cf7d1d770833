import React, { FC } from 'react';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { RenderMd } from '@/components/MdEditor/RenderMd';
import translateFinish from '@/assets/contact/translateFinish.png';
import { SingleTranslation } from '@/store/translationStore';

interface TranslationRenderProps {
  translationData: SingleTranslation;
  messageId: string;
}

const TranslationRender: FC<TranslationRenderProps> = ({
  translationData,
  messageId,
}) => {
  if (!translationData) {
    return null;
  }

  return (
    <div style={{ marginTop: '5px' }}>
      <div
        style={{
          borderBottom: '1px solid #ccc',
          marginBottom: '5px',
        }}
      />
      {translationData.translationState === 'loading' && (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <LoadingSpinner />
          <span
            style={{
              marginLeft: '5px',
              fontSize: '12px',
            }}
          >
            翻译中
          </span>
        </div>
      )}
      {translationData.translationState === 'finish' && (
        <div style={{ color: '#333', lineHeight: '22px' }}>
          <span
            style={{
              fontSize: '14px',
              color: 'rgba(29,28,29,1)',
            }}
          >
            <RenderMd
              id={messageId}
              value={translationData.translatedContent}
            />
          </span>
          <span
            style={{
              marginLeft: '5px',
              whiteSpace: 'nowrap',
              verticalAlign: 'middle',
            }}
          >
            <img
              src={translateFinish}
              alt="翻译完成"
              style={{
                marginRight: '5px',
                width: '12px',
                height: '12px',
              }}
            />
            <span
              style={{
                fontSize: '12px',
                color: 'rgba(153,155,160,1)',
              }}
            >
              翻译完成
            </span>
          </span>
        </div>
      )}
    </div>
  );
};

export default TranslationRender;
