/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable indent */
import React, { useState, useEffect } from 'react';
import { useConversationStore } from '@/store';
import classNames from 'classnames';
import { ConversationItem } from '@ht/openim-wasm-client-sdk';
import { Divider, Dropdown, Menu } from '@ht/sprite-ui';
import { emit } from '@/utils/events';
import styles from './index.less';

interface Props {
  content: string;
  propClassName: string;
}

const menuList = [
  {
    label: '今天',
    key: 'today',
  },
  // {
  //   label: '昨天',
  //   key: 'yesterday',
  // },
];

const DateSeparator: React.FC<Props> = ({ content, propClassName }) => {
  const [menus] = React.useState(JSON.parse(JSON.stringify(menuList)));
  const currentConversation = useConversationStore(
    (state) => state.currentConversation
  );
  const handleClick = (item: { key: string }) => {
    switch (item.key) {
      case 'today':
        emit('CHAT_LIST_SCROLL_TO_BOTTOM', {
          conversation: currentConversation as ConversationItem,
        });
        break;
      case 'yesterday':
        break;
      default:
        break;
    }
  };
  const menu = <Menu onClick={handleClick} items={menuList} />;

  return (
    <div className={classNames(styles.dateSeparator, styles[propClassName])}>
      {/* <Divider>
        <Dropdown
          overlay={menu}
          trigger={['click']}
          overlayClassName={styles.dateDropdown}
          destroyPopupOnHide={true}
          placement="bottomLeft"
          disabled={true}
        >
          <div className={styles.dateSeparatorButton}>{content}</div>
        </Dropdown>
      </Divider> */}
      {content}
    </div>
  );
};
export default DateSeparator;
