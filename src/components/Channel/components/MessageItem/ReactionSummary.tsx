import { useEffect, useState, FC, useCallback, useMemo } from 'react';
import _ from 'lodash';
import classNames from 'classnames';
import { MessageItem, getSDK } from '@ht/openim-wasm-client-sdk';
import { useConversationStore, useUserStore } from '@/store';
import emoji_1 from '@/assets/images/emojiPicker/emoji/emoji_1.png';
import emojiAddIcon from '@/assets/images/emojiPicker/emojiAdd.png';
import EmojiPickerList from '@/components/EmojiPickerList';
import EmojiPicker from '@/components/EmojiPicker';
import { useConversationSettings } from '@/hooks/useConversationSettings';
import { IMSDK } from '@/layouts/BasicLayout';
import styles from './index.less';

export type ReactionInfo = {
  emoji: string;
  userID: string[];
};

type ReactionSummaryProps = {
  reactionList: any[];
  conversationID: string;
  clientMsgID: string;
  message: MessageItem;
  isDisabled: boolean;
  isFileOrDoc: boolean;
};

const ReactionSummary: FC<ReactionSummaryProps> = ({
  reactionList,
  conversationID,
  clientMsgID,
  message,
  isDisabled = false,
  isFileOrDoc = false,
}) => {
  // const [reactionData, setreactionData] = useState<any>([]);
  const { userID } = useUserStore.getState().selfInfo;
  const currentConversation = useConversationStore(
    (state) => state.currentConversation
  );

  const { markConversationMessageAsRead } =
    useConversationSettings(currentConversation);

  const reactionData = useMemo(() => {
    const newList = _(reactionList)
      .groupBy('emoji') // 按 emoji 分组
      .map((group, emoji) => ({
        emoji,
        userID: _.map(group, 'userID'), // 提取每个分组的 userID
      }))
      .value();
    return newList;
  }, [reactionList]);

  const onSelect = (item: ReactionInfo) => {
    if (_.includes(item.userID, userID)) {
      const sdk = IMSDK;
      sdk.reactMessage({
        conversationID,
        clientMsgID,
        reaction: { emoji: item.emoji, action: 2 },
      });
    } else {
      const sdk = IMSDK;
      sdk.reactMessage({
        conversationID,
        clientMsgID,
        reaction: { emoji: item.emoji, action: 1 },
      });
    }
  };

  const reactionChange = useCallback(
    _.throttle(onSelect, 300, {
      leading: true,
      trailing: false,
    }),
    [userID]
  );

  return (
    <div
      className={styles.reactionWrapper}
      // style={isFileOrDoc ? { paddingTop: '4px' } : {}}
    >
      {reactionData ? (
        reactionData.map((reactionItem: ReactionInfo) => {
          return (
            <EmojiPickerList
              key={reactionItem.emoji}
              reactionItem={reactionItem}
            >
              <div style={{ padding: '0 8px 4px 0' }}>
                <div
                  className={classNames(
                    styles.reactionItem,
                    _.includes(reactionItem.userID, userID) &&
                      styles.reactionActivedItem
                  )}
                  onClick={() => {
                    if (!isDisabled) {
                      reactionChange(reactionItem);
                      // 点击reaction时触发把消息标为已读
                      markConversationMessageAsRead(message);
                    }
                  }}
                >
                  {reactionItem.emoji === 'plusone' ? (
                    <img src={emoji_1} className={styles.reactionIcon} />
                  ) : (
                    <span className={styles.reactionIcon1}>
                      {reactionItem.emoji}
                    </span>
                  )}

                  <span className={styles.reactionNum}>
                    {reactionItem.userID?.length}
                  </span>
                </div>
              </div>
            </EmojiPickerList>
          );
        })
      ) : (
        <></>
      )}
      {!isDisabled && (
        <EmojiPicker message={message} conversationID={conversationID}>
          <div className={styles.reactionItem}>
            <img
              src={emojiAddIcon}
              className={styles.reactionIcon}
              style={{ width: '20px' }}
            />
          </div>
        </EmojiPicker>
      )}
    </div>
  );
};
export default ReactionSummary;
