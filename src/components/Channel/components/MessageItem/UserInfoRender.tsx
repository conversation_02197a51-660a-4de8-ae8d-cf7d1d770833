/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-lines */
/* eslint-disable react/no-danger */
/* eslint-disable complexity */
import { FC, memo, useEffect, useState, useMemo, useRef } from 'react';
import classNames from 'classnames';
import OIMAvatar from '@/components/OIMAvatar';
import { Popover, Dropdown } from '@ht/sprite-ui';
import { usePrevious } from 'ahooks';
import dayjs from 'dayjs';
import {
  useConversationStore,
  useSearchInfoStore,
  useUserStore,
  useMultiSelectStore,
} from '@/store';
import useThreadState from '@/hooks/useThreadState';
import {
  MessageItem,
  MessageType,
  GroupStatus,
  GroupMemberRole,
  MessageStatus,
} from '@ht/openim-wasm-client-sdk';
import ForwardModal from '@/components/ForwardModal';
import MessageSendAndReadStatus from '@/components/MessageSendAndReadStatus';
import { getAvatarUrlFromMessage, isBotUser } from '@/utils/avatar';
import { parseThreadIdFromMessage, isSingleChat } from '@/utils/utils';
import { updateOneMessage } from '@/hooks/useHistoryMessageList';
import multiUnSelectedIcon from '@/assets/channel/messageRender/multiUnSelected.svg';
import multiSelectedIcon from '@/assets/channel/messageRender/multiSelected.svg';
import multiDisabledIcon from '@/assets/channel/messageRender/multiDisabled.svg';

import MessageMenuContent from './MessageMenuContent';
import RightButtonMenu from './RightButtonMenu';
import ReactionSummary from './ReactionSummary';
import { IMessageItemProps } from '.';
import styles from './index.less';
import { PopPersonCardContainer } from './PopPersonCardContainer';

export const getUserName = (message: MessageItem) => {
  const groupCreatedDetail = JSON.parse(message.notificationElem!.detail);
  const { opUser } = groupCreatedDetail;
  return opUser.nickname || '';
};

export const ifMultiSelectDisabled = (message: MessageItem) => {
  let ifDisabled = message.status === MessageStatus.Failed;
  if (message.contentType === MessageType.CustomMessage) {
    let content: any = {};
    try {
      content = JSON.parse(message?.customElem?.data || '{}');
    } catch (e) {
      content = message?.customElem?.data;
    }
    if (content?.type === 'stream' && content?.content?.error) {
      ifDisabled = true;
    }
  }
  return ifDisabled;
};

type UserInfoRenderProps = IMessageItemProps;

// eslint-disable-next-line max-statements
const UserInfoRender: FC<UserInfoRenderProps> = ({
  message,
  isSender,
  conversationID,
  // setShowMessageColumn,
  showName,
  // showTread = false,
  children,
  isThread,
  isExit = false,
  isSearch = false,
  searchValue = '',
  inHistoryList = false,
  inMultiMessageModal = false,
  markConversationMessageAsRead,
}) => {
  const currentConversation = useConversationStore(
    (state) => state.currentConversation
  );

  const setCurrentThreadConversation = useConversationStore(
    (state) => state.setCurrentThreadConversation
  );
  const changeRightArea = useConversationStore(
    (state) => state.changeRightArea
  );
  const currentGroupInfo = useConversationStore(
    (state) => state.currentGroupInfo
  );
  const currentMemberInGroup = useConversationStore(
    (state) => state.currentMemberInGroup
  );
  const isMultiSession = useConversationStore((state) => state.isMultiSession);

  const changeSearchRightArea = useSearchInfoStore(
    (state) => state.changeRightArea
  );

  const selfID = useUserStore.getState().selfInfo.userID;

  const removeSingleMsgFromUnReadList = useConversationStore(
    (state) => state.removeSingleMsgFromUnReadList
  );

  const multiSelectState = useMultiSelectStore(
    (state) => state.multiSelectState
  );

  const updateMultiSelectList = useMultiSelectStore(
    (state) => state.updateMultiSelectList
  );

  const multiSelectList = useMultiSelectStore((state) => state.multiSelectList);

  const { joinThread } = useThreadState();
  const [loading, setLoading] = useState<boolean>(false);
  const [forwardModal, setForwardModal] = useState<boolean>(false);
  const [msgMenuOpen, setMsgMenuOpen] = useState<boolean>(false);
  const [rightBtnOpen, setRightBtnOpen] = useState<boolean>(false);
  const [showSendTime, setShowSendTime] = useState<boolean>(false);
  const userInfoRef = useRef<HTMLDivElement>(null);
  const timer = useRef<any>(null);
  const sendTimer = useRef<any>(null);

  const observerRef = useRef<MutationObserver>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const hasReadRef = useRef(false);
  const contentRef = useRef(null);

  const [highlighted, setHighlighted] = useState(false);
  const prev = usePrevious(message.ex);
  const hightlightTimeRef = useRef<NodeJS.Timeout | null>(null);

  // 高亮
  useEffect(() => {
    if (!prev && message.ex === 'needHighLight') {
      setHighlighted(true);
      hightlightTimeRef.current = setTimeout(() => setHighlighted(false), 3000);
    }
  }, [message.ex, prev]);

  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
      if (hightlightTimeRef.current) {
        clearTimeout(hightlightTimeRef.current);
      }

      if (observerRef.current) {
        observerRef.current?.disconnect();
      }
    };
  }, []);

  // 头像可见+消息整体的10%可见且停留超过100ms，则标记为已读
  useEffect(() => {
    if (isSearch) {
      return;
    }
    const root = document.getElementById('messageList');

    if (message == null || !root || !contentRef.current) {
      return;
    }

    const intersectedStates = {
      content: false,
    };

    // 如果已经滚动到要跳转的消息的位置，则隐藏“跳转到未读”按钮
    const resetReadSeqInfo = () => {
      if (
        message.seq != null &&
        conversationID != null &&
        intersectedStates.content &&
        message.sendID !== selfID
      ) {
        removeSingleMsgFromUnReadList(conversationID, message.seq);
      }
    };

    const checkAndSetRead = () => {
      if (hasReadRef.current) {
        return;
      }

      if (intersectedStates.content) {
        if (timerRef.current) {
          clearTimeout(timerRef.current);
        }
        timerRef.current = setTimeout(() => {
          hasReadRef.current = true;
          markConversationMessageAsRead(message);
        }, 100);
      } else if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }
    };

    // 清除高亮状态
    const clearHeightLightState = () => {
      if (intersectedStates.content && message.ex === 'needHighLight') {
        updateOneMessage({
          ...message,
          ex: '',
        });
      }
    };

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const { target } = entry;
          if (target === contentRef.current) {
            intersectedStates.content = entry.intersectionRatio >= 0.01;
          }
        });
        checkAndSetRead();
        resetReadSeqInfo();
        clearHeightLightState();
      },
      {
        root,
        threshold: [0, 0.01, 1],
      }
    );

    observer.observe(contentRef.current);

    observerRef.current = observer;
  }, [
    contentRef,
    conversationID,
    isSearch,
    markConversationMessageAsRead,
    message,
    removeSingleMsgFromUnReadList,
    selfID,
  ]);

  const showTread = async () => {
    if (currentConversation?.conversationID == null) {
      console.error('当前未选中子群');
      return;
    }

    // const currentGroupMap = messageThreadMap.get(currentConversation?.groupID);
    // if (currentGroupMap != null) {

    const threadId = parseThreadIdFromMessage(message);
    // const threadId = currentGroupMap?.get(message.clientMsgID);

    if (threadId == null || threadId === '') {
      console.error(
        '加入消息列失败,当前消息无有效消息列，clientMsgID为',
        message?.clientMsgID
      );
      return;
    }
    setLoading(true);
    // 需要把当前人加到thread里，否则不在thread里的人点开时看不到历史消息
    const joinThreadResult = await joinThread(threadId);
    if (joinThreadResult) {
      setCurrentThreadConversation(threadId, message.groupID, message);
    }
    setLoading(false);
    // }
  };

  const showTreadBtnVisible = false;

  const disabledMessageMenuContent = useMemo(() => {
    return (
      isBotUser(currentConversation?.userID || '') ||
      isSearch ||
      isExit ||
      (currentGroupInfo?.status === GroupStatus.Muted &&
        currentMemberInGroup?.roleLevel === GroupMemberRole.Normal) ||
      inMultiMessageModal
    );
  }, [
    isSearch,
    isExit,
    currentGroupInfo,
    currentMemberInGroup,
    inMultiMessageModal,
    currentConversation,
  ]);

  const ifReactionShow = useMemo(() => {
    return (
      message.attachedInfoElem?.reaction?.logs?.length > 0 &&
      !isSearch &&
      !inMultiMessageModal
    );
  }, [inMultiMessageModal, isSearch, message]);

  const isFileOrDoc = useMemo(() => {
    if (message.contentType === MessageType.FileMessage) {
      return true;
    } else if (message.contentType === MessageType.PictureMessage) {
      return true;
    } else if (message.contentType === MessageType.CustomMessage) {
      let content: any = {};
      try {
        content = JSON.parse(message?.customElem?.data || '{}');
      } catch (e) {
        content = message?.customElem?.data;

        return null;
      }
      if (content?.type === 'clouddocument') {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }, [message.contentType]);

  const onMouseEnter = () => {
    timer.current = setTimeout(() => {
      clearTimeout(timer.current);
      setMsgMenuOpen(true);
    }, 300);
  };

  const onMouseLeave = () => {
    setMsgMenuOpen(false);
    if (timer.current) {
      clearTimeout(timer.current);
    }
  };

  const onMsgBoxMouseEnter = () => {
    sendTimer.current = setTimeout(() => {
      clearTimeout(sendTimer.current);
      setShowSendTime(true);
    }, 300);
  };

  const onMsgBoxMouseLeave = () => {
    setShowSendTime(false);
    if (sendTimer.current) {
      clearTimeout(sendTimer.current);
    }
  };

  // useEffect(() => {
  //   if (message.ex === 'needHighLight') {
  //     setTimeout(() => {
  //       updateOneMessage({
  //         ...message,
  //         ex: '',
  //       });
  //     }, 5000);
  //   }
  // }, [message.ex]);

  const showBotTag = !isSender && isBotUser(message.sendID);

  const ifMultiSelected = useMemo(() => {
    return multiSelectList.find(
      (item) => item.clientMsgID === message.clientMsgID
    );
  }, [multiSelectList, message.clientMsgID]);

  const renderMultiSelectBtn = useMemo(() => {
    let imgSrc = multiUnSelectedIcon;
    if (ifMultiSelectDisabled(message)) {
      imgSrc = multiDisabledIcon;
    } else if (ifMultiSelected) {
      imgSrc = multiSelectedIcon;
    }
    return (
      multiSelectState &&
      !inMultiMessageModal &&
      !inHistoryList && (
        <div className={styles.multiSelectWrapper}>
          <img src={imgSrc} />
        </div>
      )
    );
  }, [
    ifMultiSelected,
    inHistoryList,
    inMultiMessageModal,
    message,
    multiSelectState,
  ]);

  const ifDisabledPopPersonCardPopover = useMemo(() => {
    return isSingleChat();
  }, []);

  const sendTimeFormat = useMemo(() => {
    return inMultiMessageModal ? 'YYYY/MM/DD HH:mm' : 'MM月DD日 HH:mm';
  }, [inMultiMessageModal]);

  const renderMergeSearchTip = useMemo(() => {
    return (
      isSearch &&
      searchValue.trim() &&
      message.contentType === MessageType.MergeMessage && (
        <div className={styles.mergeSearchTip}>
          [聊天记录]&nbsp;包含&nbsp;
          <div className={styles.search_Highlight}>{searchValue.trim()}</div>
        </div>
      )
    );
  }, [isSearch, message.contentType, searchValue]);

  const ifShowSelectedBackColor = useMemo(() => {
    return ifMultiSelected && !inMultiMessageModal && !inHistoryList;
  }, [ifMultiSelected, inHistoryList, inMultiMessageModal]);

  const canSelect = useMemo(() => {
    return (
      multiSelectState &&
      !inMultiMessageModal &&
      !inHistoryList &&
      !ifMultiSelectDisabled(message)
    );
  }, [inHistoryList, inMultiMessageModal, message, multiSelectState]);

  return (
    <div
      ref={userInfoRef}
      style={{
        paddingBottom: inHistoryList ? '' : '4px',
      }}
      className={classNames(
        styles.userInfoRenderBox,
        ifShowSelectedBackColor && styles.userInfoRenderBox_multiSelected,
        canSelect && styles.userInfoRenderBox_canSelect
      )}
      onClick={() => {
        if (canSelect) {
          updateMultiSelectList(message);
        }
      }}
    >
      {renderMultiSelectBtn}
      <div
        className={classNames(
          styles.userInfoRenderWarp,
          showBotTag && !isSender ? styles.userInfoRenderWarp_multi : '',
          // isMergedMessage ? styles.userInfoRenderWarpMerged : '',
          isSender ? styles.isSender : styles.isReceiver,
          isSearch ? styles.userInfoRenderWarp_isSearch : '',
          inHistoryList ? styles.userInfoRenderWarp_inHistoryList : '',
          inMultiMessageModal
            ? styles.userInfoRenderWarp_inMultiMessageModal
            : '',
          highlighted && !inHistoryList && styles.userInfoRenderHighlight,
          message.contentType === MessageType.GroupAnnouncementUpdated &&
            !(!inHistoryList && isSearch) &&
            styles.userInfoGroupAnnouncementWarp,
          message.contentType === MessageType.MergeMessage &&
            styles.userInfoRenderWarp_MergeMessage
        )}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
      >
        {!inMultiMessageModal && (
          <PopPersonCardContainer
            sendID={message.sendID}
            isDisabled={ifDisabledPopPersonCardPopover}
          >
            <div
              className={styles.avatarContainer}
              onClick={(e) => {
                e.stopPropagation();
                if (ifDisabledPopPersonCardPopover) {
                  return;
                }
                if (location.pathname === '/linkflow/search') {
                  changeSearchRightArea('OPEN_PERSON_DETAIL', message.sendID);
                } else {
                  changeRightArea('OPEN_PERSON_DETAIL', message.sendID);
                }
              }}
            >
              <OIMAvatar
                src={getAvatarUrlFromMessage(message)}
                userID={message?.sendID}
                hideOnlineStatus={true}
                size={40}
                shape="square"
              />
            </div>
          </PopPersonCardContainer>
        )}
        <div className={styles.detailContainer} ref={contentRef}>
          <div className={classNames(styles.senderInfo)}>
            <span className={styles.senderNameArea}>
              <span className={styles.senderNickname}>
                {message.contentType === MessageType.GroupCreated
                  ? getUserName(message)
                  : message.senderNickname}
              </span>
              {showBotTag && (
                <span className={classNames(styles.tag, styles.tag_3)}>
                  机器人
                </span>
              )}
            </span>
            <span
              className={styles.sendTime}
              style={showSendTime ? {} : { visibility: 'hidden' }}
            >
              {dayjs(message.sendTime).format(sendTimeFormat)}
            </span>
          </div>

          {renderMergeSearchTip}

          <Popover
            overlayClassName={styles.menuWrap}
            content={
              !disabledMessageMenuContent ? (
                <div style={isSender ? {} : { marginLeft: '8px' }}>
                  <MessageMenuContent
                    message={message}
                    conversationID={conversationID!}
                    isThread={isThread}
                  />
                </div>
              ) : (
                <></>
              )
            }
            title={null}
            destroyTooltipOnHide={true}
            // mouseEnterDelay={0.3}
            placement={isSender ? 'leftTop' : 'rightTop'}
            autoAdjustOverflow={true}
            getPopupContainer={() => userInfoRef.current || document.body}
            open={msgMenuOpen && message.status === MessageStatus.Succeed}
          >
            <MessageSendAndReadStatus
              message={message}
              conversationID={conversationID}
              isSender={isSender}
              isSearch={isSearch}
              onMouseLeave={onMouseLeave}
              onMouseEnter={onMouseEnter}
              inHistoryList={inHistoryList}
              inMultiMessageModal={inMultiMessageModal}
              isExit={isExit}
            >
              <Dropdown
                overlay={
                  <RightButtonMenu
                    message={message}
                    isSender={isSender}
                    isThread={isThread}
                    showTreadBtnVisible={showTreadBtnVisible}
                    conversationID={conversationID!}
                    setRightBtnOpen={setRightBtnOpen}
                    openForwardModal={() => {
                      setForwardModal(true);
                    }}
                    isExit={isExit}
                  />
                }
                disabled={isSearch || isSingleChat() || inMultiMessageModal}
                trigger={['contextMenu']}
                overlayClassName={styles.rightButtonMenu}
                onOpenChange={(open: boolean) => setRightBtnOpen(open)}
              >
                <div
                  className={classNames(
                    styles.messageContentBox,
                    isFileOrDoc && !ifReactionShow
                      ? ''
                      : styles.messageContentStyle
                  )}
                  onMouseEnter={onMsgBoxMouseEnter}
                  onMouseLeave={onMsgBoxMouseLeave}
                >
                  <div
                    className={classNames(
                      styles.maskBox,
                      rightBtnOpen && styles.maskBoxShow
                    )}
                  ></div>

                  {children}

                  {ifReactionShow ? (
                    <ReactionSummary
                      reactionList={
                        message.attachedInfoElem?.reaction?.logs || []
                      }
                      clientMsgID={message.clientMsgID}
                      conversationID={conversationID!}
                      message={message}
                      isDisabled={isSearch || isExit || false}
                      isFileOrDoc={isFileOrDoc || false}
                    />
                  ) : (
                    <></>
                  )}
                  {showTreadBtnVisible ? (
                    <div
                      style={{ color: '#1E64AA', cursor: 'pointer' }}
                      onClick={() => showTread()}
                    >
                      {loading ? '加载子群ing，请稍后' : '查看子群'}
                    </div>
                  ) : (
                    <></>
                  )}
                </div>
              </Dropdown>
            </MessageSendAndReadStatus>
          </Popover>
        </div>
      </div>
      {forwardModal && (
        <ForwardModal
          open={forwardModal}
          onClose={() => setForwardModal(false)}
          message={message}
          isThread={isThread}
          isSender={isSender}
          conversationID={conversationID}
          showName={showName}
        />
      )}
    </div>
  );
};

export default memo(UserInfoRender);
