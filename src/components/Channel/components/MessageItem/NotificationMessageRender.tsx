/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable react/no-danger */
/* eslint-disable indent */
import React, { FC, useEffect, useRef } from 'react';
import classNames from 'classnames';
import {
  MessageItem,
  PublicUserItem,
  MessageType,
  GroupMemberRole,
} from '@ht/openim-wasm-client-sdk';
import { useConversationStore, useUserStore } from '@/store';
import { t } from 'i18next';
import styles from './index.less';
import { IMessageItemProps } from '.';

const NotificationMessageRender: FC<IMessageItemProps> = ({ ...props }) => {
  const { message, mseInfoClassName = '', isSearch, conversationID } = props;
  const selfID = useUserStore.getState().selfInfo.userID;

  const getName = (user: PublicUserItem) => {
    return user.userID === selfID ? '你' : user.nickname;
  };

  const getGroupCreatedRenderdMsg = (msg: MessageItem) => {
    const groupNameDetails = JSON.parse(msg.notificationElem!.detail);
    const name = groupNameDetails.group.groupName;
    return `${getName(groupNameDetails.opUser)}已创建群聊“${name}”`;
  };

  const getMemberInvitedMsg = (msg: MessageItem) => {
    const inviteDetails = JSON.parse(msg.notificationElem!.detail);
    const invitedUserList = inviteDetails.invitedUserList ?? [];
    const index = invitedUserList.findIndex(
      (item: any) => item.userID === selfID
    );
    if (index !== -1) {
      const itemToMove = invitedUserList[index];
      invitedUserList.splice(index, 1);
      invitedUserList.push(itemToMove);
    }
    const inviteOpUser = inviteDetails.opUser;
    let inviteStr = `${getName(inviteOpUser)}邀请`;
    invitedUserList.map((user: any) => (inviteStr += `${getName(user)}、`));
    inviteStr = inviteStr.slice(0, -1);
    inviteStr += `加入群聊`;
    return inviteStr;
  };

  const getMemberKickedMsg = (msg: MessageItem) => {
    const kickDetails = JSON.parse(msg.notificationElem!.detail);
    const kickOpUser = kickDetails.opUser;
    const kickdUserList = kickDetails.kickedUserList ?? [];
    let kickStr = '';
    if (kickOpUser.userID === selfID) {
      kickStr = '你将';
      kickdUserList.map((user: any) => (kickStr += `${getName(user)}、`));
      kickStr = kickStr.slice(0, -1);
      kickStr += `移出了群聊`;
    } else if (kickdUserList.some((user: any) => user.userID === selfID)) {
      kickStr = `你被${kickOpUser.nickname}移出了群聊`;
    }
    return kickStr;
  };

  const getGroupDismissedMsg = (msg: MessageItem) => {
    const dismissedDetails = JSON.parse(msg.notificationElem!.detail);
    const { opUser } = dismissedDetails;
    const name = msg.sendID === selfID ? '你' : opUser.nickname;
    return `${name}已经解散群聊，所有成员被移除群聊`;
  };

  const getMemberQuitMsg = (msg: MessageItem) => {
    const quitDetails = JSON.parse(msg.notificationElem!.detail);
    const { quitUser, group } = quitDetails;
    if (group.ownerUserID === selfID || quitUser.userID === selfID) {
      return `${getName(quitUser)}退出了群聊`;
    } else {
      return '';
    }
  };

  const getMemberEnterMsg = (msg: MessageItem) => {
    const quitDetails = JSON.parse(msg.notificationElem!.detail);
    const { entrantUser } = quitDetails;
    return `${getName(entrantUser)}加入了群聊`;
  };

  const getThreadCreateMsg = (msg: MessageItem) => {
    const quitDetails = JSON.parse(msg.notificationElem!.detail);
    const { quitUser } = quitDetails;
    return `${getName(
      quitUser
    )}创建了一条子群，该通知为临时消息，thread实现方式修改后不会展示`;
  };
  const getGroupNameUpdatedMsg = (msg: MessageItem) => {
    const groupNameDetails = JSON.parse(msg.notificationElem!.detail);
    const name = groupNameDetails.group.groupName;
    return `${getName(groupNameDetails.opUser)}修改群名为“${name}”`;
  };

  const getRevokeMessageMsg = (msg: MessageItem) => {
    const data = JSON.parse(msg.notificationElem!.detail);
    const operator = data.revokerID === selfID ? '你' : data.revokerNickname;
    const revoker =
      data.sourceMessageSendID === selfID
        ? '你'
        : data.sourceMessageSenderNickname;
    const isAdminRevoke = data.revokerID !== data.sourceMessageSendID;
    if (isAdminRevoke) {
      return `${operator}撤回了${revoker}一条消息`;
    } else {
      return `${operator}撤回了一条消息`;
    }
  };

  const getCancelMutedMessage = (msg: MessageItem) => {
    return '群主已经关闭"全员禁言"';
  };

  const getOperator = (msg: MessageItem) => {
    const GROUPMUTEDDetails = JSON.parse(msg.notificationElem!.detail);
    const groupMuteOpUser = GROUPMUTEDDetails.opUser;
    if (groupMuteOpUser.roleLevel === GroupMemberRole.Owner) {
      return '群主';
    } else if (groupMuteOpUser.roleLevel === GroupMemberRole.Admin) {
      return '管理员';
    } else {
      return '';
    }
  };

  const getUpMessage = (msg: MessageItem) => {
    const upMessageDetails = JSON.parse(msg.notificationElem!.detail);
    return `${getName(upMessageDetails.opUser)}置顶了一条消息`;
  };

  const getUpMessageClear = (msg: MessageItem) => {
    const upMessageDetails = JSON.parse(msg.notificationElem!.detail);
    return `${getName(upMessageDetails.opUser)}移除了一条置顶消息`;
  };

  const notificationMessageFormat = (msg: MessageItem) => {
    try {
      switch (msg.contentType) {
        case MessageType.GroupCreated:
          return getGroupCreatedRenderdMsg(msg);
        case MessageType.MemberInvited:
          return getMemberInvitedMsg(msg);
        case MessageType.MemberKicked:
          return getMemberKickedMsg(msg);
        case MessageType.GroupDismissed:
          return getGroupDismissedMsg(msg);
        case MessageType.MemberQuit:
          return getMemberQuitMsg(msg);
        case MessageType.MemberEnter:
          return getMemberEnterMsg(msg);
        case MessageType.GroupNameUpdated:
          return getGroupNameUpdatedMsg(msg);
        case MessageType.RevokeMessage:
          return getRevokeMessageMsg(msg);
        case MessageType.ThreadCreatedNotification:
          return getThreadCreateMsg(msg);
        case MessageType.GroupMuted:
          return t('messageDescription.allMuteMessage', {
            operator: getOperator(msg),
          });
        case MessageType.GroupCancelMuted:
          return t('messageDescription.cancelAllMuteMessage', {
            operator: getOperator(msg),
          });
        case MessageType.UpMessage:
          return getUpMessage(msg);
        case MessageType.UpMessageClear:
          return getUpMessageClear(msg);
        default:
          return '';
      }
    } catch (error) {
      return '';
    }
  };

  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const contentRef = useRef(null);

  const removeSingleMsgFromUnReadList = useConversationStore(
    (state) => state.removeSingleMsgFromUnReadList
  );

  useEffect(() => {
    if (isSearch) {
      return;
    }
    const root = document.getElementById('messageList');

    if (message == null || message?.isRead || !root || !contentRef.current) {
      return;
    }

    const intersectedStates = {
      content: false,
    };

    // 如果已经滚动到要跳转的消息的位置，则隐藏“跳转到未读”按钮
    const resetReadSeqInfo = () => {
      if (
        message.seq != null &&
        conversationID != null &&
        intersectedStates.content &&
        message.sendID !== selfID
      ) {
        removeSingleMsgFromUnReadList(conversationID, message.seq);
      }
    };

    const checkAndSetRead = () => {
      if (intersectedStates.content) {
        resetReadSeqInfo();
      }
    };

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const { target } = entry;
          if (target === contentRef.current) {
            intersectedStates.content = entry.intersectionRatio >= 0.01;
          }
        });
        checkAndSetRead();
      },
      {
        root,
        threshold: [0, 0.01, 1],
      }
    );

    observer.observe(contentRef.current);

    return () => {
      if (observer) {
        observer.disconnect();
      }
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, [
    conversationID,
    isSearch,
    message,
    removeSingleMsgFromUnReadList,
    selfID,
  ]);

  return (
    <div
      ref={contentRef}
      className={classNames(
        styles.mseInfo,
        mseInfoClassName && styles[mseInfoClassName]
      )}
    >
      <div>{notificationMessageFormat(message)}</div>
    </div>
  );
};

export default NotificationMessageRender;
