import { memo } from 'react';
import { Divider } from '@ht/sprite-ui';
import { v4 as uuidv4 } from 'uuid';
import { MessageItem as MessageItemType } from '@ht/openim-wasm-client-sdk';
import MessageItem from '../MessageItem';
import styles from './index.less';

interface IThreadFirstMessageProps {
  currentThreadFirstMessage?: MessageItemType;
}

const ThreadFirstMessage = ({
  currentThreadFirstMessage,
}: IThreadFirstMessageProps) => {
  if (!currentThreadFirstMessage) {
    return null;
  }

  return (
    <div>
      <div className={styles.messageItemWrap}>
        <MessageItem
          message={{
            ...currentThreadFirstMessage,
            clientMsgID: currentThreadFirstMessage.clientMsgID + uuidv4(),
          }}
          isSender={false}
          isThread={true}
          disabled={true}
        />
      </div>
      <Divider />
    </div>
  );
};
export default memo(ThreadFirstMessage);
