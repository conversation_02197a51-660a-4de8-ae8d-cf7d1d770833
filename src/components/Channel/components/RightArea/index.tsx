import { useMemo } from 'react';
import withScrollBar from '@/components/withScrollBar';
import styles from './index.less';

const RightArea = ({ children }: { children: any }) => {
  return <div className={styles.rightArea}>{children}</div>;
};

const RightAreaWithScrollBar = ({
  children,
  defaultWidth,
  domId = 'drag-scroll-bar-right',
  needLocalStorageWidth = true,
}: {
  children: any;
  defaultWidth?: string;
  domId?: string;
  needLocalStorageWidth?: boolean;
}) => {
  const ScrollableComponent = useMemo(() => {
    return withScrollBar(RightArea, {
      domId,
      direction: 'right',
      defaultWidth,
      dragBarClassName: 'scrollbarRight',
      needLocalStorageWidth,
    });
  }, [defaultWidth, domId, needLocalStorageWidth]);

  return <ScrollableComponent>{children}</ScrollableComponent>;
};

export default RightAreaWithScrollBar;
