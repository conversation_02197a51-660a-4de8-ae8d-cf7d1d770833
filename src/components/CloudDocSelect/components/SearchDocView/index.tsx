import React, { FC } from 'react';
import DocItem from '../DocItem';
import styles from './index.less';

interface SearchDocViewProps {
  dataSource: any[];
  onSelect: (val: any) => void;
}

const SearchDocView: FC<SearchDocViewProps> = ({ dataSource, onSelect }) => {
  return (
    <div>
      {dataSource.map((item: any) => {
        return (
          <div key={item.documentId} onClick={() => onSelect(item)}>
            <DocItem docInfo={item} btnClick={() => {}} sourceType="search" />
          </div>
        );
      })}
    </div>
  );
};
export default SearchDocView;
