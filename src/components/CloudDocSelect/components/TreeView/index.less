.treeWrapper {
  :global {
    .linkflow-tree {
      margin: 0;
      padding: 0;
    }
    .linkflow-tree-treenode {
      padding: 0 !important;

      &:hover {
        background: var(--primary-background-color-15);
        border-radius: 8px;
      }
    }
    .linkflow-tree .linkflow-tree-node-content-wrapper {
      height: auto;
    }
    .linkflow-tree .linkflow-tree-node-content-wrapper:hover {
      background-color: transparent !important;
    }
    .linkflow-tree-switcher {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .linkflow-tree-switcher-noop {
      display: none;
      background-color: transparent !important;
    }
    .linkflow-tree-switcher-noop::after {
      background-color: transparent !important;
    }
  }
}
