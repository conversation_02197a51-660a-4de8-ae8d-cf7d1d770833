import { FC, useState, useEffect } from 'react';
import { Tree } from '@ht/sprite-ui';
import _ from 'lodash';
import { docTypeEnum, filterExpandkeys } from '@/utils/utils';
import DocItem from '../DocItem';
import styles from './index.less';

interface LinkWpsProps {
  dataSource: any;
  loadTreeData: any;
  onSelect: (val: any) => void;
}

const TreeView: FC<LinkWpsProps> = ({ dataSource, loadTreeData, onSelect }) => {
  const [selectedKeys, setSelectedKeys] = useState<any>([]);
  const [expandedKeys, setExpandedKeys] = useState<any>([]);
  const [data, setData] = useState(dataSource);

  useEffect(() => {
    setData(dataSource);
    if (
      !dataSource.some(
        (item) => item.fileType === docTypeEnum.FOLDER && item.children
      )
    ) {
      setExpandedKeys([]);
    }
  }, [dataSource]);

  const treeNodeClick = async (node: any) => {
    setSelectedKeys([node.key]);
    if (node.fileType === docTypeEnum.FOLDER) {
      let d: any = _.cloneDeep(expandedKeys);
      if (expandedKeys.includes(node.key)) {
        d = d.filter((i) => i !== node.key);
        const keys: any = [...filterExpandkeys(d, node)];
        setExpandedKeys(keys);
      } else {
        d.push(node.key);
        // eslint-disable-next-line @typescript-eslint/await-thenable
        await loadTreeData(node);
        setExpandedKeys(d);
      }
    } else {
      onSelect(node);
    }
  };

  const renderTreeNode = (node: any) => (
    <div className={styles.linkwpsTreeNode} onClick={() => treeNodeClick(node)}>
      <DocItem docInfo={node} btnClick={() => {}} />
    </div>
  );

  return (
    <div className={styles.treeViewWrapper}>
      <div className={styles.treeWrapper}>
        <Tree
          treeData={data}
          loadData={loadTreeData}
          expandedKeys={expandedKeys}
          loadedKeys={expandedKeys}
          blockNode={true}
          onExpand={(expandedKey: any, info: any) => {
            setSelectedKeys([info.node.key]);
            if (info.expanded) {
              setExpandedKeys(expandedKey);
            } else {
              const keys: any = [...filterExpandkeys(expandedKey, info.node)];
              setExpandedKeys(keys);
            }
          }}
          selectedKeys={selectedKeys}
          onSelect={(keys: any, e) => {
            setSelectedKeys(keys);
          }}
          titleRender={(node: any) => {
            return (
              <div key={node.fileId} title="">
                {renderTreeNode(node)}
              </div>
            );
          }}
          height={372}
        />
      </div>
    </div>
  );
};

export default TreeView;
