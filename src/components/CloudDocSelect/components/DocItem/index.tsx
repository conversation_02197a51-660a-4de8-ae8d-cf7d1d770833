import React, { <PERSON> } from 'react';
import dayjs from 'dayjs';
import { getDocIcon, docTypeEnum } from '@/utils/utils';
import fileIcon from '@/assets/channel/clouddocument/file.png';
import fileHoverIcon from '@/assets/channel/clouddocument/fileHover.svg';
import classNames from 'classnames';
import styles from './index.less';

interface DocItemProps {
  docInfo: any;
  btnClick: (val: any) => void;
  sourceType?: 'doc' | 'search';
}

const DocItem: FC<DocItemProps> = ({ docInfo, btnClick, sourceType }) => {
  const type = docInfo.documentType || docInfo.fileType;
  const docName = docInfo.documentName || docInfo.fileName;

  const docItem = (
    <div
      className={classNames(
        styles.docItemWarp,
        sourceType === 'search' ? styles.docSearchItem : ''
      )}
    >
      <div className={styles.docIcon}>
        <img src={getDocIcon(type, docInfo?.isShortCut === 1)} />
      </div>
      <div className={styles.docInfoBox}>
        <div className={styles.docName} title={docName}>
          {docName}
        </div>
        <div className={styles.docSource}>
          <span
            title={`创建者:${docInfo.createdBy}  创建时间:${dayjs(
              docInfo.createdTime
            ).format('YYYY-MM-DD HH:mm')}`}
          >
            创建者:{docInfo.createdBy}{' '}
            <span style={{ color: 'var(--primary-background-color-5)' }}>
              |
            </span>{' '}
            创建时间:
            {dayjs(docInfo.createdTime).format('YYYY-MM-DD HH:mm')}
          </span>
          {/* <div className={styles.line}></div>
          <span>
            创建时间:{dayjs(docInfo.createdTime).format('YYYY-MM-DD HH:mm')}
          </span> */}
        </div>
      </div>
    </div>
  );

  const folderItem = (
    <div className={styles.folderBox}>
      <div className={styles.docIcon}>
        <img src={fileIcon} className={styles.fileIcon} />
        <img src={fileHoverIcon} className={styles.fileHoverIcon} />
      </div>
      <div className={styles.docInfoBox} title={docName}>
        {docName}
      </div>
    </div>
  );

  return type === docTypeEnum.FOLDER ? folderItem : docItem;
};
export default DocItem;
