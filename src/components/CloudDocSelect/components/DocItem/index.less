.docItemWarp {
  display: flex;
  align-items: center;
  height: 52px;
  cursor: pointer;
  .docIcon {
    display: flex;
    align-items: center;
    margin-right: 10px;
    > img {
      width: 34px;
      height: 34px;
    }
  }
  .docInfoBox {
    width: calc(100% - 60px);
    .docName {
      font-size: 14px;
      font-weight: 600;
      color: var(--primary-text-color-2);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      line-height: 20px;
    }
    .docSource {
      display: flex;
      align-items: center;
      font-size: 12px;
      font-weight: 400;
      color: var(--primary-text-color);
      line-height: 17px;
      height: 17px;
      .line {
        width: 1px;
        height: 14px;
        background: var(--primary-background-color-5);
        margin: 0 4px;
      }
      > span {
        line-height: 17px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
  }
}
.docSearchItem {
  padding: 0 9px;
}
.docSearchItem:hover {
  background-color: var(--primary-background-color-15);
  border-radius: 8px;
}

.folderBox {
  display: flex;
  align-items: center;
  padding: 8px 0;
  .docIcon {
    display: flex;
    align-items: center;
    width: 16px;
    height: 16px;
    margin-right: 8px;
    > img {
      width: 100%;
      height: 100%;
    }
    .fileIcon {
      display: inline-block;
    }
    .fileHoverIcon {
      display: none;
    }
  }
  .docInfoBox {
    width: calc(100% - 30px);
    font-size: 14px;
    font-weight: 600;
    color: var(--primary-text-color-2);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
// .folderBox:hover {
//   .docIcon {
//     .fileIcon {
//       display: none;
//     }
//     .fileHoverIcon {
//       display: inline-block;
//     }
//   }
// }
