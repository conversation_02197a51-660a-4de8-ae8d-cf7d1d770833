import React, { FC, useEffect, useRef, useState } from 'react';
import { useDebounceFn } from 'ahooks';
import { Input, message, Spin } from '@ht/sprite-ui';
import { IMSDK } from '@/layouts/BasicLayout';
import { useUserStore } from '@/store';
import { docTypeEnum, updateTreeData } from '@/utils/utils';
import searchIcon from '@/assets/channel/clouddocument/search.png';
import emptyForSearchIcon from '@/assets/channel/clouddocument/emptyForSearch.svg';
import clearIcon from '@/assets/channel/clouddocument/clearIcon.svg';
import classNames from 'classnames';
import SearchDocView from './components/SearchDocView';
import TreeView from './components/TreeView';
import styles from './index.less';

const EmptyComponent = (
  <div className={styles.emptyBox}>
    <img src={emptyForSearchIcon} />
    <div>未搜索到相关结果</div>
  </div>
);

interface CloudDocSelectProps {
  onChange: (val: any) => void;
  conversationID: string;
}

const CloudDocSelect: FC<CloudDocSelectProps> = ({
  onChange,
  conversationID,
}) => {
  const selfID = useUserStore.getState().selfInfo.userID;
  const [value, setValue] = useState<string>('');
  const [lock, setLock] = useState<boolean>(false);
  const [isFocus, setIsFocus] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [status, setStatus] = useState<'success' | 'error' | ''>('');
  const [rednderType, setRednderType] = useState<'doc' | 'search'>('doc');
  const countRef = useRef<number>(0);

  useEffect(() => {
    getDataSource();
  }, []);

  const { run: runSearch } = useDebounceFn(
    (val) => {
      setLoading(true);
      setDataSource([]);
      if (val.trim()) {
        setRednderType('search');
        searchDoc(val.trim());
      } else {
        setRednderType('doc');
        getDataSource();
      }
    },
    {
      wait: 300,
    }
  );

  const updateData = (
    data: any[],
    count: number,
    type: 'success' | 'error'
  ) => {
    if (count === countRef.current) {
      setDataSource(data || []);
      setLoading(false);
      setStatus(type);
      if (type === 'error') {
        message.info('搜索失败');
      }
    }
  };

  const searchDoc = async (val: string) => {
    countRef.current++;
    const nowCount = countRef.current;
    try {
      const { data } = await IMSDK.searchDocument({
        userId: selfID,
        condition: val,
      });
      const arr = data.items || [];
      updateData(arr, nowCount, 'success');
    } catch (error) {
      console.error('searchDocument', error);
      updateData([], nowCount, 'error');
    }
  };

  const getDataSource = async (id?: number) => {
    countRef.current++;
    const nowCount = countRef.current;
    try {
      const { data } = await IMSDK.getConversationFolder({
        userId: selfID,
        conversationID,
        folderId: id ? id : 0,
      });
      const arr = (data.items || []).map((item: any) => {
        item.isLeaf = item.fileType !== docTypeEnum.FOLDER;
        item.rowKey = `${item.fileId}_${item.fileType}${
          item.isShortCut === 1 ? '_shortcut' : ''
        }`;
        item.key = `${item.fileId}_${item.fileType}_${1}`;
        item.depth = 0;
        return item;
      });
      updateData(arr, nowCount, 'success');
    } catch (error) {
      console.error('getConversationFolder', error);
      updateData([], nowCount, 'error');
    }
  };

  const loadTreeData = (item: any) => {
    const { key, children } = item;
    return new Promise<void>(async (resolve) => {
      if (children) {
        resolve();
        return;
      }
      const { data } = await IMSDK.getConversationFolder({
        userId: selfID,
        conversationID,
        folderId: item.fileId,
      });
      const arr = (data.items || []).map((it: any) => {
        it.isLeaf = it.fileType !== docTypeEnum.FOLDER;
        it.key = `${item.fileId}_${it.fileId}_${it.fileType}_${item.depth + 1}`;
        it.rowKey = `${item.fileId}_${it.fileType}${
          item.isShortCut === 1 ? '_shortcut' : ''
        }`;
        it.depth = item.depth + 1;
        return it;
      });
      if (arr.length > 0) {
        const nextData: any = [...updateTreeData(dataSource, key, arr)];
        setDataSource(nextData);
      }
      resolve();
    });
  };

  const onInputChange = (e: any) => {
    if (e.type === 'compositionstart') {
      setLock(true);
      return;
    }
    setValue(e.target.value);
    if (e.type === 'compositionend') {
      setValue(e.target.value);
      setLock(false);
    }
    if (!lock) {
      setValue(e.target.value);
    }
    runSearch(e.target.value.trim());
  };

  const selectDoc = (val: any) => {
    const documentType = val.documentType || val.fileType;
    const documentName = val.documentName || val.fileName;
    const documentId = val.documentId || val.fileId;
    onChange({
      documentType,
      documentName,
      documentId,
      createdBy: val?.createdBy || '',
      createdTime: val?.createdTime || undefined,
      fileCrdcId: val?.fileCrdcId,
      isShortCut: val.isShortCut || 2,
      originFileId: val.originFileId || undefined,
    });
  };

  return (
    <div className={styles.cloudDocSelectWarp}>
      <div className={classNames(styles.searchBox, isFocus && styles.focus)}>
        <div className={styles.searchIcon}>
          <img src={searchIcon} />
        </div>
        <Input
          value={value}
          placeholder="搜索"
          onChange={onInputChange}
          onCompositionStart={onInputChange}
          onCompositionEnd={onInputChange}
          onBlur={() => setIsFocus(false)}
          onFocus={() => setIsFocus(true)}
          style={{ width: '270px', border: 'none' }}
          allowClear={{
            clearIcon: <img src={clearIcon} />,
          }}
        />
      </div>
      <div>
        <Spin spinning={loading} style={{ height: '100%' }}>
          <div className={styles.cloudDocSelectContent}>
            {!loading && status === 'success' && dataSource.length > 0 ? (
              rednderType === 'doc' ? (
                <TreeView
                  dataSource={dataSource}
                  loadTreeData={loadTreeData}
                  onSelect={selectDoc}
                />
              ) : (
                <SearchDocView dataSource={dataSource} onSelect={selectDoc} />
              )
            ) : (
              ''
            )}
            {!loading && dataSource.length === 0 ? EmptyComponent : ''}
          </div>
        </Spin>
      </div>
    </div>
  );
};
export default CloudDocSelect;
