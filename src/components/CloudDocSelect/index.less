.cloudDocSelectWarp {
  width: 334px;
  border-radius: 8px;
  border: 1px solid var(--primary-border-color);
  background-color: var(--primary-background-color-6);

  .searchBox {
    display: flex;
    height: 36px;
    margin: 12px 16px 0;
    border-radius: 8px;
    border: 1px solid var(--offline-border-color);
    overflow: hidden;
    .searchIcon {
      display: flex;
      align-items: center;
      height: 100%;
      padding: 0 0 0 12px;
      > img {
        width: 16px;
        height: 16px;
      }
    }
  }
  .focus {
    border: 1px solid var(--file-backgroud-color-1);
    box-shadow: 0 0 4px 1px var(--box-shadow-color);
  }
  .cloudDocSelectContent {
    height: 400px;
    padding: 8px 6px;
    overflow: auto;
    .emptyBox {
      width: 100%;
      height: 100%;
      text-align: center;
      font-weight: 400;
      font-size: 14px;
      color: var(--primary-text-color-3);
      line-height: 20px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      > img {
        margin: 0 0 10px;
      }
    }
  }
}
