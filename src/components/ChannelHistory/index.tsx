import { useEffect, useState, useCallback } from 'react';
import { isEmpty, throttle } from 'lodash';
import withScrollBar from '@/components/withScrollBar';
import ChannelHistoryHeader from './components/ChannelHistoryHeader';
import ChannelHistorySearch from './components/ChannelHistorySearch';
import MessageList from './components/MessageList';
import CloudList from './components/MessageList/OtherList';
import styles from './index.less';

export type ChannelHistoryHeaderTabType =
  | 'message'
  | 'file'
  | 'picture'
  | 'docs';

const ChannelHistory = () => {
  const [currentTab, setCurrentTab] =
    useState<ChannelHistoryHeaderTabType>('message');
  const [searchFilterParams, setSearchFilterParams] = useState({
    searchTimePosition: 0,
    searchTimePeriod: 0,
    customizeDate: [],
    dateSelect: {},
    searchValue: '',
  });

  const onSearchFilterParamsChange = (params: any) => {
    setSearchFilterParams((pre) => {
      return {
        ...pre,
        ...params,
      };
    });
  };

  const onInputChangeThrottle = useCallback(
    throttle(
      (val) => {
        onSearchFilterParamsChange({
          searchValue: val,
        });
      },
      500,
      { leading: false, trailing: true }
    ),
    []
  );

  return (
    <div className={styles.channelHistoryWrapper}>
      <ChannelHistoryHeader
        curTab={currentTab}
        onChange={(val: ChannelHistoryHeaderTabType) => {
          onSearchFilterParamsChange({
            searchTimePosition: 0,
            searchTimePeriod: 0,
            customizeDate: [],
            dateSelect: {},
            searchValue: '',
          });
          setCurrentTab(val);
        }}
      />
      <ChannelHistorySearch
        onInputChange={(val) => {
          onInputChangeThrottle(val);
        }}
        searchFilterParams={searchFilterParams}
        onSearchFilterParamsChange={onSearchFilterParamsChange}
        currentTab={currentTab}
      />
      {currentTab === 'message' ? (
        <MessageList
          searchFilterParams={searchFilterParams}
          currentTab={currentTab}
        />
      ) : (
        <CloudList
          searchFilterParams={searchFilterParams}
          currentTab={currentTab}
        />
      )}
    </div>
  );
};

// export default withScrollBar(ChannelHistory, {
//   domId: 'drag-scroll-bar-right-channelHistory',
//   direction: 'right',
//   defaultWidth: '560px',
//   dragBarClassName: 'scrollbarRight',
//   needLocalStorageWidth: false,
// });

export default ChannelHistory;
