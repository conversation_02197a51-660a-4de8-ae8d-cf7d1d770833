import classNames from 'classnames';
import { useConversationStore } from '@/store';
import deleteIcon from '@/assets/contact/deleteIcon.svg';
import RightAreaBackBtn from '@/components/RightAreaBackBtn';
import { ChannelHistoryHeaderTabType } from '../..';
import styles from './index.less';

interface tabItemProps {
  key: ChannelHistoryHeaderTabType;
  title: string;
}

const HistoryHeaderTab: tabItemProps[] = [
  {
    key: 'message',
    title: '消息',
  },
  {
    key: 'docs',
    title: '云文档',
  },
  {
    key: 'file',
    title: '文件',
  },
  {
    key: 'picture',
    title: '图片',
  },
];

interface Props {
  curTab: ChannelHistoryHeaderTabType;
  onChange?: (tab: ChannelHistoryHeaderTabType) => void;
}

const ChannelHistoryHeader: React.FC<Props> = (props) => {
  const { curTab, onChange } = props;
  const changeRightArea = useConversationStore(
    (state) => state.changeRightArea
  );
  const tabClikc = (type: ChannelHistoryHeaderTabType) => {
    onChange?.(type);
  };

  return (
    <div className={styles.channelHistoryHeaderWrapper}>
      <div className={styles.basicInfoWarp}>
        <RightAreaBackBtn />
        <div className={styles.headerTextWrap}>聊天记录</div>
        <div
          className={styles.deleteIcon}
          onClick={() => changeRightArea('CLEAR_RIGHT_AREA')}
        >
          <img src={deleteIcon}></img>
        </div>
      </div>
      <div className={styles.tabwarp}>
        {HistoryHeaderTab.map((item) => {
          return (
            <div
              key={item.key}
              className={classNames(
                styles.tabItem,
                curTab === item.key && styles.active
              )}
              onClick={() => tabClikc(item.key)}
            >
              <span>{item.title}</span>
            </div>
          );
        })}
      </div>
    </div>
  );
};
export default ChannelHistoryHeader;
