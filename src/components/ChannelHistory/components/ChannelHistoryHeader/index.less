.channelHistoryHeaderWrapper {
  // border-bottom: 1px solid rgba(0, 0, 0, 5%);
  border-radius: 8px 8px 0 0;
  z-index: 1;

  .basicInfoWarp {
    padding: 16px 16px 0 20px;
    font-size: 18px;
    font-weight: 600;
    font-family: PingFangSC-Regular;
    color: #2f3035;
    line-height: 28px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .headerTextWrap {
      flex: 1;
      color: var(--primary-text-color-1);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .rightAreaBack {
      margin-right: 6px;
      cursor: pointer;
      &:hover {
        border-radius: 4px;
        background: var(--msg-qute-backgroud-color);
      }
    }

    .deleteIcon {
      width: 28px;
      height: 28px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      margin-left: 8px;

      > img {
        width: 14px;
      }
      &:hover {
        background: var(--msg-qute-backgroud-color);
      }
    }
  }

  .tabwarp {
    width: 100%;
    overflow-x: auto;
    display: flex;
    align-items: center;
    padding: 24px 20px 9px;
    border-bottom: 1px solid #dddee0;

    .tabItem {
      flex-shrink: 0;
      font-size: 14px;
      font-weight: 400;
      font-family: PingFangSC-Regular;
      color: var(--primary-text-color-1);
      cursor: pointer;
      position: relative;
      // padding: 0 16px;
      // border-radius: 6px;
      line-height: 22px;
      margin-right: 42px;
      // height: 36px;
      display: flex;
      align-items: center;
    }

    .tabItem:hover {
      cursor: pointer;
    }

    .active {
      color: #0074e2 !important;
      font-weight: 600;
      position: relative;

      &::after {
        width: 100%;
        height: 2px;
        content: "";
        display: block;
        background: #0074e2;
        position: absolute;
        bottom: -9px;
        border-radius: 6px;
      }
    }
  }
}
