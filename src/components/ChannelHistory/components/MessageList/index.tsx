/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-lines */
/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useState, useCallback, FC } from 'react';
import { useLatest, useRequest } from 'ahooks';
import dayjs from 'dayjs';
import classNames from 'classnames';
import { Empty } from '@ht/sprite-ui';
import { throttle, isEmpty, reverse, cloneDeep } from 'lodash';
import { Virtuoso } from '@ht/react-virtuoso';
import emitter from '@/utils/events';
import MessageItem from '@/components/Channel/components/MessageItem/index';
import {
  MessageItem as MessageItemType,
  MessageType,
} from '@ht/openim-wasm-client-sdk';
import { useConversationStore } from '@/store';
import { IMSDK } from '@/layouts/BasicLayout';
import { isSameDate } from '@/utils/date';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import searchEmpty from '@/assets/images/searchModal/searchEmpty.png';
import { ChannelHistoryHeaderTabType } from '../..';
import styles from './index.less';

const START_INDEX = 10000;
const SPLIT_COUNT = 50;
const Count = 80;

const MessageTypeList = [
  MessageType.TextMessage,
  MessageType.MergeMessage,
  MessageType.QuoteMessage,
  MessageType.FileMessage,
  MessageType.VideoMessage,
  MessageType.VoiceMessage,
  MessageType.AtTextMessage,
  MessageType.CustomMessage,
  MessageType.PictureMessage,
  MessageType.GroupAnnouncementUpdated,
];

interface MessageListProps {
  searchFilterParams: any;
  currentTab: ChannelHistoryHeaderTabType;
}

const MessageList: FC<MessageListProps> = ({
  searchFilterParams,
  currentTab,
}) => {
  const { currentConversation } = useConversationStore();
  const conversationID = currentConversation?.conversationID || '';

  const [loadState, setLoadState] = useState({
    initLoading: true,
    hasMoreOld: true,
    messageList: [] as MessageItemType[],
    firstItemIndex: START_INDEX,
    initialTopMostItemIndex: SPLIT_COUNT - 1,
    messagePage: 0,
    loading: true,
  });
  const latestLoadState = useLatest(loadState);

  const deleteOnewMessage = (clientMsgID: string) => {
    setLoadState((preState) => {
      const tmpList = [...preState.messageList];
      const idx = tmpList.findIndex((msg) => msg.clientMsgID === clientMsgID);
      if (idx < 0) {
        return preState;
      }
      tmpList.splice(idx, 1);

      return {
        ...preState,
        messageList: tmpList,
      };
    });
  };

  const updateOneMessage = (message: MessageItemType) => {
    if (
      (!isEmpty(searchFilterParams.searchValue) ||
        !isEmpty(searchFilterParams.dateSelect)) &&
      message.contentType === MessageType.RevokeMessage
    ) {
      deleteOnewMessage(message.clientMsgID);
    } else {
      setLoadState((preState) => {
        debugger;
        const tmpList = [...preState.messageList];
        const idx = tmpList.findIndex(
          (msg) => msg.clientMsgID === message.clientMsgID
        );
        if (idx < 0) {
          return preState;
        }
        const attachedInfoElem = {
          ...tmpList[idx].attachedInfoElem,
          ...message.attachedInfoElem,
        };
        const frontendExtension = tmpList[idx]?.frontendExtension || {};
        tmpList[idx] = {
          ...tmpList[idx],
          ...message,
          attachedInfoElem,
          frontendExtension,
        };
        if (
          tmpList[idx].frontendExtension &&
          tmpList[idx].frontendExtension.data === 'date-separator'
        ) {
          const currentDate = new Date(tmpList[idx].sendTime);
          tmpList[idx].frontendExtension.description = isSameDate(
            currentDate,
            new Date()
          )
            ? '今天'
            : dayjs(currentDate).format('YYYY/MM/DD');
        }
        if (message.contentType === MessageType.RevokeMessage) {
          const newtmpList = tmpList.map((item) => {
            let data = cloneDeep(item);
            if (item.contentType === MessageType.QuoteMessage) {
              const quoteMessage = item.quoteElem?.quoteMessage;
              if (quoteMessage?.clientMsgID === message.clientMsgID) {
                const newQuoteMessage = {
                  ...quoteMessage,
                  contentType: MessageType.RevokeMessage,
                };
                data = {
                  ...item,
                  quoteElem: {
                    quoteMessage: newQuoteMessage,
                    text: item.quoteElem?.text || '',
                  },
                };
              }
            }
            return data;
          });
          return {
            ...preState,
            messageList: newtmpList,
          };
        } else {
          return {
            ...preState,
            messageList: tmpList,
          };
        }
      });
    }
  };

  useEffect(() => {
    if (conversationID) {
      emitter.on('DELETE_ONE_MSG', deleteOnewMessage);
      emitter.on('UPDATE_ONE_MSG', updateOneMessage);
      getMoreOldMessages(false, 0);
    }
    return () => {
      emitter.off('DELETE_ONE_MSG', deleteOnewMessage);
      emitter.off('UPDATE_ONE_MSG', updateOneMessage);
    };
  }, [conversationID, searchFilterParams]);

  const { loading: moreOldLoading, runAsync: getMoreOldMessages } = useRequest(
    async (loadMore = true, page = 0) => {
      if (!loadMore) {
        setLoadState((pre) => {
          return {
            ...pre,
            loading: true,
            initLoading: true,
          };
        });
      } else {
        setLoadState((pre) => {
          return {
            ...pre,
            loading: true,
          };
        });
      }
      try {
        let isEnd = false;
        let resultItems: any = [];
        if (
          !isEmpty(searchFilterParams.searchValue) ||
          !isEmpty(searchFilterParams.dateSelect)
        ) {
          const params = {
            conversationIDs: [conversationID],
            keywordList: isEmpty(searchFilterParams.searchValue)
              ? []
              : [searchFilterParams.searchValue],
            keywordListMatchType: 0,
            messageTypeList: MessageTypeList,
            searchTimePosition: searchFilterParams.searchTimePosition,
            searchTimePeriod: searchFilterParams.searchTimePeriod,
            pageIndex: page,
            count: Count,
            sort: [
              {
                field: 'send_time',
                ascending: false,
              },
            ],
          };
          const { data } = await IMSDK.searchServerMsg(params);

          resultItems = data.searchResultItems
            ? reverse(data.searchResultItems)
            : [];
          isEnd = resultItems.length < Count;
        } else {
          const { data } = await IMSDK.getAdvancedHistoryMessageList({
            count: SPLIT_COUNT,
            startClientMsgID: loadMore
              ? latestLoadState.current.messageList[0]?.clientMsgID
              : '',
            conversationID: conversationID ?? '',
            viewType: 0,
          });
          isEnd = data.isEnd || false;
          resultItems = data.messageList || [];
        }

        const needShowList = resultItems?.map((item, index, array) => {
          if (index >= 1) {
            const itemObj = { ...item };
            if (
              !isSameDate(
                new Date(item.sendTime),
                new Date(array[index - 1].sendTime)
              )
            ) {
              const currentDate = new Date(item.sendTime);
              itemObj.frontendExtension = {
                data: 'date-separator',
                description: isSameDate(currentDate, new Date())
                  ? '今天'
                  : dayjs(currentDate).format('YYYY/MM/DD'),
                extension: '',
              };
            }
            return itemObj;
          } else {
            const itemObj = { ...item };
            const currentDate = new Date(item.sendTime);
            itemObj.frontendExtension = {
              data: 'date-separator',
              description: isSameDate(currentDate, new Date())
                ? '今天'
                : dayjs(currentDate).format('YYYY/MM/DD'),
              extension: 'first-date-separator',
            };
            return itemObj;
          }
        });

        // 移除 setTimeout，直接更新状态
        setLoadState((preState) => {
          return {
            ...preState,
            initLoading: false,
            hasMoreOld: !isEnd,
            messageList: [
              ...needShowList,
              ...(loadMore ? preState.messageList : []),
            ],
            firstItemIndex: preState.firstItemIndex - needShowList.length,
            initialTopMostItemIndex: loadMore
              ? SPLIT_COUNT - 1
              : needShowList.length - 1,
            messagePage: page,
            loading: false,
          };
        });
      } catch (e) {
        setLoadState((preState) => ({
          ...preState,
          initLoading: false,
          hasMoreOld: false,
          messageList: [],
          firstItemIndex: 0,
          loading: false,
        }));
      }
    },
    {
      manual: true,
    }
  );

  const loadMoreMessage = useCallback(
    throttle(
      () => {
        if (!loadState.hasMoreOld || moreOldLoading) {
          return;
        }
        if (isEmpty(searchFilterParams.searchValue)) {
          getMoreOldMessages();
        } else {
          getMoreOldMessages(true, loadState.messagePage + 1);
        }
      },
      200,
      { leading: true, trailing: false }
    ),
    [loadState, moreOldLoading, searchFilterParams.searchValue]
  );

  const itemContentWrap = useCallback(
    (index: number, msg: MessageItemType) => {
      try {
        return (
          <div className={classNames(styles.listItemBorder)}>
            <MessageItem
              hasMoreMessageBefore={loadState.hasMoreOld}
              messageIndex={loadState.messageList?.findIndex(
                (item) => item.clientMsgID === msg.clientMsgID
              )}
              clientMsgId={msg.clientMsgID}
              sendID={msg.sendID}
              key={msg.clientMsgID}
              message={msg}
              isSender={false}
              isThread={false}
              inRightThread={false}
              inHistoryList={true}
              isSearch={true}
              searchValue={searchFilterParams.searchValue}
              conversationID={conversationID}
              historyTab={currentTab}
              hasForwardBtn={true}
            />
          </div>
        );
      } catch (e) {
        console.error('渲染消息报错', e);
        return <></>;
      }
    },
    [loadState.hasMoreOld, loadState.messageList]
  );

  return (
    <div className={styles.messageListWrapper}>
      {loadState.initLoading || loadState.loading ? (
        <div
          style={{
            position: 'absolute',
            width: '100%',
            display: 'flex',
            justifyContent: 'center',
            padding: '5px 0',
          }}
        >
          <LoadingSpinner />
        </div>
      ) : (
        <Virtuoso
          style={{
            position: 'absolute',
            width: '100%',
          }}
          id={`${conversationID}_history`}
          className={styles.virtuosoListContainer}
          followOutput={(isAtBottom) => {
            if (document.hidden || !isAtBottom) {
              return false;
            }
            return 'smooth';
          }}
          firstItemIndex={loadState.firstItemIndex}
          initialTopMostItemIndex={{
            index: loadState.initialTopMostItemIndex,
            align: 'end',
          }}
          startReached={() => {
            if (loadState.initLoading || !loadState.hasMoreOld) {
              return;
            }
            loadMoreMessage();
          }}
          //   ref={virtuoso}
          data={loadState.messageList}
          // computeItemKey={(_, item) => item.clientMsgID}
          increaseViewportBy={{ top: 1500, bottom: 1200 }}
          defaultItemHeight={400}
          itemContent={itemContentWrap}
          components={{
            Header: () => {
              if (loadState.hasMoreOld) {
                return (
                  <div
                    style={{
                      position: 'fixed',
                      left: '50%',
                      display: 'flex',
                      justifyContent: 'center',
                      padding: '5px 0',
                      visibility: loadState.hasMoreOld ? 'visible' : 'hidden',
                    }}
                  >
                    <LoadingSpinner />
                  </div>
                );
              } else {
                return <></>;
              }
            },
            EmptyPlaceholder: () => {
              return (
                <Empty
                  image={<img src={searchEmpty} />}
                  description={<div>暂无相关聊天记录</div>}
                />
              );
            },
          }}
        />
      )}
    </div>
  );
};

export default MessageList;
