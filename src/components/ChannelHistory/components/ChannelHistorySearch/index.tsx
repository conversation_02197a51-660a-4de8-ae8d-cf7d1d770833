import { useState, useMemo, useEffect } from 'react';
import moment from 'moment';
import { isEmpty } from 'lodash';
import { Input, Dropdown } from '@ht/sprite-ui';
import CalendarSelectMenu from '@/pages/search/components/SearchFilter/CalendarSelectMenu';
import deleteIcon from '@/assets/contact/deleteIcon.png';
import searchIcon from '@/assets/channelHistory/searchIcon.png';
import FilterSelect from '@/pages/search/components/SearchFilter/FilterSelect';
import { ChannelHistoryHeaderTabType } from '../..';
import styles from './index.less';

interface Props {
  onInputChange: (val: string) => void;
  onSearchFilterParamsChange: (params: any) => void;
  searchFilterParams: any;
  currentTab: ChannelHistoryHeaderTabType;
}

const ChannelHistorySearch: React.FC<Props> = (props) => {
  const {
    onInputChange,
    onSearchFilterParamsChange,
    searchFilterParams,
    currentTab,
  } = props;
  const [inputvalue, setInputValue] = useState<any>();
  const [lock, setLock] = useState<boolean>(false);
  const [dateOpen, setDateOpen] = useState(false);

  const handleInputChange = (e: any) => {
    if (e.type === 'compositionstart') {
      setLock(true);
      return;
    }
    setInputValue(e.target.value.trim());
    if (e.type === 'compositionend') {
      onInputChange(e.target.value.trim());
      setLock(false);
    }
    if (!lock) {
      onInputChange(e.target.value.trim());
    }
  };

  const getPopupContainer = useMemo(() => {
    return document.getElementById('channelHistorySearchId') || document.body;
  }, []);

  useEffect(() => {
    setInputValue('');
    setDateOpen(false);
    setLock(false);
  }, [currentTab]);

  const renderFilterTitle = (type: string) => {
    let title = '';
    const { customizeDate = [], dateSelect = {} } = searchFilterParams;
    let timeStr = dateSelect.label;
    if (dateSelect.key === 'customizeTime') {
      timeStr = `${moment(customizeDate[0]).format('YYYY/M/D')} ~ ${moment(
        customizeDate[1]
      ).format('YYYY/M/D')}`;
    }

    switch (type) {
      case 'dateSelect':
        title = isEmpty(searchFilterParams[type]) ? '日期' : timeStr;
        break;
      default:
        break;
    }
    return title;
  };

  return (
    <div
      className={styles.channelHistorySearchWrapper}
      id="channelHistorySearchId"
    >
      {currentTab !== 'picture' && (
        <div className={styles.searchInputWrapper}>
          <div className={styles.iconContainer}>
            <img src={searchIcon} />
          </div>
          <Input
            value={inputvalue}
            onChange={handleInputChange}
            placeholder={'搜索'}
            style={{ width: '100%' }}
            className={styles.searchInput}
          />
          <div
            className={styles.deleteIcon}
            onClick={(e) => {
              e.stopPropagation();
              setInputValue('');
              onInputChange('');
            }}
          >
            {inputvalue != null && inputvalue !== '' && (
              <img src={deleteIcon}></img>
            )}
          </div>
        </div>
      )}

      <Dropdown
        trigger={['click']}
        getPopupContainer={() => getPopupContainer}
        open={dateOpen}
        onOpenChange={(open) => {
          setDateOpen(open);
        }}
        placement="bottomLeft"
        overlayStyle={{ width: 'fit-content', minWidth: 'fit-content' }}
        overlay={
          dateOpen ? (
            <CalendarSelectMenu
              handleClose={() => {
                setDateOpen(false);
              }}
              onSearchFilterParamsChange={onSearchFilterParamsChange}
              searchFilterParams={searchFilterParams}
            />
          ) : (
            <></>
          )
        }
      >
        <div
          onClick={() => {
            setDateOpen(true);
          }}
        >
          <FilterSelect
            filterTitle={renderFilterTitle('dateSelect')}
            hasSelectData={!isEmpty(searchFilterParams.dateSelect)}
            isOpen={dateOpen}
            type="calendar"
            wrapperClassName="history_filterSelect"
            wrapperStyle={{ width: '130px', color: 'red' }}
            onClear={() => {
              onSearchFilterParamsChange({
                dateSelect: '',
                searchTimePosition: 0,
                searchTimePeriod: 0,
                customizeDate: [],
              });
            }}
          />
        </div>
      </Dropdown>
    </div>
  );
};
export default ChannelHistorySearch;
