.channelHistorySearchWrapper {
  padding: 12px 20px 10px;

  .searchInputWrapper {
    position: relative;
    margin-bottom: 8px;

    .iconContainer {
      position: absolute;
      left: 16px;
      top: 0;
      z-index: 1;
      height: 32px;
      display: flex;
      align-items: center;
    }

    .searchInput {
      height: 32px;
      background: #ffffff;
      border-radius: 8px;
      border: 1px solid #dddee0;
      padding: 0 42px;
    }

    .deleteIcon {
      position: absolute;
      right: 16px;
      top: 0;
      height: 32px;
      z-index: 1;
      display: flex;
      align-items: center;
      cursor: pointer;
    }

    :global {
      .linkflow-input:focus,
      .linkflow-input-focused {
        border: 1px solid var(--primary-text-color-9) !important;
        box-shadow: 0 0 0 2px var(--box-shadow-color);
      }
      .linkflow-input::placeholder {
        color: #999ba0;
      }
    }
  }

  .searchDateWrapper {
    padding: 0 17px;
    height: 32px;
    width: 220px;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid var(--primary-border-color);
    display: flex;
    align-items: center;

    &:hover {
      cursor: pointer;
    }

    .dateIcon {
      margin-right: 8px;
    }

    .filterName {
      font-size: 14px;
      font-weight: 400;
      color: #999ba0;
      line-height: 20px;
      margin-right: 10px;
      word-break: keep-all;
      white-space: nowrap;
    }

    > span {
      flex: 1;
    }

    .closeIcon {
      transform: rotate(180deg);
    }
  }

  .searchDateWrapperOpen {
    border: 1px solid var(--primary-text-color-9);
  }
}

.filterTitleTooltip {
  :global {
    .linkflow-tooltip-inner {
      padding: 7px 16px;
      border-radius: 8px;
      box-shadow: 0 6px 16px 0 rgba(29, 34, 44, 8%),
        0 3px 6px -4px rgba(29, 34, 44, 12%);
      font-size: 14px;
      font-weight: 400;
      color: #ffffff;
      line-height: 20px;
    }
    .linkflow-tooltip-arrow {
      bottom: 1px;
    }
  }
}
