import { useEffect, useState } from 'react';
import { getCurrentWindow } from '@tauri-apps/api/window';
import closeIcon from '@/assets/windowClose.svg';
import max from '@/assets/maximize.svg';
import unMax from '@/assets/unMaximize.svg';
import min from '@/assets/minimize.svg';
import styles from './index.less';

const TitleBar = () => {
  const [isMaximize, setIsMaximize] = useState<boolean>(false);
  const browserWindow = window?.htElectronSDK?.BrowserWindow;
  const currentWindowId = browserWindow?.getCurrentWindowId() || 1;
  const mainWindowId = browserWindow?.getMainWindowId() || 1;

  useEffect(() => {
    if (!currentWindowId) {
      return;
    }
    function getBrowserState() {
      const state = browserWindow?.getBrowserWindowState(currentWindowId);
      console.warn('窗口大小变化', state);
      const isMaximized = state?.isMaximized || false;
      setIsMaximize(isMaximized);
    }
    window.addEventListener('resize', getBrowserState);
    return () => {
      window.removeEventListener('resize', getBrowserState);
    };
  }, [currentWindowId]);

  return (
    <div className={styles.titlebar}>
      <div
        onClick={() => {
          browserWindow?.min(currentWindowId);
        }}
        className={styles.titlebarbutton}
      >
        <img src={min} alt="minimize" />
      </div>
      <div
        onClick={() => {
          browserWindow?.toggleMaximize(currentWindowId);
        }}
        className={styles.titlebarbutton}
      >
        <img src={isMaximize ? unMax : max} alt="maximize" />
      </div>
      <div
        onClick={() => {
          if (mainWindowId === currentWindowId) {
            browserWindow?.hide(currentWindowId);
          } else {
            browserWindow?.close(currentWindowId);
          }
        }}
        className={styles.titlebarbutton}
      >
        <img src={closeIcon} alt="close" />
      </div>
    </div>
  );
};

export default TitleBar;
