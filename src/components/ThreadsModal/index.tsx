import { useState, useRef, useEffect } from 'react';
import { Input, Modal } from '@ht/sprite-ui';
import { SearchOutlined } from '@ht-icons/sprite-ui-react';
import { ThreadVO } from '@ht/openim-wasm-client-sdk';
import { debounce } from 'lodash';
import { IMSDK } from '@/layouts/BasicLayout';
import deleteIcon from '@/assets/contact/deleteIcon.png';
import offlineIcon from '@/assets/images/searchModal/offlineIcon.png';
import { useUserStore } from '@/store';
import styles from './index.less';
import ThreadList from './threadList';

interface Props {
  open: boolean;
  handleCancel: () => void;
  groupId: string;
}

const PAGE_SIZE = 10;

const ThreadsModal: React.FC<Props> = ({ open, handleCancel, groupId }) => {
  const inputRef = useRef<any>();
  const [searchValue, setSearchValue] = useState('');

  const [joinThreads, setJoinThreads] = useState<ThreadVO[]>();
  const [notJoinThreads, setNotJoinThreads] = useState<ThreadVO[]>();
  const [distantThreads, setDistantThreads] = useState<ThreadVO[]>();
  const [lock, setLock] = useState<boolean>(false);
  const [page, setPage] = useState(0);
  const [totalNum, setTotalNum] = useState(0);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);

  useEffect(() => {
    if (open && inputRef.current) {
      inputRef?.current?.focus();
    }
  }, [open]);

  const fetchData = async (pageNo = page + 1, val = searchValue) => {
    setLoading(true);
    try {
      const res = await IMSDK.getGroupThreadPage({
        groupId,
        key: val,
        page: pageNo,
        pageSize: PAGE_SIZE,
      });

      setJoinThreads([...res.data?.join]);
      setNotJoinThreads([...res.data?.notJoin]);
      setDistantThreads((prev) =>
        prev && pageNo > 1 ? [...prev, ...res.data.distant] : res.data.distant
      );

      setHasMore(res.data?.pagination?.hasNext);
      //   setHasMore(true);
      setTotalNum(res.data?.pagination.total);
      setPage(res.data?.pagination.pageNumber);
      setLoading(false);
    } catch (e) {
      console.error('查询群组内 thread 信息失败，原因为：', e);
    } finally {
      setLoading(false);
    }
  };

  const debounceFetch = debounce(fetchData, 300);

  const initData = (value: string) => {
    setJoinThreads([]);
    setNotJoinThreads([]);
    setDistantThreads([]);
    setPage(0);
    setHasMore(true);
    debounceFetch(1, value);
  };
  useEffect(() => {
    if (groupId) {
      initData('');
    }
  }, [groupId]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.type === 'compositionstart') {
      setLock(true);
      return;
    }
    setSearchValue(e.target.value.trim());
    if (e.type === 'compositionend') {
      setSearchValue(e.target.value.trim());
      setLock(false);
    }
    if (!lock) {
      setSearchValue(e.target.value.trim());
    }

    setPage(0);
    setHasMore(true);
    debounceFetch(1, e.target.value.trim());
  };

  const { syncState, connectState } = useUserStore();
  const showConnecting =
    syncState === 'success' &&
    (connectState === 'loading' || connectState === 'failed');

  return (
    <Modal
      open={true}
      footer={null}
      closable={false}
      className={styles.modalContainer}
      onCancel={() => handleCancel()}
      mask={false}
      maskClosable={true}
      width={600}
    >
      <div className={styles.searchInputAndClose}>
        <div className={styles.title}>子群</div>
        <div className={styles.line}></div>
        <Input
          ref={inputRef}
          value={searchValue}
          onChange={handleInputChange}
          placeholder="搜索子群名"
          className={styles.input}
          allowClear={true}
          suffix={<SearchOutlined />}
        />
        {/* <div className={styles.searchBtn}>搜索</div> */}
        <div className={styles.deleteIcon} onClick={() => handleCancel()}>
          <img src={deleteIcon}></img>
        </div>
      </div>

      <div
        id="modalContent"
        className={styles.modalContent}
        style={{ overflowY: 'auto', maxHeight: 54 * PAGE_SIZE }}
      >
        <ThreadList
          threadList={joinThreads}
          needJoinThread={false}
          title={'加入的子群'}
          handleCancel={() => handleCancel()}
        />

        <ThreadList
          threadList={notJoinThreads}
          title={'其他活跃的子群'}
          handleCancel={() => handleCancel()}
        />

        <ThreadList
          threadList={distantThreads}
          totalNum={totalNum}
          title={'远期的子群'}
          loading={loading}
          handleCancel={() => handleCancel()}
          getSearchData={() => debounceFetch(page + 1)}
          hasMore={hasMore}
          scrollableTarget={'modalContent'}
        />
      </div>

      {showConnecting && (
        <div className={styles.netErrorWarning}>
          <img src={offlineIcon} />
          <span>当前处于离线状态，搜索结果可能不完整。</span>
        </div>
      )}
    </Modal>
  );
};

export default ThreadsModal;
