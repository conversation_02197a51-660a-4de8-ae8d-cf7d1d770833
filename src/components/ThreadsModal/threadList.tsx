import { MessageItem, SessionType, ThreadVO } from '@ht/openim-wasm-client-sdk';
import { Spin, Typography } from '@ht/sprite-ui';
import { formatMessageByType, getShowDiffTime } from '@/utils/utils';
import InfiniteScroll from 'react-infinite-scroll-component';
import { useUserStore } from '@/store';
import { useConversationToggle } from '@/hooks/useConversationToggle';
import useThreadState from '@/hooks/useThreadState';
import styles from './index.less';
import OIMAvatar from '../OIMAvatar';

interface ThreadListProp {
  title: string;
  totalNum?: number;
  threadList?: ThreadVO[];
  getSearchData?: () => void;
  hasMore?: boolean;
  needJoinThread?: boolean;
  handleCancel?: () => void;
  scrollableTarget?: string;
  loading?: boolean;
}
const ThreadList = ({
  threadList,
  totalNum,
  title,
  getSearchData,
  hasMore,
  needJoinThread = true,
  handleCancel,
  scrollableTarget,
  loading = false,
}: ThreadListProp) => {
  const selfID = useUserStore((state) => state.selfInfo.userID);
  const { joinThread } = useThreadState();
  const { toSpecifiedConversation } = useConversationToggle();
  const handleCreateConversation = async (threadId: string) => {
    try {
      if (needJoinThread) {
        const result = await joinThread(threadId);
        if (!result) {
          throw new Error(threadId);
        }
      }
      if (handleCancel != null) {
        handleCancel();
      }

      toSpecifiedConversation({
        sourceID: threadId ?? '',
        sessionType: SessionType.Group,
      });
    } catch (e) {}
  };
  const renderThreadList = () => {
    return (
      <div>
        {threadList?.map((item) => {
          try {
            const messageDetail = JSON.parse(
              item?.lastMessage || '{}'
            ) as MessageItem;

            const messageText =
              item?.lastMessage === ''
                ? ''
                : formatMessageByType(messageDetail, selfID);

            return (
              <div
                key={item?.info?.groupID}
                className={styles.resultItem}
                onClick={() => {
                  handleCreateConversation(item?.info?.groupID);
                }}
              >
                <div className={styles.name}>{item?.info?.groupName}</div>
                {messageText && (
                  <div className={styles.message}>
                    <OIMAvatar
                      userID={messageDetail?.sendID || ''}
                      size={24}
                      hideOnlineStatus={true}
                    />

                    <div className={styles.messageInfo}>
                      <Typography.Text
                        ellipsis={true}
                        strong={false}
                        title={`${
                          messageDetail?.senderNickname
                            ? `${messageDetail?.senderNickname}：${messageText}`
                            : messageText
                        }`}
                      >
                        {`${
                          messageDetail?.senderNickname
                            ? `${messageDetail?.senderNickname}：${messageText}`
                            : messageText
                        }`}
                      </Typography.Text>
                    </div>
                    <div className={styles.time}>
                      {getShowDiffTime(messageDetail?.sendTime)}
                    </div>
                  </div>
                )}
              </div>
            );
          } catch (e) {
            return null;
          }
        })}
      </div>
    );
  };

  return (
    <div className={styles.contentArea}>
      <div className={styles.areaTitle}>
        {totalNum != null && totalNum > 0 && `${totalNum}个`}
        {totalNum != null && totalNum === 0 && `无`}
        {totalNum == null &&
          threadList != null &&
          threadList?.length > 0 &&
          `${threadList?.length}个`}
        {totalNum == null &&
          (threadList == null || threadList?.length === 0) &&
          `无`}
        {title}
      </div>

      <Spin spinning={loading}>
        {hasMore != null ? (
          <InfiniteScroll
            dataLength={threadList?.length || 0}
            next={() => {
              if (getSearchData != null) {
                getSearchData();
              }
            }}
            hasMore={hasMore}
            loader={<span />}
            scrollableTarget={scrollableTarget}
            scrollThreshold={0.05}
          >
            {renderThreadList()}
          </InfiniteScroll>
        ) : (
          renderThreadList()
        )}
      </Spin>
    </div>
  );
};
export default ThreadList;
