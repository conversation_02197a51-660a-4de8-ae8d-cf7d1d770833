.modalContainer {
  height: min(85vh, 660px);
  width: 600px;
  border-radius: 8px;
  border: 1px solid var(--primary-background-color-5);
  display: flex;
  flex-direction: column;
  padding-bottom: 0;
  :global {

    .linkflow-modal-content {
      background: var(--primary-background-color-6);
      border-radius: 8px;
      height: 100%;
      overflow: hidden;
      box-shadow: none;
    }

    .linkflow-modal-body {
      padding: 0;
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .linkflow-tabs-tab.linkflow-tabs-tab-active .linkflow-tabs-tab-btn {
      color: var(--primary-color);
    }
    .linkflow-tabs-ink-bar {
      background-color: var(--primary-color);
    }

    .infinite-scroll-component {
      overflow-x: hidden !important;
      overflow-y: scroll !important;
    }
  }

  .searchInputAndClose {
    display: flex;
    align-items: center;
    width: 100%;
    height: 54px;
    background: var(--primary-background-color-8);
    border-bottom: 1px solid var(--primary-border-color);

    .title {
      font-size: 18px;
      font-weight: 600;
      color: var(--link-color-base-inv-pry);
      line-height: 24px;
      margin-left: 36px;
      padding-top: 15px;
      height: 54px;
    }

    .line {
      width: 1px;
      height: 30px;
      background: var(--primary-background-color-9);
      text-align: center;
      margin: 0 30px;
    }

    .input {
      flex: 1;
      height: 32px;
      background: var(--primary-background-color-6);
      border-radius: 5px;
      opacity: 0.52;
      border: 1px solid var(--primary-border-color);
    }

    .searchBtn {
      margin-left: 28px;
      width: 62px;
      height: 30px;
      background: var(--primary-background-color-4);
      border-radius: 2px;
      font-size: 14px;
      font-weight: 400;
      color: var(--primary-background-color-6);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }

    .deleteIcon {
      width: 50px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .modalContent {
    flex: 1;

    .contentArea {
      padding: 0 24px;

      .areaTitle {
        height: 56px;
        display: flex;
        justify-items: center;
        align-items: center;
        font-size: 14px;
        font-weight: 400;
        color: var(--primary-text-color-2);
      }
    }

    .resultItem {
      cursor: pointer;
      width: 552px;
      max-height: 90px;
      background: var(--primary-background-color-2);
      border-radius: 4px;
      overflow: hidden;
      margin-top: 12px;
      padding: 16px 24px;

      .name {
        font-size: 16px;
        font-weight: 600;
      }
      &:first-of-type {
        margin-top: 0;
      }
      .message {
        display: flex;
        align-items: center;
        margin-top: 14px;
        .messageInfo {
          margin-left: 12px;
          height: 20px;
          font-size: 14px;
          font-weight: 500;
          color: var(--primary-text-color-2);
          line-height: 20px;
          width: 330px;
        }

        .time {
          font-size: 14px;
          font-weight: 500;
          color: var(--offline-border-color);
          line-height: 20px;
          flex: 1;
          text-align: right;
        }
      }
    }

  }

  .netErrorWarning {
    display: flex;
    align-items: center;
    height: 39px;
    background: #f8f8f8;

    border-radius: 0 0 8px 8px;
    padding: 0 29px;
    img {
      width: 12px;
      height: 12px;
      margin-right: 4px;
    }

    span {
      font-size: 13px;
      font-weight: 400;
      color: #6c6f76;
      line-height: 18px;
    }
  }
}
