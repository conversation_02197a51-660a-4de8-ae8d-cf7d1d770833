import * as React from 'react';

import { useState, useEffect } from 'react';
import classNames from 'classnames';
import globalLoadingAnimation from '@/assets/loading.gif';
import { Carousel } from '@ht/sprite-ui';
import styles from './index.less';

interface GlobalLoadingProps {
  style?: any;
}

const GlobalLoading: React.FC<GlobalLoadingProps> = ({ style }) => {
  const [progress, setProgress] = useState(0);
  useEffect(() => {
    const timer = setInterval(() => {
      setProgress((progress) => {
        if (progress < 95) {
          return progress + 1;
        } else {
          clearInterval(timer);
          return 95;
        }
      });
    }, 50);
    return () => {
      clearInterval(timer);
    };
  });
  return (
    <div className={styles.globalLoading} style={style}>
      <div className={styles.loadingContainer}>
        <img src={globalLoadingAnimation} />
        <div className={styles.loadingText}>LOADING... {progress}%</div>
        {/* <h1>我们正在加载应用数据，请稍等，只有第一次登陆时需要此过程🍮</h1> */}

        <Carousel
          autoplay={true}
          dots={false}
          easing={'linear'}
          speed={1500}
          vertical={true}
          style={{ color: '6C6F76', fontSize: '16px', marginTop: '30px' }}
        >
          <h3>使用技巧：会话时输入&quot;/&quot;，唤起快捷指令 </h3>
          <h3>使用技巧：点击我的头像，添加工作状态😀 </h3>
          <h3>使用技巧：在消息里创建子群，有助于更高效的交流</h3>
          <h3>使用技巧：当你认同别人的发言，给出👍表示你的肯定</h3>
        </Carousel>
      </div>
    </div>
  );
};

export default GlobalLoading;
