import {
  ConversationItem,
  EmployeeItem,
  GroupMemberItem,
} from '@ht/openim-wasm-client-sdk';

export interface AddGroupModalProps {
  handleCancel: () => void;
  visible: boolean;
  groupMemberList?: GroupMemberItem[];
  currentConversation?: ConversationItem | undefined;
  type?: 'create' | 'update';
  afterConfirmBtnClick?: (prop?: any) => void;
}

export type UserItemType = Pick<EmployeeItem, 'employeeID'> & {
  employeeCode?: EmployeeItem['employeeCode'];
  employeeName?: EmployeeItem['employeeName'];
  faceURL?: EmployeeItem['faceURL'];
} & Partial<Pick<GroupMemberItem, 'roleLevel'>>;
