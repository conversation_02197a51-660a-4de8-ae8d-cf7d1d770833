.channelModal {
  height: min(85vh, 820px);
  :global {
    .linkflow-modal-content {
      background: #ffffff;
      border-radius: 8px;
      border: 1px solid #c6c8ca;
      height: 100%;
    }

    .linkflow-modal-body {
      padding: 24px;
      height: 100%;
    }

    .linkflow-tabs-tab.linkflow-tabs-tab-active .linkflow-tabs-tab-btn {
      color: var(--primary-color);
    }
    .linkflow-tabs-ink-bar {
      background-color: var(--primary-color);
    }
  }

  .modalContent {
    height: 100%;
    display: flex;
    flex-direction: column;

    .header {
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      .channelInput {
        // flex: 1;
        width: 306px;
        height: 34px;
        border-radius: 4px;
        margin-right: 16px;
      }

      .delectIcon {
        width: 16px;
        height: 16px;
        margin-left: 211px;
        cursor: pointer;
      }

      .icon {
        width: 24px;
        height: 24px;
      }
    }

    .searchMember {
      //   background-color: red;
      width: 100%;
      height: 40px;
      //   margin-bottom: 20px;
    }

    .addMember {
      // margin-bottom: 20px;
      height: 40px;
      display: flex;
      align-items: center;
      font-weight: bold;
    }

    .memberList {
      flex: 1;
      overflow: auto;
      .memberItem {
        display: flex;
        align-items: center;
        height: 40px;

        .name {
          height: 100%;
          display: flex;
          align-items: center;
          flex: 1;
          margin-left: 14px;
        }
        .tag {
          padding: 0 4px;
          height: 16px;
          border-radius: 4px;
          font-size: 11px;
          display: flex;
          align-items: center;
          margin-left: 12px;
          line-height: 16px;
        }

        .tag_3 {
          color: #cc8521;
          background: #fcf3e6;
          border: 1px solid #cc8521;
        }

        .code {
          margin-left: 8px;
          font-size: 14px;
          font-weight: 400;
          color: var(--primary-text-color-7);
        }
        img {
          width: 24px;
          height: 24px;
        }
        .role {
          display: inline-block;
          padding: 2px 4px;
          font-size: 12px;
          font-weight: 400;
          color: var(--mention-backgroup-color);
          border-radius: 4px;
          border: 1px solid var(--mention-backgroup-color);
          margin-left: 10px;
        }

        .moreOptions {
          cursor: pointer;
          width: 22px;
          height: 20px;
        }
      }
    }

    .newChannel {
      margin-top: 20px;
      text-align: right;
      cursor: pointer;
      font-size: 16px;
      font-weight: 400;
      color: #ffffff;
    }
  }
}
.option {
  display: flex;
  align-items: center;
  color: #3f434b;
  cursor: pointer;
  height: 32px;
  background: #ffffff;
  margin: 5px 13px 5px 11px;
  .memberInfo {
    display: flex;
    align-items: center;
    img {
      margin-right: 10px;
    }
  }
  > div {
    margin-left: 8px;
    flex: 1;
    text-align: left;
  }
  .plusIcon {
    width: 11px;
    height: 11px;
  }
}

.dropdownArea {
  width: 300px;
  // height: 84px;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #c6c8ca;

  .menuItem {
    height: 40px;
    line-height: 40px;
    padding-left: 16px;
  }
}
