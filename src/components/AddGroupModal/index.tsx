/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-lines */
import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { useHistory } from 'react-router-dom';
import _ from 'lodash';
import { Modal, Input, Dropdown, Menu, Button } from '@ht/sprite-ui';
import { useConversationStore, useUserStore } from '@/store';
import { Virtuoso } from '@ht/react-virtuoso';
import moreMenuIcon from '@/assets/contact/moreMenuIcon.png';
import {
  GroupVerificationType,
  GroupMemberRole,
} from '@ht/openim-wasm-client-sdk';
import { IMSDK } from '@/layouts/BasicLayout';
import { feedbackToast } from '@/utils/common';
import deleteIcon from '@/assets/contact/deleteIcon.png';
import SearchUser from '@/pages/contact/components/SearchUser';
import { useDeepCompareEffect } from 'ahooks';
import { isBotUser } from '@/utils/avatar';
import classNames from 'classnames';
import styles from './index.less';
import OIMAvatar from '../OIMAvatar';
import { AddGroupModalProps, UserItemType } from './type';

const AddGroupModal: React.FC<AddGroupModalProps> = ({
  handleCancel,
  visible,
  groupMemberList = [],
  currentConversation,
  afterConfirmBtnClick,
  type = 'create',
}) => {
  const { userID, employeeCode, nickname, faceURL } =
    useUserStore.getState().selfInfo;
  const [channelName, setChannelName] = useState('');

  const [users, setUsers] = useState<UserItemType[]>();
  const [groupID, setGroupID] = useState<string>('');
  const [changeRoleData, setChangeRoleData] = useState<UserItemType[]>([]);
  const updateCurrentConversation = useConversationStore(
    (state) => state.updateCurrentConversation
  );

  const conversationList = useConversationStore(
    (state) => state.conversationList
  );
  const currentMemberInGroup = useConversationStore(
    (state) => state.currentMemberInGroup
  );
  const navigate = useHistory();
  const isShowOperateBtn = useCallback(
    (member) => {
      if (member.roleLevel === GroupMemberRole.Owner) {
        return false;
      }
      if (
        type === 'create' ||
        (type === 'update' &&
          (currentMemberInGroup?.roleLevel === GroupMemberRole.Owner ||
            currentMemberInGroup?.roleLevel === GroupMemberRole.Admin))
      ) {
        return true;
      } else {
        return false;
      }
    },
    [type, currentMemberInGroup]
  );

  useDeepCompareEffect(() => {
    const initUserList = async () => {
      if (groupMemberList?.length > 0) {
        const userIDLlst = groupMemberList?.map((item) => item.userID);

        const { data: userInfoList } = await IMSDK.getUsersInfo(userIDLlst);

        const result =
          groupMemberList?.map((item) => {
            if (item.userID != null && item.userID !== '') {
              const userEmployeeCode =
                userInfoList?.filter(
                  (userInfo) => userInfo.userID === item.userID
                )?.[0] || {};
              return {
                ...item,
                employeeID: userEmployeeCode.userID,
                employeeCode: userEmployeeCode?.employeeCode,
                employeeName: userEmployeeCode?.nickname,
              };
            } else {
              return { ...item, employeeID: item.userID };
            }
          }) || [];

        setUsers(result);
      } else {
        // 当前用户默认为群主

        setUsers([
          {
            employeeCode,
            employeeName: nickname,
            employeeID: userID,
            faceURL,
            roleLevel: GroupMemberRole.Owner,
          },
        ]);
      }
    };

    initUserList();
  }, [groupMemberList, employeeCode, nickname, faceURL, type]);

  useEffect(() => {
    if (currentConversation?.showName) {
      setChannelName(currentConversation?.showName);
    }
  }, [currentConversation?.showName]);

  useEffect(() => {
    if (groupID) {
      const currentconversation = conversationList?.filter(
        (conversation) => conversation.groupID === groupID
      );
      if (currentconversation?.[0]) {
        setGroupID('');
        updateCurrentConversation(currentconversation[0]);
        navigate.push(`./chat`);
      }
    }
  }, [groupID, conversationList, updateCurrentConversation, navigate]);

  const getMenu = (user: UserItemType) => {
    // bot类型不展示按钮
    if (
      (type === 'create' ||
        currentMemberInGroup?.roleLevel === GroupMemberRole.Owner) &&
      !isBotUser(user.employeeID)
    ) {
      return (
        <Menu>
          <Menu.Item
            key="1"
            onClick={() =>
              setUserAsAdmin(
                user,
                user.roleLevel === GroupMemberRole.Admin
                  ? GroupMemberRole.Normal
                  : GroupMemberRole.Admin
              )
            }
            className={styles.menuItem}
          >
            {user.roleLevel === GroupMemberRole.Admin
              ? '取消管理权限'
              : '设为群组管理者'}
          </Menu.Item>
          <Menu.Item
            key="2"
            onClick={() => handleUserRemove(user)}
            className={styles.menuItem}
            style={{
              color: 'red',
            }}
          >
            移除群成员
          </Menu.Item>
        </Menu>
      );
    } else {
      return (
        <Menu>
          <Menu.Item
            key="1"
            onClick={() => handleUserRemove(user)}
            className={styles.menuItem}
            style={{
              color: 'red',
            }}
          >
            移除群成员
          </Menu.Item>
        </Menu>
      );
    }
  };

  const setUserAsAdmin = (user: UserItemType, memberRole: GroupMemberRole) => {
    const updatedUserList = users?.map((u) =>
      u.employeeID === user.employeeID ? { ...u, roleLevel: memberRole } : u
    );
    const index = changeRoleData?.findIndex(
      (u) => u.employeeID === user.employeeID
    );
    if (index > -1) {
      const data = _.clone(changeRoleData);
      data[index] = { ...user, roleLevel: memberRole };
      setChangeRoleData(data);
    } else {
      setChangeRoleData([
        ...changeRoleData,
        { ...user, roleLevel: memberRole },
      ]);
    }
    setUsers(updatedUserList);
  };

  const handleUserRemove = (user: UserItemType) => {
    const newUsers = users?.filter((u) => u.employeeID !== user.employeeID);
    if (newUsers != null && newUsers?.length > 0) {
      setUsers([...newUsers]);
    } else {
      setUsers(undefined);
    }
  };

  const handleAddUser = (user: UserItemType) => {
    if (user.employeeID === userID) {
      feedbackToast({ error: '已有当前成员', msg: '已有当前成员' });
      return;
    }
    const tempData =
      users?.filter((item) => item.employeeID === user.employeeID) || [];

    if (tempData?.length != null && tempData?.length !== 0) {
      feedbackToast({ error: '已有当前成员', msg: '已有当前成员' });
    } else {
      setUsers((prevUser) => {
        if (prevUser) {
          return [...prevUser, user];
        } else {
          return [user];
        }
      });
    }
  };

  const handleCreateGroup = async () => {
    if (channelName == null || channelName?.trim() === '') {
      feedbackToast({ error: '请输入群组名称', msg: '请输入群组名称' });
      return;
    }

    if (users == null || users?.length <= 1) {
      feedbackToast({
        error: '请至少选择一个其他人员',
        msg: '请至少选择一个其他人员',
      });
      return;
    }

    try {
      const { data: newGroupInfo } = await IMSDK.createGroup({
        groupInfo: {
          groupName: channelName,
          groupType: 2,
          needVerification: GroupVerificationType.AllNot, // 直接进群
        },
        memberUserIDs:
          users
            ?.filter(
              (user) =>
                user?.roleLevel !== GroupMemberRole.Admin &&
                user?.employeeID !== userID
            )
            ?.map((user) => user.employeeID) || [],
        adminUserIDs:
          users
            ?.filter(
              (user) =>
                user?.roleLevel === GroupMemberRole.Admin &&
                user?.employeeID !== userID
            )
            ?.map((user) => user.employeeID) || [],
      });

      feedbackToast({
        msg: '群聊创建成功',
      });

      if (afterConfirmBtnClick) {
        afterConfirmBtnClick(newGroupInfo);
      }
      handleCancel();
    } catch (error) {
      feedbackToast({ error, msg: '群聊创建失败' });
    }
  };

  const handleChangeGroupInfo = async () => {
    try {
      if (!users || !currentConversation?.groupID) {
        handleCancel();
        return;
      }
      if (
        currentConversation?.groupID &&
        currentConversation.showName !== channelName.trim()
      ) {
        await IMSDK.setGroupInfo({
          groupID: currentConversation?.groupID,
          groupName: channelName,
        });
      }
      const addUser = users.filter((itemA) =>
        groupMemberList.every((itemB) => itemB.userID !== itemA.employeeID)
      );
      const removeUser = groupMemberList.filter((itemA) =>
        users.every((itemB) => itemB.employeeID !== itemA.userID)
      );
      if (addUser && addUser.length > 0) {
        await IMSDK.inviteUserToGroup({
          groupID: currentConversation?.groupID,
          reason: '',
          userIDList: addUser.map((user) => user.employeeID),
        });
      }
      if (removeUser && removeUser.length > 0) {
        await IMSDK.kickGroupMember({
          groupID: currentConversation?.groupID,
          reason: '',
          userIDList: removeUser.map((user) => user.userID),
        });
      }
      if (changeRoleData && changeRoleData.length > 0) {
        const data = changeRoleData.filter((user) =>
          users.some((item) => item.employeeID === user.employeeID)
        );
        await changeRole(data, currentConversation?.groupID);
      }
      if (afterConfirmBtnClick) {
        afterConfirmBtnClick();
      }
      handleCancel();
    } catch (error) {
      console.error('修改群信息失败，原因为：', error);
      feedbackToast({ error, msg: '修改群信息失败' });
    }
  };

  const isDisable = useMemo(() => {
    if (type === 'create') {
      return false;
    } else {
      let flag = true;
      if (
        currentMemberInGroup?.roleLevel === GroupMemberRole.Owner ||
        currentMemberInGroup?.roleLevel === GroupMemberRole.Admin
      ) {
        flag = false;
      }
      return flag;
    }
  }, [currentMemberInGroup?.roleLevel, type]);

  const changeRole = async (data: UserItemType[], groupId: string) => {
    for (const item of data) {
      await IMSDK.setGroupMemberInfo({
        groupID: groupId,
        roleLevel: item.roleLevel,
        userID: item.employeeID,
      });
    }
  };
  const showRole = (roleLevel: number | undefined) => {
    if (roleLevel === GroupMemberRole.Owner) {
      return <div className={styles.role}>群主</div>;
    } else if (roleLevel === GroupMemberRole.Admin) {
      return <div className={styles.role}>管理员</div>;
    } else {
      return '';
    }
  };

  //
  return (
    <div>
      <Modal
        onCancel={() => handleCancel()} // 取消按钮回调
        footer={null} // 隐藏默认的底部按钮
        width={580}
        open={visible}
        // style={{ top: 215 }}
        centered={true}
        closable={false}
        className={styles.channelModal}
        maskClosable={false}
      >
        <div className={styles.modalContent}>
          {/* 群聊名称输入区域 */}
          <div className={styles.header}>
            <Input
              placeholder="#请输入群聊名称"
              value={channelName}
              onChange={(e) => setChannelName(e.target.value)}
              className={styles.channelInput}
              disabled={isDisable}
              maxLength={50}
            />
            <img
              className={styles.delectIcon}
              onClick={() => handleCancel()}
              src={deleteIcon}
              alt="icon"
            />
          </div>

          <SearchUser
            disabled={isDisable}
            handleUserClicked={(item) => handleAddUser(item)}
            style={{ marginBottom: 4, marginTop: 4 }}
          />
          {/* 添加人员区域 */}
          <div className={styles.addMember}>
            <span>添加人员</span>
          </div>
          {/* 成员列表区域 */}
          <div className={styles.memberList}>
            <Virtuoso
              className={styles.virtuosoListContainer}
              data={users || []}
              // computeItemKey={(index, user) => user.employeeCode}
              itemContent={(_index, member) => {
                return (
                  <div key={member.employeeID} className={styles.memberItem}>
                    <OIMAvatar
                      userID={member.employeeID}
                      className={styles.avatar}
                      size={20}
                      borderRadius={4}
                    />
                    <div className={styles.name}>
                      {member.employeeName}
                      {isBotUser(member.employeeID) ? (
                        <div className={classNames(styles.tag, styles.tag_3)}>
                          机器人
                        </div>
                      ) : (
                        <div className={styles.code}>{member.employeeCode}</div>
                      )}
                      {showRole(member?.roleLevel)}
                    </div>
                    {isShowOperateBtn(member) && (
                      <Dropdown
                        overlay={getMenu(member)}
                        trigger={['click']}
                        placement="topLeft"
                        overlayClassName={styles.dropdownArea}
                      >
                        <img
                          className={styles.moreOptions}
                          src={moreMenuIcon}
                        ></img>
                      </Dropdown>
                    )}
                  </div>
                );
              }}
            />
          </div>
          {/* 新建频道按钮 */}
          {type === 'create' ? (
            <div className={styles.newChannel}>
              <Button
                onClick={_.debounce(handleCreateGroup, 500, {
                  leading: true,
                  trailing: false,
                })}
                type="primary"
                disabled={isDisable}
              >
                确认
              </Button>
            </div>
          ) : (
            <div className={styles.newChannel}>
              <Button
                onClick={_.debounce(handleChangeGroupInfo, 500, {
                  leading: true,
                  trailing: false,
                })}
                type="primary"
                disabled={isDisable}
              >
                确认
              </Button>
            </div>
          )}
        </div>
      </Modal>
    </div>
  );
};

export default memo(AddGroupModal);
