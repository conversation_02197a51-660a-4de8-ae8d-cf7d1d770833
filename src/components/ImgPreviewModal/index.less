.imgPreviewModalWarp {
  width: 100%;
  height: 95vh;
  background: rgba(0, 0, 0, 30%);
  position: relative;
  overflow: hidden;
  .header {
    display: none;
    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;
    width: 100%;
    height: 100px;
    background: linear-gradient(
      180deg,
      var(--primary-text-color-1) 0%,
      rgba(29, 28, 29, 0%) 100%
    );
    > div {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px;
      position: relative;
      z-index: 1;
    }
    .closeIcon {
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 99;
      border-radius: 8px;

      &:hover {
        background: #1d1c1dd9;
        cursor: pointer;
      }
    }
  }
  .warp {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .imgBox {
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      // 当没有固定尺寸时，确保容器能够自适应内容
      min-width: 0;
      min-height: 0;
      // 确保容器在父级中居中
      margin: auto;
      > img {
        cursor: grab;
        user-select: none;
        max-width: 100%;
        max-height: 100%;
        // 确保图片在容器中居中显示
        display: block;
        &[style*="position:absolute"] {
          position: absolute;
        }
      }
    }
  }
  .footer {
    display: none;
    flex-direction: column-reverse;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100px;
    background: linear-gradient(
      0deg,
      #000000 0%,
      rgba(29, 28, 29, 45%) 47%,
      rgba(29, 28, 29, 0%) 100%
    );
    > div {
      display: flex;
      align-items: center;
      padding: 12px;
      justify-content: space-between;
    }
    .left {
      display: flex;
      .item {
        width: 35px;
        height: 32px;
        border-radius: 4px;
        border: 1px solid var(--primary-text-color-2);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        &:hover {
          background-color: var(--primary-text-color-1);
        }
      }
    }
    .right {
      > div {
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
      }
      > div:hover {
        background-color: var(--link-color-base-inv-pry);
      }
    }
  }
  .imgBackdrop {
    width: 170%;
    height: 170%;
    position: absolute;
    top: -35%;
    left: -35%;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      filter: blur(100px);
    }
    .bg {
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 30%);
      position: absolute;
      top: 0;
      left: 0;
    }
  }
}

.imgPreviewModalWarp:hover {
  .header {
    display: block;
  }
  .footer {
    display: flex;
  }
}
.progressComponentWarp {
  display: flex;
  align-items: center;
  height: 32px;
  border-radius: 4px;
  border: 1px solid var(--primary-text-color-2);
  margin: 0 10px;
  padding: 0 8px;
  .btn {
    display: flex;
    align-items: center;
    cursor: pointer;
    > img {
      user-select: none;
    }
  }
  .line {
    width: 62px;
    height: 100%;
    margin: 0 8px;
  }
}

.silderWarp {
  :global {
    .linkflow-slider-rail {
      height: 3px;
    }
    .linkflow-slider-track {
      height: 3px;
      background: var(--primary-text-color-pressed);
    }
    .linkflow-slider-handle {
      width: 14px;
      height: 14px;
      background: var(--file-backgroud-color-1);
      margin-top: -6px;
    }
  }
  &:hover {
    :global {
      .linkflow-slider-track {
        background: var(--primary-text-color-pressed);
      }
    }
  }
}
