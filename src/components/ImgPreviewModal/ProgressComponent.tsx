import { FC } from 'react';
import { Slider, Tooltip } from '@ht/sprite-ui';
import addIcon from '@/assets/channel/messageRender/add.png';
import reduceIcon from '@/assets/channel/messageRender/reduce.png';
import styles from './index.less';

interface ProgressComponentProps {
  percent: number;
  onChange: (val: number) => void;
}

const ProgressComponent: FC<ProgressComponentProps> = ({
  percent,
  onChange,
}) => {
  const add = () => {
    onChange(percent + 0.2 > 10 ? 10 : percent + 0.2);
  };

  const reduce = () => {
    onChange(percent - 0.2 < 0.15 ? 0.15 : percent - 0.2);
  };

  return (
    <div className={styles.progressComponentWarp}>
      <Tooltip title="缩小">
        <div className={styles.btn} onClick={reduce}>
          <img src={reduceIcon} />
        </div>
      </Tooltip>
      <div className={styles.line}>
        <Slider
          defaultValue={100}
          value={percent * 100}
          min={15}
          max={1000}
          onChange={(val: number) => {
            onChange(val / 100);
          }}
          tooltip={{
            formatter: (val) => (val ? `${val}%` : ''),
          }}
          className={styles.silderWarp}
        />
      </div>
      <Tooltip title="放大">
        <div className={styles.btn} onClick={add}>
          <img src={addIcon} />
        </div>
      </Tooltip>
    </div>
  );
};
export default ProgressComponent;
