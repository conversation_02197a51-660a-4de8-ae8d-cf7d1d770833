import { MessageItem as MessageItemType } from '@ht/openim-wasm-client-sdk';
import { FC } from 'react';

/**
 * 图片预览模态框属性
 */
export interface ImgPreviewModalProps {
  /** 是否打开模态框 */
  open: boolean;
  /** 关闭模态框的回调函数 */
  onClose: () => void;
  /** 下载图片的回调函数 */
  download?: () => void;
  /** 在新窗口打开图片的回调函数 */
  openView: () => void;
  /** 文件名 */
  fileName: string;
  /** 消息对象 */
  imgMsg?: MessageItemType;
  /** 是否为输入模式 */
  isInput?: boolean;
  /** 图片信息 */
  imgInfo?: {
    url: string;
    width?: number;
    height?: number;
    type?: string;
  };
  /** 显示名称 */
  showName?: string;
  inMultiMessageModal?: boolean; // 是否在聊天记录弹窗里
}

/**
 * 图片预览打开选项
 */
export interface ImgPreviewOpenOptions {
  /** 图片URL */
  url: string;
  /** 文件名 */
  fileName?: string;
  /** 显示名称 */
  showName?: string;
  /** 图片宽度 */
  width?: number;
  /** 图片高度 */
  height?: number;
  /** 图片类型 */
  type?: string;
  /** 已打开的图片预览窗口id */
  imgWinId?: any;
}

/**
 * 图片预览模态框组件类型，包含静态open方法
 */
export interface ImgPreviewModalComponent extends FC<ImgPreviewModalProps> {
  /**
   * 打开图片预览的静态方法
   * @param options 打开选项
   * @returns 包含close方法的对象，用于关闭预览
   */
  open: (options: ImgPreviewOpenOptions) => Promise<{
    close: () => void;
    imgWinId: any;
  }>;
}
