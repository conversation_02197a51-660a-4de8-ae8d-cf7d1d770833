/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-lines */
/* eslint-disable max-statements */
import { FC, useEffect, useRef, useState } from 'react';
import { CloseOutlined } from '@ht-icons/sprite-ui-react';
import { Modal, Tooltip } from '@ht/sprite-ui';
import resetIcon from '@/assets/channel/messageRender/reset.png';
import rotateIcon from '@/assets/channel/messageRender/rotate.png';
import { openImgPreviewWin } from '@/utils/message';
import ProgressComponent from './ProgressComponent';
import FileHeader from '../FilePreviewModal/FileHeader';
import FileFooter from '../FilePreviewModal/FileFooter';
import styles from './index.less';
import {
  ImgPreviewModalProps,
  ImgPreviewOpenOptions,
  ImgPreviewModalComponent,
} from './types';

const ImgPreviewModal: FC<ImgPreviewModalProps> = ({
  open = false,
  isInput = false,
  inMultiMessageModal = false,
  onClose,
  download,
  openView,
  fileName = '',
  imgMsg,
  imgInfo,
  showName = '',
}) => {
  const { pictureElem } = imgMsg || {};
  const {
    url = '',
    width = 0,
    height = 0,
    type,
  } = isInput ? imgInfo : pictureElem?.sourcePicture;
  const bgUrl = isInput ? imgInfo.url : pictureElem?.snapshotPicture.url;

  const [scaleNumber, setScaleNumber] = useState<number>(1);
  const [rotateNum, setRotateNum] = useState<number>(0);
  const [isDrag, setIsDrag] = useState<boolean>(false);
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [bounds, setBounds] = useState({
    left: 0,
    top: 0,
  });
  const [move, setMove] = useState({
    left: 0,
    top: 0,
  });

  const imgRef = useRef<HTMLImageElement>(null);

  useEffect(() => {
    reset();
    if (type === 'image/gif') {
      displayFirstFrame(url);
    }
    document.addEventListener('wheel', wheelChange);
    return () => {
      document.removeEventListener('wheel', wheelChange);
    };
  }, [pictureElem, type, url]);

  const wheelChange = (event: any) => {
    let delta = 1;
    if (event.deltaY > 0) {
      delta = -1;
    }
    const zoomFactor = 0.1;
    setScaleNumber((pre) => {
      return Math.max(0.15, Math.min(pre + delta * (pre * zoomFactor), 10));
    });
  };

  // 鼠标按下
  const onMouseDown = (event: any) => {
    if (imgRef.current) {
      const startX = event.pageX - imgRef.current.offsetLeft;
      const startY = event.pageY - imgRef.current.offsetTop;
      setBounds({
        left: startX,
        top: startY,
      });
      setIsDrag(true);
    }
  };

  // 鼠标松开
  const onMouseUp = () => {
    setIsDrag(false);
  };
  // 鼠标移出
  const mouseOut = () => {
    setIsDrag(false);
  };

  // 鼠标移动
  const mouseMove = (event: any) => {
    if (isDrag) {
      const x = event.pageX - bounds.left;
      const y = event.pageY - bounds.top;
      setMove({
        left: x,
        top: y,
      });
    }
  };

  const reset = () => {
    setScaleNumber(1);
    setRotateNum(0);
    setBounds({
      left: 0,
      top: 0,
    });
    setMove({
      left: 0,
      top: 0,
    });
  };

  const rotateChange = () => {
    setRotateNum((pre) => {
      return pre + 90 > 360 ? 90 : pre + 90;
    });
  };

  const displayFirstFrame = async (gifUrl: string) => {
    try {
      const response = await fetch(gifUrl);
      const blob = await response.blob();
      const img = new Image();

      img.onload = function () {
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;
        const ctx = canvas.getContext('2d');
        if (ctx) {
          ctx.drawImage(img, 0, 0);
        }
        const imgUrl = canvas.toDataURL('image/jpeg');
        setPreviewUrl(imgUrl);
      };

      img.src = URL.createObjectURL(blob);
    } catch (error) {
      console.error('处理GIF失败:', error);
    }
  };

  return (
    <Modal
      title={null}
      footer={null}
      closable={false}
      centered={true}
      open={open}
      onCancel={onClose}
      width={window.innerWidth * 0.95}
      bodyStyle={{
        padding: 0,
      }}
      style={{
        borderRadius: '16px',
        overflow: 'hidden',
      }}
      maskClosable={true}
      keyboard={true}
    >
      <div className={styles.imgPreviewModalWarp}>
        <div className={styles.header}>
          <div>
            <div>
              {isInput || inMultiMessageModal ? (
                <div></div>
              ) : imgMsg ? (
                <FileHeader
                  message={imgMsg}
                  showName={showName}
                  fileName={fileName}
                />
              ) : null}
            </div>
            <div className={styles.closeIcon} onClick={onClose}>
              <CloseOutlined
                style={{
                  color: 'var(--primary-background-color-6)',
                  fontSize: '16px',
                }}
              />
            </div>
          </div>
        </div>
        <div className={styles.warp} onMouseMove={mouseMove}>
          <div className={styles.imgBackdrop}>
            <img src={type === 'image/gif' ? previewUrl : bgUrl}></img>
            <div className={styles.bg}></div>
          </div>
          <div
            className={styles.imgBox}
            style={{
              // 当有明确的width和height时使用，否则让容器自适应
              ...(width && height ? { width, height } : {}),
              maxHeight: '100%',
              maxWidth: '100%',
            }}
          >
            <img
              ref={imgRef}
              src={url}
              onMouseDown={onMouseDown}
              onMouseUp={onMouseUp}
              onMouseLeave={mouseOut}
              onDoubleClick={reset}
              style={{
                transform: `scale(${scaleNumber}) rotate(${rotateNum}deg)`,
                left: move.left,
                top: move.top,
              }}
              draggable={false}
            />
          </div>
        </div>
        <div className={styles.footer}>
          <div>
            <div className={styles.left}>
              <Tooltip title="旋转">
                <div className={styles.item} onClick={rotateChange}>
                  <img src={rotateIcon} />
                </div>
              </Tooltip>
              <ProgressComponent
                percent={scaleNumber}
                onChange={(val: number) => {
                  setScaleNumber(val);
                }}
              />
              {scaleNumber !== 1 && (
                <Tooltip title="重制">
                  <div className={styles.item} onClick={reset}>
                    <img src={resetIcon} />
                  </div>
                </Tooltip>
              )}
            </div>
            {!isInput && imgMsg && (
              <FileFooter
                message={imgMsg}
                download={download || (() => {})}
                openView={openView}
                onClose={onClose}
              />
            )}
          </div>
        </div>
      </div>
    </Modal>
  );
};

// 函数式调用方法
const openImgPreview = async (options: ImgPreviewOpenOptions) => {
  const {
    url,
    fileName = '',
    showName = '',
    width = 0,
    height = 0,
    type,
  } = options;

  // 创建简化的图片信息对象
  const imgInfo = {
    url,
    width,
    height,
    type,
    fileName,
  };

  if (window.electronAPI) {
    openImgPreviewWin({
      ...imgInfo,
    });
    return {
      close: () => {},
    };
  }

  let modalInstance: any = null;

  const handleClose = () => {
    if (modalInstance) {
      modalInstance.destroy();
      modalInstance = null;
    }
  };

  modalInstance = Modal.confirm({
    title: null,
    icon: null,
    width: window.innerWidth * 0.95,
    centered: true,
    maskClosable: true,
    className: 'no-padding-modal',
    style: {
      borderRadius: '16px',
      overflow: 'hidden',
    },
    content: (
      <ImgPreviewModal
        open={true}
        onClose={handleClose}
        openView={() => window.open(url, '_blank')}
        fileName={fileName}
        showName={showName}
        isInput={true}
        imgInfo={imgInfo}
      />
    ),
    onCancel: handleClose,
  });

  return {
    close: handleClose,
    imgWinId: null,
  };
};

// 为组件添加静态方法
const ImgPreviewModalWithOpen = ImgPreviewModal as ImgPreviewModalComponent;
ImgPreviewModalWithOpen.open = openImgPreview;

export default ImgPreviewModalWithOpen;
