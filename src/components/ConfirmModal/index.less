.confirmModalWrapper {
  :global {
    .linkflow-modal-content {
      background: #ffffff;
      box-shadow: 0 2px 4px 2px rgba(0, 0, 0, 4%);
      border-radius: 8px;
      border: 1px solid #dddee0;

      .linkflow-modal-close {
        width: 28px;
        height: 28px;
        top: 24px;
        right: 24px;

        .linkflow-modal-close-x {
          width: 28px;
          height: 28px;

          &:hover {
            border-radius: 4px;
            background: var(--msg-qute-backgroud-color);
          }
        }
      }

      .linkflow-modal-header {
        padding: 24px 24px 20px;
        border-radius: 8px 8px 0 0;
        border-bottom: none;

        .linkflow-modal-title {
          font-size: 18px;
          font-weight: 600;
          color: #1d1c1d;
          line-height: 28px;
        }
      }

      .linkflow-modal-body {
        padding: 0 24px;
      }

      .linkflow-modal-footer {
        border-top: none;
        padding: 20px 24px 24px;
        display: flex;
        align-items: center;
        justify-content: flex-end;

        .linkflow-btn {
          width: 88px;
          height: 36px;
          border-radius: 8px;
          font-size: 14px;
          font-weight: 600;
          line-height: 36px;
          text-align: center;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          border: none;
        }

        .linkflow-btn-default {
          background: #dddee0;
          color: #1d1c1d;
        }

        .linkflow-btn-primary {
          background: #0074e2;
          color: #ffffff;
        }
      }
      .linkflow-modal-footer
        .linkflow-btn
        + .linkflow-btn:not(.linkflow-dropdown-trigger) {
        margin-left: 16px;
      }
    }
  }
}
