import { useMemo } from 'react';
import { shallow } from 'zustand/shallow';
import {
  useConversationStore,
  useGlobalModalStore,
  useUserStore,
  useSearchInfoStore,
} from '@/store';
import OnlineOrTypingStatus from '@/pages/contact/components/OnlineOrTypingStatus';
import defaultIcon from '@/assets/userDetail/defaultIcon.png';
import smileHoverIcon from '@/assets/userDetail/smileHoverIcon.png';
import selfStateIcon from '@/assets/userDetail/selfStateIcon.png';
import editIcon from '@/assets/userDetail/editIcon.png';
import SelfAvatar from '@/pages/contact/components/SelfAvatar';
import { IMSDK } from '@/layouts/BasicLayout';
import { Modal } from '@ht/sprite-ui';
import styles from './index.less';
import { statusInfoList } from '../UserState/SetStateModal/const';

interface setStateModalProps {
  onHide: () => void;
}

const CurrentUserToolBar: React.FC<setStateModalProps> = ({ onHide }) => {
  const { userID, nickname, faceURL, employeeCode } = useUserStore(
    (state) => state.selfInfo
  );
  const { selfStatus, updateSelfState, clearSelfState } = useUserStore();
  const { setStatusModalOpen } = useGlobalModalStore();
  const userLogout = useUserStore((state) => state.userLogout);

  const changeRightArea = useConversationStore(
    (state) => state.changeRightArea
  );

  const { changeSearchRightArea } = useSearchInfoStore(
    (state) => ({
      changeSearchRightArea: state.changeRightArea,
    }),
    shallow
  );

  const handleRightDetailClicked = () => {
    if (location.pathname === '/linkflow/search') {
      changeSearchRightArea('OPEN_PERSON_DETAIL', userID);
    } else {
      changeRightArea('OPEN_PERSON_DETAIL', userID);
    }
    onHide();
  };

  const currentStatusInfo = statusInfoList.find(
    (item) => item.type === selfStatus?.code
  );

  const handleClearStatus = async () => {
    const lastSelfStatus = selfStatus;
    clearSelfState();
    try {
      await IMSDK.setSelfState({});
    } catch (e) {
      console.error('清除状态失败', e);
      updateSelfState(JSON.stringify(lastSelfStatus));
    }
  };

  const handleLogout = () => {
    Modal.confirm({
      content: '确定要退出登陆吗？',
      onOk: () => {
        userLogout();
      },
    });
  };

  const hasNoStatus = useMemo(
    () =>
      (selfStatus?.code == null || selfStatus?.code === '') &&
      (selfStatus?.desc == null || selfStatus?.desc === ''),
    [selfStatus?.code, selfStatus?.desc]
  );

  return (
    <>
      <div className={styles.toolbarContainer}>
        <div className={styles.userInfo}>
          <SelfAvatar style={{ borderRadius: '4px' }} src={faceURL} size={36} />
          <div className={styles.userDetails}>
            <div className={styles.userName}>
              <span>{nickname}</span>
              <span>{employeeCode}</span>
            </div>
            <OnlineOrTypingStatus
              userID={userID}
              showText={true}
              size={8}
              textStyle={{
                marginLeft: '5px',
                fontSize: '10px',
                color: '#6C6F76',
              }}
            />
          </div>
        </div>

        {hasNoStatus ? (
          <div
            className={styles.customStatusButton}
            onClick={() => {
              setStatusModalOpen(true);
              onHide();
            }}
          >
            <img src={defaultIcon}></img>
            <img src={smileHoverIcon}></img>
            <span>更新你的状态</span>
          </div>
        ) : (
          <div
            className={styles.customStatusButton}
            onClick={() => {
              setStatusModalOpen(true);
              onHide();
            }}
          >
            <img src={currentStatusInfo?.imgSrc || selfStateIcon}></img>
            <img src={editIcon}></img>
            <span>{currentStatusInfo?.desc || selfStatus?.desc}</span>
          </div>
        )}
        {!hasNoStatus && (
          <div className={styles.menuItemButton} onClick={handleClearStatus}>
            清除状态
          </div>
        )}
        {/* <div className={styles.menuItemButton}>
          设置为 <strong>离线</strong>
        </div> */}
        {/* <div className={styles.menuItemButton}>暂停通知</div> */}
        <div className={styles.menuSeparator} />
        <div
          className={styles.menuItemButton}
          onClick={handleRightDetailClicked}
        >
          个人档案
        </div>
        {/* <div className={styles.menuItemButton}>首选项</div> */}
        {/* <div className={styles.menuSeparator} /> */}
        {/* <div
          className={classnames(styles.menuItemButton, styles.exit)}
          onClick={() => {
            handleLogout();
          }}
        >
          退出登陆
        </div> */}
      </div>
    </>
  );
};

export default CurrentUserToolBar;
