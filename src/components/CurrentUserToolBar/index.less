.toolbarContainer {
  width: 300px;
  background: #ffffff;
  padding: 20px 0 6px;
  border-radius: 8px;
  color: #1d1c1d;
  display: flex;
  flex-direction: column;

  .userInfo {
    display: flex;
    align-items: center;
    padding-left: 24px;
  }

  .userDetails {
    margin-left: 8px;
  }

  .userName {
    line-height: 17px;
    height: 17px;
    margin-bottom: 8px;
    :nth-child(1) {
      font-size: 12px;
      font-weight: 600;
    }
    :nth-child(2) {
      line-height: 14px;
      margin-left: 4px;
      font-size: 10px;
      font-weight: 400;
    }
  }

  .customStatusButton {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin: 20px 24px 8px 32px;
    height: 32px;
    border-radius: 4px;
    font-size: 12px;
    border: 1px solid rgba(29, 28, 29, 13%);

    &:hover {
      border: 1px solid rgba(29, 28, 29, 50%);

      img {
        &:nth-child(1) {
          display: none;
        }
        &:nth-child(2) {
          display: block;
        }
      }
    }

    img {
      width: 16px;
      height: 16px;
      margin: 0 8px 0 10px;

      &:nth-child(1) {
        display: block;
      }
      &:nth-child(2) {
        display: none;
      }
    }
  }

  .menuItemButton {
    display: flex;
    align-content: center;
    align-items: center;
    color: #1d1c1d;
    padding: 0 24px;
    cursor: pointer;
    line-height: 28px;
    height: 28px;
    font-size: 14px;
    &:hover {
      background-color: #1264a3;
      color: #fff;
    }
    &.exit {
      color: #c01343;
      &:hover {
        background-color: #d60e2f;
        color: #fff;
      }
    }
  }

  .menuSeparator {
    // margin: 8px 0;
    border-top: 1px solid #d8d8d8;
  }
}
