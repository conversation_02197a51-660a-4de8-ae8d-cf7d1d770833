import { getAvatarUrl, getRandomDefaultAvatar } from '@/utils/avatar';
import OnlineOrTypingStatus from '@/pages/contact/components/OnlineOrTypingStatus';
import detailIcon from '@/assets/coversation/detailIcon.png';
import messageIcon from '@/assets/coversation/messageIcon.png';
import { SessionType } from '@ht/openim-wasm-client-sdk';
import { useConversationToggle } from '@/hooks/useConversationToggle';
import { Typography } from '@ht/sprite-ui';
import { shallow } from 'zustand/shallow';
import {
  useConversationStore,
  useUserStore,
  useSearchInfoStore,
} from '@/store';
import useUserInfo from '@/hooks/useUserInfo';
import styles from './index.less';
import {
  getShowDescByStatus,
  getStatusImgSrcByStatus,
} from '../UserState/SetStateModal/const';

const PersonalCard = ({ userID }: { userID: string }) => {
  const { userDetail, userState, userIsBot } = useUserInfo(userID);

  const changeRightArea = useConversationStore(
    (state) => state.changeRightArea
  );

  const { changeSearchRightArea } = useSearchInfoStore(
    (state) => ({
      changeSearchRightArea: state.changeRightArea,
    }),
    shallow
  );

  const { toSpecifiedConversation } = useConversationToggle();
  const handleCreateConversation = () => {
    toSpecifiedConversation({
      sourceID: userID ?? '',
      sessionType: SessionType.Single,
    });
  };

  const handleShowDetail = (userIDParam: string) => {
    if (location.pathname === '/linkflow/search') {
      changeSearchRightArea('OPEN_PERSON_DETAIL', userIDParam);
    } else {
      changeRightArea('OPEN_PERSON_DETAIL', userIDParam);
    }
  };

  const { selfInfo } = useUserStore();

  console.debug({ selfInfo });
  return (
    <div className={styles.personalCardContainer}>
      <div className={styles.personDetail}>
        <div className={styles.avatarContainer}>
          {/* <OIMAvatar userID={userID} src={showDetailData?.faceURL} size={60} /> */}
          <img
            className={styles.avatar}
            src={
              userID === selfInfo.userID
                ? selfInfo.faceURL
                : getAvatarUrl(userID)
            }
            onLoad={(e) => {
              if (!(e.target as any).inited) {
                (e.target as any).inited = true;
                (e.target as any).src =
                  userID === selfInfo.userID
                    ? selfInfo.faceURL
                    : getAvatarUrl(userID);
              }
            }}
            onError={(e) => {
              (e.target as any).src = getRandomDefaultAvatar(userID);
            }}
          />
        </div>
        <div className={styles.infoContainer}>
          <div className={styles.name}>
            <Typography.Text
              ellipsis={true}
              title={`${userDetail?.nickname || ''}  ${
                userDetail?.employeeCode || ''
              }`}
              style={{ maxWidth: '200px' }}
            >
              {`${userDetail?.nickname || ''}  ${
                !userIsBot ? userDetail?.employeeCode || '--' : ''
              }`}
            </Typography.Text>
            <OnlineOrTypingStatus
              userID={userID}
              style={{ height: 28, marginLeft: 12 }}
            />
          </div>
          {userDetail?.positionInfos?.map((item) => {
            return (
              <div
                className={styles.position}
                key={item?.departmentID + item?.positionName}
              >
                <Typography.Text
                  ellipsis={true}
                  title={item?.departmentName}
                  className={styles.left}
                >
                  {item?.departmentName}
                </Typography.Text>
                <Typography.Text
                  ellipsis={true}
                  title={item?.positionName}
                  className={styles.right}
                >
                  {item?.positionName}
                </Typography.Text>
              </div>
            );
          })}
        </div>
      </div>
      <div className={styles.line} />
      {(userState?.code || userState?.desc) && (
        <div className={styles.state}>
          <img src={getStatusImgSrcByStatus(userState)}></img>
          <div className={styles.right}>{getShowDescByStatus(userState)}</div>
        </div>
      )}
      <div className={styles.btns}>
        <div
          className={styles.btn}
          onClick={(e) => {
            e.stopPropagation();
            handleCreateConversation();
          }}
        >
          <img src={messageIcon} />
          消息
        </div>
        <div
          className={styles.btn}
          onClick={(e) => {
            e.stopPropagation();
            handleShowDetail(userID);
          }}
        >
          <img src={detailIcon} />
          查看个人档案
        </div>
      </div>
    </div>
  );
};

export default PersonalCard;
