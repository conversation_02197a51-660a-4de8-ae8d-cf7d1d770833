.personalCardContainer {
  width: 352px;
  background: #ffffff;
  box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 10%);
  border-radius: 8px;
  border: 1px solid var(--primary-background-color-5);
  display: flex;
  flex-direction: column;

  .personDetail {
    display: flex;
    align-items: center;
    margin: 24px;
    .avatarContainer {
      width: 60px;
      height: 60px;
      border-radius: 4px;

      .avatar {
        width: 100%;
        height: 100%;
        border-radius: 4px;
      }
    }

    .infoContainer {
      display: flex;
      flex-direction: column;
    }

    .name,
    .position {
      display: flex;
      margin-left: 16px;
    }

    .name {
      height: 28px;
      font-size: 20px;
      font-weight: 600;
      color: #3f434b;
      line-height: 28px;
    }

    .position {
      margin-top: 4px;
      height: 22px;
      font-size: 16px;
      font-weight: 400;
      color: #3f434b;
      line-height: 22px;

      .left {
        max-width: 100px;
      }
      .right {
        max-width: 120px;
        margin-left: 12px;
        color: #999ba0;
      }
    }
  }
  .line {
    width: 100%;
    height: 1px;
    background: var(--primary-background-color-5);
  }

  .state {
    display: flex;
    // flex-direction: column;
    align-items: center;
    height: 20px;
    font-size: 14px;
    font-weight: 400;
    color: #1d1c1d;
    line-height: 20px;
    margin: 16px 24px 0;
    img {
      margin-right: 8px;
    }
  }
  .btns {
    display: flex;
    margin: 16px 24px;
    .btn {
      background: #ffffff;
      border-radius: 4px;
      border: 1px solid #c6c8ca;
      height: 32px;
      width: 72px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      img {
        width: 20px;
        height: 20px;
        margin-right: 8px;
      }
      &:nth-child(1) {
        width: 88px;
      }
      &:nth-child(2) {
        margin-left: 16px;
        width: 144px;
      }
    }
  }
}
