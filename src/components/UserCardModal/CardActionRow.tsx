import {
  FriendUserItem,
  SelfUserInfo,
} from '@ht/openim-wasm-client-sdk/lib/types/entity';
import { Space, Tooltip } from '@ht/sprite-ui';
import { t } from 'i18next';
import { memo } from 'react';

import cancel from '@/assets/images/common/cancel.png';
import card from '@/assets/images/common/card.png';
import { IMSDK } from '@/layouts/BasicLayout';
import { feedbackToast } from '@/utils/common';
import { emit } from '@/utils/events';

const CardActionRow = ({
  isFriend,
  cardInfo,
  closeOverlay,
}: {
  isFriend?: boolean;
  cardInfo?: Partial<SelfUserInfo & FriendUserItem>;
  closeOverlay: () => void;
}) => {
  const shareCard = () => {
    const cardMessageOptions = {
      userID: cardInfo?.userID ?? '',
      nickname: cardInfo?.nickname ?? '',
      faceURL: cardInfo?.faceURL ?? '',
      ex: cardInfo?.ex ?? '',
    };
    emit('OPEN_CHOOSE_MODAL', {
      type: 'SHARE_CARD',
      extraData: cardMessageOptions,
    });
    closeOverlay();
  };

  const tryUnfriend = () => {
    Modal.confirm({
      title: t('placeholder.unfriend'),
      content: t('toast.confirmUnfriend'),
      onOk: async () => {
        try {
          await IMSDK.deleteFriend(cardInfo!.userID!);
        } catch (error) {
          feedbackToast({ error, msg: t('toast.unfriendFailed') });
        }
      },
    });
    closeOverlay();
  };

  return (
    <div className="flex items-center">
      <Space size={4}>
        <Tooltip title={t('placeholder.share')} placement="bottom">
          <img
            className="cursor-pointer"
            width={18}
            src={card}
            alt=""
            onClick={shareCard}
          />
        </Tooltip>
        {isFriend && (
          <Tooltip title={t('placeholder.unfriend')} placement="bottom">
            <img
              className="cursor-pointer"
              width={18}
              src={cancel}
              alt=""
              onClick={tryUnfriend}
            />
          </Tooltip>
        )}
      </Space>
    </div>
  );
};

export default memo(CardActionRow);
