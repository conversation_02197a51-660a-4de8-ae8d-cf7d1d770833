import { useMemo } from 'react';
import { useSearchInfoStore, useConversationStore } from '@/store';
import rightAreaBack from '@/assets/rightAreaBack.svg';
import styles from './index.less';

const RightAreaBackBtn = () => {
  const rightAreaInSearchList = useSearchInfoStore(
    (state) => state.rightAreaInSearchList
  );
  const changeSearchRightAreaBack = useSearchInfoStore(
    (state) => state.changeRightAreaBack
  );
  const rightAreaInGroupList = useConversationStore(
    (state) => state.rightAreaInGroupList
  );
  const changeRightAreaBack = useConversationStore(
    (state) => state.changeRightAreaBack
  );

  const handleRightAreaBack = () => {
    if (location.pathname === '/linkflow/search') {
      changeSearchRightAreaBack();
    } else {
      changeRightAreaBack();
    }
  };

  const ifRenderBackIcon = useMemo(() => {
    if (location.pathname === '/linkflow/search') {
      return rightAreaInSearchList.length > 1;
    } else {
      return rightAreaInGroupList.length > 1;
    }
  }, [rightAreaInGroupList.length, rightAreaInSearchList.length]);

  return ifRenderBackIcon ? (
    <img
      src={rightAreaBack}
      className={styles.rightAreaBackWrapper}
      onClick={() => handleRightAreaBack()}
    ></img>
  ) : (
    <></>
  );
};

export default RightAreaBackBtn;
