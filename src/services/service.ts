import request from '@/utils/request';
import { platformID } from '@config/imconfig';

export async function queryProjectNotice() {
  return request('/api/project/notice');
}

export async function queryActivities() {
  return request('/api/activities');
}

export async function fakeChartData() {
  return request('/api/fake_chart_data');
}
export async function getAdminToken() {
  return request('/auth/get_admin_token', {
    method: 'POST',
    body: JSON.stringify({
      secret: 'openIM123',
      userID: 'imAdmin',
    }),
    headers: {
      operationID: String(Date.now()),
    },
  });
}
// export async function getUserToken(userID: string, adminToken: string) {
//   return request('/auth/get_user_token', {
//     body: JSON.stringify({
//       platformID: 5,
//       userID,
//     }),
//     method: 'POST',
//     headers: {find
//       token: adminToken,
//       operationID: String(Date.now()),
//     },
//   });
// }

// eipsit登陆接口，用户开发环境获取网关token
export async function getEipsitTokenByJobId(jobId: string) {
  return request.get(`/coco-bff/dev/auth/gateway/token/${jobId}`);
}

// 获取IMtoken接口
export async function queryIMToken(overridePlatformID?: number) {
  return request.get(
    `/coco-bff/openim/user/getToken?platformID=${
      overridePlatformID ? overridePlatformID : platformID
    }`
  );
}

// 心跳——为了eip不超时
export async function heartbeat() {
  return request.get(`/coco-service/crdc-doc/advertisement/get`);
}
