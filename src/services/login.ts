import { useMutation } from 'react-query';
import { v4 as uuidv4 } from 'uuid';
import request from '@/utils/request';
import { getChatToken } from '@/utils/storage';

const platform = 5;

const getAreaCode = (code?: string) =>
  code ? (code.includes('+') ? code : `+${code}`) : code;

// Send verification code
export const useSendSms = () => {
  return useMutation((params: API.Login.SendSmsParams) =>
    request.post('/account/code/send', {
      data: params,
      headers: {
        'operation-id': uuidv4(),
      },
    })
  );
};

// Verify mobile phone number
export const useVerifyCode = () => {
  return useMutation((params: API.Login.VerifyCodeParams) =>
    request.post('/account/code/verify', {
      data: {
        ...params,
        areaCode: getAreaCode(params.areaCode),
      },
      headers: {
        operationID: uuidv4(),
      },
    })
  );
};

// register
export const useRegister = () => {
  return useMutation((params: API.Login.DemoRegisterType) =>
    request.post<{ chatToken: string; imToken: string; userID: string }>(
      '/account/register',
      {
        data: {
          ...params,
          user: {
            ...params.user,
            areaCode: getAreaCode(params.user.areaCode),
          },
          platform,
        },
        headers: {
          operationID: uuidv4(),
        },
      }
    )
  );
};

// reset passwords
export const useReset = () => {
  return useMutation((params: API.Login.ResetParams) =>
    request.post('/account/password/reset', {
      data: {
        ...params,
        areaCode: getAreaCode(params.areaCode),
      },
      headers: {
        operationID: uuidv4(),
      },
    })
  );
};

// change password
export const modifyPassword = async (params: API.Login.ModifyParams) => {
  const token = (await getChatToken()) as string;
  return request.post('/account/password/change', {
    data: params,
    headers: {
      token,
      operationID: uuidv4(),
    },
  });
};

// log in
export const useLogin = () => {
  return useMutation((params: API.Login.LoginParams) =>
    request.post<{ chatToken: string; imToken: string; userID: string }>(
      '/account/login',
      {
        data: {
          ...params,
          platform,
          areaCode: getAreaCode(params.areaCode),
        },
        headers: {
          operationID: uuidv4(),
        },
      }
    )
  );
};

// Get user information
export interface BusinessUserInfo {
  userID: string;
  employeeCode: string;
  password: string;
  account: string;
  phoneNumber: string;
  areaCode: string;
  email: string;
  nickname: string;
  faceURL: string;
  gender: number;
  level: number;
  birth: number;
  allowAddFriend: BusinessAllowType;
  allowBeep: BusinessAllowType;
  allowVibration: BusinessAllowType;
  globalRecvMsgOpt: MessageReceiveOptType;
}

export enum BusinessAllowType {
  Allow = 1,
  NotAllow = 2,
}
export enum MessageReceiveOptType {
  Receive = 0,
  NotReceive = 1,
  NotNotify = 2,
}

export const getBusinessUserInfo = async (userIDs: string[]) => {
  const token = (await getChatToken()) as string;
  return request.post<{ users: BusinessUserInfo[] }>('/user/find/full', {
    data: {
      userIDs,
    },
    headers: {
      operationID: uuidv4(),
      token,
    },
  });
};

export const searchBusinessUserInfo = async (keyword: string) => {
  const token = (await getChatToken()) as string;
  return request.post<{ total: number; users: BusinessUserInfo[] }>(
    '/user/search/full',
    {
      data: {
        keyword,
        pagination: {
          pageNumber: 1,
          showNumber: 1,
        },
      },
      headers: {
        operationID: uuidv4(),
        token,
      },
    }
  );
};

export const updateBusinessUserInfo = async (
  params: Partial<UpdateBusinessUserInfoParams>
) => {
  const token = (await getChatToken()) as string;
  return request.post<void>('/user/update', {
    data: params,
    headers: {
      operationID: uuidv4(),
      token,
    },
  });
};
