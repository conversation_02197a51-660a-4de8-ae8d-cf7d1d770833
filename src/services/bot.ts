import request from '@/utils/request';

interface BotRequest {
  /** 应用ID */
  appId: number;
  /** 机器人ID */
  botId: number;
  /** 请求类型：1-指令，2-交互消息 */
  type: 1 | 2;
  /** 指令/交互消息ID */
  actionId: string;
  /** 指令/交互消息名称 */
  actionName?: string;
  /** 会话类型：1-单聊，3-群聊 */
  sessionType: 1 | 3;
  /** 用户ID，sessionType=1时必填 */
  userId?: string;
  /** 群组ID，sessionType=3时必填 */
  groupId?: string;
  /** 交互数据 */
  data?: Record<string, any>;
  /** 消息数据 */
  msgInfo?: Record<string, any>;
}
export async function interactionBot(params: BotRequest) {
  return request('/linkflow/bot/interaction/invoke', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: {
      'content-Type': 'application/json',
    },
  });
}
