
export const AXIOSURL = "http://************/openim/api"

// export const IMWsAddr = (location.host.indexOf('localhost') >-1? `${location.protocol.replace('http','ws')}//***********:10001` : `${location.protocol.replace('http','ws')}//${location.host}/openimws`)
export const IMWsAddr = `${location.protocol.replace('http','ws')}//${location.host}/linkFlowService/openimws`
export const IMApiAddr = `${location.protocol}//${location.host}/linkFlowService/openim`

export const platformID = 5

export const coreWasmPath = () => {
    const href = location.href

    if (process.env.REACT_APP_ENV === 'sit') return '/PortalStatics/openIM.wasm';
    else if (href.indexOf('localhost') >=0 || href.indexOf('127.0.0.1')>=0 ) {
        return '/PortalStatics/openIM.local.wasm';
    } else if (href.indexOf('eipdev') >= 0) {
        return '/PortalStatics/openIM.dev.wasm';
    }
    else {
        return '/PortalStatics/openIM.wasm';
    }
}

export const sqlWasmPath = `/PortalStatics/sql-wasm.wasm`
