// https://umijs.org/config/
import path from 'path';
import { defineConfig } from '@oula/oula';
import proxy from './proxy';
import routes from './routes';
import theme from './theme';
import BaseSettings from './BaseSettings';

function resolve(dir: string) {
  console.log(path.join(__dirname, '..', dir));
  return path.join(__dirname, '..', dir);
}

const { REACT_APP_ENV } = process.env;

// console.log('process.env',process.env.REACT_APP_ENV)
export default defineConfig({
  bundlerConfig: {
    output: {
      filenameHash: true,
      //生产环境下publicPath
      assetPrefix: `/${BaseSettings.appName}/`,
      cssModules: {
         auto: /^(?!.*global\.)(?!.*node_modules)(?!.*src[\\/]components[\\/]Crepe[\\/]theme).*/,
      }
    },
    
    dev: {
      //测试环境下publicPath
      assetPrefix: `/${BaseSettings.appName}/`
    },
    tools: {
      less: {
        lessOptions: {
          modifyVars: theme,
        },
      },
    },
    source: {
      alias: {
        '@config': resolve('config'),
      }
    },
    html: {
      title: BaseSettings.title,
      tags: [
        {
          tag: 'script',
          attrs: { src: '/PortalStatics/wasm_exec.js' },
          head: true,
          append: true,
          publicPath: false,
          hash: false,
        },
        {
          tag: 'script',
          attrs: { src: '/iat.min.js' },
          head: true,
          append: true,
          publicPath: REACT_APP_ENV!=='dev',
          hash: true,
        },
        {
          tag: 'script',
          attrs: { src: '/adapter-latest.js' },
          head: true,
          append: true,
          publicPath: REACT_APP_ENV!=='dev',
          hash: true,
        },
        {
          tag: 'script',
          attrs: { src: '/lame.min.js' },
          head: true,
          append: true,
          publicPath: REACT_APP_ENV!=='dev',
          hash: true,
        },
        {
          tag: 'script',
          attrs: { src: '/tts.min.js' },
          head: true,
          append: true,
          publicPath: REACT_APP_ENV!=='dev',
          hash: true,
        },
        // {
        //   tag: 'script',
        //   attrs: { src: '/iat.min.js.map' },
        //   head: true,
        //   append: true,
        //   publicPath: false,
        //   hash: true,
        // },
      ],
    },
    performance: {
      removeMomentLocale: true,
        chunkSplit:{
        strategy: 'split-by-size',
        minSize: 30000,
        maxSize: 50000,
      },
      bundleAnalyze: {},

    },
    server: {
      proxy: proxy[REACT_APP_ENV || 'dev'],
    },
  },
  dva: {
    hmr: false,
  },
  history: {
    type: 'browser',
  },
  locale: {
    default: 'zh-CN',
    spriteui: true
  },
  spriteui: {
    // theme: 'dark', // uncomment this line when using dark theme
  },
  // dynamicImport: {
  //   loading: '@/components/PageLoading/index',
  // },
  // 为 hash 路由添加统一前缀，最终格式为 `${hashPrefix}${route.path}`
  plugins: ['@ht/umi-plugin-hashprefix'],
  hashPrefix: `/${BaseSettings.appName}`,
  routes,
  // 埋点配置，需先在数据中台接入项目，获取 productName 和 productId
  xlog: {
    appName: 'appName', // 添加对应应用名称，方便定位
    initOption: {
      // xlog的初始化参数
      submitType: 'myTrack',
      from: 'appName', // 添加对应应用名称，方便定位
      types: [
        'consoleMehods',
        'windowError',
        'unhandledrejection',
        'performance',
      ], // 'performance' 性能监控
      routeConfig: {
        routeType: 'history', // 路由类型'hash' | 'history'，默认为hash
        dynamicRoutes: [], // 动态路由集合，例：/home/<USER>/detail，当页面url匹配到该路由时，xlog上报的页面path将是动态路由('/home/<USER>/detail')
        redirectRoute: {}, // 重定向的路由配置，例：{ '/' : '/home/<USER>' }代表首页'/'被重定向到'/home/<USER>'，当页面url匹配到'/'，xlog上报的页面path将是'/home/<USER>'
      },
      consoleMethods: ['error', 'warn', 'info'],
      myTrackConfig: {
        product_id: '286', // 数字中台申请
        product_name: 'product_name', // 数字中台申请
        getEnv: () =>
          window.location.host === 'eip.htsc.com.cn'
            ? 'prd_outer'
            : 'prd_inner_test',
      },
    },
  },
  nodePolyfill: {
    fs: 'empty',
    path: 'empty'
  }

});
